# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/2cc49517-cc53-416c-b905-a93453fe2701

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/2cc49517-cc53-416c-b905-a93453fe2701) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- Stripe Connect Express for payment processing
- Supabase for database and authentication
- GetStream for real-time chat functionality

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/2cc49517-cc53-416c-b905-a93453fe2701) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes it is!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

## Stripe Connect Integration

This project includes Stripe Connect Express integration for payment processing. Key features:

- Create Stripe Connect Express accounts
- Complete Stripe onboarding process
- Access Stripe dashboard
- Process payments with platform fees
- Delete and recreate accounts

To use this functionality:

1. Set up your Stripe API keys in the `.env.local` file
2. Start the development environment with `npm run dev:all` (includes Stripe server)
3. Navigate to the Stripe Connect page in the application

## Server Architecture

The project uses the following servers:

1. **Stripe API Server** (`server/index.js`):
   - Handles all Stripe-related functionality
   - Runs on port 3001

2. **Development Server** (Vite):
   - Serves the frontend application
   - Runs on port 8082

3. **Supabase Edge Functions**:
   - Handles email functionality and other serverless operations
   - Hosted on Supabase.com

See `server-cleanup.md` for details on recent server architecture changes.

## GetStream Chat Integration

This project includes GetStream Chat integration for real-time messaging. Key features:

1. **Real-time Chat**: Instant messaging between users
2. **System Messages**: Automated messages for task status changes
3. **Mobile-Friendly UI**: Responsive chat interface for all devices
4. **Vercel API Routes**: Serverless functions for GetStream token generation and channel management

To use this functionality:

1. Set up your GetStream API keys in the `.env.local` file
2. Start the development environment with `npm run dev:all` (includes GetStream token server)
3. Navigate to any task page to use the chat functionality

See `VERCEL_DEPLOYMENT.md` for details on deploying the GetStream integration to Vercel.

## Password Reset Functionality

This project includes a complete password reset flow using Supabase's authentication system:

1. **Forgot Password Page**: Users can request a password reset by entering their email
2. **Reset Password Page**: Users can set a new password after clicking the reset link in their email
3. **Secure Token Handling**: Ensures only valid reset links work

### Required Configuration

For the password reset functionality to work correctly in production, you need to:

1. **Configure the Supabase Site URL**:
   - Go to the Supabase dashboard
   - Select your project
   - Go to Authentication > URL Configuration
   - Set the Site URL to your production URL (e.g., `https://classtasker.com`)
   - This ensures that password reset links point to your production site

2. **Set Redirect URLs**:
   - In the same Authentication settings page, add your production URL to the Redirect URLs list
   - Format: `https://classtasker.com/reset-password`
   - This allows Supabase to redirect users back to your application after authentication

These settings are critical for the password reset flow to work correctly in production.
