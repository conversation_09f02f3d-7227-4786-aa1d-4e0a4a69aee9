# Security Guidelines for ClassTasker

## 🔒 Critical Security Rules

### 1. **NEVER Expose Service Role Keys in Client-Side Code**
- ❌ **NEVER** use `VITE_SUPABASE_SERVICE_ROLE_KEY` in client-side code
- ❌ **NEVER** hardcode service role keys in any client-accessible files
- ✅ **ONLY** use service role keys in:
  - Supabase Edge Functions
  - Server-side API routes
  - Backend services

### 2. **Safe vs Unsafe Environment Variables**

#### ✅ **SAFE for Client-Side (VITE_ prefix)**
- `VITE_SUPABASE_URL` - Public Supabase URL
- `VITE_SUPABASE_ANON_KEY` - Public anon key (designed for client-side)
- `VITE_GETSTREAM_API_KEY` - Public GetStream API key
- `VITE_GOOGLE_MAPS_API_KEY` - Public Google Maps key
- `VITE_STRIPE_PUBLIC_KEY` - Public Stripe key

#### ❌ **NEVER Client-Side (Server-only)**
- `SUPABASE_SERVICE_ROLE_KEY` - Admin access to entire database
- `GETSTREAM_API_SECRET` - Server-side GetStream secret
- `STRIPE_SECRET_KEY` - Server-side Stripe secret
- `RESEND_API_KEY` - Email service secret
- Any API secrets or private keys

### 3. **Environment Variable Security**

#### Development (.env.local)
```bash
# Safe for client-side
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Server-side only (for Edge Functions/API routes)
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
GETSTREAM_API_SECRET=your-getstream-secret
```

#### Production (Vercel/Supabase)
- Set client-side variables in Vercel environment variables
- Set server-side secrets in Supabase Edge Function secrets
- Never commit actual credentials to repository

### 4. **Debug Panel Security**
- Debug panel should NEVER log actual API keys
- Use `[PRESENT]` or `[NOT_SET]` instead of actual values
- Disable debug panel in production builds

### 5. **File Security Checklist**

#### ❌ **Files That Should NEVER Contain Secrets**
- `public/env-config.js` - Public file, use placeholders only
- `vite.config.ts` - Build config, no hardcoded secrets
- `lovable.config.js` - Public config, no secrets
- Any file in `src/` directory - Client-side code
- Any file in `public/` directory - Publicly accessible

#### ✅ **Secure Patterns**
```javascript
// ❌ WRONG - Hardcoded secret
const apiKey = "sk_live_actual_secret_key";

// ✅ CORRECT - Environment variable
const apiKey = process.env.STRIPE_SECRET_KEY;

// ✅ CORRECT - Client-side safe
const publicKey = import.meta.env.VITE_STRIPE_PUBLIC_KEY;
```

### 6. **Database Security**
- Use Row Level Security (RLS) policies instead of service role keys
- Create SECURITY DEFINER functions for admin operations
- Never bypass RLS in client-side code

### 7. **API Security**
- All admin operations must be server-side
- Use Supabase Edge Functions for privileged operations
- Validate user permissions on server-side

## 🚨 Security Incident Response

If credentials are accidentally exposed:

1. **Immediately rotate all exposed keys**
2. **Check git history for credential exposure**
3. **Update all deployment environments**
4. **Review access logs for unauthorized usage**
5. **Update this security documentation**

## 📋 Security Audit Checklist

- [ ] No service role keys in client-side code
- [ ] No hardcoded credentials in any files
- [ ] Debug panels don't expose actual secrets
- [ ] Environment files are in .gitignore
- [ ] Production uses proper secret management
- [ ] RLS policies are properly configured
- [ ] All admin operations are server-side

## 🔧 Secure Development Practices

1. **Use environment variables for all secrets**
2. **Prefix client-safe variables with VITE_**
3. **Keep server secrets without VITE_ prefix**
4. **Regular security audits of codebase**
5. **Never commit .env files with real credentials**
6. **Use placeholder values in example files**

## 📞 Security Contact

For security issues, contact: [security contact information]

---

**Remember: When in doubt, keep it server-side!**
