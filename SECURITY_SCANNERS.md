# Security Scanners Guide 🛡️

This document outlines the comprehensive security scanning setup for the Classtasker project.

## 🔧 Installed Scanners

### 1. **Secretlint** - Secret Detection
- **Purpose**: Detects hardcoded secrets, API keys, and credentials
- **Status**: ✅ Installed and configured
- **Config**: `.secretlintrc.json`
- **Command**: `npm run security:secrets`

### 2. **Audit-CI** - Dependency Vulnerabilities
- **Purpose**: Scans npm dependencies for known vulnerabilities
- **Status**: ✅ Installed and configured
- **Command**: `npm run security:deps`
- **Threshold**: Moderate and above

### 3. **ESLint Security Plugin** - Code Analysis
- **Purpose**: Static analysis for security issues in JavaScript/TypeScript
- **Status**: ✅ Installed (needs configuration)
- **Command**: `npm run lint`

### 4. **Custom Security Scanner** - Comprehensive Scan
- **Purpose**: Runs all security checks and generates reports
- **Status**: ✅ Custom script created
- **Command**: `npm run security:scan`
- **Output**: `security-report.json`

## 🚀 Quick Start

### Run All Security Scans
```bash
npm run security:scan
```

### Individual Scans
```bash
# Secret detection only
npm run security:secrets

# Dependency vulnerabilities only
npm run security:deps

# NPM audit only
npm run security:audit

# Existing security check
npm run security:check
```

## 📊 Current Security Status

### ✅ **SECURE** - No Secrets Found
- **Secretlint**: No hardcoded secrets detected
- **Custom patterns**: No suspicious patterns found
- **All API keys**: Properly using environment variables

### ⚠️ **DEPENDENCIES** - 11 Vulnerabilities Found
- **Moderate**: 7 vulnerabilities
- **High**: 4 vulnerabilities
- **Critical**: 0 vulnerabilities

**Key Issues**:
- `esbuild` ≤0.24.2 (dev server vulnerability)
- `path-to-regexp` 4.0.0-6.2.2 (ReDoS vulnerability)
- `undici` ≤5.28.5 (random values & DoS vulnerabilities)

**Risk Assessment**: **LOW** (mostly dev dependencies)

## 🔍 Recommended Additional Scanners

### 1. **GitLeaks** (Highly Recommended)
```bash
# Download from: https://github.com/gitleaks/gitleaks/releases
# Windows: gitleaks_8.18.0_windows_x64.zip

# Usage:
gitleaks detect --source . --verbose
```

### 2. **TruffleHog** (Git History Scanning)
```bash
# Download from: https://github.com/trufflesecurity/trufflehog/releases

# Usage:
trufflehog git https://github.com/drewrogers2025/class-tasker-connect.git
```

### 3. **Semgrep** (Advanced Code Analysis)
```bash
# Install Python first, then:
pip install semgrep

# Usage:
semgrep --config=auto .
```

### 4. **Snyk** (Comprehensive Security Platform)
```bash
npm install -g snyk
snyk auth
snyk test
```

## 🔧 Configuration Files

### `.secretlintrc.json`
```json
{
  "rules": [
    {
      "id": "@secretlint/secretlint-rule-preset-recommend"
    }
  ],
  "ignoreFiles": [
    "node_modules/**",
    "dist/**",
    "build/**",
    ".git/**",
    "*.log",
    "package-lock.json",
    "yarn.lock",
    ".env.example",
    "scripts/.env.example",
    "SECURITY.md"
  ]
}
```

## 🚨 Security Patterns Detected

The custom scanner checks for:

### Critical Patterns
- Stripe Live Secret Keys (`sk_live_*`)
- PostgreSQL Connection Strings
- MongoDB Connection Strings

### High Risk Patterns
- Stripe Test Secret Keys (`sk_test_*`)
- Google API Keys (`AIza*`)
- JWT Tokens (`eyJ*`)
- Hardcoded Passwords
- Hardcoded Secrets

### Allowed Exceptions
- Documentation examples in `SECURITY.md`
- Template placeholders in `.env.example` files
- Test fixtures with placeholder values

## 📈 Continuous Security

### Pre-commit Hooks
```bash
# Setup git hooks to prevent committing secrets
npm run security:setup-hooks
```

### CI/CD Integration
Add to your CI pipeline:
```yaml
- name: Security Scan
  run: npm run security:scan
```

### Regular Scans
- **Daily**: `npm run security:secrets`
- **Weekly**: `npm run security:deps`
- **Before releases**: `npm run security:scan`

## 🛠️ Fixing Vulnerabilities

### Dependency Issues
```bash
# Try automatic fixes
npm audit fix

# Force fixes (may cause breaking changes)
npm audit fix --force

# Manual updates
npm update package-name
```

### Secret Detection Issues
1. **Remove hardcoded secrets** from code
2. **Add to environment variables**
3. **Rotate exposed keys** immediately
4. **Update `.gitignore`** to prevent future exposure

## 📞 Support

For security concerns or questions:
- **Email**: <EMAIL>
- **Documentation**: See `SECURITY.md`
- **Emergency**: Rotate keys immediately, then investigate

## 🔄 Updates

This security setup should be reviewed and updated:
- **Monthly**: Update scanner dependencies
- **Quarterly**: Review and update security patterns
- **After incidents**: Enhance detection based on lessons learned

---

**Last Updated**: 2025-05-26  
**Next Review**: 2025-06-26
