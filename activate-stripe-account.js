import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseKey) {
  console.error('SUPABASE_SERVICE_ROLE_KEY is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function activateStripeAccount() {
  try {
    console.log('Activating Stripe account...');
    
    // Get all Stripe accounts with pending status
    const { data: pendingAccounts, error: accountsError } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('account_status', 'pending');
    
    if (accountsError) {
      console.error('Error fetching pending accounts:', accountsError);
      return;
    }
    
    console.log(`Found ${pendingAccounts.length} accounts with 'pending' status`);
    
    // Update each account to active status
    for (const account of pendingAccounts) {
      console.log(`\nAccount ID: ${account.account_id}`);
      console.log(`User ID: ${account.user_id}`);
      
      // Update the account status to active
      const { data: updatedAccount, error: updateError } = await supabase
        .from('stripe_accounts')
        .update({
          charges_enabled: true,
          payouts_enabled: true,
          account_status: 'active',
        })
        .eq('id', account.id)
        .select();
      
      if (updateError) {
        console.error(`Error updating account ${account.id}:`, updateError);
      } else {
        console.log(`✅ Successfully updated account ${account.id} to active status`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

activateStripeAccount();
