// Script to add task_id column to invoices table
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addTaskIdToInvoices() {
  try {
    console.log('Adding task_id column to invoices table...');
    
    // First, check if the task_id column already exists
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'invoices')
      .eq('table_schema', 'public');
    
    if (columnsError) {
      console.error('Error checking columns:', columnsError);
      
      // Try a different approach
      console.log('Trying a different approach to check columns...');
      
      // Get a sample invoice to check its structure
      const { data: invoice, error: invoiceError } = await supabase
        .from('invoices')
        .select('*')
        .limit(1)
        .single();
      
      if (invoiceError) {
        console.error('Error fetching invoice:', invoiceError);
      } else {
        console.log('Sample invoice structure:');
        console.log(invoice);
        
        if (invoice.task_id) {
          console.log('task_id column already exists in the invoices table.');
          return;
        }
      }
    } else {
      console.log('Columns in invoices table:');
      columns.forEach(column => console.log(`- ${column.column_name}`));
      
      const hasTaskId = columns.some(column => column.column_name === 'task_id');
      if (hasTaskId) {
        console.log('task_id column already exists in the invoices table.');
        return;
      }
    }
    
    // Add the task_id column to the invoices table
    console.log('Adding task_id column...');
    
    // Use raw SQL to add the column
    const { error: addColumnError } = await supabase.rpc('execute_sql', {
      sql_query: `
        ALTER TABLE public.invoices
        ADD COLUMN IF NOT EXISTS task_id UUID REFERENCES public.tasks(id);
      `
    });
    
    if (addColumnError) {
      console.error('Error adding task_id column using RPC:', addColumnError);
      
      // Try a different approach with direct SQL
      console.log('Trying direct SQL execution...');
      
      // Execute SQL directly
      const { data, error } = await supabase.from('_sql').select('*').execute(`
        ALTER TABLE public.invoices
        ADD COLUMN IF NOT EXISTS task_id UUID REFERENCES public.tasks(id);
      `);
      
      if (error) {
        console.error('Error with direct SQL execution:', error);
        
        // Try using the SQL API
        console.log('Trying SQL API...');
        
        try {
          // Create a temporary SQL file
          const fs = require('fs');
          const sqlFile = 'add_task_id.sql';
          
          fs.writeFileSync(sqlFile, `
            ALTER TABLE public.invoices
            ADD COLUMN IF NOT EXISTS task_id UUID REFERENCES public.tasks(id);
          `);
          
          // Execute the SQL file using the Supabase CLI
          const { execSync } = require('child_process');
          execSync(`npx supabase db execute --file ${sqlFile}`);
          
          console.log('Column added using Supabase CLI');
          
          // Clean up
          fs.unlinkSync(sqlFile);
        } catch (cliError) {
          console.error('Error using Supabase CLI:', cliError);
          
          // Last resort: try using the REST API directly
          console.log('Trying REST API directly...');
          
          const response = await fetch(`${supabaseUrl}/rest/v1/rpc/execute_sql`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'apikey': supabaseServiceKey,
              'Authorization': `Bearer ${supabaseServiceKey}`
            },
            body: JSON.stringify({
              sql_query: `
                ALTER TABLE public.invoices
                ADD COLUMN IF NOT EXISTS task_id UUID REFERENCES public.tasks(id);
              `
            })
          });
          
          if (!response.ok) {
            const errorData = await response.json();
            console.error('Error with REST API:', errorData);
          } else {
            console.log('Column added using REST API');
          }
        }
      } else {
        console.log('Column added using direct SQL execution');
      }
    } else {
      console.log('task_id column added successfully.');
    }
    
    // Verify the column was added
    console.log('\nVerifying task_id column was added...');
    
    const { data: updatedInvoice, error: updatedInvoiceError } = await supabase
      .from('invoices')
      .select('*')
      .limit(1)
      .single();
    
    if (updatedInvoiceError) {
      console.error('Error fetching updated invoice:', updatedInvoiceError);
    } else {
      console.log('Updated invoice structure:');
      console.log(updatedInvoice);
      
      if (updatedInvoice.hasOwnProperty('task_id')) {
        console.log('task_id column was successfully added to the invoices table.');
      } else {
        console.log('task_id column was NOT added to the invoices table.');
      }
    }
    
    console.log('\nTask ID column addition completed.');
  } catch (error) {
    console.error('Error adding task_id to invoices:', error);
  }
}

addTaskIdToInvoices();
