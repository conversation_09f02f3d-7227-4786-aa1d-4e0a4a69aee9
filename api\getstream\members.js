/**
 * GetStream Channel Members API Route
 *
 * This API route adds members to GetStream channels.
 * It's designed to work as a serverless function on Vercel.
 */

import { StreamChat } from 'stream-chat';

// Load environment variables
// For Vercel deployment, use GETSTREAM_API_KEY and GETSTREAM_API_SECRET
// For local development, use VITE_GETSTREAM_API_KEY and GETSTREAM_API_SECRET (server-only)
const apiKey = process.env.GETSTREAM_API_KEY || process.env.VITE_GETSTREAM_API_KEY; // SECURITY: VITE_GETSTREAM_API_KEY is safe (public key)
const apiSecret = process.env.GETSTREAM_API_SECRET; // SECURITY: Never use VITE_ prefix for secrets

// Log environment variables for debugging
console.log('API Key available:', !!apiKey);
console.log('API Secret available:', !!apiSecret);

// Create a server-side client for GetStream
let serverClient;

// Initialize the server client if API key and secret are available
if (apiKey && apiSecret) {
  serverClient = StreamChat.getInstance(apiKey, apiSecret);
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version'
  );

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if API key and secret are available
    if (!apiKey || !apiSecret || !serverClient) {
      console.error('Error: GetStream API key or secret is missing in environment variables.');
      return res.status(500).json({ error: 'Server configuration error' });
    }

    // Get the channel ID from the query parameters
    const { channelId } = req.query;

    // Get the members from the request body
    const { members } = req.body;

    if (!channelId) {
      return res.status(400).json({ error: 'Channel ID is required' });
    }

    if (!members || !Array.isArray(members) || members.length === 0) {
      return res.status(400).json({ error: 'Members array is required' });
    }

    console.log(`Adding ${members.length} members to channel:`, channelId);

    try {
      // Get the channel
      const channel = serverClient.channel('messaging', channelId);

      // Add members to the channel
      const result = await channel.addMembers(members);

      console.log('Members added successfully to channel:', channelId);

      return res.status(200).json({
        channelId,
        members: result.members,
        status: 'updated'
      });
    } catch (error) {
      console.error('Error adding members to channel:', error);
      return res.status(500).json({ error: 'Failed to add members to channel' });
    }
  } catch (error) {
    console.error('Error adding members to channel:', error);
    return res.status(500).json({ error: 'Failed to add members to channel' });
  }
}
