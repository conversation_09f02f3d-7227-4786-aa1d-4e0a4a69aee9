/**
 * GetStream Token API Route
 *
 * This API route generates a token for a GetStream user.
 * It's designed to work as a serverless function on Vercel.
 */

import { StreamChat } from 'stream-chat';

// Load environment variables
// For Vercel deployment, use GETSTREAM_API_KEY and GETSTREAM_API_SECRET
// For local development, use VITE_GETSTREAM_API_KEY and GETSTREAM_API_SECRET (server-only)
const apiKey = process.env.GETSTREAM_API_KEY || process.env.VITE_GETSTREAM_API_KEY;
const apiSecret = process.env.GETSTREAM_API_SECRET; // SECURITY: Never use VITE_ prefix for secrets

// Log environment variables for debugging
console.log('API Key available:', !!apiKey);
console.log('API Secret available:', !!apiSecret);

// Create a server-side client for GetStream
let serverClient;

// Initialize the server client if API key and secret are available
if (apiKey && apiSecret) {
  serverClient = StreamChat.getInstance(apiKey, apiSecret);
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version'
  );

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check if API key and secret are available
    if (!apiKey || !apiSecret || !serverClient) {
      console.error('Error: GetStream API key or secret is missing in environment variables.');
      return res.status(500).json({ error: 'Server configuration error' });
    }

    // Get the user ID from the request body
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('Generating token for user:', userId);

    // Generate a token for the user
    const token = serverClient.createToken(userId);

    console.log('Token generated successfully');

    // Return the token
    return res.status(200).json({ token });
  } catch (error) {
    console.error('Error generating token:', error);
    return res.status(500).json({ error: 'Failed to generate token' });
  }
}
