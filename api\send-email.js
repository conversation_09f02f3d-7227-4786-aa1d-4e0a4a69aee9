import nodemailer from 'nodemailer';

export default async function handler(req, res) {
  console.log('DEBUG: API route handler called');
  console.log('DEBUG: Request method:', req.method);
  // Enable CORS
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

  // Handle preflight request
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('DEBUG: Accessing request body');
    console.log('DEBUG: Request body:', JSON.stringify(req.body, null, 2));
    const { config, params } = req.body;

    if (!config || !params) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    // Validate email configuration
    if (config.provider === 'smtp') {
      if (!config.smtpHost || !config.smtpPort || !config.smtpUsername || !config.smtpPassword) {
        return res.status(400).json({
          error: 'Missing SMTP configuration. Please provide host, port, username, and password.'
        });
      }
    } else {
      return res.status(400).json({
        error: `Unsupported email provider: ${config.provider}`
      });
    }

    // Validate email parameters
    if (!params.to || !params.subject) {
      return res.status(400).json({
        error: 'Missing email parameters. Please provide to and subject.'
      });
    }

    // Log the email sending request
    console.log('Sending email with configuration:', {
      provider: config.provider,
      from: `${config.fromName || 'Classtasker'} <${config.fromEmail}>`,
      to: params.to,
      subject: params.subject,
      smtpHost: config.smtpHost,
      smtpPort: config.smtpPort
    });

    // Create SMTP transporter
    console.log('Creating SMTP transporter with config:', {
      host: config.smtpHost,
      port: config.smtpPort,
      secure: config.smtpSecure || false,
      auth: {
        user: config.smtpUsername,
        pass: '********' // Masked for security
      }
    });

    const transporter = nodemailer.createTransport({
      host: config.smtpHost,
      port: config.smtpPort,
      secure: config.smtpSecure || false,
      auth: {
        user: config.smtpUsername,
        pass: config.smtpPassword,
      },
      debug: true, // Enable debug output
      logger: true // Log to console
    });

    // Verify connection configuration
    try {
      console.log('Verifying SMTP connection...');
      await transporter.verify();
      console.log('SMTP connection verified successfully');
    } catch (verifyError) {
      console.error('SMTP connection verification failed:', verifyError);
      return res.status(400).json({
        success: false,
        error: `SMTP connection failed: ${verifyError.message}`,
        details: verifyError.stack
      });
    }

    // Send email
    try {
      console.log('Sending email...');
      const info = await transporter.sendMail({
        from: `"${config.fromName || 'Classtasker'}" <${config.fromEmail}>`,
        to: params.to,
        subject: params.subject,
        html: params.body,
      });

      console.log('Email sent successfully:', info);
      return res.status(200).json({
        success: true,
        message: `Email sent to ${params.to} using ${config.provider} provider. Message ID: ${info.messageId}`
      });
    } catch (sendError) {
      console.error('Error sending email:', sendError);
      return res.status(500).json({
        success: false,
        error: `Error sending email: ${sendError.message}`,
        details: sendError.stack
      });
    }
  } catch (error) {
    console.error('Unexpected error in email API:', error);
    return res.status(500).json({
      success: false,
      error: `Unexpected error: ${error.message}`,
      details: error.stack
    });
  }
}
