// <PERSON>ript to apply the comprehensive RLS fix to the database
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Create Supabase client with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Function to execute SQL directly
async function executeSql(sql) {
  try {
    // Split the SQL into individual statements
    const statements = sql
      .replace(/--.*$/gm, '') // Remove comments
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    console.log(`Executing ${statements.length} SQL statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i];
      console.log(`\nExecuting statement ${i + 1}/${statements.length}:`);
      console.log(stmt.substring(0, 100) + (stmt.length > 100 ? '...' : ''));

      const { error } = await supabase.rpc('exec_sql', { sql: stmt });
      
      if (error) {
        // If exec_sql function doesn't exist, try using REST API
        if (error.message.includes('Could not find the function')) {
          console.log('exec_sql function not found, trying direct query...');
          
          // For direct queries, we need to use the REST API
          const response = await fetch(`${supabaseUrl}/rest/v1/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'apikey': supabaseServiceKey,
              'Authorization': `Bearer ${supabaseServiceKey}`,
              'Prefer': 'params=single-object'
            },
            body: JSON.stringify({
              query: stmt
            })
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(`Error executing statement: ${errorText}`);
          } else {
            console.log('Statement executed successfully');
          }
        } else {
          console.error(`Error executing statement: ${error.message}`);
        }
      } else {
        console.log('Statement executed successfully');
      }
    }

    console.log('\nSQL execution completed');
    return { success: true };
  } catch (error) {
    console.error('Error executing SQL:', error);
    return { success: false, error };
  }
}

// Function to apply the RLS fix
async function applyRlsFix() {
  try {
    console.log('=== Applying Comprehensive RLS Fix ===\n');
    
    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'sql', 'comprehensive_rls_fix.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log(`Read SQL file: ${sqlFilePath}`);
    console.log(`SQL file size: ${sql.length} characters`);
    
    // Execute the SQL
    console.log('\nExecuting SQL to fix RLS policies...');
    const result = await executeSql(sql);
    
    if (result.success) {
      console.log('\n✅ RLS fix applied successfully!');
      console.log('\nThe fix addresses recursion issues by:');
      console.log('1. Using SECURITY DEFINER functions to bypass RLS for complex authorization checks');
      console.log('2. Eliminating self-referential policies that caused infinite recursion');
      console.log('3. Simplifying the policy structure while maintaining security');
      
      console.log('\nTo verify the fix:');
      console.log('1. Try accessing profiles as an admin user');
      console.log('2. Check that users can only see profiles in their organization');
      console.log('3. Verify that admins can update profiles in their organization');
    } else {
      console.error('\n❌ Failed to apply RLS fix:', result.error);
    }
  } catch (error) {
    console.error('Error applying RLS fix:', error);
  }
}

// Run the script
applyRlsFix();