const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyInvoicesTableUpdate() {
  try {
    console.log('Applying invoices table update...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'sql', 'update_invoices_table.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('execute_sql', { sql });
    
    if (error) {
      console.error('Error applying invoices table update:', error);
      return;
    }
    
    console.log('Invoices table update applied successfully');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

applyInvoicesTableUpdate();
