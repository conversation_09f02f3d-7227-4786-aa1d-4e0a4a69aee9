// Script to apply location schema updates to both organizations and tasks tables
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyLocationSchemaUpdates() {
  try {
    console.log('Applying location schema updates...');
    
    // Step 1: Update organizations table
    console.log('\nUpdating organizations table schema...');
    const orgSqlPath = path.join(__dirname, 'sql/update_organization_location_schema.sql');
    const orgSql = fs.readFileSync(orgSqlPath, 'utf8');
    
    const { error: orgError } = await supabase.rpc('exec_sql', { sql: orgSql });
    
    if (orgError) {
      console.error('Error updating organizations table:', orgError);
    } else {
      console.log('Successfully updated organizations table schema');
    }
    
    // Step 2: Update tasks table
    console.log('\nUpdating tasks table schema...');
    const taskSqlPath = path.join(__dirname, 'sql/update_task_location_schema.sql');
    const taskSql = fs.readFileSync(taskSqlPath, 'utf8');
    
    const { error: taskError } = await supabase.rpc('exec_sql', { sql: taskSql });
    
    if (taskError) {
      console.error('Error updating tasks table:', taskError);
    } else {
      console.log('Successfully updated tasks table schema');
    }
    
    // Step 3: Verify the schema updates
    console.log('\nVerifying schema updates...');
    
    // Check organizations table
    const { data: orgColumns, error: orgColumnsError } = await supabase
      .rpc('get_table_columns', { table_name: 'organizations' });
    
    if (orgColumnsError) {
      console.error('Error getting organizations table columns:', orgColumnsError);
    } else {
      console.log('Organizations table columns:');
      const locationColumns = orgColumns.filter(col => 
        col.column_name.startsWith('location_')
      );
      
      locationColumns.forEach(col => {
        console.log(`- ${col.column_name} (${col.data_type})`);
      });
    }
    
    // Check tasks table
    const { data: taskColumns, error: taskColumnsError } = await supabase
      .rpc('get_table_columns', { table_name: 'tasks' });
    
    if (taskColumnsError) {
      console.error('Error getting tasks table columns:', taskColumnsError);
    } else {
      console.log('\nTasks table columns:');
      const locationColumns = taskColumns.filter(col => 
        col.column_name.startsWith('location_')
      );
      
      locationColumns.forEach(col => {
        console.log(`- ${col.column_name} (${col.data_type})`);
      });
    }
    
    console.log('\nSchema updates completed');
  } catch (error) {
    console.error('Error applying schema updates:', error);
  }
}

// Run the function
applyLocationSchemaUpdates();
