import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or service role key. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyNotificationsFix() {
  try {
    console.log('Applying notifications fix...');

    // Read the SQL file
    const sqlFilePath = path.join(process.cwd(), 'sql', 'fix_notifications.sql');
    const sqlQuery = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL query
    const { error } = await supabase.rpc('execute_sql', {
      sql_query: sqlQuery
    });

    if (error) {
      console.error('Error applying notifications fix:', error);
      return;
    }

    console.log('Notifications fix applied successfully.');

    // Verify the fix
    console.log('\nVerifying notifications table and policies...');

    // Check if the table exists
    const { data: tableExists, error: tableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_name', 'notifications')
      .eq('table_schema', 'public')
      .single();

    if (tableError) {
      console.error('Error checking if notifications table exists:', tableError);
    } else {
      console.log('Notifications table exists:', !!tableExists);
    }

    // Check RLS policies
    const { data: policies, error: policiesError } = await supabase
      .from('pg_policies')
      .select('*')
      .eq('tablename', 'notifications');

    if (policiesError) {
      console.error('Error checking RLS policies:', policiesError);
    } else {
      console.log('RLS policies for notifications table:');
      console.log(policies);
    }

    // Create a test notification
    console.log('\nCreating a test notification...');

    // Get admin user for testing
    const { data: adminUser, error: adminError } = await supabase
      .from('profiles')
      .select('id, email')
      .eq('role', 'admin')
      .limit(1)
      .single();

    if (adminError) {
      console.error('Error getting admin user:', adminError);
      return;
    }

    console.log(`Found admin user: ${adminUser.email} (${adminUser.id})`);

    // Create test notification
    const { data: notification, error: notificationError } = await supabase
      .from('notifications')
      .insert({
        user_id: adminUser.id,
        type: 'system',
        message: 'Test notification after fix',
        read: false,
        email_sent: false
      })
      .select()
      .single();

    if (notificationError) {
      console.error('Error creating test notification:', notificationError);
    } else {
      console.log('Test notification created successfully:', notification);
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

applyNotificationsFix().finally(() => process.exit(0));
