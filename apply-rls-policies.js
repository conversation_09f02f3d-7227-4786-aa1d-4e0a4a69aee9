// <PERSON><PERSON><PERSON> to apply RLS policies to the database
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

// Create Supabase client with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Function to execute SQL directly using the REST API
async function executeSql(sql) {
  try {
    // Using the REST API to execute SQL
    const response = await fetch(`${supabaseUrl}/rest/v1/sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseServiceKey,
        'Authorization': `Bearer ${supabaseServiceKey}`
      },
      body: JSON.stringify({
        query: sql
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`SQL API error: ${response.status} - ${errorText}`);
      return { data: null, error: { message: `SQL API error: ${response.status}` } };
    }

    const result = await response.json();
    return { data: result, error: null };
  } catch (error) {
    console.error('Error executing SQL:', error);
    return { data: null, error };
  }
}

// Function to apply RLS policies
async function applyRlsPolicies() {
  try {
    console.log('Applying RLS policies...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'sql', 'fix_rls_recursion.sql');
    console.log(`Reading SQL file: ${sqlFilePath}`);
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Split the SQL into individual statements
    const statements = sql.split(';').filter(stmt => stmt.trim() !== '');

    console.log(`Found ${statements.length} SQL statements`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i].trim() + ';';
      console.log(`\nExecuting statement ${i + 1}/${statements.length}...`);

      try {
        const { data, error } = await executeSql(stmt);

        if (error) {
          console.error(`Error executing statement ${i + 1}:`, error);
          console.log('Statement:', stmt);
        } else {
          console.log(`Statement ${i + 1} executed successfully.`);
        }
      } catch (error) {
        console.error(`Error executing statement ${i + 1}:`, error);
        console.log('Statement:', stmt);
      }
    }

    console.log('\nRLS policies applied successfully.');
  } catch (error) {
    console.error('Error applying RLS policies:', error);
  }
}

// Run the function
applyRlsPolicies();
