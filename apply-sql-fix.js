import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import fs from 'fs';
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or service role key. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applySqlFix() {
  try {
    console.log('Applying SQL function fix...');
    
    // Read the SQL file
    const sql = fs.readFileSync('./sql/fix_accept_invitation.sql', 'utf8');
    
    // Execute the SQL directly
    const { error } = await supabase.rpc('exec_sql', {
      sql_query: sql
    });
    
    if (error) {
      console.error('Error executing SQL via RPC:', error);
      
      // Try a different approach - direct query
      console.log('Trying direct query...');
      
      // This is a workaround since we can't execute arbitrary SQL directly
      // We'll create a temporary function that executes our SQL
      const tempFunctionSql = `
      CREATE OR REPLACE FUNCTION temp_apply_fix()
      RETURNS VOID
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        ${sql}
      END;
      $$;
      
      SELECT temp_apply_fix();
      
      DROP FUNCTION temp_apply_fix();
      `;
      
      const { error: tempError } = await supabase.rpc('exec_sql', {
        sql_query: tempFunctionSql
      });
      
      if (tempError) {
        console.error('Error with temp function approach:', tempError);
        console.log('Please run the SQL manually in the Supabase SQL editor');
        console.log('SQL to run:');
        console.log(sql);
        return;
      }
    }
    
    console.log('SQL function updated successfully');
    
    // Test the function with the user
    const userId = '00ca6287-18e6-4360-a2d1-0d48929c067b';
    
    // Get user info
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);
    
    if (userError) {
      console.error('Error fetching user info:', userError);
      return;
    }
    
    // Get invitation info
    const { data: invitations, error: invitationError } = await supabase
      .from('user_invitations')
      .select('*')
      .eq('email', userData.user.email)
      .order('created_at', { ascending: false });
    
    if (invitationError || !invitations || invitations.length === 0) {
      console.error('Error fetching invitation info or no invitations found');
      return;
    }
    
    const latestInvitation = invitations[0];
    
    // Call the function
    console.log('Testing updated function...');
    const { data: result, error: funcError } = await supabase.rpc('accept_invitation', {
      token_param: latestInvitation.token,
      user_id_param: userId
    });
    
    if (funcError) {
      console.error('Error calling accept_invitation function:', funcError);
    } else {
      console.log('Function result:', result);
      
      // Check if the profile was updated
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
      
      if (profileError) {
        console.error('Error fetching profile:', profileError);
      } else {
        console.log('Profile after function call:', profile);
      }
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

applySqlFix().catch(console.error).finally(() => process.exit(0));
