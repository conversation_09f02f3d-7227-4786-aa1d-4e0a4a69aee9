// <PERSON><PERSON><PERSON> to apply the SQL function update
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create Supabase client with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applySqlUpdate() {
  try {
    console.log('Applying SQL function update...');

    // Read the SQL files
    const acceptInvitationSql = fs.readFileSync('./sql/update_accept_invitation_function.sql', 'utf8');
    const profileTriggerSql = fs.readFileSync('./sql/create_profile_trigger.sql', 'utf8');

    // Combine the SQL statements
    const sql = acceptInvitationSql + '\n' + profileTriggerSql;

    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', {
      sql_query: sql
    });

    if (error) {
      // If the exec_sql RPC function doesn't exist, try direct query
      console.error('Error executing SQL via RPC:', error);
      console.log('Trying direct query...');

      const { error: directError } = await supabase.from('_exec_sql').select('*').eq('query', sql).single();

      if (directError) {
        console.error('Error executing SQL directly:', directError);

        // Last resort: try to execute the SQL in chunks
        console.log('Trying to execute SQL in chunks...');

        const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);

        for (const stmt of statements) {
          const { error: stmtError } = await supabase.rpc('exec_sql', {
            sql_query: stmt + ';'
          });

          if (stmtError) {
            console.error(`Error executing statement: ${stmt}`, stmtError);
          } else {
            console.log(`Successfully executed statement: ${stmt.substring(0, 50)}...`);
          }
        }
      } else {
        console.log('Successfully executed SQL directly');
      }
    } else {
      console.log('Successfully executed SQL via RPC');
    }

    // Test the updated function
    console.log('\nTesting the updated accept_invitation function...');

    // Get a test invitation
    const { data: invitations, error: invError } = await supabase
      .from('user_invitations')
      .select('*')
      .limit(1);

    if (invError || !invitations || invitations.length === 0) {
      console.error('No invitations found to test with');
      return;
    }

    const invitation = invitations[0];
    console.log(`Using invitation: ${invitation.id} (${invitation.token})`);

    // Get a test user
    const { data: { users }, error: usersError } = await supabase.auth.admin.listUsers();

    if (usersError || !users || users.length === 0) {
      console.error('No users found to test with');
      return;
    }

    const user = users.find(u => u.email === invitation.email) || users[0];
    console.log(`Using user: ${user.id} (${user.email})`);

    // Call the function
    const { data: result, error: funcError } = await supabase.rpc('accept_invitation', {
      token_param: invitation.token,
      user_id_param: user.id
    });

    if (funcError) {
      console.error('Error calling accept_invitation function:', funcError);
    } else {
      console.log('Function result:', result);

      // Check if the profile was updated
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError) {
        console.error('Error fetching profile:', profileError);
      } else {
        console.log('Profile after function call:', profile);
      }
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

applySqlUpdate();
