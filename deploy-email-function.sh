#!/bin/bash

# Deploy the send-email Edge Function to Supabase
echo "Deploying send-email Edge Function to Supabase..."

# Navigate to the project root
cd "$(dirname "$0")"

# Make sure Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Supabase CLI is not installed. Please install it first."
    echo "See: https://supabase.com/docs/guides/cli"
    exit 1
fi

# Deploy the function
supabase functions deploy send-email --project-ref your-project-ref

echo "Deployment complete!"
