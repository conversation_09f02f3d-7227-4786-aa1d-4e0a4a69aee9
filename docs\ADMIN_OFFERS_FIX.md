# Admin Offers Management Fix

## Issue

Admins were unable to see the accept or reject buttons for offers on tasks they didn't own. This was preventing admins from managing offers for tasks created by other users in the organization.

## Root Cause

The issue was caused by missing Row Level Security (RLS) policies in the database. The existing policies only allowed:

1. Task owners to update offers for their tasks
2. Users to read/delete their own offers or offers for tasks they own
3. Users to create their own offers

There was no policy that specifically granted admins access to all offers. Even though a user had the admin role in their profile, they didn't have the necessary database permissions to accept or reject offers for tasks they didn't own.

## Solution

We created new RLS policies that allow users with the admin role to view, update, and delete all offers in the system, regardless of who created the task or the offer.

The solution consists of three new policies:

1. `allow_admins_to_view_all_offers`: Allows admins to view all offers
2. `allow_admins_to_update_all_offers`: Allows admins to update all offers
3. `allow_admins_to_delete_all_offers`: Allows admins to delete all offers

Each policy checks if the current user has the 'admin' role in the profiles table.

## Implementation

The fix is implemented in three files:

1. `sql/add_admin_offers_policies.sql`: The SQL script that adds the new policies
2. `run-admin-offers-policies.cjs`: A script to run the SQL file using the Supabase client
3. `verify-admin-offer-management.cjs`: A script to verify that admins can manage offers after applying the policies

## How to Apply the Fix

1. Make sure you have the correct environment variables set in `.env.local`:
   - `SUPABASE_URL`: Your Supabase project URL
   - `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key (for admin access)

2. Run the script to apply the policies:
   ```
   node run-admin-offers-policies.cjs
   ```

3. Verify the fix:
   ```
   node verify-admin-offer-management.cjs
   ```

## Expected Outcome

After applying the fix, admins should be able to:

1. See all offers for all tasks in the system
2. Accept or reject offers for tasks they didn't create
3. Manage the entire offer workflow for the organization

This ensures that admins can properly oversee and manage the task assignment process across the organization.