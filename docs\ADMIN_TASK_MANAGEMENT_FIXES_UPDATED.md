# Admin Task Management Fixes

This document describes three issues that were preventing admins from properly managing tasks and offers, along with their solutions.

## Issue 1: Admins Can't See Accept/Reject Buttons for Offers

### Problem

Admins were unable to see the accept or reject buttons for offers on tasks they didn't own. This was preventing admins from managing offers for tasks created by other users in the organization.

### Root Cause

The issue was caused by missing Row Level Security (RLS) policies in the database. The existing policies only allowed:

1. Task owners to update offers for their tasks
2. Users to read/delete their own offers or offers for tasks they own
3. Users to create their own offers

There was no policy that specifically granted admins access to all offers. Even though a user had the admin role in their profile, they didn't have the necessary database permissions to accept or reject offers for tasks they didn't own.

### Solution

We created new RLS policies that allow users with the admin role to view, update, and delete all offers in the system, regardless of who created the task or the offer.

The solution consists of three new policies:

1. `allow_admins_to_view_all_offers`: Allows admins to view all offers
2. `allow_admins_to_update_all_offers`: Allows admins to update all offers
3. `allow_admins_to_delete_all_offers`: Allows admins to delete all offers

Each policy checks if the current user has the 'admin' role in the profiles table.

### SQL Fix

```sql
-- SQL script to add policies allowing admins to manage all offers
-- This fixes the issue where admins can't see accept/reject buttons for offers on tasks they don't own

-- Enable RLS on the offers table if not already enabled
ALTER TABLE offers ENABLE ROW LEVEL SECURITY;

-- Drop any existing admin policies to avoid conflicts
DROP POLICY IF EXISTS allow_admins_to_view_all_offers ON offers;
DROP POLICY IF EXISTS allow_admins_to_update_all_offers ON offers;
DROP POLICY IF EXISTS allow_admins_to_delete_all_offers ON offers;

-- Create a policy to allow admins to view all offers
CREATE POLICY allow_admins_to_view_all_offers
ON offers
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

-- Create a policy to allow admins to update all offers
CREATE POLICY allow_admins_to_update_all_offers
ON offers
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

-- Create a policy to allow admins to delete all offers
CREATE POLICY allow_admins_to_delete_all_offers
ON offers
FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

-- Add a comment explaining the purpose of these policies
COMMENT ON TABLE offers IS 'Offers made by suppliers for tasks. Admins can manage all offers, while regular users can only manage their own offers or offers for tasks they own.';
```

## Issue 2: Admins Can't See Task Completion Component

### Problem

Admins were unable to see the task completion component that allows them to mark a task as complete and proceed to payment. This component was only visible to the task owner, not to admins.

### Root Cause

In the `FixedTask.tsx` file, the TaskCompletionActions component was conditionally rendered based on the `isTaskOwner` variable:

```jsx
{/* Task Completion Actions - Only shown to task owners for assigned tasks */}
{isTaskOwner && (
  <>
    <TaskCompletionActions
      task={task}
      acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
      onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
    />
    
    <TaskPaymentActions
      task={task}
      acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
      onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
    />
  </>
)}
```

The `isTaskOwner` variable was defined as:

```javascript
const isTaskOwner = user && task && user.id === task.user_id;
```

This meant that only the user who created the task could see the TaskCompletionActions component. Admins who didn't create the task couldn't see this component.

### Solution

We modified the condition to show the TaskCompletionActions component to both task owners and admins:

```jsx
{/* Task Completion Actions - Shown to task owners and admins for assigned tasks */}
{(isTaskOwner || isAdmin) && (
  <>
    <TaskCompletionActions
      task={task}
      acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
      onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
    />
    
    <TaskPaymentActions
      task={task}
      acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
      onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
    />
  </>
)}
```

### Code Fix

The fix is a simple change to the condition in `src/pages/FixedTask.tsx`:

```diff
- {isTaskOwner && (
+ {(isTaskOwner || isAdmin) && (
```

## Issue 3: TaskCompletionActions Component Doesn't Use isAdmin

### Problem

Even after fixing the condition in FixedTask.tsx, the TaskCompletionActions component itself doesn't have access to the isAdmin variable, which might be needed for additional checks or debugging.

### Root Cause

The TaskCompletionActions component doesn't import the useAuth hook and doesn't check for admin status internally.

### Solution

We modified the TaskCompletionActions component to import the useAuth hook and get the isAdmin value:

```jsx
import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
// ... other imports

const TaskCompletionActions = ({ task, acceptedOffer, onTaskUpdated }: TaskCompletionActionsProps) => {
  const { toast } = useToast();
  const { isAdmin } = useAuth();
  // ... rest of the component
```

We also added isAdmin to the debug info:

```jsx
// Debug info
console.log('TaskCompletionActions:', {
  taskStatus: task.status,
  acceptedOffer,
  offers: acceptedOffer ? 'Has accepted offer' : 'No accepted offer',
  isAdmin
});
```

### Code Fix

The fix adds the useAuth hook and isAdmin variable to the TaskCompletionActions component:

```diff
+ import { useAuth } from '@/contexts/AuthContext';

const TaskCompletionActions = ({ task, acceptedOffer, onTaskUpdated }: TaskCompletionActionsProps) => {
  const { toast } = useToast();
+ const { isAdmin } = useAuth();
  // ... rest of the component

  // Debug info
  console.log('TaskCompletionActions:', {
    taskStatus: task.status,
    acceptedOffer,
    offers: acceptedOffer ? 'Has accepted offer' : 'No accepted offer',
+   isAdmin
  });
```

## How to Apply the Fixes

### Fix 1: Admin Offers Management

1. Make sure you have the correct environment variables set in `.env.local`:
   - `SUPABASE_URL`: Your Supabase project URL
   - `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key (for admin access)

2. Run the script to apply the policies:
   ```
   node run-admin-offers-policies.cjs
   ```

3. Verify the fix:
   ```
   node verify-admin-offer-management.cjs
   ```

### Fix 2: Admin Task Completion in FixedTask.tsx

1. Run the script to apply the fix:
   ```
   node apply-task-completion-fix.cjs
   ```

2. Alternatively, manually edit `src/pages/FixedTask.tsx`:
   - Find the line with: `{isTaskOwner && (`
   - Replace it with: `{(isTaskOwner || isAdmin) && (`

### Fix 3: Add isAdmin to TaskCompletionActions Component

1. Run the script to apply the fix:
   ```
   node apply-task-completion-actions-fix.cjs
   ```

2. Alternatively, manually edit `src/components/tasks/TaskCompletionActions.tsx`:
   - Add the import: `import { useAuth } from '@/contexts/AuthContext';`
   - Add the isAdmin check: `const { isAdmin } = useAuth();`
   - Add isAdmin to the debug info

## Expected Outcome

After applying all three fixes, admins should be able to:

1. See all offers for all tasks in the system
2. Accept or reject offers for tasks they didn't create
3. Mark tasks as complete and proceed to payment for tasks they didn't create
4. Manage the entire task workflow for the organization

This ensures that admins can properly oversee and manage the task assignment and completion process across the organization.