# Email Notification Strategy for ClassTasker

This document outlines the strategy for implementing a dual email approach in the ClassTasker application:
- Using classtasker.com for all public users (suppliers)
- Allowing schools to use their own domains for their internal users

## Current Architecture Analysis

The current codebase already has:

1. An email service implementation (`emailService.ts`)
2. Support for SMTP configuration
3. A concept of school-specific email configurations
4. Various notification types (invitations, task updates, etc.)

## Proposed Architecture

```mermaid
flowchart TD
    A[Email Request] --> B{Determine Recipient Type}
    B -->|School User| C{School has Email Config?}
    B -->|Public User| D[Use ClassTasker Email]
    C -->|Yes| E[Use School Domain]
    C -->|No| D
    E --> F[Send Email]
    D --> F
```

## Implementation Plan

### 1. Enhance Email Configuration Types

First, let's enhance the email configuration types to support the dual approach:

```typescript
// In src/types/email.ts

// Add recipient type enum
export enum RecipientType {
  SCHOOL_USER = 'school_user',
  PUBLIC_USER = 'public_user'
}

// Update SendEmailParams to include recipient type
export interface SendEmailParams {
  to: string;
  subject: string;
  body: string;
  schoolId?: string;
  recipientType?: RecipientType; // New field
}
```

### 2. Update Email Service Logic

Modify the email service to select the appropriate configuration based on recipient type:

```typescript
// In src/services/emailService.ts

async sendEmail(params: SendEmailParams): Promise<boolean> {
  try {
    // Get base email config
    let config = await this.getEmailConfig();
    
    // Determine if we should use school-specific config
    if (
      params.recipientType === RecipientType.SCHOOL_USER && 
      params.schoolId
    ) {
      const schoolConfig = await this.getSchoolEmailConfig(params.schoolId);
      
      // Only use school config if it exists and is properly configured
      if (
        schoolConfig && 
        schoolConfig.provider && 
        schoolConfig.fromEmail &&
        (
          (schoolConfig.provider === 'smtp' && 
           schoolConfig.smtpHost && 
           schoolConfig.smtpPort && 
           schoolConfig.smtpUsername && 
           schoolConfig.smtpPassword) ||
          (schoolConfig.provider === 'sendgrid' && schoolConfig.apiKey) ||
          (schoolConfig.provider === 'mailgun' && 
           schoolConfig.apiKey && 
           schoolConfig.mailgunDomain)
        )
      ) {
        // Merge school config with base config
        config = {
          ...config,
          provider: schoolConfig.provider,
          fromEmail: schoolConfig.fromEmail,
          fromName: schoolConfig.fromName || config.fromName,
          apiKey: schoolConfig.apiKey || config.apiKey,
          mailgunDomain: schoolConfig.mailgunDomain || config.mailgunDomain,
          smtpHost: schoolConfig.smtpHost || config.smtpHost,
          smtpPort: schoolConfig.smtpPort || config.smtpPort,
          smtpUsername: schoolConfig.smtpUsername || config.smtpUsername,
          smtpPassword: schoolConfig.smtpPassword || config.smtpPassword,
          smtpSecure: schoolConfig.smtpSecure !== undefined ? 
            schoolConfig.smtpSecure : config.smtpSecure
        };
        
        console.log(`Using school-specific email config for school ${params.schoolId}`);
      } else {
        console.log(`No valid school email config for school ${params.schoolId}, using ClassTasker email`);
      }
    } else if (params.recipientType === RecipientType.PUBLIC_USER) {
      console.log('Using ClassTasker email for public user');
      // Ensure we're using the default ClassTasker email config
      config = {
        ...config,
        fromEmail: '<EMAIL>',
        fromName: 'ClassTasker'
      };
    }
    
    // Import the emailSender module
    const { sendEmail } = await import('./emailSender');
    
    // Send the email with the determined config
    const result = await sendEmail(config, params);
    
    if (!result.success) {
      console.error('Failed to send email:', result.message);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
}
```

### 3. Update Notification Service

Modify the notification service to specify recipient type:

```typescript
// In src/services/notificationService.ts

async sendEmailNotification(
  email: string,
  type: NotificationType,
  relatedId: string,
  relatedType: RelatedType,
  message: string,
  title: string,
  schoolId?: string,
  isPublicUser?: boolean // New parameter
): Promise<boolean> {
  try {
    // Create email subject and body as before...
    
    // Determine recipient type
    const recipientType = isPublicUser ? 
      RecipientType.PUBLIC_USER : 
      RecipientType.SCHOOL_USER;
    
    // Send the email with recipient type
    return await emailService.sendEmail({
      to: email,
      subject,
      body,
      schoolId,
      recipientType // Add recipient type
    });
  } catch (error) {
    console.error('Error sending email notification:', error);
    return false;
  }
}
```

### 4. Update Invitation Email Logic

```typescript
// In src/services/emailService.ts

async sendInvitationEmail(
  email: string, 
  token: string, 
  organizationName: string, 
  role: string, 
  schoolId?: string
): Promise<boolean> {
  try {
    // Create invitation email content as before...
    
    // For invitations, always use SCHOOL_USER type since invitations
    // are always for internal school users
    return await this.sendEmail({
      to: email,
      subject,
      body,
      schoolId,
      recipientType: RecipientType.SCHOOL_USER
    });
  } catch (error) {
    console.error('Error sending invitation email:', error);
    return false;
  }
}
```

### 5. Update User-Related Services

Update services that send emails to specify the recipient type:

```typescript
// Example for task notifications in hooks/use-offers.ts

// When sending notification to a supplier
await notificationService.createNotification({
  userId: offer.user_id,
  type: 'offer_accepted',
  relatedId: offer.id,
  relatedType: 'offer',
  message: `Your offer of £${data.amount} was accepted by ${senderName}`,
  title: taskData.title,
  sendEmail: true,
  isPublicUser: true // Mark as public user
});

// When sending notification to a school user
await notificationService.createNotification({
  userId: task.user_id,
  type: 'new_offer',
  relatedId: data.id,
  relatedType: 'offer',
  message: `New offer of £${data.amount} from ${senderName}`,
  title: taskData.title,
  sendEmail: true,
  schoolId: task.organization_id, // Include school ID
  isPublicUser: false // Mark as school user
});
```

### 6. Database Schema Updates

```sql
-- Add email configuration to organizations table
ALTER TABLE organizations 
ADD COLUMN email_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN email_from_name VARCHAR(255),
ADD COLUMN email_from_email VARCHAR(255),
ADD COLUMN email_provider VARCHAR(50) DEFAULT 'smtp',
ADD COLUMN email_smtp_host VARCHAR(255),
ADD COLUMN email_smtp_port INTEGER,
ADD COLUMN email_smtp_username VARCHAR(255),
ADD COLUMN email_smtp_password VARCHAR(255),
ADD COLUMN email_smtp_secure BOOLEAN DEFAULT TRUE,
ADD COLUMN email_api_key VARCHAR(255),
ADD COLUMN email_mailgun_domain VARCHAR(255);

-- Add user type to profiles table (if not already present)
ALTER TABLE profiles
ADD COLUMN user_type VARCHAR(50) DEFAULT 'school_user';
```

### 7. User Interface for Email Configuration

Create an organization settings page with email configuration:

```mermaid
flowchart TD
    A[Organization Settings] --> B[Email Configuration]
    B --> C[Enable Custom Email]
    B --> D[Email Provider Selection]
    D -->|SMTP| E[SMTP Settings]
    D -->|SendGrid| F[API Key]
    D -->|Mailgun| G[API Key & Domain]
    B --> H[From Name & Email]
    B --> I[Test Email Button]
```

## Implementation Phases

### Phase 1: Core Infrastructure
1. Update email types and interfaces
2. Modify email service to support recipient types
3. Update database schema

### Phase 2: Service Integration
1. Update notification service
2. Update invitation service
3. Modify user-related services to specify recipient types

### Phase 3: User Interface
1. Create organization email settings UI
2. Add email testing functionality
3. Implement user type management

### Phase 4: Testing & Deployment
1. Test with various email configurations
2. Monitor email deliverability
3. Deploy to production

## User Identification Strategy

To properly identify user types:

```mermaid
flowchart TD
    A[User Authentication] --> B{Has Organization Role?}
    B -->|Yes| C[School User]
    B -->|No| D[Public User]
    C --> E[Use School Email if Available]
    D --> F[Use ClassTasker Email]
```

## Email Template Considerations

### School-Branded Templates
For emails sent from school domains:
- Include school logo in header
- Use school colors for buttons and accents
- Include school name in footer
- Maintain consistent branding with school website

### ClassTasker-Branded Templates
For emails sent from classtasker.com:
- Use ClassTasker logo and branding
- Maintain neutral design suitable for all recipients
- Include clear identification of the platform

### Template Structure
Create a modular template system:
1. **Header**: Logo (school or ClassTasker) + email title
2. **Content**: Main message with customizable sections
3. **Action Buttons**: Styled according to sender branding
4. **Footer**: Legal information, unsubscribe links, and sender details

## Security and Compliance

1. **Email Authentication**:
   - Ensure proper SPF, DKIM, and DMARC records for all domains
   - Implement SMTP authentication for all outgoing emails

2. **Privacy Compliance**:
   - Include unsubscribe links in all marketing/notification emails
   - Store user email preferences and honor opt-outs
   - Include privacy policy links in email footers

3. **Data Protection**:
   - Encrypt sensitive email configuration data in the database
   - Implement access controls for email configuration settings
   - Log all email sending activities for audit purposes

4. **Regulatory Compliance**:
   - Ensure compliance with GDPR, CAN-SPAM, and other relevant regulations
   - Include sender physical address in commercial emails
   - Clearly identify the email purpose in the subject line

## Monitoring and Analytics

1. **Delivery Tracking**:
   - Implement email delivery status tracking
   - Monitor bounce rates and delivery issues

2. **Engagement Metrics**:
   - Track open rates and click-through rates
   - Analyze user engagement with different email types

3. **Error Handling**:
   - Create a robust error handling system for failed emails
   - Implement retry logic for temporary failures
   - Alert administrators about persistent delivery issues

## Fallback Mechanisms

1. **Configuration Fallbacks**:
   - Always fall back to ClassTasker email if school email configuration fails
   - Implement graceful degradation for partial configuration issues

2. **Delivery Fallbacks**:
   - If primary email provider fails, attempt delivery through secondary provider
   - Queue failed emails for retry with exponential backoff

## Testing Strategy

1. **Configuration Testing**:
   - Validate email configurations before saving
   - Provide test email functionality for administrators

2. **Template Testing**:
   - Test email rendering across various email clients
   - Ensure responsive design for mobile devices

3. **Integration Testing**:
   - Test the entire email flow from trigger to delivery
   - Verify correct template and configuration selection based on recipient type