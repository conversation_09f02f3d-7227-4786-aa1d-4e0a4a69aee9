# Fixing RLS Recursion Issues in Profiles Table

This document explains the Row Level Security (RLS) recursion issue in the profiles table and how to fix it.

## The Problem: RLS Recursion

### What is RLS Recursion?

Row Level Security (RLS) recursion occurs when an RLS policy for a table queries the same table as part of its policy expression. This creates a circular dependency:

1. User tries to access a row in the table
2. PostgreSQL checks the RLS policy to determine if access is allowed
3. The RLS policy contains a query that accesses the same table
4. PostgreSQL checks the RLS policy again for this new access
5. This creates an infinite loop (recursion)

### Specific Issue in Our Application

In our application, the recursion occurred in the profiles table with policies like:

```sql
CREATE POLICY "Ad<PERSON> can read all profiles in their organization"
  ON profiles FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin' AND organization_id = profiles.organization_id
    )
  );
```

This policy tries to check if the current user is an admin in the same organization as the profile being accessed. However, to do this check, it needs to query the profiles table again, which triggers the same policy check, creating an infinite recursion.

### Symptoms

The symptoms of this issue include:

- Queries to the profiles table hanging or timing out
- Error messages about "stack depth limit exceeded"
- High CPU usage on the database server
- Slow performance when accessing profiles

## The Solution: SECURITY DEFINER Functions

### How SECURITY DEFINER Functions Help

SECURITY DEFINER functions run with the privileges of the function creator (usually a superuser) rather than the calling user. This means they bypass RLS policies, breaking the recursion chain.

### Our Comprehensive Fix

Our solution uses three SECURITY DEFINER functions:

1. `get_user_organization_id()`: Gets the current user's organization_id
2. `check_admin_for_org(org_id)`: Checks if the current user is an admin for a specific organization
3. `is_same_organization(profile_org_id)`: Checks if a profile is in the same organization as the current user

These functions perform the necessary authorization checks without triggering RLS policies, effectively breaking the recursion chain.

### New RLS Policies

The new RLS policies use these functions instead of directly querying the profiles table:

```sql
-- Authenticated users can view profiles in their organization
CREATE POLICY "auth_profiles_view_org"
ON profiles
FOR SELECT
TO authenticated
USING (
  is_same_organization(organization_id)
);

-- Authenticated users with admin role can update profiles in their organization
CREATE POLICY "admin_update_org_profiles"
ON profiles
FOR UPDATE
TO authenticated
USING (
  check_admin_for_org(organization_id)
);
```

## How to Apply the Fix

1. Run the `apply-comprehensive-rls-fix.js` script:

```bash
node apply-comprehensive-rls-fix.js
```

2. Verify the fix with the `verify-rls-fix.js` script:

```bash
node verify-rls-fix.js
```

## Best Practices for Avoiding RLS Recursion

1. **Use SECURITY DEFINER functions** for complex authorization checks
2. **Avoid self-referential policies** that query the same table
3. **Add explicit conditions** to prevent self-reference (e.g., `admin_profile.id != profiles.id`)
4. **Simplify policies** to avoid nested subqueries
5. **Use JWT claims** when possible instead of querying the database
6. **Create views** for frequently accessed data patterns
7. **Test RLS policies** thoroughly before deploying to production

## Troubleshooting

If you still encounter recursion issues:

1. Temporarily disable RLS on the affected table:
   ```sql
   ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
   ```

2. Apply the fix

3. Re-enable RLS:
   ```sql
   ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
   ```

4. If issues persist, consider using the emergency fix that allows all authenticated users to view all profiles temporarily:
   ```sql
   CREATE POLICY "auth_profiles_view_all"
   ON profiles
   FOR SELECT
   TO authenticated
   USING (true);
   ```

## Further Reading

- [PostgreSQL RLS Documentation](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)
- [Supabase RLS Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [Security Definer Functions](https://www.postgresql.org/docs/current/sql-createfunction.html)