# Security Implementation Guide

This guide explains how to implement the security improvements for the Classtasker application.

## 1. Update Environment Variables

First, make sure your environment variables are set correctly:

```
# .env file
SUPABASE_URL=https://qcnotlojmyvpqbbgoxbc.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## 2. Apply Secure Database Functions

The secure database functions need to be applied directly in the Supabase dashboard:

1. Go to the Supabase dashboard: https://app.supabase.com/
2. Select your project
3. Go to the SQL Editor
4. Create a new query
5. Copy the contents of `sql/secure_functions_direct.sql` into the query editor
6. Run the query

This will create the following secure database functions:

- `get_all_users()`: Get all users in the system (admin only)
- `delete_user(user_id_param UUID)`: Delete a user (admin only)
- `invite_user_to_organization(email_param TEXT, organization_id_param UUID, role_param TEXT)`: Invite a user to an organization
- `get_organization_invitations_secure(org_id UUID)`: Get all invitations for an organization
- `update_user_role(user_id_param UUID, role_param TEXT)`: Update a user's role
- `create_organization(...)`: Create a new organization
- `update_organization(...)`: Update an organization
- `get_trust_schools(trust_id_param UUID)`: Get all schools in a trust
- `create_task_message(...)`: Create a task message

## 3. Set Up Row Level Security (RLS) Policies

For each table in your database, you should set up appropriate RLS policies:

### Profiles Table

```sql
-- Enable RLS on profiles table
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Allow users to read their own profile
CREATE POLICY "Users can read their own profile"
  ON profiles FOR SELECT
  USING (auth.uid() = id);

-- Allow users to update their own profile
CREATE POLICY "Users can update their own profile"
  ON profiles FOR UPDATE
  USING (auth.uid() = id);

-- Allow admins to read all profiles in their organization
CREATE POLICY "Admins can read all profiles in their organization"
  ON profiles FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin' AND organization_id = profiles.organization_id
    )
  );

-- Allow trust admins to read all profiles in schools under their trust
CREATE POLICY "Trust admins can read all profiles in their schools"
  ON profiles FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles p
      JOIN organizations o ON p.organization_id = o.id
      JOIN organizations school ON profiles.organization_id = school.id
      WHERE p.id = auth.uid() 
        AND p.role = 'admin' 
        AND o.organization_type = 'trust'
        AND school.parent_organization_id = p.organization_id
    )
  );
```

### Organizations Table

```sql
-- Enable RLS on organizations table
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Allow users to read their own organization
CREATE POLICY "Users can read their own organization"
  ON organizations FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND organization_id = organizations.id
    )
  );

-- Allow trust members to read schools in their trust
CREATE POLICY "Trust members can read schools in their trust"
  ON organizations FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles p
      JOIN organizations o ON p.organization_id = o.id
      WHERE p.id = auth.uid() 
        AND o.id = organizations.parent_organization_id
    )
  );

-- Allow admins to update their own organization
CREATE POLICY "Admins can update their own organization"
  ON organizations FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin' AND organization_id = organizations.id
    )
  );

-- Allow trust admins to update schools in their trust
CREATE POLICY "Trust admins can update schools in their trust"
  ON organizations FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM profiles p
      JOIN organizations o ON p.organization_id = o.id
      WHERE p.id = auth.uid() 
        AND p.role = 'admin' 
        AND o.organization_type = 'trust'
        AND organizations.parent_organization_id = p.organization_id
    )
  );
```

### User Invitations Table

```sql
-- Enable RLS on user_invitations table
ALTER TABLE user_invitations ENABLE ROW LEVEL SECURITY;

-- Allow users to read invitations by token (for accepting invitations)
CREATE POLICY "Users can read invitations by token"
  ON user_invitations FOR SELECT
  USING (true);

-- Allow admins to read invitations for their organization
CREATE POLICY "Admins can read invitations for their organization"
  ON user_invitations FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin' AND organization_id = user_invitations.organization_id
    )
  );

-- Allow trust admins to read invitations for schools in their trust
CREATE POLICY "Trust admins can read invitations for schools in their trust"
  ON user_invitations FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles p
      JOIN organizations o ON p.organization_id = o.id
      JOIN organizations school ON user_invitations.organization_id = school.id
      WHERE p.id = auth.uid() 
        AND p.role = 'admin' 
        AND o.organization_type = 'trust'
        AND school.parent_organization_id = p.organization_id
    )
  );

-- Allow admins to create invitations for their organization
CREATE POLICY "Admins can create invitations for their organization"
  ON user_invitations FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin' AND organization_id = user_invitations.organization_id
    )
  );

-- Allow trust admins to create invitations for schools in their trust
CREATE POLICY "Trust admins can create invitations for schools in their trust"
  ON user_invitations FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles p
      JOIN organizations o ON p.organization_id = o.id
      JOIN organizations school ON user_invitations.organization_id = school.id
      WHERE p.id = auth.uid() 
        AND p.role = 'admin' 
        AND o.organization_type = 'trust'
        AND school.parent_organization_id = p.organization_id
    )
  );

-- Allow users to update invitations they've been invited with
CREATE POLICY "Users can update invitations they've been invited with"
  ON user_invitations FOR UPDATE
  USING (email = auth.email());
```

## 4. Test the Security Implementation

After applying these changes, you should test the security implementation to ensure it's working correctly:

1. Start the application: `npm run dev:all`
2. Test creating an organization
3. Test inviting users to the organization
4. Test updating user roles
5. Test viewing organization invitations

## 5. Verify No Hardcoded Service Role Keys

Make sure there are no hardcoded service role keys in your codebase:

```
grep -r "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9" --include="*.js" --include="*.ts" .
```

All service role keys should be accessed through environment variables only.
