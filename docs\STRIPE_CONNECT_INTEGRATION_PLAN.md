# Stripe Connect Express Integration Plan for Class-Tasker-Connect

This document outlines the comprehensive plan to integrate Stripe Connect Express into the Class-Tasker-Connect application, enabling suppliers to receive direct payments with the platform taking a percentage fee.

## 1. System Architecture Overview

```mermaid
graph TD
    A[Teacher/Admin] -->|Creates Task| B[Task]
    B -->|Published| C[Public Task]
    D[Supplier] -->|Makes Offer| C
    E[Admin] -->|Accepts Offer| F[Assigned Task]
    F -->|Completed| G[Payment Flow]
    D -->|Express Onboarding| H[Stripe Connect Express]
    G -->|Processes Payment| H
    H -->|Direct Payout| D
    H -->|Platform Fee| I[Platform]
    D -->|Accesses| J[Express Dashboard]
```

## 2. Database Schema Updates

We'll need to add several tables and fields to your existing database:

```mermaid
erDiagram
    profiles ||--o{ stripe_accounts : has
    tasks ||--o{ payments : has
    offers ||--o{ payments : has
    
    stripe_accounts {
        uuid id PK
        uuid user_id FK
        string account_id
        string account_type "express"
        boolean charges_enabled
        boolean payouts_enabled
        string account_status
        string refresh_token "for Express dashboard"
        timestamp created_at
        timestamp updated_at
    }
    
    payments {
        uuid id PK
        uuid task_id FK
        uuid offer_id FK
        uuid payer_id FK
        uuid payee_id FK
        string payment_intent_id
        string transfer_id
        decimal amount
        decimal platform_fee
        decimal supplier_amount
        string status
        string currency
        jsonb metadata
        timestamp created_at
        timestamp updated_at
    }
    
    invoices {
        uuid id PK
        uuid payment_id FK
        string invoice_number
        string invoice_url
        string status
        timestamp due_date
        timestamp paid_at
        timestamp created_at
        timestamp updated_at
    }
```

## 3. Implementation Phases

### Phase 1: Stripe Setup and Configuration

1. **Create Stripe Account & Configure Connect Express**
   - Create a Stripe account for your platform
   - Enable Connect in the Stripe Dashboard
   - Configure Connect Express settings:
     - Customize branding for the Express onboarding flow
     - Set up business type and capabilities (card payments, transfers)
     - Configure payout schedules

2. **Environment Configuration**
   - Add Stripe API keys to your environment variables
   - Update `.env.example` with the new variables:
   ```
   # Stripe Configuration
   STRIPE_PUBLIC_KEY=your_stripe_public_key
   STRIPE_SECRET_KEY=your_stripe_secret_key
   STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
   PLATFORM_FEE_PERCENTAGE=10
   
   # Stripe Connect Express Configuration
   STRIPE_CONNECT_EXPRESS_CLIENT_ID=your_stripe_connect_client_id
   STRIPE_CONNECT_EXPRESS_RETURN_URL=https://your-site.com/connect/express/return
   STRIPE_CONNECT_EXPRESS_REFRESH_URL=https://your-site.com/connect/express/refresh
   ```

### Phase 2: Supplier Onboarding Flow with Express

1. **Supplier Profile Enhancement**
   - Add Stripe Connect Express status to supplier profiles
   - Create onboarding UI components with Express-specific status indicators

2. **Express Account Creation**
   - Implement API endpoint to create Stripe Connect Express accounts
   - Use the `accounts` API to create Express accounts:
   ```javascript
   const account = await stripe.accounts.create({
     type: 'express',
     country: 'US', // or appropriate country
     capabilities: {
       card_payments: { requested: true },
       transfers: { requested: true },
     },
     business_type: 'individual', // or 'company'
     // Minimal information to start
     business_profile: {
       mcc: '5734', // Computer Software Stores
       url: 'https://your-site.com',
     },
   });
   ```

3. **Express Onboarding Flow**
   - Generate Express onboarding links using the `accountLinks` API:
   ```javascript
   const accountLink = await stripe.accountLinks.create({
     account: accountId,
     refresh_url: 'https://your-site.com/connect/express/refresh',
     return_url: 'https://your-site.com/connect/express/return',
     type: 'account_onboarding',
   });
   ```
   - Redirect suppliers to the generated onboarding URL
   - Handle the return flow when suppliers complete onboarding

4. **Express Dashboard Access**
   - Implement login links for the Express dashboard:
   ```javascript
   const loginLink = await stripe.accounts.createLoginLink(accountId);
   ```
   - Provide a "Manage Payouts" button in the supplier dashboard

5. **Account Status Monitoring**
   - Implement webhook handler for account updates
   - Listen for `account.updated` events to track onboarding progress
   - Update supplier profile when onboarding is complete

```mermaid
sequenceDiagram
    participant Supplier
    participant Platform
    participant Stripe
    
    Supplier->>Platform: Request to become service provider
    Platform->>Stripe: Create Express account
    Stripe-->>Platform: Return account_id
    Platform->>Platform: Store account_id in database
    Platform->>Stripe: Generate Express onboarding link
    Platform-->>Supplier: Redirect to Express onboarding
    Supplier->>Stripe: Complete Express onboarding
    Stripe->>Platform: Webhook: account.updated
    Platform->>Platform: Update supplier status
    
    Note over Supplier,Stripe: Later - Dashboard Access
    Supplier->>Platform: Request to manage payouts
    Platform->>Stripe: Create Express dashboard login link
    Platform-->>Supplier: Redirect to Express dashboard
    Supplier->>Stripe: Manage account in Express dashboard
```

### Phase 3: Payment Processing with Express Accounts

1. **Task Completion Flow**
   - Add "Mark as Complete" functionality for assigned tasks
   - Implement client confirmation process

2. **Payment Intent Creation**
   - Create payment intent when task is marked complete
   - Calculate platform fee and supplier amount
   - Set up transfer data for the connected Express account:
   ```javascript
   const paymentIntent = await stripe.paymentIntents.create({
     amount: totalAmount,
     currency: 'usd',
     payment_method_types: ['card'],
     transfer_data: {
       destination: supplierConnectAccountId,
     },
     application_fee_amount: platformFeeAmount,
   });
   ```

3. **Payment Processing**
   - Implement payment collection UI using Stripe Elements
   - Process payment using the PaymentIntent
   - Handle successful payments and failures

```mermaid
sequenceDiagram
    participant Client
    participant Platform
    participant Stripe
    participant SupplierExpressAccount
    
    Client->>Platform: Mark task as complete
    Platform->>Platform: Validate completion
    Platform->>Stripe: Create PaymentIntent with transfer_data
    Stripe-->>Platform: Return PaymentIntent
    Platform->>Platform: Generate invoice
    Platform-->>Client: Display payment form
    Client->>Platform: Submit payment details
    Platform->>Stripe: Confirm PaymentIntent
    Stripe->>Stripe: Process payment
    Stripe-->>Platform: Payment successful
    Stripe->>SupplierExpressAccount: Transfer funds (minus fee)
    Platform->>Platform: Update task & payment status
    Platform-->>Client: Confirm payment
    Platform-->>Supplier: Notify of payment
```

### Phase 4: Dashboard and Reporting

1. **Client Payment Dashboard**
   - Create UI for viewing payment history
   - Implement invoice download functionality
   - Add payment status tracking

2. **Supplier Earnings Dashboard**
   - Create UI for viewing earnings
   - Add "View Express Dashboard" button for detailed payout information
   - Implement transaction history in your platform

3. **Admin Financial Overview**
   - Create admin dashboard for financial overview
   - Implement reporting and analytics
   - Add dispute management tools

## 4. Technical Implementation Details

### Backend Components

1. **Stripe Express Service**
   - Create a new service for Stripe API interactions
   - Implement Express-specific methods:
     - `createExpressAccount(userId)`
     - `generateOnboardingLink(accountId)`
     - `generateDashboardLink(accountId)`
     - `getAccountStatus(accountId)`
     - `createPaymentWithDirectTransfer(taskId, offerId, amount)`

2. **Express Webhook Handler**
   - Implement webhook endpoint for Stripe events
   - Handle Express-specific events:
     - `account.updated` - Track onboarding progress
     - `account.application.deauthorized` - Handle account disconnection
     - `capability.updated` - Monitor account capabilities
     - `person.updated` - Track verification status for individuals

3. **Database Updates**
   - Create migration scripts for new tables
   - Update existing tables with new fields
   - Implement RLS policies for payment data

### Frontend Components

1. **Supplier Onboarding with Express**
   - Create "Connect with Stripe" button for suppliers
   - Implement onboarding status indicators:
     - Not started
     - In progress
     - Pending verification
     - Complete
   - Add Express dashboard access button

2. **Payment Processing**
   - Implement Stripe Elements for secure payment collection
   - Create invoice viewing component
   - Add payment confirmation screens

3. **Express-specific UI Elements**
   - Add "Manage Payouts" button that redirects to Express dashboard
   - Implement account status indicators that reflect Express verification status
   - Create help content specific to Express accounts

## 5. Express Account Lifecycle Management

1. **Initial Creation**
   - Create Express account with minimal information
   - Generate and redirect to onboarding link
   - Store account ID and initial status

2. **Onboarding Monitoring**
   - Track onboarding progress via webhooks
   - Update account status in your database
   - Send reminders for incomplete onboarding

3. **Verification Handling**
   - Monitor verification status via webhooks
   - Notify suppliers of verification issues
   - Provide guidance for resolving verification problems

4. **Ongoing Management**
   - Provide access to Express dashboard
   - Monitor account capabilities
   - Handle account deauthorization

5. **Payout Management**
   - Express accounts handle their own payouts through the Express dashboard
   - Your platform doesn't need to manage payout schedules
   - Monitor payout failures via webhooks

## 6. Security Considerations

1. **Data Protection**
   - Ensure sensitive payment data never touches your servers
   - Use Stripe Elements for secure card collection
   - Implement proper encryption for stored data

2. **Authentication & Authorization**
   - Update RLS policies to protect payment data
   - Implement role-based access control for financial operations
   - Ensure webhook endpoints are properly secured

3. **Express-specific Security**
   - Securely store Express account refresh tokens
   - Implement proper authentication for Express dashboard access
   - Use HTTPS for all Express-related redirects

## 7. Testing Strategy

1. **Express Account Testing**
   - Use Stripe test mode to create test Express accounts
   - Test the complete onboarding flow
   - Verify dashboard access functionality

2. **Payment Flow Testing**
   - Test end-to-end payment flows with Express accounts
   - Validate fee calculations
   - Test various payment scenarios (successful, failed, disputed)

3. **Webhook Testing**
   - Test webhook handling for all relevant events
   - Verify database updates based on webhook events
   - Test webhook signature verification

## 8. Timeline Estimate

| Phase | Description | Estimated Duration |
|-------|-------------|-------------------|
| 1 | Stripe Setup and Express Configuration | 1 week |
| 2 | Supplier Onboarding with Express | 2 weeks |
| 3 | Payment Processing with Express | 3 weeks |
| 4 | Dashboard and Reporting | 2 weeks |
| | Testing and Refinement | 2 weeks |
| | Total | ~10 weeks |

## 9. Next Steps

1. Set up a Stripe account and enable Connect
2. Configure Express account settings in the Stripe Dashboard
3. Create the necessary database tables
4. Implement the Express account creation and onboarding flow
5. Set up webhook handling for Express events