# Stripe Connect Integration Setup

This document provides instructions for setting up and using the Stripe Connect integration in the Class Tasker Connect application.

## Prerequisites

Before you begin, you'll need:

1. A Stripe account with Connect enabled
2. API keys from your Stripe Dashboard
3. Webhook secret for secure webhook handling

## Setup Instructions

### 1. Create a Stripe Account

If you don't already have a Stripe account:

1. Go to [stripe.com](https://stripe.com) and sign up
2. Once logged in, navigate to the Dashboard
3. Enable Stripe Connect in your account settings

### 2. Configure Stripe Connect

In your Stripe Dashboard:

1. Go to Connect > Settings
2. Configure your Connect settings:
   - Set your branding information
   - Configure the business types you'll support
   - Set up the capabilities you need (card payments, transfers)
   - Configure payout schedules

### 3. Get API Keys

1. In your Stripe Dashboard, go to Developers > API keys
2. Copy your Publishable key and Secret key
3. For testing, use the test mode keys

### 4. Set Up Webhooks

1. In your Stripe Dashboard, go to Developers > Webhooks
2. Add an endpoint with URL: `https://your-site.com/api/stripe-webhook`
3. Select the following events to listen for:
   - `account.updated`
   - `account.application.deauthorized`
   - `capability.updated`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
4. Copy the Webhook Signing Secret

### 5. Configure Environment Variables

You need to update multiple .env files for different environments:

1. **Main `.env` file** (for server-side code):

```
# Stripe Configuration (Server-side)
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
PLATFORM_FEE_PERCENTAGE=10
```

2. **`.env.local` file** (for frontend in development):

```
# Stripe Configuration (Frontend)
VITE_STRIPE_PUBLIC_KEY=your_stripe_public_key
VITE_PLATFORM_FEE_PERCENTAGE=10

# Stripe Connect Express Configuration
VITE_STRIPE_CONNECT_EXPRESS_RETURN_URL=http://localhost:3000/stripe-connect
VITE_STRIPE_CONNECT_EXPRESS_REFRESH_URL=http://localhost:3000/stripe-connect
```

3. **`.env.production` file** (for frontend in production):

```
# Stripe Configuration (Frontend)
VITE_STRIPE_PUBLIC_KEY=your_stripe_public_key
VITE_PLATFORM_FEE_PERCENTAGE=10

# Stripe Connect Express Configuration
VITE_STRIPE_CONNECT_EXPRESS_RETURN_URL=https://your-production-site.com/stripe-connect
VITE_STRIPE_CONNECT_EXPRESS_REFRESH_URL=https://your-production-site.com/stripe-connect
```

Note: The frontend variables use the `VITE_` prefix because the application is built with Vite, which requires this prefix for environment variables to be exposed to the client-side code.

### 6. Apply Database Migrations

Run the following command to set up the necessary database tables:

```bash
npm run setup-stripe-connect
```

### 7. Start the Application

Use the provided start script to run the application with Stripe Connect integration:

```bash
# On Linux/Mac
./start-with-stripe.sh

# On Windows
start-with-stripe.bat
```

## Using Stripe Connect

### Supplier Onboarding

1. Suppliers can access the Stripe Connect onboarding page at `/stripe-connect`
2. They will click "Connect with Stripe" to start the process
3. They will be redirected to Stripe to complete their account setup
4. After completing the setup, they will be redirected back to the application

### Payment Processing

When a task is completed:

1. The client will be prompted to make a payment
2. The payment will be processed through Stripe
3. Funds will be transferred directly to the supplier's Stripe account, minus the platform fee
4. Both the client and supplier will receive confirmation of the payment

### Managing Stripe Connect Accounts

Suppliers can:

1. View their Stripe Connect account status at `/stripe-connect`
2. Access their Stripe Express dashboard to manage payouts and account settings
3. View their payment history and earnings

## Webhook Handling

The application listens for the following Stripe webhook events:

- `account.updated`: Updates the supplier's account status in the database
- `payment_intent.succeeded`: Marks the payment as successful and updates the task status
- `payment_intent.payment_failed`: Marks the payment as failed
- `account.application.deauthorized`: Handles when a supplier disconnects their Stripe account
- `capability.updated`: Updates the supplier's account capabilities in the database

## Testing

For testing the integration:

1. Use Stripe's test mode and test API keys
2. Use Stripe's test cards for simulating payments:
   - Success: `4242 4242 4242 4242`
   - Requires Authentication: `4000 0025 0000 3155`
   - Declined: `4000 0000 0000 0002`
3. Test the complete flow from supplier onboarding to payment processing

## Troubleshooting

Common issues:

1. **Webhook errors**: Ensure your webhook URL is accessible and the signing secret is correct
2. **Account verification issues**: Make sure suppliers complete all required verification steps
3. **Payment failures**: Check the Stripe Dashboard for detailed error messages
4. **Connect account issues**: Verify that the Connect account has the necessary capabilities enabled

For more detailed troubleshooting, check the Stripe logs in your Dashboard and the application server logs.