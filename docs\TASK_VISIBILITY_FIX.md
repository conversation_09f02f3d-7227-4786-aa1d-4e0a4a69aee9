# Task Visibility Fix

This document provides instructions on how to fix the issue with tasks not being visible to admins in the ClassTasker application.

## The Problem

The issue is related to Row Level Security (RLS) policies in Supabase that prevent admins from seeing all tasks. This causes the admin task review page to show no tasks even when tasks exist in the database.

## Solution Options

There are three ways to fix this issue:

### Option 1: Update RLS Policies in Supabase (Recommended)

This is the most robust solution as it fixes the root cause of the problem.

1. Go to the Supabase dashboard for your project
2. Navigate to the SQL Editor
3. Copy the contents of the `sql/fix_task_policies.sql` file
4. Paste the SQL into the editor and run it
5. Verify that the policies have been applied by checking the output of the verification query

Alternatively, you can run the script we've provided:

```bash
# Make sure SUPABASE_SERVICE_ROLE_KEY is set in your environment
node --experimental-modules --es-module-specifier-resolution=node src/scripts/apply-task-policies.js
```

### Option 2: Use the API Proxy (Already Implemented)

We've already implemented a client-side API proxy that bypasses RLS policies using the Supabase service role key. This solution is already in place and should work as long as the `SUPABASE_SERVICE_ROLE_KEY` environment variable is properly set.

To ensure this works:

1. Make sure the `VITE_SUPABASE_SERVICE_ROLE_KEY` environment variable is set in your `.env` file
2. Restart the development server

### Option 3: Use the Server-Side API (Already Implemented)

We've also implemented a server-side API endpoint that securely fetches tasks using the service role key. This solution is already in place and should work as a fallback if the client-side API proxy fails.

## Verifying the Fix

To verify that the fix is working:

1. Create a task as a teacher user
2. Log in as an admin user
3. Go to the Admin Tasks page
4. Click "Show Debug Info" to see all tasks in the database
5. Verify that the tasks appear in the "Tasks Pending Review" section

## Troubleshooting

If tasks still don't appear:

1. Check the browser console for error messages
2. Verify that the `SUPABASE_SERVICE_ROLE_KEY` environment variable is correctly set
3. Run the direct database access script to confirm that tasks exist in the database:

```bash
node --experimental-modules --es-module-specifier-resolution=node src/scripts/direct-task-check.js
```

4. Try clicking the "Fetch Directly" button on the Admin Tasks page

## Technical Details

The fix involves multiple layers of fallback mechanisms:

1. **Client-Side API Proxy**: Uses the Supabase service role key to bypass RLS policies
2. **Server-Side API Endpoint**: Provides a secure way to fetch tasks
3. **Direct Database Access**: Confirms that tasks exist in the database
4. **RLS Policy Updates**: Fixes the root cause by properly configuring RLS policies

Each layer has detailed logging to help diagnose issues.

## Long-Term Solution

For a more robust long-term solution:

1. Update the RLS policies as described in Option 1
2. Simplify the implementation by removing the redundant fetching methods
3. Implement proper error handling and user feedback
4. Add comprehensive testing to ensure the issue doesn't recur
