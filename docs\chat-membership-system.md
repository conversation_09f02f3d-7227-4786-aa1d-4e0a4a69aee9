# Chat Membership System for ClassTasker

## Overview

This document describes the comprehensive chat membership system implemented for ClassTasker to ensure consistent and correct chat membership across all task scenarios.

## Problem Statement

Previously, the chat membership logic was inconsistent across different parts of the application, leading to:
- Missing members in chat channels
- Inconsistent chat visibility between main app and PWA
- Teachers being excluded from chats for tasks they created
- Admins not being included when they assign tasks

## Solution: Centralized Chat Membership Logic

We've implemented a centralized system in `src/utils/chatMembershipUtils.ts` that handles all chat membership scenarios systematically.

## Scenario Matrix

The system handles 6 distinct scenarios based on who creates, assigns, and is assigned to tasks:

| Scenario | Creator | Assigner | Assignee | Members | Description |
|----------|---------|----------|----------|---------|-------------|
| 1 | Admin | Admin | Admin (self) | 1 member | Admin self-assigns for documentation |
| 2 | Admin | Admin | Internal Staff | 2 members | Admin assigns to staff |
| 3 | Admin | Admin | Supplier | 2 members | Admin assigns to external supplier |
| 4 | Teacher | Admin | Admin (self) | 2 members | Teacher creates, admin self-assigns |
| 5 | Teacher | Admin | Internal Staff | 3 members | Teacher creates, admin assigns to staff |
| 6 | Teacher | Admin | Supplier | 3 members | Teacher creates, admin assigns to supplier |

## Key Functions

### `determineTaskChatMembers(context: TaskChatContext)`
The core function that determines chat membership based on task context. It:
- Analyzes the relationship between creator, assigner, and assignee
- Determines user roles (admin, teacher, supplier, etc.)
- Returns the appropriate member list for each scenario

### `getTaskChatMembers(taskId: string)`
Convenience function that:
- Fetches task context from the database
- Calls `determineTaskChatMembers` with the context
- Returns the member list for the task

### `ensureTaskChatExists(taskId: string, taskTitle?: string)`
Creates or updates a task chat with proper membership:
- Determines correct members using centralized logic
- Creates/updates the GetStream channel via API
- Ensures all relevant parties have access

### `updateTaskChatMembership(taskId: string, newAssigneeId?: string, assignerId?: string)`
Updates chat membership when task assignments change:
- Recalculates membership based on new assignment
- Updates the GetStream channel membership
- Maintains consistency across assignment changes

## Integration Points

The centralized logic is integrated at key points in the application:

### Task Creation
- `taskService.createTask()` calls `ensureTaskChatExists()` after creating a task
- Ensures chat is created with proper initial membership

### Task Assignment
- `taskService.assignTask()` calls `updateTaskChatMembership()` after assignment
- Updates chat membership when assignments change

### Offer Acceptance
- `taskService.acceptOffer()` calls `updateTaskChatMembership()` when supplier offers are accepted
- Adds supplier to chat when their offer is accepted

### Chat Components
- `StreamUIChat` uses `ensureTaskChatExists()` to guarantee proper membership
- `PWAChatList` uses centralized logic instead of manual member calculation

## Benefits

1. **Consistency**: All chat creation uses the same logic
2. **Completeness**: No missing members across any scenario
3. **Maintainability**: Single source of truth for membership logic
4. **Scalability**: Easy to add new scenarios or modify existing ones
5. **Reliability**: Systematic approach reduces bugs and edge cases

## Usage Examples

### Creating a chat for a new task
```typescript
import { ensureTaskChatExists } from '@/utils/chatMembershipUtils';

// After creating a task
await ensureTaskChatExists(taskId, taskTitle);
```

### Updating membership after assignment
```typescript
import { updateTaskChatMembership } from '@/utils/chatMembershipUtils';

// After assigning a task
await updateTaskChatMembership(taskId, assigneeId, assignerId);
```

### Getting members for a task
```typescript
import { getTaskChatMembers } from '@/utils/chatMembershipUtils';

// Get current members for a task
const members = await getTaskChatMembers(taskId);
```

## Testing

A comprehensive test script is available at `scripts/test-chat-membership.js` that:
- Creates test tasks for all 6 scenarios
- Verifies correct membership for each scenario
- Provides detailed output for debugging

Run the test with:
```bash
node scripts/test-chat-membership.js
```

## Future Enhancements

1. **Assignment Tracking**: Add `assigned_by` field to tasks table for explicit assignment tracking
2. **Role-Based Permissions**: Implement different permission levels within chats
3. **Dynamic Membership**: Support for adding/removing members during task lifecycle
4. **Audit Trail**: Track membership changes for compliance and debugging

## Migration Notes

When deploying this system:
1. Existing chats will be updated to use the new membership logic
2. No data loss will occur - only membership will be corrected
3. Users may see previously hidden chats become visible
4. All chat functionality remains the same for end users
