# Database Policies Documentation

## Overview

This document provides comprehensive information about the Row Level Security (RLS) policies implemented in the ClassTasker application database. It explains the security model, the policies in place, and how to avoid common issues like infinite recursion.

## Table of Contents

1. [Security Model](#security-model)
2. [RLS Policies](#rls-policies)
3. [Security Functions](#security-functions)
4. [Common Issues and Solutions](#common-issues-and-solutions)
5. [Best Practices](#best-practices)
6. [Testing and Verification](#testing-and-verification)

## Security Model

The ClassTasker application uses a role-based access control system with the following roles:

- **Site Admin**: Has access to all data across the entire application
- **Organization Admin**: Has access to data within their organization
- **Teacher**: Can create tasks and view their own data
- **Maintenance Staff**: Can handle assigned tasks
- **Support Staff**: Can handle assigned tasks
- **Supplier**: Can view public tasks and submit offers

Access control is enforced through Supabase Row Level Security (RLS) policies, which restrict data access at the database level.

## RLS Policies

### Profiles Table Policies

| Policy Name | Role | Operation | Description |
|-------------|------|-----------|-------------|
| Users can access their own profile | authenticated | ALL | Users can view and update their own profile |
| Users can insert their own profile | public | INSERT | Users can create their own profile |
| Site admins can access all profiles | authenticated | ALL | Site admins can access all profiles |
| Organization admins can view profiles | authenticated | SELECT | Organization admins can view profiles in their organization |
| Organization admins can update profiles | authenticated | UPDATE | Organization admins can update profiles in their organization |
| block_anon_access | anon | ALL | Anonymous users cannot access profiles |
| service_role_profiles_all | service_role | ALL | Service role can access all profiles |

### Offers Table Policies

| Policy Name | Role | Operation | Description |
|-------------|------|-----------|-------------|
| Users can view their own offers | authenticated | SELECT | Users can view offers they created |
| Users can update their own offers | authenticated | UPDATE | Users can update offers they created |
| Users can insert their own offers | authenticated | INSERT | Users can create new offers |
| Users can delete their own offers | authenticated | DELETE | Users can delete offers they created |
| Site admins can access all offers | authenticated | ALL | Site admins can access all offers |
| Task owners can view offers for their tasks | authenticated | SELECT | Task owners can view offers for their tasks |

## Security Functions

To avoid infinite recursion issues, we use special functions with the `SECURITY DEFINER` attribute:

### is_site_admin_direct

```sql
CREATE OR REPLACE FUNCTION is_site_admin_direct(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  is_admin BOOLEAN;
BEGIN
  -- Direct query without using RLS
  SELECT is_site_admin INTO is_admin
  FROM profiles
  WHERE id = user_id;
  
  RETURN COALESCE(is_admin, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### is_admin_for_org_direct

```sql
CREATE OR REPLACE FUNCTION is_admin_for_org_direct(user_id UUID, org_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  is_admin BOOLEAN;
BEGIN
  -- Direct query without using RLS
  SELECT (role = 'admin' AND organization_id = org_id) INTO is_admin
  FROM profiles
  WHERE id = user_id;
  
  RETURN COALESCE(is_admin, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Common Issues and Solutions

### Infinite Recursion in RLS Policies

**Issue**: When RLS policies use functions that query the same table, it can create an infinite recursion loop.

**Example Error**: `infinite recursion detected in policy for relation "profiles"`

**Solution**:
1. Use `SECURITY DEFINER` functions that bypass RLS
2. Avoid circular dependencies in policies
3. Keep policies simple and direct when possible

### How We Fixed It

We replaced policies that were causing infinite recursion with simplified policies that use `SECURITY DEFINER` functions. These functions bypass RLS when executing, preventing the infinite loop.

Before:
```sql
CREATE POLICY "Site admins can access all profiles" ON profiles
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles AS admin_profiles
    WHERE admin_profiles.id = auth.uid() 
    AND admin_profiles.is_site_admin = true
  )
);
```

After:
```sql
CREATE POLICY "Site admins can access all profiles" ON profiles
FOR ALL TO authenticated
USING (is_site_admin_direct(auth.uid()));
```

## Best Practices

1. **Use SECURITY DEFINER sparingly**: Only use it when necessary to avoid RLS recursion
2. **Keep policies simple**: Complex policies are harder to debug and more prone to issues
3. **Test thoroughly**: Always test policies with different user roles
4. **Document all policies**: Keep this document updated when policies change
5. **Use explicit roles**: Specify roles explicitly in policies (e.g., `TO authenticated`)
6. **Avoid nested queries in policies**: They can lead to performance issues and recursion
7. **Use direct checks when possible**: Simple equality checks are more efficient

## Testing and Verification

To verify that policies are working correctly:

1. Run the following SQL to check all policies:
```sql
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check 
FROM pg_policies 
WHERE tablename IN ('profiles', 'offers')
ORDER BY tablename, policyname;
```

2. Test access with different user roles:
```sql
-- As site admin
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claims" TO '{"sub": "SITE_ADMIN_ID", "role": "authenticated"}';
SELECT COUNT(*) FROM profiles;

-- As organization admin
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claims" TO '{"sub": "ORG_ADMIN_ID", "role": "authenticated"}';
SELECT COUNT(*) FROM profiles;

-- Reset role
RESET ROLE;
RESET "request.jwt.claims";
```

3. Check for infinite recursion by querying the tables directly:
```sql
SELECT COUNT(*) FROM profiles;
SELECT COUNT(*) FROM offers;
```
