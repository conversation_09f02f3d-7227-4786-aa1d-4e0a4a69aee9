# Enhanced ClassTasker Development Guidelines

## Core Development Principles

### Response Quality
1. **No Hallucination:** If information is unknown, explicitly state "I don't know" rather than guessing
2. **Specific Solutions:** Provide concrete, specific answers to queries, avoiding vague suggestions
3. **Root Cause Analysis:** When analyzing mistakes, examine actual code, identify specific errors, and provide examples
4. **Code-First Approach:** Always check existing code before making claims about functionality
5. **Transparent Uncertainty:** Clearly state uncertainty, verify with appropriate tools, and explain verification process
6. **No Assumptions:** Avoid assumptions about project structure, user preferences, or configurations
7. **Detailed Error Analysis:** Provide specific, actionable feedback on errors
8. **Verify All Assumptions:** Pause and verify when making any assumptions about the codebase

### Codebase Interaction
1. **Pre-Change Research:** Always use codebase-retrieval before making changes to understand context
2. **Dependency Analysis:** Understand all dependencies before modifying any component
3. **Comprehensive File Checks:** For each change, retrieve files and check imports/patterns
4. **Complete File Review:** View entire files to identify all affected elements before editing
5. **Import Verification:** Ensure all imports are correct before submitting changes
6. **Function Reuse:** Check for existing similar functions before creating new ones
7. **Understand Before Copying:** Never blindly copy code without understanding its purpose
8. **Module Adaptation:** Preserve core functionality of working modules while making necessary architectural adaptations

## Architecture-Specific Guidelines

### Task Management System
1. **Task Type Separation:** Maintain clear distinction between internal and external tasks
2. **Always Mount Pattern:** Components should always mount but conditionally render content
3. **Role-Based Access:** Implement consistent role checking using the profiles table as source of truth
4. **Adapter Pattern:** Use adapters to integrate preserved modules with the new architecture
5. **Comprehensive Testing:** Test all components with different task types and user roles
6. **Clear Type Guards:** Use proper TypeScript type guards when working with different task types
7. **Timeline Configuration:** Use appropriate timeline configurations for each task type
8. **System Messages:** Ensure system messages are appropriate for the task type and user role

### Role Management
1. **Single Source of Truth:** Use profiles table as the authoritative source for user roles
2. **Comprehensive Role Support:** Handle all staff roles (maintenance, support, cleaning, IT, etc.)
3. **Role Group Helpers:** Use role group helpers for common access patterns
4. **Consistent Role Checking:** Use the same pattern for role checking throughout the application
5. **Role-Specific UI:** Adapt UI elements based on user role while maintaining consistent structure
6. **Assignment Logic:** Filter available staff based on task category and appropriate roles
7. **Role Display:** Clearly indicate staff roles in the UI with appropriate terminology

### Database Operations

#### SQL Execution
1. **RPC Function Usage:** Always use the exec_sql RPC function for executing SQL statements
2. **Error Handling:** Include fallback approaches when primary SQL execution fails
3. **Statement Separation:** Split complex SQL scripts into individual statements for better error isolation
4. **Schema Verification:** Always verify schema changes after execution with a separate query
5. **Proper Permissions:** Ensure scripts run with appropriate service role keys, not anon keys
6. **Recursion Prevention:** Be vigilant about RLS policies that might cause infinite recursion
7. **Environment Variables:** Always load environment variables before connecting to the database
8. **Connection Testing:** Include a simple connection test before executing critical operations

#### Supabase Integration
1. **Tool Preference:** Use the supabase tool for all Supabase API interactions
2. **Authentication Method:** Specify the correct authentication method (anon key for client-side, service role for admin)
3. **Project Verification:** Verify you're working with the correct project (qcnotlojmyvpqbbgoxbc)
4. **Direct SQL Operations:** Use the tool for direct SQL execution rather than custom scripts
5. **Schema Inspection:** Inspect table structures, policies, and functions before making changes
6. **RLS Review:** Review existing Row Level Security policies before modifying database structure
7. **Edge Function Deployment:** Deploy and test Edge Functions through the tool
8. **Error Checking:** Check response errors from the tool and implement appropriate handling
9. **Data Validation:** Validate data structures before sending to Supabase
10. **Rate Limit Awareness:** Be mindful of API rate limits when making multiple sequential requests

### Process Management
1. **Server Start Method:** Use npm run dev:all to start both frontend and Stripe API servers
2. **Port Verification:** Frontend runs on port 8082, Stripe API on port 3001
3. **Process Checking:** Always use list-processes before starting or restarting servers
4. **Process Termination:** Use kill-process with specific terminal IDs before starting new servers
5. **Server Selection:** Use npm run dev (frontend only) or npm run stripe-server (Stripe API only) when needed
6. **Environment Variables:** Ensure .env or .env.local contains required variables before starting servers
7. **Required Variables:** VITE_SUPABASE_URL, VITE_SUPABASE_ANON_KEY, STRIPE_SECRET_KEY, STRIPE_PUBLIC_KEY
8. **Process Management:** Never start new servers without first checking for and terminating existing ones

### Error Recovery
1. **Backup Creation:** Create backups before executing potentially destructive operations
2. **Incremental Changes:** Apply changes incrementally rather than all at once
3. **Rollback Planning:** Include rollback steps in case a script fails midway
4. **Verification Steps:** Add explicit verification steps after each major operation
5. **Manual Intervention:** Document steps for manual intervention when automated scripts fail
6. **Test Environment:** Test scripts in a development environment before running in production

## Security Best Practices
1. **Key Protection:** Never expose service role keys in client-side code
2. **RLS Preference:** Use Row Level Security policies instead of service role keys when possible
3. **Security Functions:** Create SECURITY DEFINER functions for operations that need to bypass RLS
4. **Environment Variable Usage:** Use environment variables for all sensitive credentials
5. **Key Rotation:** Rotate API keys periodically and after team member departures

## Code Quality Standards
1. **No Hardcoding:** Avoid hardcoding values in the codebase, especially API keys and URLs
2. **Consistent Error Handling:** Implement consistent error handling across all components
3. **Documentation:** Document complex functions and components with clear comments
4. **Testing:** Create and maintain tests for critical functionality
5. **Type Safety:** Use proper TypeScript types and interfaces for all components
6. **Component Stability:** Prioritize stable component mounting over conditional rendering
7. **Clean Architecture:** Maintain clear separation of concerns in all components
8. **Adapter Pattern:** Use adapters to integrate with existing modules rather than modifying them

## Working with Existing Modules
1. **Categorized Adaptation:** Apply appropriate modification strategy based on module category:
   - **Category 1 (Minimal Change):** Sign-up flow
   - **Category 2 (Targeted Modification):** Task creation, Dashboards, Compliance dashboard
   - **Category 3 (Significant Adaptation):** Chat module, Task assignment interface
2. **Preserve Core Functionality:** Maintain user-facing functionality and behavior while allowing internal changes
3. **Integration Approaches:**
   - Use adapters for Category 1 modules
   - Apply targeted modifications for Category 2 modules
   - Implement component extensions for Category 3 modules
4. **Backward Compatibility:** Ensure data format compatibility where needed
5. **Comprehensive Testing:** Test each modified module with focus on preserved functionality
6. **Interface Documentation:** Document the interfaces, dependencies, and core functionality to preserve
7. **Incremental Integration:** Adapt modules one at a time, testing thoroughly

## Implementation Priorities
1. **Stability First:** Prioritize stable behavior over performance optimizations
2. **Single Source of Truth:** Establish profiles table as the authoritative source for user data
3. **Clear Type System:** Implement clear task type definitions with proper TypeScript support
4. **Component Architecture:** Adopt "always mount, conditionally render" pattern for all components
5. **Role Management:** Implement comprehensive role system with proper grouping
6. **Technical Debt Cleanup:** Remove emergency code and workarounds
7. **Adapter Implementation:** Create adapters for all preserved modules
8. **Testing:** Implement comprehensive testing for all components and workflows
