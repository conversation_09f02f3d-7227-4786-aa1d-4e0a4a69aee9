# Invoice Creation Process

This document outlines the process for creating invoices in the ClassTasker application.

## Overview

When a payment is made, an invoice is automatically generated in Strip<PERSON> and then stored in our database. The invoice number is generated by <PERSON><PERSON> and used as the reference number in our system.

## Process Flow

1. **Payment Creation**:
   - A payment record is created in our database when a user pays for a task
   - The payment record includes details like the amount, payer, payee, etc.

2. **Stripe Invoice Creation**:
   - The payment triggers the creation of an invoice in Stripe
   - <PERSON><PERSON> generates a unique invoice number (e.g., `5AAEF052-0001`)
   - The invoice is finalized and sent to the customer

3. **Database Storage**:
   - The Stripe invoice details, including the invoice number, are stored in our database
   - The invoice record is linked to the payment record

4. **Email Notification**:
   - An email notification is sent to the customer with a link to the invoice

## Best Practices

### Always Use Stripe-Generated Invoice Numbers

We always use the invoice number generated by <PERSON><PERSON> (`invoice.number`) rather than creating our own. This ensures:

1. **Consistency**: The invoice number is the same in both Stripe and our database
2. **Uniqueness**: <PERSON><PERSON> ensures that invoice numbers are unique
3. **Standardization**: Stripe follows a standard format for invoice numbers
4. **Traceability**: It's easier to trace issues when the invoice numbers match

### Code Example

Here's how we store the invoice number in our database:

```javascript
// Store the invoice in our database
const { data: invoiceRecord, error: invoiceError } = await supabase
  .from('invoices')
  .insert([{
    payment_id: payment.id,
    invoice_number: sentInvoice.number, // Use the Stripe-generated invoice number
    stripe_invoice_id: sentInvoice.id,
    invoice_url: retrievedInvoice.invoice_pdf || sentInvoice.hosted_invoice_url,
    status: sentInvoice.status,
    due_date: sentInvoice.due_date ? new Date(sentInvoice.due_date * 1000).toISOString() : null,
  }])
  .select();
```

## Troubleshooting

If you encounter issues with invoice numbers not matching between Stripe and our database:

1. **Check the Invoice Creation Code**: Ensure that the code is using `sentInvoice.number` for the `invoice_number` field
2. **Check the Stripe Dashboard**: Verify the invoice number in the Stripe Dashboard
3. **Update Existing Invoices**: If necessary, update existing invoices in the database to match the Stripe invoice numbers

## References

- [Stripe Invoices API Documentation](https://stripe.com/docs/api/invoices)
- [Stripe Invoice Numbers](https://stripe.com/docs/billing/invoices/overview#invoice-numbers)
