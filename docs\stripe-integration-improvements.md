# Stripe Integration Improvements

This document outlines the improvements made to the Stripe integration in the ClassTasker application.

## Schema Improvements

### 1. `stripe_customers` Table

A new `stripe_customers` table has been created to track the relationship between users and their Stripe customer IDs:

```sql
CREATE TABLE IF NOT EXISTS stripe_customers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES profiles(id),
  stripe_customer_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id),
  UNIQUE(stripe_customer_id)
);
```

This table provides the following benefits:
- Allows reusing Stripe customers for recurring payments
- Provides a clear mapping between users and their Stripe customer IDs
- Makes it easier to track and manage Stripe customers

### 2. `stripe_connect_accounts` Table

A new `stripe_connect_accounts` table has been created to track the relationship between users and their Stripe Connect account IDs:

```sql
CREATE TABLE IF NOT EXISTS stripe_connect_accounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES profiles(id),
  stripe_account_id TEXT NOT NULL,
  account_status TEXT NOT NULL,
  charges_enabled BOOLEAN DEFAULT FALSE,
  payouts_enabled BOOLEAN DEFAULT FALSE,
  details_submitted BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id),
  UNIQUE(stripe_account_id)
);
```

This table provides the following benefits:
- Allows tracking the status of Stripe Connect accounts
- Provides a clear mapping between users and their Stripe Connect account IDs
- Makes it easier to manage payouts to suppliers

## Service Improvements

### 1. `stripeCustomerService`

A new `stripeCustomerService` has been created to manage Stripe customers:

```typescript
export const stripeCustomerService = {
  async getOrCreateCustomer(userId: string, email: string, name?: string): Promise<string> {
    // ...
  },
  
  async getCustomerId(userId: string): Promise<string | null> {
    // ...
  },
  
  async updateCustomer(userId: string, data: Stripe.CustomerUpdateParams): Promise<Stripe.Customer> {
    // ...
  },
  
  async deleteCustomer(userId: string): Promise<boolean> {
    // ...
  }
};
```

This service provides the following benefits:
- Centralizes Stripe customer management
- Handles the case where the `stripe_customers` table doesn't exist
- Provides methods for getting, creating, updating, and deleting Stripe customers

### 2. `stripeConnectService`

A new `stripeConnectService` has been created to manage Stripe Connect accounts:

```typescript
export const stripeConnectService = {
  async getAccountId(userId: string): Promise<string | null> {
    // ...
  },
  
  async createAccount(userId: string, email: string): Promise<Stripe.Account> {
    // ...
  },
  
  async getOrCreateAccount(userId: string, email: string): Promise<string> {
    // ...
  },
  
  async createAccountLink(userId: string, email: string, refreshUrl: string, returnUrl: string): Promise<string> {
    // ...
  },
  
  async updateAccountStatus(userId: string): Promise<boolean> {
    // ...
  },
  
  async createLoginLink(userId: string): Promise<string> {
    // ...
  },
  
  async deleteAccount(userId: string): Promise<boolean> {
    // ...
  }
};
```

This service provides the following benefits:
- Centralizes Stripe Connect account management
- Handles the case where the `stripe_connect_accounts` table doesn't exist
- Provides methods for getting, creating, updating, and deleting Stripe Connect accounts

### 3. Improved Invoice Creation

The invoice creation process has been improved to use the Stripe customer service:

```javascript
// Get or create a Stripe customer for the payer
const customerEmail = Array.isArray(payer.email) ? payer.email[0] : payer.email;
const customerName = payer.full_name || customerEmail;
const customerId = await getOrCreateCustomer(payment.payer_id, customerEmail, customerName);
```

This provides the following benefits:
- Handles the case where the email is returned as an array
- Reuses existing Stripe customers for recurring payments
- Provides a more robust way to create and manage Stripe customers

## Business Logic Improvements

### 1. Supplier Email and Organization Validation

The supplier user (ID: `********-2496-45a4-a1d8-675a9bf2683b`) has been updated to have a valid email and organization ID:

```sql
UPDATE profiles
SET 
  email = '<EMAIL>',
  organization_id = (SELECT organization_id FROM profiles WHERE id = '4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd')
WHERE 
  id = '********-2496-45a4-a1d8-675a9bf2683b'
  AND (email IS NULL OR organization_id IS NULL);
```

This provides the following benefits:
- Ensures that suppliers have a valid email for notifications
- Ensures that suppliers are associated with an organization
- Prevents issues when trying to send emails or payments to suppliers

### 2. Stripe Customer Management

The Stripe customer management has been improved to handle the case where the `stripe_customers` table doesn't exist:

```typescript
if (stripeCustomersError) {
  console.error('Error fetching Stripe customer:', stripeCustomersError);
  
  // If the stripe_customers table doesn't exist, return null
  if (stripeCustomersError.code === '42P01') {
    console.warn('stripe_customers table does not exist.');
    return null;
  }
  
  throw stripeCustomersError;
}
```

This provides the following benefits:
- Makes the application more robust
- Handles the case where the `stripe_customers` table doesn't exist
- Provides better error handling

### 3. Stripe Connect Account Management

The Stripe Connect account management has been improved to handle the case where the `stripe_connect_accounts` table doesn't exist:

```typescript
if (stripeAccountsError) {
  console.error('Error fetching Stripe Connect account:', stripeAccountsError);
  
  // If the stripe_connect_accounts table doesn't exist, return null
  if (stripeAccountsError.code === '42P01') {
    console.warn('stripe_connect_accounts table does not exist.');
    return null;
  }
  
  throw stripeAccountsError;
}
```

This provides the following benefits:
- Makes the application more robust
- Handles the case where the `stripe_connect_accounts` table doesn't exist
- Provides better error handling

## Next Steps

1. **Run the SQL Scripts**:
   - Run the SQL scripts to create the `stripe_customers` and `stripe_connect_accounts` tables
   - Update the supplier user to have a valid email and organization ID

2. **Integrate the New Services**:
   - Integrate the `stripeCustomerService` and `stripeConnectService` into the application
   - Update the invoice creation process to use the new services

3. **Test the Improvements**:
   - Test the invoice creation process with the specific users
   - Verify that the Stripe customers and Connect accounts are properly managed
   - Ensure that invoices are properly created and sent

4. **Monitor for Issues**:
   - Monitor the application for any issues related to Stripe integration
   - Address any issues that arise

## Conclusion

These improvements make the Stripe integration more robust and easier to manage. They address the issues identified in the test script and provide a better foundation for future development.
