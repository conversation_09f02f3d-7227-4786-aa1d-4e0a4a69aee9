# ClassTasker Task Management Developer Guide

## Quick Reference

This guide provides quick reference information for developers implementing the task management redesign.

## Core Principles

1. **Stability Over Performance**: Always prioritize reliable behavior over optimization.
2. **Always Mount, Conditionally Render**: Components should always mount but adapt their content based on props.
3. **Single Source of Truth**: Use profiles table as the authoritative source for user data.
4. **Comprehensive Role Support**: Properly handle all staff roles, not just the common ones.
5. **No Emergency Code**: Implement proper solutions instead of temporary fixes.
6. **Clear Type Separation**: Maintain clear distinction between internal and external tasks.

## Task Types

### Internal Tasks

- Assigned directly by admins to school staff
- Simplified workflow: assigned → in_progress → completed → confirmed
- No marketplace or payment functionality
- Assigned to various school staff roles (maintenance, support, cleaning, IT)

### External Tasks

- Published to marketplace for external suppliers
- Complex workflow with bidding: open → interest → questions → assigned → in_progress → completed → confirmed → pending_payment
- Includes offer and payment functionality
- Always assigned to suppliers

## Role System

### Available Roles

| Role | Type | Description | Can Be Assigned Tasks |
|------|------|-------------|----------------------|
| admin | School Staff | School administrators | No |
| maintenance | School Staff | Maintenance and facilities staff | Yes |
| support | School Staff | Support workers | Yes |
| cleaner | School Staff | Cleaning staff | Yes |
| it | School Staff | IT support staff | Yes |
| teacher | School Staff | Teaching staff | No |
| finance | School Staff | Finance department staff | No |
| reception | School Staff | Front desk staff | No |
| supplier | External | External service providers | Yes (external tasks only) |

### Role Groups

```typescript
// Common role groups
SCHOOL_STAFF: ['admin', 'maintenance', 'support', 'cleaner', 'it', 'teacher', 'finance', 'reception']
TASK_ASSIGNABLE: ['maintenance', 'support', 'cleaner', 'it']
TASK_CREATORS: ['admin', 'teacher', 'reception']
```

## Auth/Profile Access

### Correct Pattern

```typescript
// In components
const { profile, isAdmin, isMaintenance, isUserInRole } = useAuth();

// Use the helper functions for common roles
if (isAdmin) {
  // Admin-only code
}

// Use the general function for other roles
if (isUserInRole('support')) {
  // Support staff code
}

// Check role groups
if (isUserInRoleGroup('TASK_ASSIGNABLE')) {
  // Code for staff that can be assigned tasks
}
```

### Incorrect Patterns to Avoid

```typescript
// DON'T use metadata directly
const { user } = useAuth();
const role = user?.user_metadata?.role;  // WRONG!

// DON'T mix sources
const role = profile?.role || user?.user_metadata?.role;  // WRONG!

// DON'T use inconsistent role checking
if (user?.user_metadata?.role === 'admin') {  // WRONG!
  // Admin-only code
}
```

## Component Pattern: Always Mount, Conditionally Render

### Correct Pattern

```typescript
const TaskActions = ({ task, isLoading }) => {
  // Early returns for loading/missing data with appropriate UI
  if (isLoading) {
    return <TaskActionsSkeleton />;
  }

  if (!task) {
    return <NoTaskAvailable />;
  }

  // Determine available actions
  const availableActions = determineAvailableActions(task);

  // Render appropriate content based on available actions
  return (
    <Card>
      <CardHeader>
        <CardTitle>Task Actions</CardTitle>
      </CardHeader>
      <CardContent>
        {availableActions.length > 0 ? (
          availableActions.map(action => (
            <ActionButton key={action.id} action={action} task={task} />
          ))
        ) : (
          <NoActionsAvailable task={task} />
        )}
      </CardContent>
    </Card>
  );
};
```

### Incorrect Pattern to Avoid

```typescript
// DON'T conditionally mount components
{isInternalTask && isMaintenance && isAssignedStaff && (
  <InternalTaskActions task={task} />
)}

// DON'T use complex conditions for mounting
{(isInternalTask && (isAdmin || isTaskOwner)) ||
 (isInternalTask && isMaintenance && isAssignedStaff) ||
 (isMaintenance && isAssignedStaff) ? (
  <InternalTaskActions task={task} />
) : null}
```

## Error Handling

### Correct Pattern

```typescript
// Use error boundaries for component-level errors
<ErrorBoundary fallback={<ErrorFallback />}>
  <TaskComponent task={task} />
</ErrorBoundary>

// Handle async errors properly
const fetchData = async () => {
  try {
    setIsLoading(true);
    const result = await someAsyncOperation();
    setData(result);
  } catch (error) {
    console.error('Error fetching data:', error);
    setError(error);
  } finally {
    setIsLoading(false);
  }
};
```

## Type Guards

Use type guards to safely work with task types:

```typescript
// Type guard functions
function isInternalTask(task: Task): task is InternalTask {
  return task.type === 'internal';
}

function isExternalTask(task: Task): task is ExternalTask {
  return task.type === 'external';
}

// Usage
if (isInternalTask(task)) {
  // TypeScript knows task is InternalTask here
  console.log(task.assigned_role);
} else {
  // TypeScript knows task is ExternalTask here
  console.log(task.offers_count);
}
```

## Timeline Configuration

Different task types should use different timeline configurations:

```typescript
const getTimelineConfiguration = (task: Task) => {
  if (isInternalTask(task)) {
    return {
      statuses: ['assigned', 'in_progress', 'completed', 'confirmed'],
      descriptions: {
        assigned: 'Task has been assigned to staff',
        in_progress: 'Work has started on this task',
        completed: 'Staff has marked this task as completed',
        confirmed: 'Task completion has been confirmed'
      },
      currentIndex: getStatusIndex(task.status, ['assigned', 'in_progress', 'completed', 'confirmed'])
    };
  } else {
    return {
      statuses: ['open', 'interest', 'questions', 'assigned', 'in_progress', 'completed', 'confirmed', 'pending_payment'],
      descriptions: {
        open: 'Task is open for offers',
        interest: 'Suppliers have expressed interest',
        questions: 'Discussion phase with suppliers',
        assigned: 'Task has been assigned to a supplier',
        in_progress: 'Supplier has started work',
        completed: 'Supplier has marked task as completed',
        confirmed: 'Task completion has been confirmed',
        pending_payment: 'Payment is required'
      },
      currentIndex: getStatusIndex(task.status, ['open', 'interest', 'questions', 'assigned', 'in_progress', 'completed', 'confirmed', 'pending_payment'])
    };
  }
};
```

## System Messages

Create appropriate system messages based on task type and user role:

```typescript
const createStatusChangeMessage = async (task: Task, newStatus: string) => {
  // Get assigned user's information if available
  let assignedUserInfo = null;
  if (task.assigned_to) {
    const { data } = await supabase
      .from('profiles')
      .select('first_name, last_name, role')
      .eq('id', task.assigned_to)
      .single();
    assignedUserInfo = data;
  }

  // Create appropriate message based on task type
  if (isInternalTask(task)) {
    const roleText = assignedUserInfo ?
      getDisplayNameForRole(assignedUserInfo.role) :
      'staff member';

    switch (newStatus) {
      case 'assigned':
        return `Task has been assigned to ${roleText}.`;
      case 'in_progress':
        return `${roleText} has started work on this task.`;
      case 'completed':
        return `${roleText} has marked this task as completed.`;
      case 'confirmed':
        return 'Task completion has been confirmed.';
      default:
        return `Task status changed to: ${newStatus}`;
    }
  } else {
    // External task messages
    switch (newStatus) {
      case 'assigned':
        return 'Task has been assigned to a supplier.';
      // ... other cases
    }
  }
};
```

## Working with Existing Modules

The following approach balances preserving working functionality while allowing necessary modifications to align with the new architecture:

### Module Modification Strategy

#### Category 1: Minimal Change
Modules that can work with simple adapters or minimal modifications:

1. **Sign-up Flow**
   - Preserve the overall user experience and flow
   - Update role selection to align with new role system
   - Modify profile creation to ensure consistent data structure

#### Category 2: Targeted Modification
Modules requiring specific, limited changes:

2. **Task Creation Module**
   - Preserve form UI and validation logic
   - Add task type selection or determination
   - Update submission handler to use new task type system

3. **Dashboard Components**
   - Preserve visualization and interaction patterns
   - Update data fetching and processing for new task types
   - Modify filtering logic to handle different task workflows

4. **Compliance Dashboard**
   - Preserve compliance tracking functionality
   - Update data layer to work with new task structure
   - Ensure consistent status interpretation

#### Category 3: Significant Adaptation
Modules needing more substantial changes:

5. **Chat Module**
   - Preserve chat UI and user interaction patterns
   - Update message creation logic for task type awareness
   - Modify system message handling for different task workflows

6. **Task Assignment Interface**
   - Preserve assignment UI patterns
   - Update role filtering to align with comprehensive role system
   - Modify assignment logic to handle assigned_role field

### Integration Approaches

#### Adapter Pattern (for Category 1)

```typescript
// Example: Adapter for dashboard components
const DashboardAdapter = ({ filters, onFilterChange }) => {
  // Convert filters to format expected by existing components
  const adaptedFilters = {
    ...filters,
    // Map new task types to visibility values expected by existing components
    visibility: filters.type === 'internal' ? 'internal' : 'public',
  };

  // Handle filter changes from existing components
  const handleFilterChange = (newFilters) => {
    // Convert back to new format
    const updatedFilters = {
      ...newFilters,
      // Map visibility back to type
      type: newFilters.visibility === 'internal' ? 'internal' : 'external',
    };
    onFilterChange(updatedFilters);
  };

  // Pass adapted data to existing component
  return <ExistingDashboard
    filters={adaptedFilters}
    onFilterChange={handleFilterChange}
  />;
};
```

#### Targeted Modification (for Category 2)

```typescript
// Example: Targeted modification of task creation
const enhanceTaskCreation = () => {
  // Get reference to existing form submission handler
  const originalSubmitHandler = TaskCreationForm.prototype.handleSubmit;

  // Replace with enhanced version
  TaskCreationForm.prototype.handleSubmit = async function(event) {
    // Call original logic to validate and prepare task data
    const taskData = await originalSubmitHandler.call(this, event);

    // Enhance with new task type
    const enhancedTaskData = {
      ...taskData,
      type: taskData.visibility === 'internal' ? 'internal' : 'external',
      // Add other new fields as needed
    };

    // Continue with submission
    return this.submitTask(enhancedTaskData);
  };
};
```

#### Component Extension (for Category 3)

```typescript
// Example: Extending chat module
class EnhancedTaskChat extends React.Component {
  constructor(props) {
    super(props);
    this.originalChatRef = React.createRef();
  }

  // Override system message creation
  createSystemMessage = (status, task) => {
    // Use task type to determine appropriate message
    if (task.type === 'internal') {
      switch(status) {
        case 'assigned':
          return `Task has been assigned to ${this.getStaffRoleText(task.assigned_role)}.`;
        // Other cases
      }
    } else {
      // External task messages
      switch(status) {
        case 'assigned':
          return 'Task has been assigned to a supplier.';
        // Other cases
      }
    }
  }

  getStaffRoleText(role) {
    // Convert role to user-friendly text
    switch(role) {
      case 'maintenance': return 'maintenance staff';
      case 'it': return 'IT support';
      // Other roles
      default: return 'staff member';
    }
  }

  render() {
    // Pass enhanced props to original component
    return <OriginalTaskChat
      ref={this.originalChatRef}
      {...this.props}
      createSystemMessage={this.createSystemMessage}
    />;
  }
}
```

### Testing Strategy

Test each modified module with focus on preserved functionality:

```typescript
// Test for Category 1 (Adapter)
test('Dashboard adapter preserves filtering functionality', () => {
  // Setup with new task type system
  const filters = { type: 'internal', status: 'assigned' };
  const handleFilterChange = jest.fn();

  // Render with adapter
  render(<DashboardAdapter filters={filters} onFilterChange={handleFilterChange} />);

  // Verify existing filters are displayed correctly
  expect(screen.getByTestId('filter-visibility')).toHaveValue('internal');

  // Test interaction
  fireEvent.change(screen.getByTestId('filter-status'), { target: { value: 'completed' } });

  // Verify filter change is handled correctly
  expect(handleFilterChange).toHaveBeenCalledWith(
    expect.objectContaining({ type: 'internal', status: 'completed' })
  );
});

// Test for Category 2 (Targeted Modification)
test('Task creation handles new task type field', async () => {
  // Render task creation form
  render(<TaskCreationForm />);

  // Fill out form
  fireEvent.change(screen.getByLabelText('Title'), { target: { value: 'Test Task' } });
  fireEvent.change(screen.getByLabelText('Visibility'), { target: { value: 'internal' } });
  // Fill other fields

  // Submit form
  fireEvent.click(screen.getByText('Create Task'));

  // Verify submission includes new type field
  await waitFor(() => {
    expect(mockSubmitFunction).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Test Task',
        visibility: 'internal',
        type: 'internal'
      })
    );
  });
});

// Test for Category 3 (Component Extension)
test('Enhanced chat shows correct system messages for internal tasks', () => {
  // Create internal task
  const internalTask = {
    id: '123',
    type: 'internal',
    assigned_role: 'maintenance',
    // Other properties
  };

  // Render enhanced chat
  render(<EnhancedTaskChat task={internalTask} />);

  // Simulate status change to trigger system message
  fireEvent.click(screen.getByText('Start Work'));

  // Verify correct system message is displayed
  expect(screen.getByText('Task has been assigned to maintenance staff.')).toBeInTheDocument();
});
```

## Debugging

Use the debug panel for development-only debugging:

```typescript
const DebugPanel = ({ task }) => {
  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <details className="mt-4 p-4 border border-gray-200 rounded-md">
      <summary className="font-medium text-sm text-gray-500">Debug Information</summary>
      <pre className="mt-2 text-xs overflow-auto">
        {JSON.stringify(task, null, 2)}
      </pre>
    </details>
  );
};
```

## Testing

### Unit Tests

```typescript
// Test task type determination
test('TaskFactory correctly identifies internal tasks', () => {
  const internalTask = TaskFactory.createFromDatabase({
    id: '123',
    visibility: 'internal',
    // other properties
  });

  expect(internalTask.type).toBe('internal');
  expect(TaskFactory.isInternalTask(internalTask)).toBe(true);
});

// Test role checking
test('isUserInRoleGroup correctly identifies school staff', () => {
  const profile = { role: 'teacher' };

  expect(isInRoleGroup(profile.role, ROLE_GROUPS.SCHOOL_STAFF)).toBe(true);
  expect(isInRoleGroup(profile.role, ROLE_GROUPS.TASK_ASSIGNABLE)).toBe(false);
});
```

### Component Tests

```typescript
test('TaskActions always renders with appropriate content', () => {
  // Test with loading state
  const { rerender } = render(<TaskActions isLoading={true} task={null} />);
  expect(screen.getByTestId('task-actions-skeleton')).toBeInTheDocument();

  // Test with no available actions
  rerender(<TaskActions isLoading={false} task={mockTask} />);
  expect(screen.getByText('No actions available')).toBeInTheDocument();

  // Test with available actions
  const taskWithActions = { ...mockTask, status: 'assigned' };
  rerender(<TaskActions isLoading={false} task={taskWithActions} />);
  expect(screen.getByText('Start Work')).toBeInTheDocument();
});
```
