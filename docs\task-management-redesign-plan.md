# ClassTasker Task Management Redesign Plan

## Overview

This document outlines the comprehensive plan for redesigning the task management system in ClassTasker to address architectural issues, role management, auth/profile consistency, and technical debt cleanup.

## Current Issues

1. **Architectural Inconsistencies**
   - Single task model trying to handle fundamentally different workflows
   - Inconsistent status interpretation between internal and external tasks
   - Incorrect system messages and timeline descriptions
   - Unstable component mounting for task actions

2. **Role Management Problems**
   - Incomplete handling of school staff roles (maintenance, support, cleaning, IT)
   - Inconsistent role determination across components
   - Lack of proper role-based access control

3. **Auth/Profile Inconsistencies**
   - Multiple sources of truth for user data (auth metadata vs. profiles table)
   - Inconsistent role checking methods
   - Race conditions in auth/profile data loading

4. **Technical Debt**
   - Emergency fixes and workarounds
   - Debug routes and components
   - Duplicate implementations
   - Hardcoded values

## Implementation Plan

### Phase 1: Analysis and Planning

#### 1.1 Comprehensive Codebase Audit
- **Route Analysis**: Map all task-related routes, identifying duplicates and emergency implementations
- **Component Analysis**: Identify components with duplicate functionality or emergency overrides
- **Auth/Profile Analysis**: Document all sources of auth/profile data and role determination methods
- **Role System Analysis**: Create inventory of all staff roles and how they're used throughout the app
- **Technical Debt Identification**: Catalog hardcoded values, temporary fixes, and debug components

#### 1.2 Working Module Adaptation Strategy
- **Module Inventory and Categorization**: Identify well-functioning modules and categorize by modification needs:
  - **Category 1 (Minimal Change)**: Sign-up flow
  - **Category 2 (Targeted Modification)**: Task creation module, Dashboard components, Compliance dashboard
  - **Category 3 (Significant Adaptation)**: Chat module, Task assignment interface
- **Interface Analysis**: Document the interfaces, dependencies, and core functionality to preserve
- **Modification Strategy**: Develop appropriate strategies for each category:
  - Simple adapters for Category 1
  - Targeted modifications for Category 2
  - Component extensions for Category 3
- **Testing Strategy**: Create specific tests to verify preserved functionality after modifications

#### 1.3 Architecture Design
- **Task Type System**: Design clear type definitions for internal and external tasks
- **Role Management System**: Design comprehensive role taxonomy with proper grouping
- **Component Structure**: Plan "always mount, conditionally render" pattern for all components
- **Auth/Profile Strategy**: Design single source of truth for user data and role information
- **Database Schema Updates**: Plan necessary changes to support the new architecture

#### 1.4 Migration Strategy
- **Data Migration Plan**: Design scripts for updating existing tasks and user profiles
- **Route Consolidation Plan**: Determine primary routes and redirection strategy
- **System Message Cleanup**: Plan approach for fixing incorrect system messages
- **Rollout Strategy**: Design phased implementation to minimize disruption

### Phase 2: Foundation Improvements

#### 2.1 Auth and Profile Consolidation
- **Establish Single Source of Truth**: Use profiles table as authoritative source for user data
- **Update Auth Context**:
  - Implement robust auth state management with proper loading states
  - Ensure profile data is always loaded with auth data
  - Add proper error handling and recovery mechanisms
- **Standardize Role Access**:
  - Create central roles configuration with constants and helper functions
  - Implement role groups for common access patterns (school staff, assignable staff, etc.)
  - Replace all direct metadata access with profile-based role checking

#### 2.2 Database Schema Updates
- **Task Table Enhancements**:
  - Add `type` column (values: 'internal', 'external')
  - Add `assigned_role` column to track role of assigned staff
  - Maintain `visibility` field for backward compatibility
- **Profile Table Standardization**:
  - Ensure consistent role values
  - Add any fields needed for role-specific information
- **Data Migration**:
  - Update existing tasks with appropriate type values
  - Populate assigned_role for existing tasks
  - Standardize role values in profiles

#### 2.3 Technical Debt Cleanup
- **Remove Emergency Code**:
  - Eliminate hardcoded values and forced properties
  - Remove conditional debug rendering
  - Clean up temporary workarounds
- **Consolidate Duplicate Routes**:
  - Identify primary routes for each feature
  - Implement redirects from deprecated routes
  - Update navigation links to use primary routes
- **Debug Mode Refactoring**:
  - Create proper debug panel that can be toggled in development
  - Consolidate all debugging functionality in one place
  - Ensure debug tools don't affect production behavior

### Phase 3: Core Architecture Implementation

#### 3.1 Task Type System
- **Implement Type Definitions**:
  ```typescript
  interface BaseTask {
    id: string;
    // Common properties
  }

  interface InternalTask extends BaseTask {
    type: 'internal';
    status: 'assigned' | 'in_progress' | 'completed' | 'confirmed';
    assigned_to: string;
    assigned_role: string;
  }

  interface ExternalTask extends BaseTask {
    type: 'external';
    status: 'open' | 'interest' | 'questions' | 'assigned' | 'in_progress' | 'completed' | 'confirmed' | 'pending_payment';
    // External-specific properties
  }

  type Task = InternalTask | ExternalTask;
  ```
- **Create TaskFactory**:
  - Convert database records to typed tasks
  - Handle backward compatibility with visibility field
  - Ensure proper typing for all task operations

#### 3.2 Role Management System
- **Implement Role Constants and Helpers**:
  ```typescript
  export const ROLES = {
    // External roles
    SUPPLIER: 'supplier',

    // Organization roles
    ADMIN: 'admin',

    // Internal staff roles
    MAINTENANCE: 'maintenance',
    SUPPORT: 'support',
    CLEANER: 'cleaner',
    IT: 'it',
    TEACHER: 'teacher',
    // Additional roles
  };

  export const ROLE_GROUPS = {
    SCHOOL_STAFF: [/* all school staff roles */],
    TASK_ASSIGNABLE: [/* roles that can be assigned tasks */],
    // Other role groups
  };
  ```
- **Integrate with Auth Context**:
  - Add role-specific helper methods
  - Implement role group checking
  - Ensure consistent role terminology

#### 3.3 Component Architecture
- **Implement "Always Mount" Pattern**:
  - Create stable container components that handle loading/error states
  - Use conditional rendering within components rather than conditional mounting
  - Implement proper error boundaries and fallbacks
- **Create Unified Task Page**:
  - Single route that handles both internal and external tasks
  - Type-aware rendering of appropriate components
  - Clear visual indicators of task type

### Phase 4: UI Component Implementation

#### 4.1 Task Actions Component
- **Create Unified Component**:
  - Early returns for loading/missing data
  - Determine available actions based on task type, status, and user role
  - Render appropriate actions
- **Implement Action Determination Logic**:
  - Create centralized function to determine available actions
  - Handle all task types and user roles
  - Support role-specific actions for different staff types

#### 4.2 Timeline Component
- **Create Type-Aware Timeline**:
  - Get appropriate configuration based on task type
  - Render timeline with appropriate statuses
- **Implement Timeline Configurations**:
  - Create separate configurations for internal and external tasks
  - Ensure appropriate status descriptions for each task type
  - Add role-specific information where relevant

#### 4.3 System Messages
- **Implement Role-Aware Message Factory**:
  - Get assigned user's role information
  - Create appropriate message based on task type and user role
- **Message Cleanup Utility**:
  - Create utility to fix existing incorrect messages
  - Ensure consistency in role terminology
  - Handle both internal and external tasks

#### 4.4 Task Assignment Interface
- **Enhance Assignment UI**:
  - Show staff members grouped by role
  - Filter available staff based on task category
  - Clearly indicate staff roles in the selection UI
- **Implement Role-Based Assignment Logic**:
  - Determine appropriate roles for each task category
  - Store assigned role with task
  - Update task status and notifications based on role

### Phase 5: Testing and Validation

#### 5.1 Unit Testing
- Test task type determination
- Test role checking and grouping
- Test action determination logic
- Test timeline configuration

#### 5.2 Component Testing
- Test components with various loading states
- Test with different task types and statuses
- Test with different user roles
- Test error handling and fallbacks

#### 5.3 Integration Testing
- Test complete internal task workflows for each staff role
- Test external task marketplace workflow
- Test role-based permissions and access control
- Test transitions between task statuses

#### 5.4 Migration Testing
- Verify task type and assigned_role population
- Test system message cleanup
- Verify role standardization in profiles
- Test route redirections

### Phase 6: Deployment and Rollout

#### 6.1 Database Changes
- Deploy schema updates
- Run data migration scripts
- Verify data integrity

#### 6.2 Auth/Profile Updates
- Deploy updated auth context
- Implement role standardization
- Verify role-based access control

#### 6.3 Component Rollout
- Deploy core components with "always mount" pattern
- Implement unified task page
- Add redirects from deprecated routes

#### 6.4 Monitoring and Feedback
- Add comprehensive logging
- Monitor component mounting and rendering
- Track role-based access patterns
- Collect user feedback on the new implementation

### Phase 7: Documentation and Knowledge Transfer

#### 7.1 Architecture Documentation
- Document the task type system
- Explain the role management system
- Document the "always mount" pattern
- Provide examples of correct implementation

#### 7.2 Role System Documentation
- Document all available roles and their purposes
- Explain role groups and when to use them
- Provide guidelines for role-based access control
- Document the process for adding new roles

#### 7.3 Developer Guidelines
- Create guidelines for working with the new architecture
- Document best practices for role-aware components
- Provide examples of correct auth/profile access
- Document the debugging and testing process

## Implementation Principles

Throughout all phases, we will adhere to these core principles:

1. **Stability Over Performance**: Prioritize reliable behavior over optimization
2. **Always Mount, Conditionally Render**: Components should always mount but adapt their content
3. **Single Source of Truth**: Use profiles table as the authoritative source for user data
4. **Comprehensive Role Support**: Properly handle all staff roles, not just the common ones
5. **No Emergency Code**: Implement proper solutions instead of temporary fixes
6. **Clear Type Separation**: Maintain clear distinction between internal and external tasks
7. **Graceful Degradation**: Provide fallbacks for all error conditions
8. **Consistent Patterns**: Use the same patterns throughout the application

## Timeline and Resources

[To be determined based on team availability and priorities]

## Conclusion

This redesign will address the fundamental architectural issues in the task management system, creating a more stable, maintainable codebase that correctly handles all task types and user roles. By prioritizing stability and implementing clear patterns, we'll eliminate the edge cases and inconsistencies in the current implementation.
