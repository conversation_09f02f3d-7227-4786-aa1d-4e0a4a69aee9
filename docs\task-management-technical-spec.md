# ClassTasker Task Management Technical Specification

## 1. Task Type System

### 1.1 Type Definitions

```typescript
// Base task interface with common properties
interface BaseTask {
  id: string;
  title: string;
  description: string;
  location: string;
  category: string;
  budget: string;
  due_date: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  organization_id: string;
  // Common fields for all tasks
}

// Internal task assigned to school staff
interface InternalTask extends BaseTask {
  type: 'internal';
  visibility: 'internal'; // For backward compatibility
  status: 'assigned' | 'in_progress' | 'completed' | 'confirmed';
  assigned_to: string; // ID of internal staff member
  assigned_role: string; // Role of the assigned staff (maintenance, support, IT, etc.)
  payment_status: 'not_required';
}

// External task for marketplace
interface ExternalTask extends BaseTask {
  type: 'external';
  visibility: 'public'; // For backward compatibility
  status: 'open' | 'interest' | 'questions' | 'assigned' | 'in_progress' | 'completed' | 'confirmed' | 'pending_payment';
  assigned_to: string | null; // ID of supplier (if assigned)
  payment_status: 'pending' | 'processing' | 'paid' | 'not_required';
  offers_count: number;
}

// Union type for application use
type Task = InternalTask | ExternalTask;
```

### 1.2 Task Factory

```typescript
class TaskFactory {
  static createFromDatabase(data: any): Task {
    const baseTask = {
      id: data.id,
      title: data.title,
      description: data.description,
      location: data.location,
      category: data.category,
      budget: data.budget,
      due_date: data.due_date,
      created_at: data.created_at,
      updated_at: data.updated_at,
      user_id: data.user_id,
      organization_id: data.organization_id,
    };

    // Determine task type
    const isInternal = data.visibility === 'internal' || data.type === 'internal';

    if (isInternal) {
      return {
        ...baseTask,
        type: 'internal',
        visibility: 'internal',
        status: data.status || 'assigned',
        assigned_to: data.assigned_to || '',
        assigned_role: data.assigned_role || this.determineRoleFromUser(data.assigned_to),
        payment_status: 'not_required'
      } as InternalTask;
    } else {
      return {
        ...baseTask,
        type: 'external',
        visibility: 'public',
        status: data.status || 'open',
        assigned_to: data.assigned_to || null,
        payment_status: data.payment_status || 'not_required',
        offers_count: data.offers_count || 0
      } as ExternalTask;
    }
  }

  // Helper method to determine role from user ID
  private static async determineRoleFromUser(userId: string): Promise<string> {
    if (!userId) return 'unknown';

    try {
      const { data } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userId)
        .single();

      return data?.role || 'unknown';
    } catch (error) {
      console.error('Error determining role from user:', error);
      return 'unknown';
    }
  }

  // Type guard functions
  static isInternalTask(task: Task): task is InternalTask {
    return task.type === 'internal';
  }

  static isExternalTask(task: Task): task is ExternalTask {
    return task.type === 'external';
  }
}
```

## 2. Role Management System

### 2.1 Role Constants and Groups

```typescript
// roles.ts - Central role configuration
export const ROLES = {
  // External roles
  SUPPLIER: 'supplier',

  // Organization roles
  ADMIN: 'admin',

  // Internal staff roles
  MAINTENANCE: 'maintenance',
  SUPPORT: 'support',
  CLEANER: 'cleaner',
  IT: 'it',
  TEACHER: 'teacher',
  FINANCE: 'finance',
  RECEPTION: 'reception'
};

// Role groups for common access patterns
export const ROLE_GROUPS = {
  SCHOOL_STAFF: [
    ROLES.ADMIN,
    ROLES.MAINTENANCE,
    ROLES.SUPPORT,
    ROLES.CLEANER,
    ROLES.IT,
    ROLES.TEACHER,
    ROLES.FINANCE,
    ROLES.RECEPTION
  ],
  TASK_ASSIGNABLE: [
    ROLES.MAINTENANCE,
    ROLES.SUPPORT,
    ROLES.CLEANER,
    ROLES.IT
  ],
  TASK_CREATORS: [
    ROLES.ADMIN,
    ROLES.TEACHER,
    ROLES.RECEPTION
  ]
};

// Helper functions
export const isInRoleGroup = (role: string, group: string[]): boolean => {
  return group.includes(role);
};

export const isAssignable = (role: string): boolean => {
  return isInRoleGroup(role, ROLE_GROUPS.TASK_ASSIGNABLE);
};

export const getDisplayNameForRole = (role: string): string => {
  switch (role) {
    case ROLES.ADMIN: return 'Administrator';
    case ROLES.MAINTENANCE: return 'Maintenance Staff';
    case ROLES.SUPPORT: return 'Support Worker';
    case ROLES.CLEANER: return 'Cleaning Staff';
    case ROLES.IT: return 'IT Support';
    case ROLES.TEACHER: return 'Teacher';
    case ROLES.FINANCE: return 'Finance Staff';
    case ROLES.RECEPTION: return 'Reception Staff';
    case ROLES.SUPPLIER: return 'External Supplier';
    default: return role.charAt(0).toUpperCase() + role.slice(1);
  }
};
```

## 3. Component Architecture

### 3.1 Auth Context with Enhanced Role Support

```typescript
const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Auth state management
  useEffect(() => {
    // Set up auth state listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user || null);

        if (session?.user) {
          try {
            // Always fetch profile data when auth changes
            const { data, error } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', session.user.id)
              .single();

            if (error) throw error;
            setProfile(data);
          } catch (error) {
            console.error('Error fetching profile:', error);
            setError(error);
          }
        } else {
          setProfile(null);
        }

        setIsLoading(false);
      }
    );

    // Initial auth check
    checkUser();

    return () => {
      authListener?.subscription?.unsubscribe();
    };
  }, []);

  // Enhanced role helpers
  const isUserInRole = useCallback((role) => {
    return profile?.role === role;
  }, [profile]);

  const isUserInRoleGroup = useCallback((roleGroup) => {
    return profile ? ROLE_GROUPS[roleGroup].includes(profile.role) : false;
  }, [profile]);

  const value = {
    user,
    profile,
    isLoading,
    error,
    // Common role checks
    isAdmin: isUserInRole(ROLES.ADMIN),
    isSupplier: isUserInRole(ROLES.SUPPLIER),
    // Staff role checks
    isMaintenance: isUserInRole(ROLES.MAINTENANCE),
    isSupport: isUserInRole(ROLES.SUPPORT),
    isCleaner: isUserInRole(ROLES.CLEANER),
    isIT: isUserInRole(ROLES.IT),
    // Role group checks
    isSchoolStaff: isUserInRoleGroup('SCHOOL_STAFF'),
    isAssignableStaff: isUserInRoleGroup('TASK_ASSIGNABLE'),
    isTaskCreator: isUserInRoleGroup('TASK_CREATORS'),
    // Helper methods
    isUserInRole,
    isUserInRoleGroup,
    getRoleDisplayName: (role) => getDisplayNameForRole(role)
  };

  // Don't render children until auth is initialized
  if (isLoading) {
    return <AuthLoadingScreen />;
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
```

### 3.2 Task Page with "Always Mount" Pattern

```typescript
const TaskPage = () => {
  const { id } = useParams<{ id: string }>();
  const { data: task, isLoading, error } = useTask(id);

  // Always render the container structure
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link to="/tasks" className="text-gray-600 hover:text-classtasker-blue flex items-center">
            <ArrowLeft className="h-4 w-4 mr-1" /> Back to Tasks
          </Link>
        </div>

        {/* Task header always mounted */}
        <TaskHeader task={task} isLoading={isLoading} error={error} />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {/* Task details always mounted */}
            <TaskDetails task={task} isLoading={isLoading} />

            {/* Task timeline always mounted */}
            <TaskTimeline task={task} isLoading={isLoading} />

            {/* Task actions always mounted */}
            <TaskActions task={task} isLoading={isLoading} />

            {/* Task chat always mounted */}
            <TaskChat task={task} isLoading={isLoading} />
          </div>

          <div className="space-y-6">
            {/* Task sidebar always mounted */}
            <TaskSidebar task={task} isLoading={isLoading} />
          </div>
        </div>
      </div>
    </MainLayout>
  );
};
```

## 4. Database Schema Changes

### 4.1 Task Table Updates

```sql
-- Add type column to tasks table
ALTER TABLE tasks ADD COLUMN type TEXT DEFAULT 'external';

-- Add assigned_role column to tasks table
ALTER TABLE tasks ADD COLUMN assigned_role TEXT;

-- Update existing tasks
UPDATE tasks SET type = 'internal' WHERE visibility = 'internal';
UPDATE tasks SET type = 'external' WHERE visibility = 'public';

-- Create index on type column
CREATE INDEX idx_tasks_type ON tasks(type);
```

### 4.2 Profile Table Standardization

```sql
-- Ensure role column exists
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS role TEXT;

-- Create index on role column
CREATE INDEX idx_profiles_role ON profiles(role);
```

## 5. Migration Scripts

### 5.1 Task Type Migration

```typescript
const migrateTaskTypes = async () => {
  console.log('Starting task type migration...');

  // Update internal tasks
  const { data: internalTasks, error: internalError } = await supabase
    .from('tasks')
    .update({ type: 'internal' })
    .eq('visibility', 'internal')
    .select();

  if (internalError) {
    console.error('Error updating internal tasks:', internalError);
    return;
  }

  console.log(`Updated ${internalTasks.length} internal tasks`);

  // Update external tasks
  const { data: externalTasks, error: externalError } = await supabase
    .from('tasks')
    .update({ type: 'external' })
    .eq('visibility', 'public')
    .select();

  if (externalError) {
    console.error('Error updating external tasks:', externalError);
    return;
  }

  console.log(`Updated ${externalTasks.length} external tasks`);

  console.log('Task type migration completed successfully');
};
```

### 5.2 Assigned Role Migration

```typescript
const migrateAssignedRoles = async () => {
  console.log('Starting assigned role migration...');

  // Get all internal tasks with assigned_to
  const { data: tasks, error } = await supabase
    .from('tasks')
    .select('id, assigned_to')
    .eq('type', 'internal')
    .not('assigned_to', 'is', null);

  if (error) {
    console.error('Error fetching tasks:', error);
    return;
  }

  console.log(`Found ${tasks.length} internal tasks with assignments`);

  // Update each task with the assigned user's role
  for (const task of tasks) {
    if (!task.assigned_to) continue;

    // Get the assigned user's role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', task.assigned_to)
      .single();

    if (profileError) {
      console.error(`Error fetching profile for user ${task.assigned_to}:`, profileError);
      continue;
    }

    if (!profile?.role) {
      console.warn(`No role found for user ${task.assigned_to}`);
      continue;
    }

    // Update the task with the assigned role
    const { error: updateError } = await supabase
      .from('tasks')
      .update({ assigned_role: profile.role })
      .eq('id', task.id);

    if (updateError) {
      console.error(`Error updating task ${task.id}:`, updateError);
    }
  }

  console.log('Assigned role migration completed');
};
```

## 6. Working Module Adaptation

### 6.1 Module Categorization

We've categorized existing modules based on the level of modification needed:

#### Category 1: Minimal Change
Modules that can work with simple adapters or minimal modifications:

1. **Sign-up Flow**
   - **Core Functionality to Preserve**: User registration process, organization creation
   - **Modification Needed**: Update role selection to align with new role system
   - **Integration Approach**: Simple adapter for role mapping

#### Category 2: Targeted Modification
Modules requiring specific, limited changes:

2. **Task Creation Module**
   - **Core Functionality to Preserve**: Form UI, validation logic, category selection
   - **Modification Needed**: Add task type determination, update submission handler
   - **Integration Approach**: Extend submission handler while preserving UI

3. **Dashboard Components**
   - **Core Functionality to Preserve**: Task overview cards, status summaries, activity feeds
   - **Modification Needed**: Update data fetching and processing for new task types
   - **Integration Approach**: Modify data layer while preserving visualization components

4. **Compliance Dashboard**
   - **Core Functionality to Preserve**: Compliance tracking, recurring task management
   - **Modification Needed**: Update data layer to work with new task structure
   - **Integration Approach**: Adapt data processing while preserving tracking logic

#### Category 3: Significant Adaptation
Modules needing more substantial changes:

5. **Chat Module**
   - **Core Functionality to Preserve**: Chat UI, message threading, user interaction
   - **Modification Needed**: Update system message creation, thread visibility logic
   - **Integration Approach**: Component extension with overridden methods

6. **Task Assignment Interface**
   - **Core Functionality to Preserve**: Assignment UI patterns, confirmation flow
   - **Modification Needed**: Update role filtering, assignment logic for new role system
   - **Integration Approach**: Targeted refactoring with preserved UI components

### 6.2 Integration Strategies

#### Adapter Pattern (Category 1)

```typescript
// Example: Role adapter for sign-up flow
const RoleSelectionAdapter = ({ selectedRole, onRoleChange }) => {
  // Map legacy roles to new role system
  const adaptedRole = LEGACY_TO_NEW_ROLE_MAP[selectedRole] || selectedRole;

  // Handle role changes from existing component
  const handleRoleChange = (newRole) => {
    // Map new role system back to legacy format if needed
    const legacyRole = NEW_TO_LEGACY_ROLE_MAP[newRole] || newRole;
    onRoleChange(legacyRole);
  };

  return (
    <ExistingRoleSelector
      selectedRole={adaptedRole}
      onRoleChange={handleRoleChange}
    />
  );
};
```

#### Targeted Modification (Category 2)

```typescript
// Example: Enhancing task creation module
export function enhanceTaskCreationModule() {
  // Store reference to original submit function
  const originalSubmit = TaskCreationForm.prototype.submitTask;

  // Replace with enhanced version
  TaskCreationForm.prototype.submitTask = async function(formData) {
    // Determine task type based on visibility
    const taskType = formData.visibility === 'internal' ? 'internal' : 'external';

    // Add assigned_role if it's an internal task with assignment
    let assigned_role = null;
    if (taskType === 'internal' && formData.assigned_to) {
      // Fetch role from profiles table
      const { data } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', formData.assigned_to)
        .single();

      assigned_role = data?.role || null;
    }

    // Create enhanced task data
    const enhancedData = {
      ...formData,
      type: taskType,
      assigned_role
    };

    // Call original submit with enhanced data
    return originalSubmit.call(this, enhancedData);
  };
}
```

#### Component Extension (Category 3)

```typescript
// Example: Extending chat module
class EnhancedTaskChat extends React.Component {
  constructor(props) {
    super(props);
    // Initialize state for enhanced features
    this.state = {
      systemMessages: [],
      // Other state
    };
  }

  componentDidMount() {
    // Load system messages with task type awareness
    this.loadSystemMessages();
  }

  loadSystemMessages = async () => {
    const { task } = this.props;

    if (!task) return;

    try {
      // Fetch messages from database
      const { data, error } = await supabase
        .from('task_messages')
        .select('*')
        .eq('task_id', task.id)
        .eq('sender_id', SYSTEM_USER_ID)
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Process messages with task type awareness
      const processedMessages = data.map(message => ({
        ...message,
        // Fix incorrect system messages based on task type
        content: this.correctSystemMessage(message.content, task)
      }));

      this.setState({ systemMessages: processedMessages });
    } catch (error) {
      console.error('Error loading system messages:', error);
    }
  };

  correctSystemMessage = (content, task) => {
    // Correct system messages based on task type
    if (task.type === 'internal') {
      // Fix supplier references for internal tasks
      return content
        .replace('supplier', 'staff member')
        .replace('Supplier', 'Staff member');
    } else {
      // Fix staff references for external tasks
      return content
        .replace('staff member', 'supplier')
        .replace('Staff member', 'Supplier');
    }
  };

  createSystemMessage = (status, task) => {
    // Create appropriate system message based on task type
    if (task.type === 'internal') {
      switch(status) {
        case 'assigned':
          return `Task has been assigned to ${this.getStaffRoleText(task.assigned_role)}.`;
        case 'in_progress':
          return `${this.getStaffRoleText(task.assigned_role)} has started work on this task.`;
        // Other statuses
      }
    } else {
      // External task messages
      switch(status) {
        case 'assigned':
          return 'Task has been assigned to a supplier.';
        // Other statuses
      }
    }
  };

  getStaffRoleText(role) {
    // Convert role to user-friendly text
    return getDisplayNameForRole(role).toLowerCase();
  }

  render() {
    // Render original chat with enhanced props
    return (
      <OriginalTaskChat
        {...this.props}
        systemMessages={this.state.systemMessages}
        createSystemMessage={this.createSystemMessage}
      />
    );
  }
}
```

### 6.3 Testing Adapted Modules

Each adaptation approach requires specific testing strategies:

```typescript
// Testing Category 1 (Adapter)
test('Sign-up flow preserves functionality with role adapter', async () => {
  // Render sign-up with adapter
  render(<SignUpFlowWithAdapter />);

  // Complete sign-up process
  fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } });
  fireEvent.change(screen.getByLabelText('Password'), { target: { value: 'password123' } });
  fireEvent.click(screen.getByLabelText('School Admin'));
  // Fill other fields

  fireEvent.click(screen.getByText('Sign Up'));

  // Verify correct role is saved in new format
  await waitFor(() => {
    expect(mockCreateProfile).toHaveBeenCalledWith(
      expect.objectContaining({ role: 'admin' })
    );
  });
});

// Testing Category 2 (Targeted Modification)
test('Task creation module handles new task type field', async () => {
  // Apply enhancement
  enhanceTaskCreationModule();

  // Render task creation form
  render(<TaskCreationForm />);

  // Complete form
  fireEvent.change(screen.getByLabelText('Title'), { target: { value: 'Test Task' } });
  fireEvent.click(screen.getByLabelText('Internal'));
  // Fill other fields

  fireEvent.click(screen.getByText('Create Task'));

  // Verify submission includes new fields
  await waitFor(() => {
    expect(mockSubmitTask).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Test Task',
        visibility: 'internal',
        type: 'internal'
      })
    );
  });
});

// Testing Category 3 (Component Extension)
test('Enhanced chat module shows correct system messages', async () => {
  // Create mock task
  const task = {
    id: '123',
    type: 'internal',
    assigned_role: 'maintenance',
    // Other properties
  };

  // Mock system messages
  mockSupabase.from.mockImplementation((table) => {
    if (table === 'task_messages') {
      return {
        select: () => ({
          eq: () => ({
            eq: () => ({
              order: () => ({
                data: [
                  { id: '1', content: 'Task has been assigned to a supplier.', created_at: '2023-01-01' }
                ],
                error: null
              })
            })
          })
        })
      };
    }
  });

  // Render enhanced chat
  render(<EnhancedTaskChat task={task} />);

  // Verify system message is corrected
  await waitFor(() => {
    expect(screen.getByText('Task has been assigned to maintenance staff.')).toBeInTheDocument();
  });
});
```

## 7. Next Steps

1. Implement the task type system and factory
2. Update the auth context with enhanced role support
3. Refactor the Task page to use the "always mount" pattern
4. Deploy database schema changes
5. Run migration scripts
6. Implement the TaskActions component with conditional rendering
7. Implement the TaskTimeline component with type-aware configuration
8. Update system messages to be role-aware
9. Create adapters for existing modules to work with new task types

## 8. Testing Strategy

1. Unit tests for task type determination and role checking
2. Component tests for "always mount" pattern
3. Integration tests for complete task workflows
4. Migration tests for data integrity
