# ClassTasker User Guidelines for Providing Context

This document outlines how to effectively provide context when requesting assistance with the ClassTasker application. Following these guidelines will help ensure that your requests are understood correctly and that you receive the most helpful responses.

## Project Overview Context

When introducing a new feature or requesting changes, consider providing:

1. **Feature Purpose**: Explain why the feature is needed and how it fits into the overall application
2. **User Roles Involved**: Specify which user roles (admin, maintenance, supplier, etc.) will interact with the feature
3. **Workflow Integration**: Describe how the feature integrates with existing workflows
4. **Priority Level**: Indicate the importance of the feature (critical, high, medium, low)

Example:
```
I need help implementing a notification system for maintenance staff when they're assigned a new internal task. This is a high-priority feature as maintenance staff currently have no way of knowing when new tasks are assigned to them without logging in and checking manually.
```

## Technical Context

When discussing technical implementation, consider providing:

1. **Architectural Considerations**: Mention any architectural patterns or constraints to follow
2. **Existing Components**: Identify any existing components that should be leveraged or modified
3. **Data Requirements**: Specify what data is needed and where it should come from
4. **Performance Expectations**: Indicate any performance requirements or concerns

Example:
```
The notification system should follow our new task type architecture, distinguishing between internal and external tasks. It should leverage our existing TaskChat component for the UI but will need a new notification service. We need to store notification status in the database and ensure notifications load quickly on login.
```

## Module Adaptation Context

When requesting changes to existing modules, consider providing:

1. **Module Category**: Specify which category the module falls into:
   - Category 1 (Minimal Change): e.g., Sign-up flow
   - Category 2 (Targeted Modification): e.g., Task creation, Dashboards
   - Category 3 (Significant Adaptation): e.g., Chat module, Task assignment
2. **Core Functionality**: Identify the core functionality that must be preserved
3. **Modification Scope**: Clarify what aspects can be modified and what should remain unchanged
4. **Integration Approach**: Suggest the preferred integration approach (adapter, targeted modification, component extension)

Example:
```
We need to update the Dashboard components (Category 2) to support our new task type system. The core visualization and interaction patterns should be preserved, but the data fetching and filtering logic needs to be updated. A targeted modification approach is preferred, updating the data layer while preserving the UI components.
```

## User Experience Context

When discussing user experience, consider providing:

1. **User Personas**: Describe the specific users who will interact with the feature
2. **User Journey**: Outline the steps users will take when using the feature
3. **Pain Points**: Identify current pain points that the feature should address
4. **Success Criteria**: Define what a successful implementation looks like from the user's perspective

Example:
```
The notification system is primarily for maintenance staff who are often on-site and check their tasks on mobile devices. They need to quickly see new assignments and their priority level. Success means maintenance staff acknowledge new tasks within 30 minutes of assignment instead of the current average of 4 hours.
```

## Implementation Constraints

When setting implementation boundaries, consider providing:

1. **Timeline Constraints**: Mention any deadline considerations
2. **Resource Limitations**: Identify any resource constraints (e.g., third-party services, API limits)
3. **Compatibility Requirements**: Specify any backward compatibility needs
4. **Testing Requirements**: Outline testing expectations and criteria

Example:
```
The notification system needs to be implemented within the next two weeks. It should work with our existing Supabase setup without requiring additional services. It must be backward compatible with our mobile app version 2.1 and later. Comprehensive testing should include both web and mobile interfaces.
```

## Problem-Solving Context

When seeking help with issues, consider providing:

1. **Problem Description**: Clearly describe the issue, including when and how it occurs
2. **Expected Behavior**: Explain what should happen when everything works correctly
3. **Actual Behavior**: Describe what is actually happening
4. **Troubleshooting Steps**: List steps already taken to diagnose or fix the issue
5. **Error Messages**: Include any error messages or logs
6. **Environment Details**: Specify the environment where the issue occurs (development, production)

Example:
```
Internal tasks are not showing up in the maintenance staff dashboard even though they're correctly assigned in the database. The expected behavior is that all tasks assigned to the logged-in maintenance staff appear in their dashboard. I've verified that the tasks exist in the database with the correct assigned_to value matching the user's ID. The console shows no errors, and this happens in both development and production environments.
```

## Code Review Context

When requesting code review, consider providing:

1. **Code Purpose**: Explain what the code is intended to do
2. **Implementation Approach**: Describe the approach taken and why
3. **Areas of Concern**: Highlight specific areas where feedback is most needed
4. **Testing Status**: Indicate what testing has been done
5. **Alternative Approaches**: Mention any alternative approaches considered

Example:
```
I've implemented the task type system using TypeScript interfaces and a factory pattern. The approach uses type guards to ensure type safety throughout the application. I'm particularly concerned about the backward compatibility with existing components. Unit tests are passing, but I haven't tested integration with all components yet. I considered using enums instead of string literals for task types but chose string literals for database compatibility.
```

## Documentation Context

When discussing documentation, consider providing:

1. **Documentation Purpose**: Clarify the intended audience and purpose
2. **Content Requirements**: Specify what information should be included
3. **Format Preferences**: Indicate preferred documentation format
4. **Integration with Existing Docs**: Explain how it should integrate with existing documentation

Example:
```
We need developer documentation for the new task type system targeting our development team. It should include type definitions, factory pattern usage, and integration examples for each module category. Markdown format is preferred, and it should be organized as part of our existing technical documentation structure.
```

## Providing Code Snippets

When sharing code, consider:

1. **Complete Context**: Include enough code to understand the context
2. **File Location**: Specify which file the code is from
3. **Relevant Lines**: Highlight the most relevant lines
4. **Current vs. Desired**: Clearly distinguish between current code and desired changes

Example:
```
In src/components/tasks/TaskActions.tsx:

// Current implementation (problematic):
{isInternalTask && isMaintenance && isAssignedStaff && (
  <InternalTaskActions task={task} />
)}

// Desired implementation (always mount pattern):
<InternalTaskActions 
  task={task} 
  isLoading={isLoading}
  isVisible={isInternalTask && isMaintenance && isAssignedStaff} 
/>
```

## Feedback on Proposed Solutions

When providing feedback on proposed solutions, consider:

1. **Alignment with Requirements**: Indicate how well the solution meets requirements
2. **Technical Concerns**: Highlight any technical issues or concerns
3. **Alternative Suggestions**: Offer alternative approaches if applicable
4. **Implementation Preferences**: Express preferences for implementation details
5. **Prioritization**: Suggest which aspects should be prioritized

Example:
```
The proposed adapter pattern for the dashboard components looks good and aligns with our Category 2 approach. However, I'm concerned about the performance impact of converting data formats in both directions. Could we consider updating the dashboard components to natively support the new task types instead? If we go with the adapter approach, we should prioritize optimizing the data conversion to minimize performance impact.
```

By following these guidelines when providing context, you'll help ensure that your requests are understood correctly and that you receive the most helpful and relevant assistance for the ClassTasker application.
