<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <title>ClassTasker Connect</title>
    <meta name="description" content="School maintenance and task management platform" />
    <!-- Force deployment update - Cache bust v2025-01-25 -->
    <meta name="author" content="ClassTasker" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/icons/placeholder.svg" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="ClassTasker Connect" />
    <meta property="og:description" content="School maintenance and task management platform" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://classtasker.com/opengraph-image.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="ClassTasker" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="apple-touch-icon" href="/icons/icon-192x192.svg" />
    <link rel="icon" href="/icons/icon-192x192.svg" type="image/svg+xml" />

    <!-- Environment variables are now handled by Vite's built-in system -->

  </head>

  <body>
    <div id="root"></div>

    <script type="module" src="/src/main.tsx"></script>

    <!-- PWA Installation and Service Worker Registration -->
    <script>
      // Initialize global variable to store the installation prompt
      window.deferredPrompt = null;

      // Listen for beforeinstallprompt event
      window.addEventListener('beforeinstallprompt', (e) => {
        // Prevent Chrome 76+ from automatically showing the prompt
        e.preventDefault();
        // Store the event so it can be triggered later
        window.deferredPrompt = e;
        console.log('beforeinstallprompt event fired and stored globally');
      });

      // Listen for appinstalled event
      window.addEventListener('appinstalled', (e) => {
        // Clear the deferredPrompt variable
        window.deferredPrompt = null;
        console.log('PWA was installed');
      });

      // Register service worker
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/service-worker.js')
            .then(registration => {
              console.log('ServiceWorker registration successful with scope: ', registration.scope);
            })
            .catch(error => {
              console.log('ServiceWorker registration failed: ', error);
            });
        });
      }

      // Global function to force PWA install (for debugging)
      window.forcePWAInstall = function() {
        if (window.deferredPrompt) {
          console.log('Forcing PWA install prompt...');
          window.deferredPrompt.prompt();
          window.deferredPrompt.userChoice.then((choiceResult) => {
            console.log('User choice:', choiceResult.outcome);
            window.deferredPrompt = null;
          });
        } else {
          console.log('No install prompt available. Reasons could be:');
          console.log('1. App is already installed');
          console.log('2. Browser doesn\'t support PWA install');
          console.log('3. Site doesn\'t meet PWA criteria');
          console.log('4. Need more user engagement');
          console.log('5. Service worker not properly registered');

          // Show debug info
          console.log('Debug info:', {
            hasServiceWorker: 'serviceWorker' in navigator,
            hasBeforeInstallPrompt: 'BeforeInstallPromptEvent' in window,
            isStandalone: window.matchMedia('(display-mode: standalone)').matches,
            userAgent: navigator.userAgent,
            isHTTPS: location.protocol === 'https:',
            currentURL: location.href
          });
        }
      };

      // Global function to check PWA status
      window.checkPWAStatus = function() {
        console.log('PWA Status Check:', {
          hasServiceWorker: 'serviceWorker' in navigator,
          hasInstallPrompt: !!window.deferredPrompt,
          isInstalled: window.matchMedia('(display-mode: standalone)').matches,
          isHTTPS: location.protocol === 'https:',
          userAgent: navigator.userAgent,
          currentURL: location.href
        });

        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.getRegistrations().then(registrations => {
            console.log('Service Worker Registrations:', registrations);
          });
        }
      };

      // Log available debug functions
      console.log('PWA Debug Functions Available:');
      console.log('- forcePWAInstall() - Force the install prompt');
      console.log('- checkPWAStatus() - Check current PWA status');
    </script>
  </body>
</html>
