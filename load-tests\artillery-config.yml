# Artillery Load Testing Configuration
config:
  target: 'https://classtasker.com'
  phases:
    # Warm-up phase
    - duration: 60
      arrivalRate: 5
      name: "Warm-up"
    
    # Ramp-up phase
    - duration: 120
      arrivalRate: 5
      rampTo: 50
      name: "Ramp-up"
    
    # Sustained load
    - duration: 300
      arrivalRate: 50
      name: "Sustained load"
    
    # Peak load
    - duration: 120
      arrivalRate: 50
      rampTo: 100
      name: "Peak load"
    
    # Cool-down
    - duration: 60
      arrivalRate: 100
      rampTo: 5
      name: "Cool-down"

  defaults:
    headers:
      User-Agent: "Artillery Load Test"
      Accept: "application/json"

  # Performance thresholds
  ensure:
    p95: 2000  # 95% of requests should complete within 2 seconds
    p99: 5000  # 99% of requests should complete within 5 seconds
    maxErrorRate: 5  # Error rate should not exceed 5%

scenarios:
  # Test homepage loading
  - name: "Homepage Load Test"
    weight: 30
    flow:
      - get:
          url: "/"
          capture:
            - json: "$.title"
              as: "pageTitle"
      - think: 2

  # Test login flow
  - name: "Login Flow Test"
    weight: 20
    flow:
      - get:
          url: "/login"
      - think: 3
      - post:
          url: "/auth/v1/token?grant_type=password"
          headers:
            apikey: "{{ $processEnvironment.VITE_SUPABASE_ANON_KEY }}"
            Authorization: "Bearer {{ $processEnvironment.VITE_SUPABASE_ANON_KEY }}"
            Content-Type: "application/json"
          json:
            email: "<EMAIL>"
            password: "testpassword123"
          capture:
            - json: "$.access_token"
              as: "authToken"
      - think: 2

  # Test dashboard access
  - name: "Dashboard Access Test"
    weight: 25
    flow:
      - get:
          url: "/dashboard"
          headers:
            Authorization: "Bearer {{ authToken }}"
      - think: 5

  # Test task creation
  - name: "Task Creation Test"
    weight: 15
    flow:
      - get:
          url: "/post-task"
      - think: 10
      - post:
          url: "/rest/v1/tasks"
          headers:
            apikey: "{{ $processEnvironment.VITE_SUPABASE_ANON_KEY }}"
            Authorization: "Bearer {{ authToken }}"
            Content-Type: "application/json"
            Prefer: "return=representation"
          json:
            title: "Load Test Task {{ $randomString() }}"
            description: "This is a load test task created at {{ $timestamp() }}"
            task_type: "internal"
            priority: "medium"
            budget: 100
      - think: 3

  # Test API endpoints
  - name: "API Stress Test"
    weight: 10
    flow:
      - get:
          url: "/api/getstream/token"
          json:
            userId: "loadtest-user-{{ $randomInt(1, 1000) }}"
      - think: 1
      - get:
          url: "/rest/v1/tasks?select=id,title,created_at&limit=20"
          headers:
            apikey: "{{ $processEnvironment.VITE_SUPABASE_ANON_KEY }}"
            Authorization: "Bearer {{ authToken }}"
      - think: 2

# Custom functions for load testing
functions:
  generateRandomUser:
    - set:
        email: "loadtest{{ $randomInt(1, 10000) }}@example.com"
        password: "testpass{{ $randomString() }}"

# Metrics and monitoring
metrics:
  - name: "response_time_p95"
    unit: "ms"
  - name: "response_time_p99" 
    unit: "ms"
  - name: "error_rate"
    unit: "percent"
  - name: "requests_per_second"
    unit: "rps"
