// SECURITY: This file provides fallback environment variables for development
// In production, Vite automatically injects environment variables at build time
// NEVER hardcode actual API keys in this file!

window.env = window.env || {
  // Fallback values for development - these should be overridden by Vite
  VITE_GETSTREAM_API_KEY: "",
  VITE_SUPABASE_URL: "https://qcnotlojmyvpqbbgoxbc.supabase.co",
  VITE_SUPABASE_ANON_KEY: "",
  VITE_STRIPE_PUBLIC_KEY: "",
  VITE_GOOGLE_MAPS_API_KEY: ""
};

// Development warning for missing environment variables
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  const missingKeys = Object.entries(window.env)
    .filter(([key, value]) => key !== 'VITE_SUPABASE_URL' && !value)
    .map(([key]) => key);

  if (missingKeys.length > 0) {
    console.warn('[SECURITY] Missing environment variables:', missingKeys);
    console.warn('[SECURITY] Make sure to set these in your .env.local file');
  }
}