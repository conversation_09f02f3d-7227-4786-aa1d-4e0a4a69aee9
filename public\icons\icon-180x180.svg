<?xml version="1.0" encoding="UTF-8"?>
<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Modern gradient background -->
  <defs>
    <linearGradient id="bgGradient180" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient180" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F1F5F9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background with modern rounded corners -->
  <rect width="180" height="180" rx="40" fill="url(#bgGradient180)"/>
  
  <!-- School building silhouette -->
  <path d="M45 131H135V146H45V131Z" fill="url(#iconGradient180)" opacity="0.9"/>
  <path d="M52 112H128V131H52V112Z" fill="url(#iconGradient180)"/>
  <path d="M60 94H120V112H60V94Z" fill="url(#iconGradient180)" opacity="0.8"/>
  
  <!-- Central tower/main building -->
  <path d="M75 75H105V131H75V75Z" fill="url(#iconGradient180)"/>
  <path d="M79 56H101L90 45L79 56Z" fill="url(#iconGradient180)"/>
  
  <!-- Task checkmarks overlay -->
  <g transform="translate(112, 37)">
    <!-- Checklist background -->
    <rect x="0" y="0" width="26" height="34" rx="4" fill="#FFFFFF" opacity="0.95"/>
    <!-- Check marks -->
    <path d="M5 7L7 9L13 3" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M5 15L7 17L13 11" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M5 23L7 25L13 19" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  
  <!-- Windows on building -->
  <rect x="83" y="83" width="5" height="7" rx="1" fill="#3B82F6" opacity="0.6"/>
  <rect x="92" y="83" width="5" height="7" rx="1" fill="#3B82F6" opacity="0.6"/>
  <rect x="83" y="98" width="5" height="7" rx="1" fill="#3B82F6" opacity="0.6"/>
  <rect x="92" y="98" width="5" height="7" rx="1" fill="#3B82F6" opacity="0.6"/>
  
  <!-- Door -->
  <rect x="86" y="116" width="8" height="15" rx="1" fill="#3B82F6" opacity="0.7"/>
  
  <!-- Subtle shadow/depth -->
  <ellipse cx="90" cy="154" rx="45" ry="7" fill="#000000" opacity="0.1"/>
</svg>
