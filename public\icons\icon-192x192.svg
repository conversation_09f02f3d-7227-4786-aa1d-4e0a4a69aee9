<?xml version="1.0" encoding="UTF-8"?>
<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Modern gradient background -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F1F5F9;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background with modern rounded corners -->
  <rect width="192" height="192" rx="42" fill="url(#bgGradient)"/>

  <!-- School building silhouette -->
  <path d="M48 140H144V156H48V140Z" fill="url(#iconGradient)" opacity="0.9"/>
  <path d="M56 120H136V140H56V120Z" fill="url(#iconGradient)"/>
  <path d="M64 100H128V120H64V100Z" fill="url(#iconGradient)" opacity="0.8"/>

  <!-- Central tower/main building -->
  <path d="M80 80H112V140H80V80Z" fill="url(#iconGradient)"/>
  <path d="M84 60H108L96 48L84 60Z" fill="url(#iconGradient)"/>

  <!-- Task checkmarks overlay -->
  <g transform="translate(120, 40)">
    <!-- Checklist background -->
    <rect x="0" y="0" width="28" height="36" rx="4" fill="#FFFFFF" opacity="0.95"/>
    <!-- Check marks -->
    <path d="M6 8L8 10L14 4" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M6 16L8 18L14 12" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M6 24L8 26L14 20" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </g>

  <!-- Windows on building -->
  <rect x="88" y="88" width="6" height="8" rx="1" fill="#3B82F6" opacity="0.6"/>
  <rect x="98" y="88" width="6" height="8" rx="1" fill="#3B82F6" opacity="0.6"/>
  <rect x="88" y="104" width="6" height="8" rx="1" fill="#3B82F6" opacity="0.6"/>
  <rect x="98" y="104" width="6" height="8" rx="1" fill="#3B82F6" opacity="0.6"/>

  <!-- Door -->
  <rect x="92" y="124" width="8" height="16" rx="1" fill="#3B82F6" opacity="0.7"/>

  <!-- Subtle shadow/depth -->
  <ellipse cx="96" cy="164" rx="48" ry="8" fill="#000000" opacity="0.1"/>
</svg>
