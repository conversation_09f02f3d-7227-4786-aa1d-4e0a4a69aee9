<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Modern gradient background -->
  <defs>
    <linearGradient id="bgGradient512" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient512" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F1F5F9;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background with modern rounded corners -->
  <rect width="512" height="512" rx="112" fill="url(#bgGradient512)"/>

  <!-- School building silhouette (scaled up) -->
  <path d="M128 374H384V416H128V374Z" fill="url(#iconGradient512)" opacity="0.9"/>
  <path d="M149 320H363V374H149V320Z" fill="url(#iconGradient512)"/>
  <path d="M171 266H341V320H171V266Z" fill="url(#iconGradient512)" opacity="0.8"/>

  <!-- Central tower/main building -->
  <path d="M213 213H299V374H213V213Z" fill="url(#iconGradient512)"/>
  <path d="M224 160H288L256 128L224 160Z" fill="url(#iconGradient512)"/>

  <!-- Task checkmarks overlay (scaled up) -->
  <g transform="translate(320, 106)">
    <!-- Checklist background -->
    <rect x="0" y="0" width="75" height="96" rx="10" fill="#FFFFFF" opacity="0.95"/>
    <!-- Check marks -->
    <path d="M16 21L21 26L37 10" stroke="#10B981" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M16 43L21 48L37 32" stroke="#10B981" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M16 64L21 69L37 53" stroke="#10B981" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
    <!-- Task lines -->
    <rect x="45" y="18" width="20" height="3" rx="1" fill="#6B7280" opacity="0.6"/>
    <rect x="45" y="40" width="20" height="3" rx="1" fill="#6B7280" opacity="0.6"/>
    <rect x="45" y="61" width="20" height="3" rx="1" fill="#6B7280" opacity="0.6"/>
  </g>

  <!-- Windows on building (scaled up) -->
  <rect x="235" y="235" width="16" height="21" rx="2" fill="#3B82F6" opacity="0.6"/>
  <rect x="261" y="235" width="16" height="21" rx="2" fill="#3B82F6" opacity="0.6"/>
  <rect x="235" y="277" width="16" height="21" rx="2" fill="#3B82F6" opacity="0.6"/>
  <rect x="261" y="277" width="16" height="21" rx="2" fill="#3B82F6" opacity="0.6"/>
  <rect x="235" y="320" width="16" height="21" rx="2" fill="#3B82F6" opacity="0.6"/>
  <rect x="261" y="320" width="16" height="21" rx="2" fill="#3B82F6" opacity="0.6"/>

  <!-- Door -->
  <rect x="245" y="331" width="21" height="43" rx="2" fill="#3B82F6" opacity="0.7"/>

  <!-- Subtle shadow/depth -->
  <ellipse cx="256" cy="437" rx="128" ry="21" fill="#000000" opacity="0.1"/>

  <!-- Additional detail: Flag on top -->
  <rect x="254" y="128" width="4" height="32" fill="url(#iconGradient512)" opacity="0.8"/>
  <path d="M258 128H278L268 138H258V128Z" fill="#EF4444" opacity="0.9"/>
</svg>
