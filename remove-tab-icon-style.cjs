// Script to remove the TabIcon style from EnhancedPaymentProcessor.tsx
const fs = require('fs');
const path = require('path');

// Paths
const processorPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx');
const backupPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx.bak8');

// Create a backup of the original file
console.log('Creating backup of EnhancedPaymentProcessor.tsx...');
fs.copyFileSync(processorPath, backupPath);
console.log(`Backup created at ${backupPath}`);

// Read the file content
console.log('Reading EnhancedPaymentProcessor.tsx...');
let content = fs.readFileSync(processorPath, 'utf8');

// Remove the TabIcon style
if (content.includes("'.TabIcon': {")) {
  console.log('Removing TabIcon style...');
  
  // Remove the TabIcon style block
  content = content.replace(/,\s*['"]\.TabIcon['"]:\s*{[^}]*}/g, '');
  
  // Write the modified content back to the file
  fs.writeFileSync(processorPath, content, 'utf8');
  console.log('Fix applied successfully!');
  
  console.log('\nChanges made:');
  console.log('- Removed TabIcon style completely');
  
  console.log('\nPlease refresh the page and try the payment again.');
} else {
  console.log('Could not find TabIcon style in the file.');
  console.log('Please apply the fix manually:');
  console.log('1. Open src/components/stripe/EnhancedPaymentProcessor.tsx');
  console.log('2. Remove the TabIcon style block');
}

console.log('\nIf you need to restore the original file, run:');
console.log(`cp ${backupPath} ${processorPath}`);