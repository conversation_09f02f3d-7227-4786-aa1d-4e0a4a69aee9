// Script to replace the original EnhancedPaymentProcessor.tsx with the fixed version
const fs = require('fs');
const path = require('path');

// Paths
const originalPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx');
const fixedPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx.fixed');
const backupPath = path.join(__dirname, 'src', 'components', 'stripe', 'EnhancedPaymentProcessor.tsx.bak5');

// Check if the fixed file exists
if (!fs.existsSync(fixedPath)) {
  console.error(`Error: Fixed file not found at ${fixedPath}`);
  process.exit(1);
}

// Create a backup of the original file
console.log('Creating backup of EnhancedPaymentProcessor.tsx...');
fs.copyFileSync(originalPath, backupPath);
console.log(`Backup created at ${backupPath}`);

// Replace the original file with the fixed version
console.log('Replacing original file with fixed version...');
fs.copyFileSync(fixedPath, originalPath);
console.log('File replaced successfully!');

console.log('\nPlease refresh the page and try the payment again.');
console.log('\nIf you need to restore the original file, run:');
console.log(`cp ${backupPath} ${originalPath}`);