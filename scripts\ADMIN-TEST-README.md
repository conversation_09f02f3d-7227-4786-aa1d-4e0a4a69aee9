# ClassTasker Admin Test

This is an end-to-end test script for ClassTasker that <NAME_EMAIL> account to test the complete task flow, including system messages and image uploads.

## Features Tested

1. **Complete Task Flow**
   - Creating a new task
   - Submitting offers as a supplier
   - Negotiating and rejecting offers
   - Accepting an offer
   - Marking a task as in progress
   - Marking a task as completed
   - Approving a completed task
   - Processing payment

2. **Chat Enhancements**
   - Automatic system messages for timeline actions
   - Image upload functionality
   - Message display for different user types

3. **User Authentication**
   - Admin login and permissions
   - Supplier login and permissions
   - Proper role-based access control

## Setup

1. Rename `admin-test-package.json` to `package.json`:
   ```
   mv admin-test-package.json package.json
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Install Playwright browsers:
   ```
   npm run install-playwright
   ```

4. Update the configuration in `admin-test.js`:
   - Set the correct passwords for admin and supplier accounts
   - Adjust the base URL if needed (default is http://localhost:8082)

## Running the Test

1. Make sure your ClassTasker application is running at the configured URL

2. Run the test:
   ```
   npm test
   ```

3. The test will:
   - Open a browser window and run through all the steps
   - Record a video of the test in the `videos` directory
   - Output progress to the console

## Test Flow

The test script performs the following actions:

1. Logs in as admin
2. Creates a new task
3. Verifies system message for task creation
4. Logs in as supplier
5. Submits an offer
6. Uploads an image to the chat
7. Logs in as admin
8. Accepts the offer
9. Marks the task as in progress
10. Logs in as supplier
11. Marks the task as completed
12. Logs in as admin
13. Approves the completed task
14. Processes payment
15. Verifies all system messages in the chat

## Troubleshooting

- If the test fails, check the error message in the console
- Review the recorded video to see where the test failed
- Adjust the timing parameters if needed (increase `slowMo` or `delay` values)
- Make sure the admin and supplier accounts have the correct permissions
- Verify that the application is running and accessible at the configured URL

## Notes

- This test uses real user accounts and creates real data in your database
- It's recommended to run this test in a development or testing environment
- The test creates a simple test image file if one doesn't exist
- The browser is visible by default to help with debugging (set `headless: true` to run in headless mode)
