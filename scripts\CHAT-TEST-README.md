# Chat Feature Test Scripts

This directory contains scripts to test the chat functionality between suppliers and admins in ClassTasker.

## Overview

The chat feature allows suppliers to express interest in tasks, discuss requirements with admins, submit offers, and communicate throughout the task lifecycle. These test scripts help diagnose issues with the chat flow.

## Available Test Scripts

1. **run-chat-test.js** - Browser-based test script that can be run in the browser console
2. **chat-test-cli.js** - Node.js CLI version for running tests from the command line
3. **create_chat_threads_table.sql** - SQL script to create or fix the chat_threads table

## Common Issues and Solutions

### 1. Missing chat_threads Table

**Symptoms:**
- Error messages about missing chat_threads table
- Suppliers can't express interest in tasks
- Chat messages don't appear in the UI

**Solution:**
Run the SQL script to create the table:

```sql
-- In Supabase SQL Editor
\i sql/create_chat_threads_table.sql
```

### 2. System Messages Not Appearing

**Symptoms:**
- No system messages about status changes
- Missing notifications about offers

**Possible Causes:**
- Missing system user (00000000-0000-0000-0000-000000000000)
- RLS policies preventing system messages

**Solution:**
Check if the system user exists and ensure RLS policies allow it to create messages.

### 3. Messages Not Associated with Threads

**Symptoms:**
- Messages appear in wrong conversations
- Some messages are missing

**Solution:**
Run the verification function to check message associations:

```javascript
// In browser console
window.verifyMessages();
```

### 4. Thread Status Not Updating

**Symptoms:**
- Thread status remains as 'interest' even after offer submission
- UI doesn't reflect current state

**Solution:**
Check thread update permissions and ensure status transitions are correct.

## Running the Browser Test

1. Open your browser and navigate to ClassTasker
2. Log in as an admin (<EMAIL>)
3. Open the browser console (F12 or Ctrl+Shift+I)
4. Copy and paste the contents of `run-chat-test.js` into the console
5. Run the test by typing: `runChatTest()`

You can also run individual steps:
```javascript
window.createTestTask();
window.findSupplier();
window.simulateExpressInterest();
window.simulateAdminResponse();
window.simulateSupplierOffer();
window.simulateAcceptOffer();
window.verifyMessages();
```

## Running the CLI Test

1. Make sure you have Node.js installed
2. Create a `.env` file with your Supabase credentials:
```
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```
3. Install dependencies:
```bash
npm install @supabase/supabase-js dotenv
```
4. Run the test:
```bash
node scripts/chat-test-cli.js
```

## Fixing the chat_threads Table

If you need to recreate or fix the chat_threads table:

1. Go to the Supabase dashboard
2. Open the SQL Editor
3. Copy and paste the contents of `create_chat_threads_table.sql`
4. Run the SQL script

This will:
- Create the chat_threads table if it doesn't exist
- Add necessary indexes and constraints
- Set up RLS policies
- Add the thread_id column to task_messages if needed
- Update task_messages policies to work with threads

## Expected Chat Flow

1. **Express Interest**: Supplier expresses interest in a task
   - Creates a chat thread
   - Adds initial message
   - Status: 'interest'

2. **Discussion Phase**: Admin and supplier discuss requirements
   - Multiple messages exchanged
   - Status: 'interest'

3. **Offer Submission**: Supplier submits a formal offer
   - Creates an offer record
   - Updates thread status to 'questions' or 'offer_submitted'
   - Status: 'questions'

4. **Acceptance**: Admin accepts the offer
   - Updates offer status to 'accepted'
   - Updates task status to 'in_progress'
   - Updates thread status to 'accepted'
   - Status: 'accepted'

5. **Work Phase**: Ongoing communication during task execution
   - Status: 'accepted'

## Troubleshooting

If you encounter issues:

1. Check the browser console for error messages
2. Verify that all required tables exist
3. Check RLS policies for chat_threads and task_messages
4. Ensure the system user exists for system messages
5. Verify that thread_id is correctly set on all messages

For persistent issues, run the SQL script to recreate the chat_threads table and update policies.
