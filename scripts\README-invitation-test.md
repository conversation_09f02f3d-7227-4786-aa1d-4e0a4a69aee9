# Invitation Email Load Test

This script tests the invitation email system by sending 50 test invitation emails. It helps verify that the Resend API integration works correctly under load and that the invitation flow is robust.

## What the Test Does

1. Generates 50 test email addresses with the domain `test.classtasker.com`
2. Sends invitation emails to all of them using the Resend API via the Edge Function
3. Tracks the success/failure of each invitation
4. Generates a detailed report with timing information

## Setup

1. Copy `.env.example` to `.env` in the scripts directory:
   ```
   cp .env.example .env
   ```

2. Edit the `.env` file and add your Supabase service role key:
   ```
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
   ```

3. (Optional) Set a specific organization ID to use for the test:
   ```
   TEST_ORGANIZATION_ID=your-organization-id-here
   ```
   If not provided, the script will use the first organization found in the database.

4. Install dependencies:
   ```
   npm install
   ```

## Running the Test

Run the test with:

```
npm run test:invitations
```

## Test Results

The test will generate a detailed report in `invitation-test-results.json` with:

- Total number of invitations sent
- Number of successful and failed invitations
- Success rate
- Total time taken
- Average time per invitation
- Detailed information for each invitation (email, role, status, time taken)

## Interpreting Results

- **Success Rate**: Should be close to 100%. If not, check the error messages in the failed invitations.
- **Average Time**: Typically should be under 1000ms per invitation. Higher times may indicate performance issues.
- **Error Patterns**: Look for patterns in any failed invitations to identify potential issues.

## Notes

- The test uses the `test.classtasker.com` domain for all test emails to avoid sending emails to real users.
- Each email address includes a timestamp to ensure uniqueness.
- The test adds a small delay (500ms) between requests to avoid rate limiting.
- The test uses the `support-email-sender` Edge Function to send emails, which is the same function used in production.

## Troubleshooting

- **Rate Limiting**: If you see rate limiting errors, increase the delay between requests.
- **Edge Function Errors**: Check the Supabase logs for any errors in the Edge Function.
- **Missing Environment Variables**: Ensure all required environment variables are set in the `.env` file.
