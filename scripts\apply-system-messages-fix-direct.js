/**
 * <PERSON><PERSON><PERSON> to apply the system messages fix using Supabase Management API directly
 * 
 * This script:
 * 1. Reads the SQL script
 * 2. Applies it to the Supabase database using the Management API
 * 
 * Run with: node apply-system-messages-fix-direct.js
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const supabaseServiceRoleKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceRoleKey) {
  console.error('Error: VITE_SUPABASE_SERVICE_ROLE_KEY is not defined in the environment variables.');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

// Path to the SQL script
const sqlScriptPath = path.join(__dirname, 'fix-system-messages.sql');

async function applySystemMessagesFix() {
  try {
    console.log('Starting system messages fix application...');
    
    // Read the SQL script
    const sqlScript = fs.readFileSync(sqlScriptPath, 'utf8');
    console.log('SQL script loaded successfully.');
    
    // Apply the SQL script using direct database query
    console.log('Applying SQL script to the database...');
    
    // Split the SQL script into individual statements
    const statements = sqlScript
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    console.log(`Found ${statements.length} SQL statements to execute.`);
    
    // Execute each statement separately
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { data, error } = await supabase
          .rpc('pg_query', { query: statement + ';' });
        
        if (error) {
          console.warn(`Warning: Error executing statement ${i + 1}: ${error.message}`);
          console.warn(`Statement: ${statement.substring(0, 100)}...`);
        } else {
          console.log(`Statement ${i + 1} executed successfully.`);
        }
      } catch (error) {
        console.warn(`Warning: Exception executing statement ${i + 1}: ${error.message}`);
      }
      
      // Add a small delay between statements
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('SQL script application completed.');
    
    // Verify the system user was created
    console.log('Verifying system user creation...');
    try {
      const { data, error } = await supabase
        .rpc('pg_query', { 
          query: "SELECT id, email[1] as email, role FROM profiles WHERE id = '00000000-0000-0000-0000-000000000000';" 
        });
      
      if (error) {
        console.warn(`Warning: Could not verify system user: ${error.message}`);
      } else if (data && data.length > 0) {
        console.log(`System user verified: ${JSON.stringify(data[0])}`);
      } else {
        console.warn('Warning: System user not found in profiles table.');
      }
    } catch (error) {
      console.warn(`Warning: Exception verifying system user: ${error.message}`);
    }
    
    // Verify the create_system_message function was created
    console.log('Verifying function creation...');
    try {
      const { data, error } = await supabase
        .rpc('pg_query', { 
          query: "SELECT proname, prosrc FROM pg_proc WHERE proname = 'create_system_message';" 
        });
      
      if (error) {
        console.warn(`Warning: Could not verify function creation: ${error.message}`);
      } else if (data && data.length > 0) {
        console.log(`Function create_system_message verified: Found in pg_proc`);
      } else {
        console.warn('Warning: Function create_system_message not found.');
      }
    } catch (error) {
      console.warn(`Warning: Exception verifying function: ${error.message}`);
    }
    
    console.log('System messages fix application completed!');
    console.log('You should now see system messages in the chat when task status changes.');
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

// Create the pg_query function if it doesn't exist
async function createPgQueryFunction() {
  try {
    console.log('Creating pg_query function...');
    
    const pgQueryFunctionSQL = `
    CREATE OR REPLACE FUNCTION pg_query(query text)
    RETURNS SETOF json
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
      RETURN QUERY EXECUTE query;
    END;
    $$;
    `;
    
    const { error } = await supabase
      .from('_exec_sql')
      .insert({ query: pgQueryFunctionSQL });
    
    if (error) {
      console.warn(`Warning: Could not create pg_query function via _exec_sql: ${error.message}`);
      console.log('Trying alternative method...');
      
      // Try direct query
      const { error: directError } = await supabase
        .rpc('pg_query', { query: pgQueryFunctionSQL });
      
      if (directError && !directError.message.includes('function already exists')) {
        console.warn(`Warning: Could not create pg_query function: ${directError.message}`);
      } else {
        console.log('pg_query function created or already exists.');
      }
    } else {
      console.log('pg_query function created successfully.');
    }
  } catch (error) {
    console.warn(`Warning: Exception creating pg_query function: ${error.message}`);
    console.log('Continuing anyway, the function might already exist...');
  }
}

// Run the script
async function run() {
  await createPgQueryFunction();
  await applySystemMessagesFix();
}

run();
