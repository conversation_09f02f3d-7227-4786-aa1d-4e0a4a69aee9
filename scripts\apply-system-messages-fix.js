/**
 * <PERSON><PERSON><PERSON> to apply the system messages fix using Supabase MCP
 *
 * This script:
 * 1. Reads the SQL script
 * 2. Applies it to the Supabase database using the MCP tool
 * 3. Verifies the changes were applied successfully
 *
 * Run with: node apply-system-messages-fix.js
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const supabaseServiceRoleKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceRoleKey) {
  console.error('Error: VITE_SUPABASE_SERVICE_ROLE_KEY is not defined in the environment variables.');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

// Path to the SQL script
const sqlScriptPath = path.join(__dirname, 'fix-system-messages.sql');

async function applySystemMessagesFix() {
  try {
    console.log('Starting system messages fix application...');

    // Read the SQL script
    const sqlScript = fs.readFileSync(sqlScriptPath, 'utf8');
    console.log('SQL script loaded successfully.');

    // Apply the SQL script using direct database query
    console.log('Applying SQL script to the database...');
    const { data, error } = await supabase
      .from('_exec_sql')
      .insert({ query: sqlScript });

    if (error) {
      throw new Error(`Error applying SQL script: ${error.message}`);
    }

    console.log('SQL script applied successfully.');

    // Verify the system user was created
    console.log('Verifying system user creation...');
    const { data: systemUser, error: systemUserError } = await supabase
      .from('profiles')
      .select('id, email, role')
      .eq('id', '00000000-0000-0000-0000-000000000000')
      .single();

    if (systemUserError) {
      console.warn(`Warning: Could not verify system user: ${systemUserError.message}`);
    } else {
      console.log(`System user verified: ${JSON.stringify(systemUser)}`);
    }

    // Verify the create_task_message function was updated
    console.log('Verifying function updates...');
    const { data: functionData, error: functionError } = await supabase
      .from('_exec_sql')
      .insert({
        query: "SELECT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'create_system_message')"
      });

    if (functionError) {
      console.warn(`Warning: Could not verify function updates: ${functionError.message}`);
    } else {
      console.log(`Function updates verified: ${functionData ? 'Success' : 'Failed'}`);
    }

    console.log('System messages fix applied successfully!');
    console.log('You should now see system messages in the chat when task status changes.');

  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

// Create the check_function_exists function if it doesn't exist
async function createHelperFunctions() {
  try {
    console.log('Creating helper functions...');

    const helperFunctionSQL = `
    CREATE OR REPLACE FUNCTION check_function_exists(function_name TEXT)
    RETURNS BOOLEAN
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    DECLARE
      func_exists BOOLEAN;
    BEGIN
      SELECT EXISTS (
        SELECT 1 FROM pg_proc
        WHERE proname = function_name
      ) INTO func_exists;

      RETURN func_exists;
    END;
    $$;
    `;

    const { error } = await supabase
      .from('_exec_sql')
      .insert({ query: helperFunctionSQL });

    if (error) {
      console.warn(`Warning: Could not create helper function: ${error.message}`);
    } else {
      console.log('Helper functions created successfully.');
    }
  } catch (error) {
    console.warn(`Warning: Could not create helper functions: ${error.message}`);
  }
}

// Run the script
async function run() {
  await createHelperFunctions();
  await applySystemMessagesFix();
}

run();
