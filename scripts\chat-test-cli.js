/**
 * Chat Flow Test CLI
 *
 * This script creates test data to help you test the chat flow between admin and supplier.
 * Run this from the command line with Node.js.
 *
 * Usage: node scripts/chat-test-cli.js
 */

// Import required modules
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Configuration
const config = {
  adminEmail: '<EMAIL>',
  supplierEmail: '<EMAIL>', // Default test supplier email
  taskId: null,
  adminId: null,
  supplierId: null,
  threadId: null,
  offerId: null,
  debug: true // Set to true for verbose logging
};

// Utility functions for logging
function log(message) {
  console.log(`[ChatTest] ${message}`);
}

function warn(message) {
  console.warn(`[ChatTest] ⚠️ ${message}`);
}

function error(message) {
  console.error(`[ChatTest] 🛑 ${message}`);
}

function success(message) {
  console.log(`[ChatTest] ✅ ${message}`);
}

function debug(message) {
  if (config.debug) {
    console.debug(`[ChatTest] 🔍 ${message}`);
  }
}

// Validate chat_threads table exists and has correct structure
async function validateChatThreadsTable() {
  log('Validating chat_threads table...');

  try {
    // Check if we can query the table
    const { data, error } = await supabase
      .from('chat_threads')
      .select('id')
      .limit(1);

    if (error) {
      error(`Failed to query chat_threads table: ${error.message}`);
      error('This may indicate the table does not exist or you lack permissions');
      return false;
    }

    success('chat_threads table exists and is accessible');

    // Check table structure by attempting to create a dummy record
    // We'll immediately delete it afterward
    const testData = {
      task_id: '00000000-0000-0000-0000-000000000000',
      supplier_id: '00000000-0000-0000-0000-000000000000',
      admin_id: '00000000-0000-0000-0000-000000000000',
      status: 'test',
      has_offer: false
    };

    const { data: insertData, error: insertError } = await supabase
      .from('chat_threads')
      .insert(testData)
      .select()
      .single();

    if (insertError) {
      if (insertError.code === '23503') {
        // Foreign key violation is expected with dummy UUIDs
        debug('Foreign key constraints are working correctly');
        return true;
      } else {
        warn(`Unexpected error when testing chat_threads structure: ${insertError.message}`);
        warn('Table may have incorrect structure or constraints');
        return false;
      }
    } else {
      // If insert succeeded, delete the test record
      await supabase
        .from('chat_threads')
        .delete()
        .eq('id', insertData.id);

      success('chat_threads table structure appears correct');
      return true;
    }
  } catch (err) {
    error(`Unexpected error validating chat_threads table: ${err.message}`);
    return false;
  }
}

// Find admin account
async function findAdmin() {
  log('Finding admin account...');

  const { data: admins, error: adminsError } = await supabase
    .from('profiles')
    .select('id, email, role')
    .eq('role', 'admin')
    .limit(5);

  if (adminsError) {
    error(`Failed to find admins: ${adminsError.message}`);
    return null;
  }

  if (admins.length === 0) {
    error('No admin accounts found. Please create an admin account first.');
    return null;
  }

  // Find the specific admin or use the first one
  const admin = admins.find(a =>
    a.email && a.email[0] && a.email[0].includes(config.adminEmail)
  ) || admins[0];

  config.adminId = admin.id;
  config.adminEmail = admin.email[0];

  success(`Using admin: ${config.adminEmail} (ID: ${config.adminId})`);
  return admin;
}

// Create a test task
async function createTestTask() {
  log('Creating test task...');

  if (!config.adminId) {
    error('Admin ID not found. Cannot create task.');
    return null;
  }

  // Create task
  const { data: task, error: taskError } = await supabase
    .from('tasks')
    .insert({
      title: `Chat Test Task ${new Date().toISOString().substring(0, 16)}`,
      description: 'This is a test task to verify the chat flow functionality.',
      category: 'Maintenance',
      budget: 100,
      due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      location: 'Test Location',
      visibility: 'public',
      status: 'open',
      user_id: config.adminId
    })
    .select()
    .single();

  if (taskError) {
    error(`Failed to create test task: ${taskError.message}`);
    return null;
  }

  config.taskId = task.id;
  success(`Task created with ID: ${task.id}`);

  return task;
}

// Find supplier account
async function findSupplier() {
  log('Finding supplier account...');

  const { data: suppliers, error: suppliersError } = await supabase
    .from('profiles')
    .select('id, email, role, first_name, last_name')
    .eq('role', 'supplier')
    .limit(5);

  if (suppliersError) {
    error(`Failed to find suppliers: ${suppliersError.message}`);
    return null;
  }

  if (suppliers.length === 0) {
    error('No supplier accounts found. Please create a supplier account first.');
    return null;
  }

  debug(`Found ${suppliers.length} supplier accounts`);

  // Find the specific supplier or use the first one
  const supplier = suppliers.find(s =>
    s.email && s.email[0] && s.email[0].includes(config.supplierEmail)
  ) || suppliers[0];

  config.supplierId = supplier.id;
  config.supplierEmail = supplier.email[0];

  success(`Using supplier: ${config.supplierEmail} (ID: ${config.supplierId})`);
  debug(`Supplier details: ${JSON.stringify(supplier)}`);
  return supplier;
}

// Check if a thread already exists for this task and supplier
async function checkExistingThread() {
  log('Checking for existing chat thread...');

  const { data: existingThreads, error: threadError } = await supabase
    .from('chat_threads')
    .select('*')
    .eq('task_id', config.taskId)
    .eq('supplier_id', config.supplierId);

  if (threadError) {
    error(`Failed to check for existing threads: ${threadError.message}`);
    return null;
  }

  if (existingThreads && existingThreads.length > 0) {
    warn(`Found existing thread for this task and supplier: ${existingThreads[0].id}`);
    return existingThreads[0];
  }

  debug('No existing thread found, will create a new one');
  return null;
}

// Simulate supplier expressing interest
async function simulateExpressInterest() {
  log('Simulating supplier expressing interest...');

  // Check for existing thread first
  const existingThread = await checkExistingThread();
  if (existingThread) {
    config.threadId = existingThread.id;
    warn(`Using existing thread: ${existingThread.id}`);
    return existingThread;
  }

  // Create chat thread
  const { data: thread, error: threadError } = await supabase
    .from('chat_threads')
    .insert({
      task_id: config.taskId,
      supplier_id: config.supplierId,
      admin_id: config.adminId,
      status: 'interest',
      has_offer: false
    })
    .select()
    .single();

  if (threadError) {
    error(`Failed to create chat thread: ${threadError.message}`);
    return null;
  }

  config.threadId = thread.id;
  success(`Chat thread created with ID: ${thread.id}`);

  // Add initial message from supplier
  const { error: msgError } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: config.supplierId,
      content: "Hi, I'm interested in this task and would like to discuss the requirements.",
      thread_id: thread.id
    });

  if (msgError) {
    warn(`Failed to create initial message: ${msgError.message}`);
  } else {
    success('Initial supplier message created');
  }

  // Add system message
  const { error: sysError } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: '00000000-0000-0000-0000-000000000000', // System user
      content: 'A supplier has expressed interest in this task.',
      thread_id: thread.id
    });

  if (sysError) {
    warn(`Failed to create system message: ${sysError.message}`);
    warn('This may indicate issues with the system user or RLS policies');
  } else {
    success('System message created');
  }

  success('Supplier interest simulated successfully');
  return thread;
}

// Simulate admin response
async function simulateAdminResponse() {
  log('Simulating admin response...');

  const { error: msgError } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: config.adminId,
      content: "Thank you for your interest. What experience do you have with this type of work?",
      thread_id: config.threadId
    });

  if (msgError) {
    error(`Failed to create admin response: ${msgError.message}`);
    return false;
  }

  success('Admin response simulated successfully');
  return true;
}

// Simulate supplier submitting an offer
async function simulateSupplierOffer() {
  log('Simulating supplier submitting an offer...');

  // Add supplier response
  const { error: msgError } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: config.supplierId,
      content: "I have 5 years of experience with similar projects. I'd like to submit a formal offer.",
      thread_id: config.threadId
    });

  if (msgError) {
    warn(`Failed to create supplier response: ${msgError.message}`);
  } else {
    success('Supplier response message created');
  }

  // Check for existing offers
  const { data: existingOffers, error: checkError } = await supabase
    .from('offers')
    .select('*')
    .eq('task_id', config.taskId)
    .eq('user_id', config.supplierId);

  if (!checkError && existingOffers && existingOffers.length > 0) {
    warn(`Supplier already has an offer for this task: ${existingOffers[0].id}`);
    config.offerId = existingOffers[0].id;
    return existingOffers[0];
  }

  // Create offer
  const { data: offer, error: offerError } = await supabase
    .from('offers')
    .insert({
      task_id: config.taskId,
      user_id: config.supplierId,
      amount: 95,
      message: "Based on our discussion, I can complete this work for £95. I'll ensure it meets all requirements and is delivered on time.",
      status: 'awaiting'
    })
    .select()
    .single();

  if (offerError) {
    error(`Failed to create offer: ${offerError.message}`);
    return null;
  }

  config.offerId = offer.id;
  success(`Offer created with ID: ${offer.id}`);

  // Update thread status
  const { error: updateError } = await supabase
    .from('chat_threads')
    .update({
      status: 'questions',
      has_offer: true,
      updated_at: new Date().toISOString(),
      last_message_at: new Date().toISOString()
    })
    .eq('id', config.threadId);

  if (updateError) {
    warn(`Failed to update thread status: ${updateError.message}`);
  } else {
    success('Thread status updated to questions phase');
  }

  // Add system messages
  const { error: sysError1 } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: '00000000-0000-0000-0000-000000000000',
      content: `${config.supplierEmail} has submitted a formal offer of £95.00.`,
      thread_id: config.threadId
    });

  if (sysError1) {
    warn(`Failed to create system message about offer: ${sysError1.message}`);
  } else {
    success('System message about offer created');
  }

  const { error: sysError2 } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: '00000000-0000-0000-0000-000000000000',
      content: 'Discussion phase has started. Please discuss requirements before submitting formal offers.',
      thread_id: config.threadId
    });

  if (sysError2) {
    warn(`Failed to create system message about questions phase: ${sysError2.message}`);
  } else {
    success('System message about questions phase created');
  }

  success('Supplier offer simulated successfully');
  return offer;
}

// Simulate admin accepting the offer
async function simulateAcceptOffer() {
  log('Simulating admin accepting the offer...');

  if (!config.offerId) {
    error('No offer ID found. Cannot accept offer.');
    return false;
  }

  // Update offer status
  const { error: offerError } = await supabase
    .from('offers')
    .update({ status: 'accepted' })
    .eq('id', config.offerId);

  if (offerError) {
    error(`Failed to accept offer: ${offerError.message}`);
    return false;
  }

  // Update task status
  const { error: taskError } = await supabase
    .from('tasks')
    .update({
      status: 'in_progress',
      assigned_to: config.supplierId
    })
    .eq('id', config.taskId);

  if (taskError) {
    error(`Failed to update task status: ${taskError.message}`);
    return false;
  }

  // Update thread status
  const { error: threadError } = await supabase
    .from('chat_threads')
    .update({
      status: 'accepted',
      updated_at: new Date().toISOString(),
      last_message_at: new Date().toISOString()
    })
    .eq('id', config.threadId);

  if (threadError) {
    warn(`Failed to update thread status: ${threadError.message}`);
  }

  // Add system message
  const { error: sysError } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: '00000000-0000-0000-0000-000000000000',
      content: `The admin has accepted the offer. The task has been assigned to ${config.supplierEmail}.`,
      thread_id: config.threadId
    });

  if (sysError) {
    warn(`Failed to create system message about acceptance: ${sysError.message}`);
  }

  // Add admin message
  const { error: msgError } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: config.adminId,
      content: "I've accepted your offer. Please proceed with the work and keep me updated on your progress.",
      thread_id: config.threadId
    });

  if (msgError) {
    warn(`Failed to create admin acceptance message: ${msgError.message}`);
  }

  success('Offer accepted successfully');
  return true;
}

// Verify messages are correctly associated with thread
async function verifyMessages() {
  log('Verifying messages...');

  const { data: messages, error } = await supabase
    .from('task_messages')
    .select('*')
    .eq('task_id', config.taskId)
    .order('created_at', { ascending: true });

  if (error) {
    error(`Failed to retrieve messages: ${error.message}`);
    return false;
  }

  if (!messages || messages.length === 0) {
    error('No messages found for this task');
    return false;
  }

  success(`Found ${messages.length} messages for this task`);

  // Check if all messages have thread_id
  const messagesWithoutThread = messages.filter(m => !m.thread_id);
  if (messagesWithoutThread.length > 0) {
    warn(`Found ${messagesWithoutThread.length} messages without thread_id`);
    debug(`Messages without thread_id: ${JSON.stringify(messagesWithoutThread)}`);
  } else {
    success('All messages have thread_id');
  }

  // Check if all messages have the correct thread_id
  const messagesWithWrongThread = messages.filter(m => m.thread_id && m.thread_id !== config.threadId);
  if (messagesWithWrongThread.length > 0) {
    warn(`Found ${messagesWithWrongThread.length} messages with incorrect thread_id`);
    debug(`Messages with wrong thread_id: ${JSON.stringify(messagesWithWrongThread)}`);
  } else {
    success('All messages have correct thread_id');
  }

  return true;
}

// Run the full test
async function runChatTest() {
  log('Starting enhanced chat flow test...');

  try {
    // Validate chat_threads table
    const tableValid = await validateChatThreadsTable();
    if (!tableValid) {
      warn('Issues detected with chat_threads table. Continuing test but expect errors.');
    }

    // Find admin account
    const admin = await findAdmin();
    if (!admin) {
      error('Failed to find admin account. Aborting test.');
      return;
    }

    // Create test data
    const task = await createTestTask();
    if (!task) {
      error('Failed to create test task. Aborting test.');
      return;
    }

    const supplier = await findSupplier();
    if (!supplier) {
      error('Failed to find supplier. Aborting test.');
      return;
    }

    const thread = await simulateExpressInterest();
    if (!thread) {
      error('Failed to simulate supplier interest. Aborting test.');
      return;
    }

    const adminResponseSuccess = await simulateAdminResponse();
    if (!adminResponseSuccess) {
      warn('Failed to simulate admin response. Continuing test but expect issues.');
    }

    const offer = await simulateSupplierOffer();
    if (!offer) {
      warn('Failed to simulate supplier offer. Continuing test but expect issues.');
    }

    const acceptSuccess = await simulateAcceptOffer();
    if (!acceptSuccess) {
      warn('Failed to simulate offer acceptance. Continuing test but expect issues.');
    }

    // Verify messages
    await verifyMessages();

    success('Test completed!');
    log('Test Summary:');
    log(`- Task ID: ${config.taskId}`);
    log(`- Admin ID: ${config.adminId}`);
    log(`- Supplier ID: ${config.supplierId}`);
    log(`- Thread ID: ${config.threadId}`);
    log(`- Offer ID: ${config.offerId}`);
  } catch (err) {
    error(`Test failed with unexpected error: ${err.message}`);
    console.error(err);
  }
}

// Run the test
runChatTest();
