#!/usr/bin/env node

/**
 * Comprehensive Security Scanner for Console Log Vulnerabilities
 * Detects all patterns of sensitive data exposure in console logs
 */

const fs = require('fs');
const path = require('path');

// Colors for output
const colors = {
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

/**
 * Security patterns to detect in console logs
 */
const SECURITY_PATTERNS = [
  // User ID patterns (UUIDs)
  {
    pattern: /console\.log\([^)]*[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}[^)]*\)/gi,
    name: 'User ID (UUID) in console.log',
    severity: 'CRITICAL',
    category: 'USER_DATA'
  },
  
  // Email addresses in logs
  {
    pattern: /console\.log\([^)]*[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}[^)]*\)/gi,
    name: 'Email address in console.log',
    severity: 'CRITICAL',
    category: 'USER_DATA'
  },
  
  // Organization ID patterns
  {
    pattern: /console\.log\([^)]*organization[_\s]*id[^)]*\)/gi,
    name: 'Organization ID in console.log',
    severity: 'CRITICAL',
    category: 'ORGANIZATION_DATA'
  },
  
  // User role patterns
  {
    pattern: /console\.log\([^)]*role[^)]*admin|teacher|supplier|maintenance|support[^)]*\)/gi,
    name: 'User role in console.log',
    severity: 'HIGH',
    category: 'USER_DATA'
  },
  
  // Profile data patterns
  {
    pattern: /console\.log\([^)]*profile[^)]*\)/gi,
    name: 'Profile data in console.log',
    severity: 'HIGH',
    category: 'USER_DATA'
  },
  
  // Authentication data patterns
  {
    pattern: /console\.log\([^)]*auth[^)]*user[^)]*\)/gi,
    name: 'Authentication data in console.log',
    severity: 'HIGH',
    category: 'AUTH_DATA'
  },
  
  // Task data patterns
  {
    pattern: /console\.log\([^)]*task[^)]*\)/gi,
    name: 'Task data in console.log',
    severity: 'MEDIUM',
    category: 'BUSINESS_DATA'
  },
  
  // API key patterns
  {
    pattern: /console\.log\([^)]*api[_\s]*key[^)]*\)/gi,
    name: 'API key in console.log',
    severity: 'CRITICAL',
    category: 'CREDENTIALS'
  },
  
  // Token patterns
  {
    pattern: /console\.log\([^)]*token[^)]*\)/gi,
    name: 'Token in console.log',
    severity: 'HIGH',
    category: 'CREDENTIALS'
  },
  
  // Database query results
  {
    pattern: /console\.log\([^)]*data[^)]*\)/gi,
    name: 'Database data in console.log',
    severity: 'MEDIUM',
    category: 'DATABASE_DATA'
  },
  
  // Error messages with user data
  {
    pattern: /console\.log\([^)]*error[^)]*\)/gi,
    name: 'Error with potential user data in console.log',
    severity: 'MEDIUM',
    category: 'ERROR_DATA'
  },
  
  // Alert with sensitive data
  {
    pattern: /alert\([^)]*[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}[^)]*\)/gi,
    name: 'User ID in alert()',
    severity: 'CRITICAL',
    category: 'USER_DATA'
  },
  
  // Console logs without development check
  {
    pattern: /^\s*console\.log\(/gm,
    name: 'Console.log without development check',
    severity: 'HIGH',
    category: 'UNPROTECTED_LOGGING'
  }
];

/**
 * Files and directories to exclude
 */
const EXCLUDE_PATTERNS = [
  /node_modules/,
  /\.git/,
  /dist/,
  /build/,
  /\.env/,
  /\.log$/,
  /package-lock\.json$/,
  /yarn\.lock$/,
  /comprehensive-security-scanner\.js$/,
  /final-security-check\.js$/,
  /remove-console-logs\.js$/
];

/**
 * File extensions to scan
 */
const SCAN_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

/**
 * Scan results storage
 */
let scanResults = {
  totalFiles: 0,
  scannedFiles: 0,
  vulnerabilities: [],
  summary: {
    CRITICAL: 0,
    HIGH: 0,
    MEDIUM: 0,
    LOW: 0
  }
};

/**
 * Log with colors
 */
function log(level, message) {
  const color = {
    ERROR: colors.red,
    WARNING: colors.yellow,
    SUCCESS: colors.green,
    INFO: colors.blue,
    CRITICAL: colors.red + colors.bold,
    HIGH: colors.yellow + colors.bold,
    MEDIUM: colors.cyan,
    LOW: colors.reset
  }[level] || colors.reset;
  
  console.log(`${color}[${level}]${colors.reset} ${message}`);
}

/**
 * Check if file should be excluded
 */
function shouldExcludeFile(filePath) {
  return EXCLUDE_PATTERNS.some(pattern => pattern.test(filePath));
}

/**
 * Check if file should be scanned
 */
function shouldScanFile(filePath) {
  const ext = path.extname(filePath);
  return SCAN_EXTENSIONS.includes(ext) && !shouldExcludeFile(filePath);
}

/**
 * Scan file for security vulnerabilities
 */
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    scanResults.scannedFiles++;
    
    SECURITY_PATTERNS.forEach(({ pattern, name, severity, category }) => {
      let match;
      const globalPattern = new RegExp(pattern.source, pattern.flags);
      
      while ((match = globalPattern.exec(content)) !== null) {
        // Find line number
        const beforeMatch = content.substring(0, match.index);
        const lineNumber = beforeMatch.split('\n').length;
        
        // Get the actual line content
        const lineContent = lines[lineNumber - 1]?.trim() || '';
        
        // Skip if it's already protected with development check
        const contextStart = Math.max(0, lineNumber - 3);
        const contextEnd = Math.min(lines.length, lineNumber + 2);
        const context = lines.slice(contextStart, contextEnd).join('\n');
        
        const hasDevCheck = /process\.env\.NODE_ENV\s*===\s*['"]development['"]/.test(context);
        
        if (!hasDevCheck) {
          scanResults.vulnerabilities.push({
            file: filePath,
            line: lineNumber,
            pattern: name,
            severity,
            category,
            content: lineContent,
            match: match[0].substring(0, 100) // Limit match length
          });
          
          scanResults.summary[severity]++;
        }
      }
    });
    
  } catch (error) {
    log('ERROR', `Failed to scan ${filePath}: ${error.message}`);
  }
}

/**
 * Recursively scan directory
 */
function scanDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !shouldExcludeFile(fullPath)) {
        scanDirectory(fullPath);
      } else if (stat.isFile() && shouldScanFile(fullPath)) {
        scanResults.totalFiles++;
        scanFile(fullPath);
      }
    });
  } catch (error) {
    log('ERROR', `Failed to scan directory ${dirPath}: ${error.message}`);
  }
}

/**
 * Generate detailed report
 */
function generateReport() {
  console.log('\n' + '='.repeat(80));
  log('INFO', colors.bold + 'COMPREHENSIVE SECURITY SCAN RESULTS' + colors.reset);
  console.log('='.repeat(80));
  
  // Summary
  console.log(`\n📊 ${colors.bold}SCAN SUMMARY${colors.reset}`);
  console.log(`Total files found: ${scanResults.totalFiles}`);
  console.log(`Files scanned: ${scanResults.scannedFiles}`);
  console.log(`Total vulnerabilities: ${scanResults.vulnerabilities.length}`);
  
  // Severity breakdown
  console.log(`\n🚨 ${colors.bold}SEVERITY BREAKDOWN${colors.reset}`);
  console.log(`${colors.red}CRITICAL: ${scanResults.summary.CRITICAL}${colors.reset}`);
  console.log(`${colors.yellow}HIGH: ${scanResults.summary.HIGH}${colors.reset}`);
  console.log(`${colors.cyan}MEDIUM: ${scanResults.summary.MEDIUM}${colors.reset}`);
  console.log(`${colors.reset}LOW: ${scanResults.summary.LOW}${colors.reset}`);
  
  // Category breakdown
  const categories = {};
  scanResults.vulnerabilities.forEach(vuln => {
    categories[vuln.category] = (categories[vuln.category] || 0) + 1;
  });
  
  console.log(`\n📋 ${colors.bold}CATEGORY BREAKDOWN${colors.reset}`);
  Object.entries(categories).forEach(([category, count]) => {
    console.log(`${category}: ${count}`);
  });
  
  // Detailed vulnerabilities
  if (scanResults.vulnerabilities.length > 0) {
    console.log(`\n🔍 ${colors.bold}DETAILED VULNERABILITIES${colors.reset}`);
    console.log('-'.repeat(80));
    
    scanResults.vulnerabilities.forEach((vuln, index) => {
      const severityColor = {
        CRITICAL: colors.red + colors.bold,
        HIGH: colors.yellow + colors.bold,
        MEDIUM: colors.cyan,
        LOW: colors.reset
      }[vuln.severity];
      
      console.log(`\n${index + 1}. ${severityColor}[${vuln.severity}]${colors.reset} ${vuln.pattern}`);
      console.log(`   File: ${vuln.file}:${vuln.line}`);
      console.log(`   Category: ${vuln.category}`);
      console.log(`   Code: ${vuln.content}`);
      if (vuln.match.length < 100) {
        console.log(`   Match: ${vuln.match}`);
      }
    });
  }
  
  // Recommendations
  console.log(`\n💡 ${colors.bold}RECOMMENDATIONS${colors.reset}`);
  if (scanResults.summary.CRITICAL > 0) {
    log('CRITICAL', 'IMMEDIATE ACTION REQUIRED: Critical vulnerabilities found!');
    console.log('   - User IDs, emails, or API keys are being logged in production');
    console.log('   - This poses serious privacy and security risks');
  }
  
  if (scanResults.summary.HIGH > 0) {
    log('HIGH', 'High priority fixes needed');
    console.log('   - Sensitive user data or authentication info being logged');
    console.log('   - Should be wrapped in development-only checks');
  }
  
  if (scanResults.vulnerabilities.length > 0) {
    console.log('\n🔧 To fix these issues:');
    console.log('1. Wrap console.log statements with: if (process.env.NODE_ENV === \'development\')');
    console.log('2. Remove or sanitize sensitive data from logs');
    console.log('3. Use boolean flags instead of actual IDs (e.g., hasUserId: !!userId)');
    console.log('4. Run this scanner again to verify fixes');
  } else {
    log('SUCCESS', '🎉 No security vulnerabilities found! Your console logs are secure.');
  }
}

/**
 * Main execution
 */
function main() {
  console.log(`${colors.blue}🔍 Starting Comprehensive Security Scan...${colors.reset}\n`);
  
  const startTime = Date.now();
  
  // Scan src directory
  if (fs.existsSync('src')) {
    log('INFO', 'Scanning src/ directory...');
    scanDirectory('src');
  }
  
  // Scan other relevant directories
  ['components', 'pages', 'hooks', 'services', 'utils', 'contexts'].forEach(dir => {
    if (fs.existsSync(dir)) {
      log('INFO', `Scanning ${dir}/ directory...`);
      scanDirectory(dir);
    }
  });
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  console.log(`\n⏱️ Scan completed in ${duration} seconds`);
  
  // Generate report
  generateReport();
  
  // Save results to file
  const reportFile = 'security-scan-results.json';
  fs.writeFileSync(reportFile, JSON.stringify(scanResults, null, 2));
  log('INFO', `Detailed results saved to ${reportFile}`);
  
  // Exit with appropriate code
  const hasVulnerabilities = scanResults.vulnerabilities.length > 0;
  process.exit(hasVulnerabilities ? 1 : 0);
}

// Run the scanner
main();
