/**
 * This script deploys the compliance-notifications Edge Function to Supabase.
 */

import { execSync } from 'child_process';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Initialize dotenv
dotenv.config();

// Configuration
const PROJECT_REF = process.env.SUPABASE_PROJECT_REF || 'qcnotlojmyvpqbbgoxbc';
const APP_URL = process.env.APP_URL || 'https://classtasker.com';

function deployComplianceNotifications() {
  try {
    console.log('Deploying compliance-notifications Edge Function...');

    // Set environment variables
    console.log('Setting environment variables...');
    execSync(`npx supabase secrets set APP_URL="${APP_URL}" --project-ref ${PROJECT_REF}`, { stdio: 'inherit' });

    // Deploy the function
    console.log('Deploying the function...');
    execSync(`npx supabase functions deploy compliance-notifications --project-ref ${PROJECT_REF}`, { stdio: 'inherit' });

    console.log('Deployment completed successfully!');
  } catch (error) {
    console.error('Error deploying Edge Function:', error);
    process.exit(1);
  }
}

// Run the deployment
deployComplianceNotifications();
