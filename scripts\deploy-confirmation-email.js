/**
 * This script deploys the send-confirmation-email Edge Function to Supabase.
 * Run with: node scripts/deploy-confirmation-email.js
 */

import { execSync } from 'child_process';
import dotenv from 'dotenv';

dotenv.config();

// Configuration
const PROJECT_REF = process.env.SUPABASE_PROJECT_REF || 'qcnotlojmyvpqbbgoxbc';

function deployConfirmationEmail() {
  try {
    console.log('Deploying send-confirmation-email Edge Function...');

    // Deploy the function
    console.log('Deploying the function...');
    execSync(`npx supabase functions deploy send-confirmation-email --project-ref ${PROJECT_REF}`, { stdio: 'inherit' });

    console.log('✅ Deployment completed successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Test the function manually');
    console.log('2. Configure Supabase Auth to use Email Hooks');
    console.log('3. Update auth configuration to point to this function');
    console.log('');
    console.log('Function URL: https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/send-confirmation-email');

  } catch (error) {
    console.error('❌ Error deploying Edge Function:', error.message);
    console.log('');
    console.log('Alternative deployment options:');
    console.log('1. Use Supabase Dashboard to deploy manually');
    console.log('2. Copy the function code to an existing working function');
    console.log('3. Use the Supabase CLI directly if available');
    process.exit(1);
  }
}

// Run the deployment
deployConfirmationEmail();
