#!/usr/bin/env node

/**
 * Final Security Check Script
 * 
 * This script performs a final verification that no hardcoded API keys
 * or secrets remain in the codebase after cleanup.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(level, message) {
  const color = {
    ERROR: colors.red,
    WARNING: colors.yellow,
    SUCCESS: colors.green,
    INFO: colors.blue
  }[level] || colors.reset;
  
  console.log(`${color}[${level}]${colors.reset} ${message}`);
}

/**
 * Patterns to search for potential secrets
 */
const SECURITY_PATTERNS = [
  // Specific old keys that should be completely removed
  { pattern: /qeaycbch5vhf/gi, name: 'Old GetStream API Key', severity: 'CRITICAL' },
  { pattern: /e379cn39jrwz/gi, name: 'Older GetStream API Key', severity: 'CRITICAL' },
  { pattern: /AIzaSyADP5PrGRFA7BDgenK26HrU66VITsHWL58/gi, name: 'Old Google Maps API Key', severity: 'CRITICAL' },
  
  // Generic API key patterns
  { pattern: /sk_live_[a-zA-Z0-9]{24,}/g, name: 'Stripe Live Secret Key', severity: 'CRITICAL' },
  { pattern: /sk_test_[a-zA-Z0-9]{24,}/g, name: 'Stripe Test Secret Key', severity: 'HIGH' },
  { pattern: /pk_live_[a-zA-Z0-9]{24,}/g, name: 'Stripe Live Public Key', severity: 'HIGH' },
  { pattern: /AIza[0-9A-Za-z\\-_]{35}/g, name: 'Google API Key Pattern', severity: 'HIGH' },
  
  // JWT tokens (but exclude documentation examples)
  { pattern: /eyJ[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]{10,}/g, name: 'JWT Token', severity: 'HIGH' },
  
  // Generic secret patterns
  { pattern: /password\s*[:=]\s*["'][^"']{8,}["']/gi, name: 'Hardcoded Password', severity: 'HIGH' },
  { pattern: /secret\s*[:=]\s*["'][^"']{8,}["']/gi, name: 'Hardcoded Secret', severity: 'HIGH' },
  { pattern: /token\s*[:=]\s*["'][^"']{20,}["']/gi, name: 'Hardcoded Token', severity: 'MEDIUM' }
];

/**
 * Files and directories to exclude from scanning
 */
const EXCLUDE_PATTERNS = [
  /node_modules/,
  /\.git/,
  /dist/,
  /build/,
  /\.env/,
  /\.log$/,
  /package-lock\.json$/,
  /yarn\.lock$/,
  /final-security-check\.js$/ // Exclude this script itself
];

/**
 * Files that are allowed to contain certain patterns (like documentation)
 */
const ALLOWED_FILES = {
  'SECURITY.md': ['sk_live_actual_secret_key'], // Documentation examples
  '.env.example': ['your_', '_here'], // Template placeholders
  'scripts/.env.example': ['your-', '-here'] // Template placeholders
};

/**
 * Get all files to scan
 */
function getFilesToScan() {
  const files = [];
  
  function scanDirectory(dir) {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      const relativePath = path.relative(projectRoot, fullPath);
      
      // Skip excluded patterns
      if (EXCLUDE_PATTERNS.some(pattern => pattern.test(relativePath))) {
        continue;
      }
      
      if (entry.isDirectory()) {
        scanDirectory(fullPath);
      } else if (entry.isFile()) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(projectRoot);
  return files;
}

/**
 * Check if a match is allowed in a specific file
 */
function isMatchAllowed(filePath, match) {
  const relativePath = path.relative(projectRoot, filePath);
  const allowedPatterns = ALLOWED_FILES[relativePath];
  
  if (!allowedPatterns) {
    return false;
  }
  
  return allowedPatterns.some(pattern => match.includes(pattern));
}

/**
 * Scan a file for security issues
 */
function scanFile(filePath) {
  const issues = [];
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(projectRoot, filePath);
    
    for (const { pattern, name, severity } of SECURITY_PATTERNS) {
      const matches = content.match(pattern);
      
      if (matches) {
        for (const match of matches) {
          // Skip if this match is allowed in this file
          if (isMatchAllowed(filePath, match)) {
            continue;
          }
          
          issues.push({
            file: relativePath,
            pattern: name,
            match: match.substring(0, 50) + (match.length > 50 ? '...' : ''),
            severity,
            line: content.substring(0, content.indexOf(match)).split('\n').length
          });
        }
      }
    }
  } catch (error) {
    // Skip binary files or files that can't be read
    if (error.code !== 'EISDIR') {
      log('WARNING', `Could not read file: ${filePath}`);
    }
  }
  
  return issues;
}

/**
 * Main security check function
 */
function runFinalSecurityCheck() {
  console.log(`${colors.bold}🔒 Final Security Check${colors.reset}\n`);
  
  log('INFO', 'Scanning for hardcoded secrets and API keys...');
  
  const files = getFilesToScan();
  log('INFO', `Scanning ${files.length} files...`);
  
  const allIssues = [];
  
  for (const file of files) {
    const issues = scanFile(file);
    allIssues.push(...issues);
  }
  
  // Group issues by severity
  const issuesBySeverity = {
    CRITICAL: allIssues.filter(i => i.severity === 'CRITICAL'),
    HIGH: allIssues.filter(i => i.severity === 'HIGH'),
    MEDIUM: allIssues.filter(i => i.severity === 'MEDIUM')
  };
  
  console.log(`\n${colors.bold}=== SECURITY SCAN RESULTS ===${colors.reset}\n`);
  
  let hasIssues = false;
  
  // Report critical issues
  if (issuesBySeverity.CRITICAL.length > 0) {
    hasIssues = true;
    console.log(`${colors.red}🚨 CRITICAL ISSUES (${issuesBySeverity.CRITICAL.length}):${colors.reset}`);
    issuesBySeverity.CRITICAL.forEach(issue => {
      console.log(`  ${colors.red}❌${colors.reset} ${issue.file}:${issue.line}`);
      console.log(`     Pattern: ${issue.pattern}`);
      console.log(`     Match: ${issue.match}`);
      console.log('');
    });
  }
  
  // Report high priority issues
  if (issuesBySeverity.HIGH.length > 0) {
    hasIssues = true;
    console.log(`${colors.yellow}⚠️  HIGH PRIORITY ISSUES (${issuesBySeverity.HIGH.length}):${colors.reset}`);
    issuesBySeverity.HIGH.forEach(issue => {
      console.log(`  ${colors.yellow}⚠️${colors.reset} ${issue.file}:${issue.line}`);
      console.log(`     Pattern: ${issue.pattern}`);
      console.log(`     Match: ${issue.match}`);
      console.log('');
    });
  }
  
  // Report medium priority issues
  if (issuesBySeverity.MEDIUM.length > 0) {
    console.log(`${colors.blue}ℹ️  MEDIUM PRIORITY ISSUES (${issuesBySeverity.MEDIUM.length}):${colors.reset}`);
    issuesBySeverity.MEDIUM.forEach(issue => {
      console.log(`  ${colors.blue}ℹ️${colors.reset} ${issue.file}:${issue.line}`);
      console.log(`     Pattern: ${issue.pattern}`);
      console.log(`     Match: ${issue.match}`);
      console.log('');
    });
  }
  
  // Final result
  if (!hasIssues) {
    console.log(`${colors.green}✅ SECURITY CHECK PASSED${colors.reset}`);
    console.log(`${colors.green}No hardcoded secrets or API keys found!${colors.reset}`);
    console.log(`${colors.green}Your application is secure for deployment.${colors.reset}`);
  } else {
    console.log(`${colors.red}❌ SECURITY CHECK FAILED${colors.reset}`);
    console.log(`${colors.red}Found ${allIssues.length} potential security issue(s).${colors.reset}`);
    console.log(`${colors.yellow}Please review and fix the issues above before deploying.${colors.reset}`);
    process.exit(1);
  }
  
  console.log(`\n${colors.bold}Scan Summary:${colors.reset}`);
  console.log(`Files scanned: ${files.length}`);
  console.log(`Critical issues: ${issuesBySeverity.CRITICAL.length}`);
  console.log(`High priority issues: ${issuesBySeverity.HIGH.length}`);
  console.log(`Medium priority issues: ${issuesBySeverity.MEDIUM.length}`);
}

// Run the security check
if (import.meta.url === `file://${process.argv[1]}`) {
  runFinalSecurityCheck();
}

export { runFinalSecurityCheck };
