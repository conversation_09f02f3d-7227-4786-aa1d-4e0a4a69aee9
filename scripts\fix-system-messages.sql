-- Fix system messages in ClassTasker
-- This script:
-- 1. Creates a system user in auth.users
-- 2. Creates a corresponding profile in public.profiles
-- 3. Modifies the create_task_message function to allow system messages

-- Step 1: Create the system user in auth.users
-- Note: We're using a specific UUID for the system user
INSERT INTO auth.users (
  id,
  instance_id,
  email,
  encrypted_password,
  email_confirmed_at,
  recovery_sent_at,
  last_sign_in_at,
  raw_app_meta_data,
  raw_user_meta_data,
  created_at,
  updated_at,
  confirmation_token,
  email_change,
  email_change_token_new,
  recovery_token
)
VALUES (
  '00000000-0000-0000-0000-000000000000',
  '00000000-0000-0000-0000-000000000000',
  '<EMAIL>',
  '$2a$10$XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', -- Placeholder password hash
  NOW(),
  NOW(),
  NOW(),
  '{"provider":"email","providers":["email"]}',
  '{"name":"System"}',
  NOW(),
  NOW(),
  '',
  '',
  '',
  ''
)
ON CONFLICT (id) DO NOTHING;

-- Step 2: Create a corresponding profile in public.profiles
INSERT INTO public.profiles (
  id,
  email,
  first_name,
  last_name,
  role,
  created_at,
  updated_at
)
VALUES (
  '00000000-0000-0000-0000-000000000000',
  '{<EMAIL>}',
  'System',
  'User',
  'system',
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- Step 3: Modify the create_task_message function to allow system messages
CREATE OR REPLACE FUNCTION public.create_task_message(task_id_param uuid, sender_id_param uuid, content_param text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  message_id UUID;
  task_org_id UUID;
  sender_org_id UUID;
  task_visibility TEXT;
  task_assigned_to UUID;
  task_user_id UUID;
BEGIN
  -- Get task information
  SELECT 
    p.organization_id, t.visibility, t.assigned_to, t.user_id
  INTO 
    task_org_id, task_visibility, task_assigned_to, task_user_id
  FROM 
    public.tasks t
  JOIN
    public.profiles p ON t.user_id = p.id
  WHERE 
    t.id = task_id_param;
  
  -- Get sender's organization_id
  SELECT organization_id INTO sender_org_id
  FROM public.profiles
  WHERE id = sender_id_param;
  
  -- Check if sender has permission to message this task
  IF sender_id_param = task_user_id THEN
    -- Task owner can always message
    NULL;
  ELSIF sender_id_param = task_assigned_to THEN
    -- Assigned user can always message
    NULL;
  ELSIF task_visibility = 'public' THEN
    -- Public tasks can be messaged by anyone
    NULL;
  ELSIF sender_org_id = task_org_id THEN
    -- Users in the same organization can message internal tasks
    NULL;
  ELSIF sender_id_param = '00000000-0000-0000-0000-000000000000' THEN
    -- System user can always message any task
    NULL;
  ELSE
    RAISE EXCEPTION 'Access denied: You do not have permission to message this task';
  END IF;

  -- Insert the message
  INSERT INTO public.task_messages (
    task_id,
    sender_id,
    content,
    created_at,
    updated_at
  )
  VALUES (
    task_id_param,
    sender_id_param,
    content_param,
    NOW(),
    NOW()
  )
  RETURNING id INTO message_id;
  
  RETURN message_id;
END;
$function$;

-- Step 4: Create a helper function specifically for system messages
CREATE OR REPLACE FUNCTION public.create_system_message(task_id_param uuid, status_param text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  message_id UUID;
  system_user_id UUID := '00000000-0000-0000-0000-000000000000';
  message_content TEXT;
BEGIN
  -- Determine the message content based on the status
  CASE status_param
    WHEN 'open' THEN
      message_content := 'Task created and is now open for offers.';
    WHEN 'assigned' THEN
      message_content := 'Task has been assigned to a supplier.';
    WHEN 'in_progress' THEN
      message_content := 'Task is now in progress. Work has started.';
    WHEN 'completed' THEN
      message_content := 'Supplier has marked this task as completed.';
    WHEN 'confirmed' THEN
      message_content := 'Task has been approved by the administrator.';
    WHEN 'pending_payment' THEN
      message_content := 'Task is awaiting payment.';
    WHEN 'paid' THEN
      message_content := 'Payment has been processed. Task is complete.';
    WHEN 'file_upload' THEN
      message_content := 'A file has been uploaded to this task.';
    ELSE
      message_content := 'Task status changed to: ' || status_param;
  END CASE;

  -- Call the create_task_message function with the system user ID
  SELECT create_task_message(task_id_param, system_user_id, message_content) INTO message_id;
  
  RETURN message_id;
END;
$function$;

-- Step 5: Create a trigger to automatically add system messages when task status changes
CREATE OR REPLACE FUNCTION public.task_status_change_message()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  -- Only create a message if the status has changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    -- Call the create_system_message function
    PERFORM create_system_message(NEW.id, NEW.status);
  END IF;
  
  RETURN NEW;
END;
$function$;

-- Create the trigger if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'task_status_change_message_trigger'
  ) THEN
    CREATE TRIGGER task_status_change_message_trigger
    AFTER UPDATE OF status ON public.tasks
    FOR EACH ROW
    EXECUTE FUNCTION public.task_status_change_message();
  END IF;
END
$$;
