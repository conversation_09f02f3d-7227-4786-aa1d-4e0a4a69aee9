#!/usr/bin/env node

/**
 * Generate env-config.js with actual environment variables for production builds
 * This script is run during the Vercel build process to replace placeholder values
 * with actual environment variables from Vercel.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Environment variables to include in the client-side config
const ENV_VARS = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY',
  'VITE_SUPABASE_FUNCTIONS_URL',
  'VITE_GETSTREAM_API_KEY',
  'VITE_GOOGLE_MAPS_API_KEY',
  'VITE_STRIPE_PUBLIC_KEY',
  'VITE_SITE_URL'
];

function generateEnvConfig() {
  console.log('[ENV-CONFIG] Generating env-config.js with production environment variables...');

  // Collect environment variables
  const envConfig = {};
  const missingVars = [];

  ENV_VARS.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      envConfig[varName] = value;
      console.log(`[ENV-CONFIG] ✅ ${varName}: ${varName.includes('KEY') ? '[REDACTED]' : value}`);
    } else {
      missingVars.push(varName);
      console.log(`[ENV-CONFIG] ⚠️  ${varName}: NOT SET`);
    }
  });

  // Warn about missing variables
  if (missingVars.length > 0) {
    console.warn(`[ENV-CONFIG] Warning: Missing environment variables: ${missingVars.join(', ')}`);
  }

  // Generate the JavaScript content
  const jsContent = `// Auto-generated env-config.js for production
// Generated at: ${new Date().toISOString()}
// DO NOT EDIT - This file is automatically generated during build

window.env = ${JSON.stringify(envConfig, null, 2)};

// SECURITY: No logging in production to prevent infrastructure exposure
`;

  // Write to public directory (for development) and dist directory (for production)
  const publicPath = path.join(process.cwd(), 'public', 'env-config.js');
  const distPath = path.join(process.cwd(), 'dist', 'env-config.js');

  // Always write to public directory
  fs.writeFileSync(publicPath, jsContent);
  console.log(`[ENV-CONFIG] ✅ Written to: ${publicPath}`);

  // Write to dist directory if it exists (production build)
  if (fs.existsSync(path.dirname(distPath))) {
    fs.writeFileSync(distPath, jsContent);
    console.log(`[ENV-CONFIG] ✅ Written to: ${distPath}`);
  }

  console.log('[ENV-CONFIG] Environment configuration generated successfully!');

  // Validate critical variables
  if (!envConfig.VITE_SUPABASE_URL || !envConfig.VITE_SUPABASE_ANON_KEY) {
    console.error('[ENV-CONFIG] ❌ CRITICAL: Missing Supabase configuration!');
    process.exit(1);
  }

  return envConfig;
}

// Run if called directly (ES module equivalent)
if (import.meta.url === `file://${process.argv[1]}`) {
  try {
    generateEnvConfig();
  } catch (error) {
    console.error('[ENV-CONFIG] Error generating environment config:', error);
    process.exit(1);
  }
}

export { generateEnvConfig };
