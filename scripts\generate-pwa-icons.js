const fs = require('fs');
const path = require('path');
const { createCanvas, loadImage } = require('canvas');

// Icon sizes needed for PWA
const sizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Shortcut icon sizes
const shortcutSizes = [192];
const shortcutIcons = ['dashboard', 'tasks', 'messages', 'post-task'];

// Path to the source SVG
const svgPath = path.join(__dirname, '../public/icons/icon-base.svg');

// Path to the output directory
const outputDir = path.join(__dirname, '../public/icons');

// Ensure the output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Function to generate a PNG from the SVG
async function generatePNG(size, outputPath) {
  try {
    // Create a canvas with the desired size
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // Load the SVG image
    const image = await loadImage(svgPath);
    
    // Draw the image on the canvas
    ctx.drawImage(image, 0, 0, size, size);
    
    // Convert the canvas to a PNG buffer
    const buffer = canvas.toBuffer('image/png');
    
    // Write the buffer to a file
    fs.writeFileSync(outputPath, buffer);
    
    console.log(`Generated ${outputPath}`);
  } catch (error) {
    console.error(`Error generating ${outputPath}:`, error);
  }
}

// Generate the main app icons
async function generateIcons() {
  console.log('Generating PWA icons...');
  
  for (const size of sizes) {
    const outputPath = path.join(outputDir, `icon-${size}x${size}.png`);
    await generatePNG(size, outputPath);
  }
  
  // Generate shortcut icons
  for (const shortcut of shortcutIcons) {
    for (const size of shortcutSizes) {
      const outputPath = path.join(outputDir, `${shortcut}.png`);
      await generatePNG(size, outputPath);
    }
  }
  
  console.log('Icon generation complete!');
}

// Run the icon generation
generateIcons().catch(console.error);
