/**
 * <PERSON><PERSON><PERSON> to get user IDs from the database
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

// Initialize Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function getUserIds() {
  try {
    console.log('Fetching user IDs from the database...');

    // Get admin user ID using raw SQL query
    const { data: adminData, error: adminError } = await supabase
      .rpc('get_user_id_by_email', { email_param: '<EMAIL>' });

    if (adminError) {
      console.error('Error fetching admin user:', adminError);

      // Fallback: Try to get users directly
      console.log('Trying alternative method to get users...');
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('id, email')
        .limit(10);

      if (usersError) {
        console.error('Error fetching users:', usersError);
      } else {
        console.log('Available users:');
        users.forEach(user => {
          console.log(`- ${user.email}: ${user.id}`);
        });
      }
    } else {
      console.log(`Admin User (<EMAIL>): ${adminData}`);
    }

    // Get supplier user ID
    const { data: supplierData, error: supplierError } = await supabase
      .rpc('get_user_id_by_email', { email_param: '<EMAIL>' });

    if (supplierError) {
      console.error('Error fetching supplier user:', supplierError);
    } else {
      console.log(`Supplier User (<EMAIL>): ${supplierData.id}`);
    }

    console.log('\nUpdate your .env file with these IDs:');
    console.log(`ADMIN_USER_ID=${adminData?.id || 'not-found'}`);
    console.log(`SUPPLIER_USER_ID=${supplierData?.id || 'not-found'}`);

  } catch (error) {
    console.error('Error fetching user IDs:', error);
  }
}

getUserIds();
