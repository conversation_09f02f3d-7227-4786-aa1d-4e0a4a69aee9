#!/usr/bin/env node

/**
 * Install Git Hooks for Security
 * 
 * This script installs git hooks to prevent committing secrets and API keys
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(level, message) {
  const color = {
    ERROR: colors.red,
    WARNING: colors.yellow,
    SUCCESS: colors.green,
    INFO: colors.blue
  }[level] || colors.reset;
  
  console.log(`${color}[${level}]${colors.reset} ${message}`);
}

/**
 * Install git hooks
 */
function installGitHooks() {
  log('INFO', 'Installing git hooks for security...');
  
  const gitHooksDir = path.join(projectRoot, '.git', 'hooks');
  const sourceHooksDir = path.join(projectRoot, '.githooks');
  
  // Check if .git directory exists
  if (!fs.existsSync(path.join(projectRoot, '.git'))) {
    log('ERROR', 'Not a git repository. Please run this from the project root.');
    process.exit(1);
  }
  
  // Create hooks directory if it doesn't exist
  if (!fs.existsSync(gitHooksDir)) {
    fs.mkdirSync(gitHooksDir, { recursive: true });
    log('INFO', 'Created .git/hooks directory');
  }
  
  // Check if source hooks directory exists
  if (!fs.existsSync(sourceHooksDir)) {
    log('ERROR', '.githooks directory not found. Please ensure it exists.');
    process.exit(1);
  }
  
  // Install each hook
  const hooks = fs.readdirSync(sourceHooksDir);
  let installedCount = 0;
  
  hooks.forEach(hookName => {
    const sourcePath = path.join(sourceHooksDir, hookName);
    const targetPath = path.join(gitHooksDir, hookName);
    
    try {
      // Copy the hook file
      fs.copyFileSync(sourcePath, targetPath);
      
      // Make it executable (Unix/Linux/Mac)
      if (process.platform !== 'win32') {
        fs.chmodSync(targetPath, 0o755);
      }
      
      log('SUCCESS', `Installed ${hookName} hook`);
      installedCount++;
      
    } catch (error) {
      log('ERROR', `Failed to install ${hookName} hook: ${error.message}`);
    }
  });
  
  if (installedCount > 0) {
    log('SUCCESS', `Successfully installed ${installedCount} git hook(s)`);
    log('INFO', 'Git hooks will now check for secrets before each commit');
    log('INFO', 'To bypass hook checks (not recommended): git commit --no-verify');
  } else {
    log('WARNING', 'No git hooks were installed');
  }
}

/**
 * Test git hooks
 */
function testGitHooks() {
  log('INFO', 'Testing git hooks...');
  
  const gitHooksDir = path.join(projectRoot, '.git', 'hooks');
  const preCommitHook = path.join(gitHooksDir, 'pre-commit');
  
  if (!fs.existsSync(preCommitHook)) {
    log('ERROR', 'Pre-commit hook not found. Please install hooks first.');
    return false;
  }
  
  // Create a test file with a fake API key
  const testFile = path.join(projectRoot, 'test-secret.txt');
  const testContent = 'API_KEY="sk_test_1234567890abcdef1234567890abcdef12345678"';
  
  try {
    fs.writeFileSync(testFile, testContent);
    
    // Stage the test file
    const { execSync } = require('child_process');
    execSync('git add test-secret.txt', { cwd: projectRoot });
    
    log('INFO', 'Created test file with fake API key');
    log('INFO', 'The pre-commit hook should detect this and block the commit');
    log('WARNING', 'Run "git commit -m test" to test the hook (it should fail)');
    log('INFO', 'Then run "git reset HEAD test-secret.txt && rm test-secret.txt" to clean up');
    
    return true;
    
  } catch (error) {
    log('ERROR', `Failed to create test file: ${error.message}`);
    return false;
  }
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  
  console.log(`${colors.bold}🔒 Git Hooks Security Installer${colors.reset}\n`);
  
  if (args.includes('--test')) {
    testGitHooks();
  } else {
    installGitHooks();
    
    console.log(`\n${colors.bold}Next Steps:${colors.reset}`);
    console.log(`${colors.green}1. Test the hooks: node scripts/install-git-hooks.js --test${colors.reset}`);
    console.log(`${colors.green}2. The hooks will now prevent committing secrets${colors.reset}`);
    console.log(`${colors.green}3. Use environment variables for all sensitive data${colors.reset}`);
  }
}

// Run the installer
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { installGitHooks, testGitHooks };
