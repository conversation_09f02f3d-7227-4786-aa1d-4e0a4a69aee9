#!/usr/bin/env node

/**
 * Production build script to remove console.log statements
 * This script removes console.log, console.debug, and console.info statements
 * while preserving console.warn and console.error for production monitoring
 */

const fs = require('fs');
const path = require('path');

// Directories to process
const directories = [
  'src',
  'server',
  'api'
];

// File extensions to process
const extensions = ['.ts', '.tsx', '.js', '.jsx'];

// Console methods to remove (keep warn and error for production monitoring)
const consoleMethodsToRemove = [
  'console.log',
  'console.debug', 
  'console.info',
  'console.trace'
];

/**
 * Check if file should be processed
 */
function shouldProcessFile(filePath) {
  const ext = path.extname(filePath);
  return extensions.includes(ext);
}

/**
 * Remove console statements from file content
 */
function removeConsoleStatements(content) {
  let modifiedContent = content;
  
  // Remove simple console.log statements
  consoleMethodsToRemove.forEach(method => {
    // Match console.method(...) statements
    const regex = new RegExp(`\\s*${method.replace('.', '\\.')}\\s*\\([^;]*\\);?\\s*`, 'g');
    modifiedContent = modifiedContent.replace(regex, '');
    
    // Match multiline console statements
    const multilineRegex = new RegExp(`\\s*${method.replace('.', '\\.')}\\s*\\([\\s\\S]*?\\);?\\s*`, 'g');
    modifiedContent = modifiedContent.replace(multilineRegex, '');
  });
  
  // Remove debug comments that might contain sensitive info
  modifiedContent = modifiedContent.replace(/\/\/\s*DEBUG:.*$/gm, '');
  modifiedContent = modifiedContent.replace(/\/\/\s*CRITICAL DEBUG.*$/gm, '');
  modifiedContent = modifiedContent.replace(/\/\*\s*DEBUG[\s\S]*?\*\//g, '');
  
  return modifiedContent;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const modifiedContent = removeConsoleStatements(content);
    
    if (content !== modifiedContent) {
      fs.writeFileSync(filePath, modifiedContent, 'utf8');
      console.log(`✅ Cleaned: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Process directory recursively
 */
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  let filesProcessed = 0;
  
  items.forEach(item => {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and dist directories
      if (!['node_modules', 'dist', '.git', '.vercel'].includes(item)) {
        filesProcessed += processDirectory(itemPath);
      }
    } else if (stat.isFile() && shouldProcessFile(itemPath)) {
      if (processFile(itemPath)) {
        filesProcessed++;
      }
    }
  });
  
  return filesProcessed;
}

/**
 * Main execution
 */
function main() {
  console.log('🧹 Removing console logs for production build...\n');
  
  let totalFilesProcessed = 0;
  
  directories.forEach(dir => {
    if (fs.existsSync(dir)) {
      console.log(`📁 Processing directory: ${dir}`);
      const filesProcessed = processDirectory(dir);
      totalFilesProcessed += filesProcessed;
      console.log(`   ${filesProcessed} files cleaned\n`);
    } else {
      console.log(`⚠️  Directory not found: ${dir}\n`);
    }
  });
  
  console.log(`✨ Production cleanup complete!`);
  console.log(`📊 Total files cleaned: ${totalFilesProcessed}`);
  
  if (totalFilesProcessed > 0) {
    console.log('\n🔒 Console logs removed for production security');
    console.log('⚠️  console.warn and console.error preserved for monitoring');
  }
}

// Run the script
main();
