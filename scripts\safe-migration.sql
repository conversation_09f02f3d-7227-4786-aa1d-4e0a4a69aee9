-- Safe Database Migration Template
-- Always use transactions and rollback plans

BEGIN;

-- 1. Add new columns (safe - doesn't break existing code)
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS new_field TEXT;

-- 2. Create new tables (safe)
CREATE TABLE IF NOT EXISTS new_feature_table (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Add indexes (safe - can be done online)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_new_field ON tasks(new_field);

-- 4. Update data (be careful with large tables)
-- Use batched updates for large datasets
UPDATE tasks 
SET new_field = 'default_value' 
WHERE new_field IS NULL 
  AND created_at > NOW() - INTERVAL '1 day'  -- Limit scope
LIMIT 1000;  -- Batch processing

-- 5. Rollback plan (always have one)
-- ROLLBACK;  -- Uncomment if something goes wrong

COMMIT;

-- Post-deployment cleanup (run separately after confirming success)
-- DROP COLUMN old_field;  -- Only after new code is deployed and stable
