/**
 * This script sets up a scheduled job to trigger the compliance-notifications Edge Function.
 * It can be run manually or set up as a cron job on the server.
 *
 * To run once: node scripts/schedule-compliance-notifications.js --run-now
 * To run as a scheduler: node scripts/schedule-compliance-notifications.js
 */

const fetch = require('node-fetch');
const cron = require('node-cron');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const FUNCTION_NAME = 'compliance-notifications';
const SCHEDULE = '0 7 * * *'; // Run daily at 7:00 AM

// Function to trigger the Edge Function
async function triggerComplianceNotifications() {
  try {
    console.log(`[${new Date().toISOString()}] Triggering compliance notifications...`);

    if (!SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('SUPABASE_SERVICE_ROLE_KEY is not set. Please set this environment variable.');
    }

    const response = await fetch(`${SUPABASE_URL}/functions/v1/${FUNCTION_NAME}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to trigger function: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log(`[${new Date().toISOString()}] Compliance notifications triggered successfully:`, result);
    return result;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error triggering compliance notifications:`, error);
    throw error;
  }
}

// Check if running as a script or being imported
if (require.main === module) {
  // If run directly, determine if we should run once or schedule
  const args = process.argv.slice(2);

  if (args.includes('--run-now') || args.length === 0) {
    // Run once immediately
    console.log('Running compliance notifications once...');
    triggerComplianceNotifications()
      .then((result) => {
        console.log('Result:', JSON.stringify(result, null, 2));
        console.log('Done!');
      })
      .catch(err => {
        console.error('Failed:', err);
        process.exit(1);
      });
  } else {
    // Schedule the job
    console.log(`Scheduling compliance notifications to run ${SCHEDULE}`);
    cron.schedule(SCHEDULE, () => {
      triggerComplianceNotifications()
        .catch(err => console.error('Scheduled run failed:', err));
    });
    console.log('Scheduler running. Press Ctrl+C to exit.');
  }
}

module.exports = { triggerComplianceNotifications };
