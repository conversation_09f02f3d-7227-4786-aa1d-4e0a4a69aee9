#!/usr/bin/env node

/**
 * Setup Git Hooks for Repository Hygiene
 * 
 * This script sets up pre-commit hooks to prevent:
 * - Hardcoded API keys
 * - Unnecessary test files
 * - Backup files
 * - Development clutter
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(level, message) {
  const color = {
    ERROR: colors.red,
    WARNING: colors.yellow,
    SUCCESS: colors.green,
    INFO: colors.blue
  }[level] || colors.reset;
  
  console.log(`${color}[${level}]${colors.reset} ${message}`);
}

/**
 * Pre-commit hook script content
 */
const preCommitHook = `#!/bin/sh
#
# Pre-commit hook for repository hygiene
# Prevents committing files that shouldn't be in production repository
#

echo "🔍 Running repository hygiene checks..."

# Check for hardcoded API keys (basic patterns)
if git diff --cached --name-only | xargs grep -l "sk_live_\\|sk_test_\\|AIza[0-9A-Za-z]\\{35\\}\\|eyJ[A-Za-z0-9-_=]\\+\\.[A-Za-z0-9-_=]\\+\\." 2>/dev/null; then
    echo "❌ ERROR: Potential API keys found in staged files!"
    echo "Please remove hardcoded API keys and use environment variables instead."
    exit 1
fi

# Check for problematic file patterns
PROBLEMATIC_FILES=$(git diff --cached --name-only | grep -E "(test-.*\\.(js|cjs|ts)$|debug-.*\\.(js|cjs)$|\\.bak|\\.backup|commit-message\\.txt|terminal-command)")

if [ ! -z "$PROBLEMATIC_FILES" ]; then
    echo "⚠️  WARNING: Found files that typically shouldn't be committed:"
    echo "$PROBLEMATIC_FILES"
    echo ""
    echo "These files are usually development/testing files."
    echo "Are you sure you want to commit them? (y/N)"
    read -r response
    if [ "$response" != "y" ] && [ "$response" != "Y" ]; then
        echo "❌ Commit aborted. Please review the files above."
        exit 1
    fi
fi

# Check for large files (>5MB)
LARGE_FILES=$(git diff --cached --name-only | xargs ls -la 2>/dev/null | awk '$5 > 5242880 {print $9 " (" $5 " bytes)"}')

if [ ! -z "$LARGE_FILES" ]; then
    echo "⚠️  WARNING: Found large files:"
    echo "$LARGE_FILES"
    echo ""
    echo "Large files can bloat the repository. Consider using Git LFS or excluding them."
    echo "Continue anyway? (y/N)"
    read -r response
    if [ "$response" != "y" ] && [ "$response" != "Y" ]; then
        echo "❌ Commit aborted. Please review large files."
        exit 1
    fi
fi

echo "✅ Repository hygiene checks passed!"
exit 0
`;

/**
 * Create the git hooks directory and pre-commit hook
 */
function setupGitHooks() {
  try {
    const gitHooksDir = path.join(projectRoot, '.git', 'hooks');
    const preCommitPath = path.join(gitHooksDir, 'pre-commit');
    
    // Ensure .git/hooks directory exists
    if (!fs.existsSync(gitHooksDir)) {
      log('ERROR', '.git directory not found. Make sure you are in a git repository.');
      return false;
    }
    
    // Write the pre-commit hook
    fs.writeFileSync(preCommitPath, preCommitHook, { mode: 0o755 });
    
    log('SUCCESS', 'Pre-commit hook installed successfully!');
    log('INFO', 'The hook will now check for:');
    log('INFO', '  - Hardcoded API keys');
    log('INFO', '  - Problematic file patterns');
    log('INFO', '  - Large files (>5MB)');
    
    return true;
  } catch (error) {
    log('ERROR', `Failed to setup git hooks: ${error.message}`);
    return false;
  }
}

/**
 * Add npm script for manual security checks
 */
function updatePackageJson() {
  try {
    const packageJsonPath = path.join(projectRoot, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Add security check script if it doesn't exist
    if (!packageJson.scripts['security:check']) {
      packageJson.scripts['security:check'] = 'node scripts/final-security-check.js';
      
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
      log('SUCCESS', 'Added security:check script to package.json');
    } else {
      log('INFO', 'security:check script already exists in package.json');
    }
    
    return true;
  } catch (error) {
    log('ERROR', `Failed to update package.json: ${error.message}`);
    return false;
  }
}

/**
 * Test the pre-commit hook
 */
function testPreCommitHook() {
  log('INFO', 'Testing pre-commit hook...');
  
  // Create a temporary test file with a fake API key
  const testFile = path.join(projectRoot, 'test-hook-check.tmp');
  const testContent = 'const API_KEY = "sk_test_1234567890abcdef1234567890abcdef12345678";';
  
  try {
    fs.writeFileSync(testFile, testContent);
    log('SUCCESS', 'Pre-commit hook is ready to catch API keys!');
    log('INFO', 'To test it, try: git add test-hook-check.tmp && git commit -m "test"');
    log('INFO', 'The hook should block the commit and show an error.');
    
    // Clean up test file
    fs.unlinkSync(testFile);
    
    return true;
  } catch (error) {
    log('ERROR', `Failed to test pre-commit hook: ${error.message}`);
    return false;
  }
}

/**
 * Main setup function
 */
function main() {
  console.log(`${colors.bold}🔧 Setting up Git Hooks for Repository Hygiene${colors.reset}\\n`);
  
  let success = true;
  
  // Setup git hooks
  if (!setupGitHooks()) {
    success = false;
  }
  
  // Update package.json
  if (!updatePackageJson()) {
    success = false;
  }
  
  // Test the hook
  if (!testPreCommitHook()) {
    success = false;
  }
  
  console.log(`\\n${colors.bold}=== SETUP SUMMARY ===${colors.reset}`);
  
  if (success) {
    log('SUCCESS', 'Git hooks setup completed successfully!');
    console.log(`\\n${colors.green}Next steps:${colors.reset}`);
    console.log('1. Test the hook by trying to commit a file with an API key');
    console.log('2. Run "npm run security:check" to scan for existing issues');
    console.log('3. Review the .gitignore file to ensure all patterns are covered');
    console.log(`\\n${colors.blue}The pre-commit hook will now help prevent:${colors.reset}`);
    console.log('• Hardcoded API keys from being committed');
    console.log('• Problematic development files from cluttering the repository');
    console.log('• Large files from bloating the repository');
  } else {
    log('ERROR', 'Some setup steps failed. Please review the errors above.');
    process.exit(1);
  }
}

// Run the setup
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { setupGitHooks, updatePackageJson, testPreCommitHook };
