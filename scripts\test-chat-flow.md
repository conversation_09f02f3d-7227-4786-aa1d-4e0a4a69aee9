# Chat Flow Test Script

This script will guide you through testing the complete chat flow between an admin and a supplier, including the new "Express Interest" and "Questions" stages.

## Prerequisites

- Two browser windows or tabs (one in incognito/private mode)
- Admin account: <EMAIL>
- Supplier account: <EMAIL> (replace with your actual supplier test account)

## Test Setup

### Step 1: Create a Test Task (Admin)

1. Log <NAME_EMAIL>
2. Navigate to Dashboard
3. Click "Create Task"
4. Fill in the task details:
   - Title: "Chat Flow Test Task"
   - Description: "This is a test task to verify the chat flow functionality."
   - Category: "Maintenance"
   - Budget: £100
   - Due Date: Select a date 1 week from now
   - Location: "Test Location"
   - Visibility: "Public"
5. Click "Create Task"
6. Note the task ID from the URL (e.g., /tasks/[task-id])

### Step 2: Prepare Supplier Account

1. Open a new incognito/private browser window
2. Log <NAME_EMAIL>
3. Navigate to the Tasks page
4. Find the "Chat Flow Test Task" you just created

## Test Flow

### Phase 1: Express Interest

1. **Supplier Action**: Express Interest
   - As <EMAIL>, view the task details
   - Click the "Express Interest" button
   - Verify that you're redirected to the chat interface
   - Verify that an initial message is automatically sent: "Hi, I'm interested in this task and would like to discuss the requirements."
   - Verify that a system message appears: "A supplier has expressed interest in this task."

2. **Admin Response**: Check Messages
   - Switch to the admin browser window
   - Navigate to the task details page
   - Click on the "Messages" tab
   - Verify that you see the supplier's thread in the tabs
   - Verify that you see the initial message and system message
   - Send a response: "Thank you for your interest. What experience do you have with this type of work?"

3. **Supplier Response**: Continue Discussion
   - Switch back to the supplier browser window
   - Verify that you can see the admin's message
   - Reply with: "I have 5 years of experience with similar projects. Could you provide more details about the specific requirements?"

### Phase 2: Questions and Discussion

1. **Admin Action**: Provide Details
   - As admin, reply: "We need this completed within 3 days. The work involves [add specific details]. Does that timeline work for you?"
   - Verify the message appears correctly

2. **Supplier Action**: Confirm and Submit Offer
   - As supplier, reply: "Yes, that timeline works for me. I can start immediately."
   - Look for the "Submit an Offer" button at the bottom of the chat
   - Click "Submit an Offer"
   - Fill in the offer form:
     - Amount: £95
     - Message: "Based on our discussion, I can complete this work for £95. I'll ensure it meets all requirements and is delivered on time."
   - Click "Submit Formal Offer"
   - Verify that a system message appears: "<EMAIL> has submitted a formal offer of £95.00."
   - Verify that another system message appears: "Discussion phase has started. Please discuss requirements before submitting formal offers."

### Phase 3: Offer Review and Acceptance

1. **Admin Action**: Review and Accept Offer
   - As admin, verify you can see the offer in the chat
   - Navigate to the "Offers" section of the task
   - Verify the offer appears with the correct amount (£95)
   - Click "Accept Offer"
   - Verify that a system message appears in the chat: "Task has been assigned to a supplier."
   - Verify that the task status changes to "Assigned"

2. **Supplier Action**: Start Work
   - As supplier, navigate to the task
   - Verify you see a "Start Task" button
   - Click "Accept & Start Task"
   - Verify that a system message appears: "Task is now in progress. Work has started."
   - Verify that the task status changes to "In Progress"

### Phase 4: Task Completion

1. **Supplier Action**: Complete Task
   - As supplier, navigate to the task
   - Click "Mark as Completed"
   - Verify that a system message appears: "Supplier has marked this task as completed."
   - Verify that the task status changes to "Completed"

2. **Admin Action**: Approve Completion
   - As admin, navigate to the task
   - Click "Approve Completed Work"
   - Verify that a system message appears: "Task has been approved by the administrator."
   - Verify that the task status changes to "Confirmed"

## Verification Points

For each step, verify:
1. Messages appear in the correct thread
2. System messages are displayed properly
3. Task status updates correctly
4. UI elements (buttons, forms) appear and function as expected
5. No errors appear in the browser console

## Error Handling Tests

1. **Multiple Suppliers**:
   - Log in as a different supplier
   - Express interest in the same task
   - Verify that the admin sees separate tabs for each supplier
   - Verify that each supplier only sees their own conversation

2. **Message Notifications**:
   - Check if notifications appear for new messages
   - Verify that system messages don't trigger notification errors

## Cleanup

After testing:
1. As admin, you can delete the test task if desired
2. Log out of both accounts

## Notes

- If you encounter any issues, note the specific step, expected behavior, and actual behavior
- Check browser console for any errors
- Take screenshots of any unexpected behavior
