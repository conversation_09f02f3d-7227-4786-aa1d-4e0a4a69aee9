/**
 * Test script to load test the invitation email system with 50 users
 *
 * This script:
 * 1. Generates 50 test email addresses
 * 2. Sends invitation emails to all of them
 * 3. Tracks the success/failure of each invitation
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import crypto from 'crypto';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.VITE_SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Configuration
const NUM_USERS = 50;
const ORGANIZATION_ID = null; // We'll get this from the database
const ROLES = ['teacher', 'maintenance', 'support', 'admin'];
const TEST_EMAIL_DOMAIN = 'test.classtasker.com';
const RESULTS_FILE = path.join(__dirname, 'invitation-test-results.json');

// Helper function to generate a random test email
function generateTestEmail(index) {
  const timestamp = Date.now();
  return `test-user-${index}-${timestamp}@${TEST_EMAIL_DOMAIN}`;
}

// Helper function to get a random role
function getRandomRole() {
  const randomIndex = Math.floor(Math.random() * ROLES.length);
  return ROLES[randomIndex];
}

// Global variable to store a valid user ID
let validUserId = "4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd"; // Using the specified user ID

// Helper function to get organization ID for the specified user
async function getOrganizationId() {
  console.log(`Using specified user ID: ${validUserId}`);

  // Get the organization ID for this user
  console.log('Fetching organization for the specified user...');

  const { data: userData, error: userError } = await supabase
    .from('profiles')
    .select('organization_id')
    .eq('id', validUserId)
    .single();

  if (userError) {
    console.error('Error fetching user organization:', userError);
    throw new Error('Could not fetch organization ID for the specified user.');
  }

  if (!userData.organization_id) {
    throw new Error('The specified user does not have an associated organization.');
  }

  // Get the organization details
  const { data: orgData, error: orgError } = await supabase
    .from('organizations')
    .select('id, name')
    .eq('id', userData.organization_id)
    .single();

  if (orgError) {
    console.error('Error fetching organization details:', orgError);
    throw new Error('Could not fetch organization details.');
  }

  console.log(`Found organization: ${orgData.name} (${orgData.id})`);

  return orgData.id;
}

// Main function to send invitations
async function sendInvitations() {
  try {
    console.log(`Starting invitation load test with ${NUM_USERS} users...`);

    // Get organization ID
    const organizationId = await getOrganizationId();
    console.log(`Using organization ID: ${organizationId}`);

    // Results tracking
    const results = {
      total: NUM_USERS,
      successful: 0,
      failed: 0,
      startTime: new Date().toISOString(),
      endTime: null,
      details: []
    };

    // Send invitations
    for (let i = 0; i < NUM_USERS; i++) {
      const email = generateTestEmail(i + 1);
      const role = getRandomRole();

      console.log(`[${i + 1}/${NUM_USERS}] Sending invitation to ${email} with role ${role}...`);

      try {
        // Start timing
        const startTime = Date.now();

        // Generate a random UUID for the invitation
        const invitationId = crypto.randomUUID();

        // Generate a random token
        const token = crypto.randomUUID().replace(/-/g, '') + crypto.randomUUID().replace(/-/g, '');

        // Use the valid user ID we found earlier
        const inviterId = validUserId;

        // Insert the invitation directly using the Supabase API
        const now = new Date().toISOString();
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now

        const { data: invitation, error: insertError } = await supabase
          .from('user_invitations')
          .insert({
            id: invitationId,
            email: email,
            organization_id: organizationId,
            role: role,
            invited_by: inviterId,
            status: 'pending',
            token: token,
            created_at: now,
            expires_at: expiresAt.toISOString()
          })
          .select()
          .single();

        if (insertError) {
          throw insertError;
        }

        // Get organization name
        const { data: orgData, error: orgError } = await supabase
          .from('organizations')
          .select('name')
          .eq('id', organizationId)
          .single();

        if (orgError) {
          throw orgError;
        }

        // Send invitation email using the Edge Function
        const { data: emailData, error: emailError } = await supabase.functions.invoke('support-email-sender', {
          body: {
            from: 'Classtasker <<EMAIL>>',
            to: email,
            subject: `Invitation to join ${orgData.name} on Classtasker`,
            name: 'Classtasker',
            email: '<EMAIL>',
            support_type: 'Invitation',
            message: `You have been invited to join ${orgData.name} as a ${role} on Classtasker.`,
            html_content: generateEmailHtml(email, invitation.token, orgData.name, role)
          }
        });

        if (emailError) {
          throw emailError;
        }

        // Calculate time taken
        const endTime = Date.now();
        const timeTaken = endTime - startTime;

        // Record success
        results.successful++;
        results.details.push({
          email,
          role,
          status: 'success',
          invitationId,
          token: invitation.token,
          timeTaken: `${timeTaken}ms`
        });

        console.log(`✅ Successfully sent invitation to ${email} (${timeTaken}ms)`);
      } catch (error) {
        // Record failure
        results.failed++;
        results.details.push({
          email,
          role,
          status: 'failed',
          error: error.message || String(error)
        });

        console.error(`❌ Failed to send invitation to ${email}:`, error);
      }

      // Add a small delay between requests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Record end time
    results.endTime = new Date().toISOString();

    // Calculate total time
    const totalTimeMs = new Date(results.endTime) - new Date(results.startTime);
    const totalTimeSec = totalTimeMs / 1000;

    // Add summary
    results.summary = {
      totalTime: `${totalTimeSec.toFixed(2)} seconds`,
      averageTimePerInvitation: `${(totalTimeMs / NUM_USERS).toFixed(2)} ms`,
      successRate: `${((results.successful / NUM_USERS) * 100).toFixed(2)}%`
    };

    // Save results to file
    fs.writeFileSync(RESULTS_FILE, JSON.stringify(results, null, 2));

    // Print summary
    console.log('\n===== INVITATION LOAD TEST RESULTS =====');
    console.log(`Total invitations: ${NUM_USERS}`);
    console.log(`Successful: ${results.successful}`);
    console.log(`Failed: ${results.failed}`);
    console.log(`Success rate: ${results.summary.successRate}`);
    console.log(`Total time: ${results.summary.totalTime}`);
    console.log(`Average time per invitation: ${results.summary.averageTimePerInvitation}`);
    console.log(`Detailed results saved to: ${RESULTS_FILE}`);

  } catch (error) {
    console.error('Error in load test:', error);
  }
}

// Generate HTML email content
function generateEmailHtml(email, token, organizationName, role) {
  const invitationUrl = `${process.env.VITE_APP_URL || 'https://www.classtasker.com'}/invitation/accept?token=${token}&email=${encodeURIComponent(email)}`;

  return `
    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml">
    <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Invitation to Classtasker</title>
    </head>
    <body style="font-family: Arial, Helvetica, sans-serif; line-height: 1.6; color: #333333; margin: 0; padding: 0; background-color: #f5f5f5;">
      <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
        <tr>
          <td style="background-color: #4f46e5; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0;">
            <h1 style="margin: 0; font-size: 24px;">You've been invited to join ${organizationName}</h1>
          </td>
        </tr>
        <tr>
          <td style="padding: 20px; border: 1px solid #e0e0e0; border-top: none; border-radius: 0 0 5px 5px;">
            <p style="margin-top: 0;">Hello,</p>
            <p>You have been invited to join <strong>${organizationName}</strong> as a <strong>${role}</strong> on Classtasker.</p>
            <p>Classtasker is a platform that connects schools with maintenance workers and suppliers to efficiently manage tasks and services.</p>
            <p>To accept this invitation, please click the button below:</p>
            <table border="0" cellpadding="0" cellspacing="0" width="100%">
              <tr>
                <td align="center" style="padding: 20px 0;">
                  <table border="0" cellpadding="0" cellspacing="0">
                    <tr>
                      <td align="center" bgcolor="#4f46e5" style="border-radius: 5px;">
                        <a href="${invitationUrl}" target="_blank" style="display: inline-block; padding: 12px 24px; font-family: Arial, sans-serif; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 5px; font-weight: bold;">Accept Invitation</a>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
            <p>Or copy and paste this URL into your browser:</p>
            <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 3px; font-size: 14px;">${invitationUrl}</p>
            <p>This invitation will expire in 7 days.</p>
            <p style="margin-bottom: 0;">If you have any questions, please contact the organization administrator.</p>
          </td>
        </tr>
        <tr>
          <td style="padding: 20px; font-size: 12px; color: #666; text-align: center;">
            <p style="margin-top: 0;">This is an automated message from Classtasker. Please do not reply to this email.</p>
            <p style="margin-bottom: 0;">&copy; ${new Date().getFullYear()} Classtasker. All rights reserved.</p>
          </td>
        </tr>
      </table>
    </body>
    </html>
  `;
}

// Run the test
sendInvitations().catch(console.error);
