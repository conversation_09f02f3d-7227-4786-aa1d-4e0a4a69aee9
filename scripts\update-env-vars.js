/**
 * <PERSON><PERSON>t to update environment variables in .env.local file
 * 
 * Usage:
 * node scripts/update-env-vars.js
 * 
 * This script will:
 * 1. Check if .env.local exists, if not create it
 * 2. Add VITE_SITE_URL if it doesn't exist
 * 3. Update VITE_SITE_URL if it exists but with a different value
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Default values
const defaultValues = {
  VITE_SITE_URL: 'https://classtasker.com'
};

// Path to .env.local file
const envFilePath = path.resolve(process.cwd(), '.env.local');

// Check if .env.local exists
const envFileExists = fs.existsSync(envFilePath);

// Function to parse .env file
function parseEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return {};
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const envVars = {};
  
  lines.forEach(line => {
    // Skip comments and empty lines
    if (line.startsWith('#') || line.trim() === '') {
      return;
    }
    
    const match = line.match(/^([^=]+)=(.*)$/);
    if (match) {
      const key = match[1].trim();
      const value = match[2].trim();
      envVars[key] = value;
    }
  });
  
  return envVars;
}

// Function to write .env file
function writeEnvFile(filePath, envVars) {
  const lines = Object.entries(envVars).map(([key, value]) => `${key}=${value}`);
  fs.writeFileSync(filePath, lines.join('\n') + '\n');
}

// Main function
async function main() {
  console.log('Updating environment variables...');
  
  // Parse existing .env file if it exists
  const existingEnvVars = envFileExists ? parseEnvFile(envFilePath) : {};
  
  // Check if VITE_SITE_URL exists and has the correct value
  if (!existingEnvVars.VITE_SITE_URL) {
    console.log(`VITE_SITE_URL not found. Adding with default value: ${defaultValues.VITE_SITE_URL}`);
    existingEnvVars.VITE_SITE_URL = defaultValues.VITE_SITE_URL;
  } else if (existingEnvVars.VITE_SITE_URL !== defaultValues.VITE_SITE_URL) {
    console.log(`VITE_SITE_URL found with value: ${existingEnvVars.VITE_SITE_URL}`);
    
    // Ask if user wants to update
    const answer = await new Promise(resolve => {
      rl.question(`Do you want to update VITE_SITE_URL to ${defaultValues.VITE_SITE_URL}? (y/n): `, resolve);
    });
    
    if (answer.toLowerCase() === 'y') {
      existingEnvVars.VITE_SITE_URL = defaultValues.VITE_SITE_URL;
      console.log(`VITE_SITE_URL updated to: ${defaultValues.VITE_SITE_URL}`);
    } else {
      console.log('Keeping existing VITE_SITE_URL value');
    }
  } else {
    console.log(`VITE_SITE_URL already set to: ${existingEnvVars.VITE_SITE_URL}`);
  }
  
  // Write updated .env file
  writeEnvFile(envFilePath, existingEnvVars);
  
  console.log('.env.local file updated successfully');
  console.log('\nIMPORTANT: Make sure to add VITE_SITE_URL to your Vercel project settings:');
  console.log('1. Go to the Vercel dashboard');
  console.log('2. Select your project');
  console.log('3. Go to Settings > Environment Variables');
  console.log(`4. Add VITE_SITE_URL with value: ${defaultValues.VITE_SITE_URL}`);
  console.log('5. Deploy your project again to apply the changes');
  
  rl.close();
}

main().catch(error => {
  console.error('Error updating environment variables:', error);
  process.exit(1);
});
