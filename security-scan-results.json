{"totalFiles": 405, "scannedFiles": 405, "vulnerabilities": [{"file": "src\\App.tsx", "line": 57, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierProtectedRoute from \"./components/auth/SupplierProtectedRoute\";", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 57, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierProtectedRoute from \"./components/auth/SupplierProtectedRoute\";", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 68, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierSignup from \"./pages/SupplierSignup\";", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 68, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierSignup from \"./pages/SupplierSignup\";", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 69, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierResources from \"./pages/SupplierResources\";", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 69, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierResources from \"./pages/SupplierResources\";", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 70, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierFAQ from \"./pages/SupplierFAQ\";", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 70, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierFAQ from \"./pages/SupplierFAQ\";", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 104, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SupplierProtectedRoute>", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 106, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "</SupplierProtectedRoute>", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 125, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Route path=\"/supplier/onboarding\" element={", "match": "supplier"}, {"file": "src\\App.tsx", "line": 129, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{React.createElement(React.lazy(() => import('./pages/SupplierOnboarding')))}", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 301, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Route path=\"/supplier-signup\" element={<SupplierSignup />} />", "match": "supplier"}, {"file": "src\\App.tsx", "line": 301, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Route path=\"/supplier-signup\" element={<SupplierSignup />} />", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 302, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Route path=\"/supplier-resources\" element={<SupplierResources />} />", "match": "supplier"}, {"file": "src\\App.tsx", "line": 302, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Route path=\"/supplier-resources\" element={<SupplierResources />} />", "match": "Supplier"}, {"file": "src\\App.tsx", "line": 303, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Route path=\"/supplier-faq\" element={<SupplierFAQ />} />", "match": "supplier"}, {"file": "src\\App.tsx", "line": 303, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Route path=\"/supplier-faq\" element={<SupplierFAQ />} />", "match": "Supplier"}, {"file": "src\\components\\admin\\AdminAssignedTasks.tsx", "line": 83, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "When you assign tasks to staff members or make them available to suppliers, they will appear here.", "match": "supplier"}, {"file": "src\\components\\admin\\AdminSettings.tsx", "line": 36, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "maintenanceMode: z.boolean().default(false),", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminSettings.tsx", "line": 52, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "maintenanceMode: false,", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminSettings.tsx", "line": 268, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "name=\"maintenanceMode\"", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminSettings.tsx", "line": 273, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Maintenance Mode", "match": "Maintenance"}, {"file": "src\\components\\admin\\AdminSettings.tsx", "line": 276, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Put the platform in maintenance mode.", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 176, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const maintenanceUsers = organizationUsers?.filter(user => user.role === 'maintenance') || [];", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 176, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const maintenanceUsers = organizationUsers?.filter(user => user.role === 'maintenance') || [];", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 177, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supportUsers = organizationUsers?.filter(user => user.role === 'support') || [];", "match": "supportUsers = organizationUsers?.filter(user => user.role === 'support')"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 181, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Explicitly exclude teachers from the assignable users list", "match": "teacher"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 182, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const internalUsers = [...maintenanceUsers, ...supportUsers, ...adminUsers];", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 182, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const internalUsers = [...maintenanceUsers, ...supportUsers, ...adminUsers];", "match": "supportUsers, ...adminUsers];\r\n\r\n  // Log the filtered users for debugging (development only)"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 195, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log('DEBUG AdminTaskReview - maintenanceUsers: completed');", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 227, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const [staffRoleFilter, setStaffRoleFilter] = useState<'all' | 'maintenance' | 'support' | 'admin'>('all');", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 227, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const [staffRoleFilter, setStaffRoleFilter] = useState<'all' | 'maintenance' | 'support' | 'admin'>('all');", "match": "support' | 'admin'>('all')"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 232, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": staffRoleFilter === 'maintenance'", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 233, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "? maintenanceUsers", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 234, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": staffRoleFilter === 'support'", "match": "support'\r\n        ? supportUsers\r\n        : adminUsers;\r\n\r\n  // Check if there are no maintenance or"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 244, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (assignmentType === 'internal' && !hasMaintenanceOrSupportStaff && adminUsers.length > 0 && !selectedUserId) {", "match": "Maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 244, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (assignmentType === 'internal' && !hasMaintenanceOrSupportStaff && adminUsers.length > 0 && !selectedUserId) {", "match": "SupportStaff && adminUsers.length > 0 && !selectedUserId)"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 266, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "}, [assignmentType, hasMaintenanceOrSupportStaff, adminUsers.length, user?.id]); // Removed adminUsers from deps", "match": "Maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 266, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "}, [assignmentType, hasMaintenanceOrSupportStaff, adminUsers.length, user?.id]); // Removed adminUsers from deps", "match": "SupportStaff, adminUsers.length, user?.id])"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 334, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: \"The task is now visible to suppliers and has been moved to the Assigned Tasks tab.\",", "match": "supplier"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 404, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "When teachers create new tasks, they will appear here for you to review and assign.", "match": "teacher"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 648, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Assign \"{selectedTask?.title}\" to a staff member or make it available to suppliers.", "match": "supplier"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 665, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Assign to admin, maintenance, or support staff within your organization", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 665, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Assign to admin, maintenance, or support staff within your organization", "match": "support staff within your organization\r\n                  </p>\r\n                </div>\r\n            "}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 701, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Set a budget for suppliers to bid on this task", "match": "supplier"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 724, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "variant={staffRoleFilter === 'maintenance' ? 'default' : 'outline'}", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 726, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "onClick={() => setStaffRoleFilter('maintenance')}", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 729, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span>Maintenance</span>", "match": "Maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 730, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{maintenanceUsers.length > 0 && (", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 731, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Badge variant=\"secondary\" className=\"ml-2\">{maintenanceUsers.length}</Badge>", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 735, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "variant={staffRoleFilter === 'support' ? 'default' : 'outline'}", "match": "support' ? 'default' : 'outline'}\r\n                      size=\"sm\"\r\n                      onClick={("}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 737, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "onClick={() => setStaffRoleFilter('support')}", "match": "support')"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 740, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span>Support</span>", "match": "Support</span>\r\n                      {supportUsers.length > 0 && (\r\n                        <Badge "}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 759, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Show message if no maintenance or support staff */}", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 759, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Show message if no maintenance or support staff */}", "match": "support staff */}\r\n                {!hasMaintenanceOrSupportStaff && (\r\n                  <div class"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 804, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "user.role === 'maintenance' ? 'bg-blue-50 text-blue-700 border-blue-200' :", "match": "maintenance"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 805, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "user.role === 'support' ? 'bg-purple-50 text-purple-700 border-purple-200' :", "match": "support' ? 'bg-purple-50 text-purple-700 border-purple-200' :\r\n                                user."}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 195, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log('DEBUG AdminTaskReview - maintenanceUsers: completed');", "match": "console.log('DEBUG AdminTaskReview - maintenanceUsers: completed')"}, {"file": "src\\components\\admin\\AdminTaskReview.tsx", "line": 297, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log(`Assigning task ${selectedTask.id} to ${selectedUserId || 'public'} with visibility ${assignmentType}`);", "match": "console.log(`Assigning task ${selectedTask.id} to ${selectedUserId || 'public'} with visibility ${as"}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 8, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "interface SupplierProtectedRouteProps {", "match": "Supplier"}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 14, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* A route component that only allows suppliers to access the content", "match": "supplier"}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 15, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* Non-suppliers will see an access denied message", "match": "supplier"}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 17, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const SupplierProtectedRoute: React.FC<SupplierProtectedRouteProps> = ({", "match": "Supplier"}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 17, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const SupplierProtectedRoute: React.FC<SupplierProtectedRouteProps> = ({", "match": "Supplier"}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 21, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, isSupplier, isLoading } = useAuth();", "match": "Supplier"}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 37, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// If user is not a supplier, show enhanced desktop marketplace information", "match": "supplier"}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 38, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!isSupplier) {", "match": "Supplier"}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "The marketplace is where professional suppliers can view and bid on tasks that need external support.", "match": "supplier"}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "The marketplace is where professional suppliers can view and bid on tasks that need external support.", "match": "support.\n              </p>\n            </div>\n\n            {/* How It Works Section */}\n           "}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 153, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Learn About Supplier Accounts", "match": "Supplier"}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 163, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// User is a supplier, allow access", "match": "supplier"}, {"file": "src\\components\\auth\\SupplierProtectedRoute.tsx", "line": 167, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "export default SupplierProtectedRoute;", "match": "Supplier"}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 13, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isTeacher: boolean;", "match": "Teacher"}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 15, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isMaintenance: boolean;", "match": "Maintenance"}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 16, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupport?: boolean;", "match": "Support?: boolean;\n  isSupplier: boolean;\n}\n\nconst ActionItems: React.FC<ActionItemsProps> = ({\n  ta"}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 38, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (is<PERSON>eacher) {", "match": "Teacher"}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 39, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For teachers: Tasks pending admin review", "match": "teacher"}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 62, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isMaintenance) {", "match": "Maintenance"}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 63, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For maintenance: Assigned tasks not yet started", "match": "maintenance"}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 69, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isSupport) {", "match": "Support)"}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 70, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For support: Assigned tasks not yet started", "match": "support: Assigned tasks not yet started\n    actionItems = tasks.filter(task =>\n      task.status ==="}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 76, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isSupplier) {", "match": "Supplier"}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 77, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For suppliers: Tasks with accepted offers", "match": "supplier"}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 151, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Link to={isTeacher || isAdmin || isSupport ? \"/dashboard?tab=my-tasks\" : \"/dashboard?tab=my-jobs\"}>", "match": "Teacher"}, {"file": "src\\components\\dashboard\\ActionItems.tsx", "line": 151, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Link to={isTeacher || isAdmin || isSupport ? \"/dashboard?tab=my-tasks\" : \"/dashboard?tab=my-jobs\"}>", "match": "Support ? \"/dashboard?tab=my-tasks\" : \"/dashboard?tab=my-jobs\"}>\n                  View All <ArrowRi"}, {"file": "src\\components\\dashboard\\DashboardHeader.tsx", "line": 17, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isTeacher: boolean;", "match": "Teacher"}, {"file": "src\\components\\dashboard\\DashboardHeader.tsx", "line": 19, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isMaintenance: boolean;", "match": "Maintenance"}, {"file": "src\\components\\dashboard\\DashboardHeader.tsx", "line": 20, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupport: boolean;", "match": "Support: boolean;\n  isSupplier: boolean;\n  pendingReviewCount: number;\n  urgentTasksCount: number;\n}"}, {"file": "src\\components\\dashboard\\DashboardHeader.tsx", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (is<PERSON>eacher) {", "match": "Teacher"}, {"file": "src\\components\\dashboard\\DashboardHeader.tsx", "line": 96, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isMaintenance) {", "match": "Maintenance"}, {"file": "src\\components\\dashboard\\DashboardHeader.tsx", "line": 116, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isSupport) {", "match": "Support)"}, {"file": "src\\components\\dashboard\\DashboardHeader.tsx", "line": 136, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isSupplier) {", "match": "Supplier"}, {"file": "src\\components\\dashboard\\DashboardHeader.tsx", "line": 156, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isTeacher && 'Create and manage your tasks from your dashboard.'}", "match": "Teacher"}, {"file": "src\\components\\dashboard\\DashboardHeader.tsx", "line": 158, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isMaintenance && 'Create tasks and manage your assigned work from your dashboard.'}", "match": "Maintenance"}, {"file": "src\\components\\dashboard\\DashboardHeader.tsx", "line": 159, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupport && 'Create tasks and manage your assigned support requests from your dashboard.'}", "match": "Support && 'Create tasks and manage your assigned support requests from your dashboard.'}\n          "}, {"file": "src\\components\\dashboard\\DashboardLayout.tsx", "line": 23, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, profile, isSchool, isSupplier } = useAuth();", "match": "Supplier"}, {"file": "src\\components\\dashboard\\DashboardStats.tsx", "line": 22, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isTeacher: boolean;", "match": "Teacher"}, {"file": "src\\components\\dashboard\\DashboardStats.tsx", "line": 24, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isMaintenance: boolean;", "match": "Maintenance"}, {"file": "src\\components\\dashboard\\DashboardStats.tsx", "line": 25, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupport?: boolean;", "match": "Support?: boolean;\r\n  isSupplier: boolean;\r\n  isLoading: boolean;\r\n}\r\n\r\n// Interface for stat items\r"}, {"file": "src\\components\\dashboard\\DashboardStats.tsx", "line": 58, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (is<PERSON>eacher) {", "match": "Teacher"}, {"file": "src\\components\\dashboard\\DashboardStats.tsx", "line": 114, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isMaintenance) {", "match": "Maintenance"}, {"file": "src\\components\\dashboard\\DashboardStats.tsx", "line": 141, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isSupport) {", "match": "Support)"}, {"file": "src\\components\\dashboard\\DashboardStats.tsx", "line": 168, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isSupplier) {", "match": "Supplier"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 30, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isCreatorTeacher = creatorRole === 'teacher';", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 30, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isCreatorTeacher = creatorRole === 'teacher';", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 51, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) {", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 52, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Teacher created the task", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 54, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Teacher created, no assignment yet", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 55, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "scenario = 'teacher_created_pending';", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 56, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description = 'Teacher created task, no assignment yet';", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 57, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Only teacher is needed (already added as creator)", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 59, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Teacher created, admin assigned to staff", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 60, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "scenario = 'teacher_created_admin_assigned_to_staff';", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 61, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description = 'Teacher created task, admin assigned to staff';", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 163, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "category: 'Maintenance',", "match": "Maintenance"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 199, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: '<PERSON><PERSON> creates task and assigns to maintenance staff',", "match": "maintenance"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 226, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Find a maintenance staff member", "match": "maintenance"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 231, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('role', 'maintenance')", "match": "maintenance"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 237, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "throw new Error(`No maintenance staff found in organization ${organizationId}`);", "match": "maintenance"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 246, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "category: 'Maintenance',", "match": "Maintenance"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 270, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "id: 'teacher_created_pending',", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 271, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "name: 'Teacher Created (Pending)',", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 272, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: 'Teacher creates task, no assignment yet - only teacher in chat',", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 272, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: 'Teacher creates task, no assignment yet - only teacher in chat',", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 294, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Find the teacher user (drew <PERSON>)", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 295, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { data: teacherUser, error: teacherError } = await supabase", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 295, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { data: teacherUser, error: teacherError } = await supabase", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 299, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('role', 'teacher')", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 303, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (teacherError || !teacherUser) {", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 303, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (teacherError || !teacherUser) {", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 304, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "throw new Error(`No teacher found in organization ${organizationId}`);", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 313, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Create a task simulating teacher workflow (admin creates but we'll test as if teacher created)", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 313, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Create a task simulating teacher workflow (admin creates but we'll test as if teacher created)", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 314, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Note: Due to RLS policy requiring user_id = auth.uid(), we create as admin but test teacher logic", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 318, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "title: `Test Teacher Pending ${Date.now()} (Simulated)`,", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 319, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: 'Test task simulating teacher creation, no assignment',", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 320, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "category: 'Maintenance',", "match": "Maintenance"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 335, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Now update the task to simulate teacher creation by changing user_id using service role", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 339, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".update({ user_id: teacherUser.id })", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 343, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.warn('Could not update task creator to teacher (RLS limitation), proceeding with admin as creator');", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 346, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For testing purposes, we'll manually override the context to simulate teacher creation", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 350, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "expectedMembers: [teacherUser.id], // Only teacher in this scenario", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 350, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "expectedMembers: [teacherUser.id], // Only teacher in this scenario", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 351, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "expectedScenario: 'teacher_created_pending',", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 352, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "simulatedCreatorId: teacherUser.id // We'll use this to override the context", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 358, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "id: 'teacher_created_admin_assigned',", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 359, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "name: 'Teacher Created, Admin Assigned to Staff',", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 360, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: 'Teacher creates task, admin assigns to maintenance staff - teacher, admin, and staff in chat',", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 360, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: 'Teacher creates task, admin assigns to maintenance staff - teacher, admin, and staff in chat',", "match": "maintenance"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 360, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: 'Teacher creates task, admin assigns to maintenance staff - teacher, admin, and staff in chat',", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 382, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Find the teacher user", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 383, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { data: teacherUser, error: teacherError } = await supabase", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 383, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { data: teacherUser, error: teacherError } = await supabase", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 387, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('role', 'teacher')", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 391, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (teacherError || !teacherUser) {", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 391, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (teacherError || !teacherUser) {", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 392, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "throw new Error(`No teacher found in organization ${organizationId}`);", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 395, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Find a maintenance staff member", "match": "maintenance"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 400, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('role', 'maintenance')", "match": "maintenance"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 405, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "throw new Error(`No maintenance staff found in organization ${organizationId}`);", "match": "maintenance"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 411, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Create a task simulating teacher workflow with admin assignment", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 412, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Note: Due to RLS policy requiring user_id = auth.uid(), we create as admin but test teacher logic", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 416, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "title: `Test Teacher Created Admin Assigned ${Date.now()} (Simulated)`,", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 417, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: 'Test task simulating teacher creation, admin assigned to staff',", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 418, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "category: 'Maintenance',", "match": "Maintenance"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 433, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Now update the task to simulate teacher creation by changing user_id", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 436, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".update({ user_id: teacherUser.id })", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 440, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.warn('Could not update task creator to teacher (RLS limitation), proceeding with admin as creator');", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 443, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For testing purposes, we'll manually override the context to simulate teacher creation", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 446, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "expectedMembers: [teacherUser.id, currentUser.id, staffMember.id], // Teacher + Admin + Staff", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 446, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "expectedMembers: [teacherUser.id, currentUser.id, staffMember.id], // Teacher + Admin + Staff", "match": "Teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 447, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "expectedScenario: 'teacher_created_admin_assigned_to_staff',", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 448, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "simulatedCreatorId: teacherUser.id // We'll use this to override the context", "match": "teacher"}, {"file": "src\\components\\dashboard\\InternalTaskChatTester.tsx", "line": 680, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "category: 'Maintenance',", "match": "Maintenance"}, {"file": "src\\components\\dashboard\\JobsSection.tsx", "line": 16, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, isSupplier } = useAuth();", "match": "Supplier"}, {"file": "src\\components\\dashboard\\JobsSection.tsx", "line": 74, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "}, [userOffers, tasks, isSupplier, user?.id]);", "match": "Supplier"}, {"file": "src\\components\\dashboard\\TasksList.tsx", "line": 18, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isTeacher = userRole === 'teacher';", "match": "Teacher"}, {"file": "src\\components\\dashboard\\TasksList.tsx", "line": 18, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isTeacher = userRole === 'teacher';", "match": "teacher"}, {"file": "src\\components\\dashboard\\TasksList.tsx", "line": 37, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "}, [isTeacher, tasks]);", "match": "Teacher"}, {"file": "src\\components\\dashboard\\TestChatSection.tsx", "line": 175, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "(userProfile?.account_type === 'supplier' && task.visibility === 'public')", "match": "supplier"}, {"file": "src\\components\\layout\\Footer.tsx", "line": 18, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Connecting schools with qualified maintenance professionals for all your facility needs.", "match": "maintenance"}, {"file": "src\\components\\layout\\Footer.tsx", "line": 47, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<h3 className=\"text-lg font-semibold mb-4\">For Suppliers</h3>", "match": "Supplier"}, {"file": "src\\components\\layout\\Footer.tsx", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li><Link to=\"/supplier-signup\" className=\"text-gray-600 hover:text-classtasker-blue transition-colors\">Become a Supplier</Link></li>", "match": "supplier"}, {"file": "src\\components\\layout\\Footer.tsx", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li><Link to=\"/supplier-signup\" className=\"text-gray-600 hover:text-classtasker-blue transition-colors\">Become a Supplier</Link></li>", "match": "Supplier"}, {"file": "src\\components\\layout\\Footer.tsx", "line": 51, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li><Link to=\"/supplier-resources\" className=\"text-gray-600 hover:text-classtasker-blue transition-colors\">Supplier Resources</Link></li>", "match": "supplier"}, {"file": "src\\components\\layout\\Footer.tsx", "line": 51, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li><Link to=\"/supplier-resources\" className=\"text-gray-600 hover:text-classtasker-blue transition-colors\">Supplier Resources</Link></li>", "match": "Supplier"}, {"file": "src\\components\\layout\\Footer.tsx", "line": 52, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li><Link to=\"/supplier-faq\" className=\"text-gray-600 hover:text-classtasker-blue transition-colors\">Supplier FAQ</Link></li>", "match": "supplier"}, {"file": "src\\components\\layout\\Footer.tsx", "line": 52, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li><Link to=\"/supplier-faq\" className=\"text-gray-600 hover:text-classtasker-blue transition-colors\">Supplier FAQ</Link></li>", "match": "Supplier"}, {"file": "src\\components\\layout\\Footer.tsx", "line": 57, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<h3 className=\"text-lg font-semibold mb-4\">Support</h3>", "match": "Support</h3>\r\n            <ul className=\"space-y-2\">\r\n              <li><Link to=\"/help\" className=\""}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 23, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, signOut, isAdmin, isTeacher, isMaintenance, isSupport, isSupplier, userRole } = useAuth();", "match": "Teacher"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 23, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, signOut, isAdmin, isTeacher, isMaintenance, isSupport, isSupplier, userRole } = useAuth();", "match": "Maintenance"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 23, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, signOut, isAdmin, isTeacher, isMaintenance, isSupport, isSupplier, userRole } = useAuth();", "match": "Support, isSupplier, userRole } = useAuth()"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 146, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Supplier-specific links */}", "match": "Supplier"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 147, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier && (", "match": "Supplier"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 152, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span>Supplier</span>", "match": "Supplier"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 171, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Teacher-specific links */}", "match": "Teacher"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 172, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isTeacher && (", "match": "Teacher"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 177, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span>Teacher</span>", "match": "Teacher"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 196, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Maintenance Staff links */}", "match": "Maintenance"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 197, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isMaintenance && (", "match": "Maintenance"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 202, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span>Maintenance</span>", "match": "Maintenance"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 221, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Support Staff links */}", "match": "Support Staff links */}\r\n                  {isSupport && (\r\n                    <>\r\n                "}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 323, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Help & Support link for all users */}", "match": "Support link for all users */}\r\n                  <DropdownMenuSeparator />\r\n                  <Drop"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 467, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Supplier-specific links */}", "match": "Supplier"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 468, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier && (", "match": "Supplier"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 473, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "SUPPLIER", "match": "SUPPLIER"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 499, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Teacher-specific links */}", "match": "Teacher"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 500, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isTeacher && (", "match": "Teacher"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 505, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "TEACHER", "match": "TEACHER"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 531, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Maintenance Staff links */}", "match": "Maintenance"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 532, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isMaintenance && (", "match": "Maintenance"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 537, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "MAINTENANCE", "match": "MAINTENANCE"}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 563, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Support Staff links */}", "match": "Support Staff links */}\r\n                {isSupport && (\r\n                  <>\r\n                    "}, {"file": "src\\components\\layout\\Navbar.tsx", "line": 701, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Help & Support", "match": "Support\r\n                </NavLink>\r\n                <Button variant=\"outline\" onClick={handleSignOu"}, {"file": "src\\components\\layout\\RoleBasedNavigation.tsx", "line": 125, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Teacher Links */}", "match": "Teacher"}, {"file": "src\\components\\layout\\RoleBasedNavigation.tsx", "line": 126, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{userRole === 'teacher' && (", "match": "teacher"}, {"file": "src\\components\\layout\\RoleBasedNavigation.tsx", "line": 131, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "TEACHER", "match": "TEACHER"}, {"file": "src\\components\\layout\\RoleBasedNavigation.tsx", "line": 161, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Maintenance Staff Links */}", "match": "Maintenance"}, {"file": "src\\components\\layout\\RoleBasedNavigation.tsx", "line": 162, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{userRole === 'maintenance' && (", "match": "maintenance"}, {"file": "src\\components\\layout\\RoleBasedNavigation.tsx", "line": 167, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "MAINTENANCE", "match": "MAINTENANCE"}, {"file": "src\\components\\layout\\RoleBasedNavigation.tsx", "line": 197, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Support Staff Links */}", "match": "Support Staff Links */}\n      {userRole === 'support' && (\n        <>\n          <div className=\"px-3"}, {"file": "src\\components\\layout\\RoleBasedNavigation.tsx", "line": 233, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Supplier Links */}", "match": "Supplier"}, {"file": "src\\components\\layout\\RoleBasedNavigation.tsx", "line": 234, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{userRole === 'supplier' && (", "match": "supplier"}, {"file": "src\\components\\layout\\RoleBasedNavigation.tsx", "line": 239, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "SUPPLIER", "match": "SUPPLIER"}, {"file": "src\\components\\mobile\\GlobalMobileNavBar.tsx", "line": 86, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = profile?.account_type === 'supplier';", "match": "Supplier"}, {"file": "src\\components\\mobile\\GlobalMobileNavBar.tsx", "line": 86, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = profile?.account_type === 'supplier';", "match": "supplier"}, {"file": "src\\components\\mobile\\GlobalMobileNavBar.tsx", "line": 136, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{(canCreateTasks || isSupplier) && (", "match": "Supplier"}, {"file": "src\\components\\mobile\\MobileNavBar.tsx", "line": 106, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = profile?.account_type === 'supplier';", "match": "Supplier"}, {"file": "src\\components\\mobile\\MobileNavBar.tsx", "line": 106, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = profile?.account_type === 'supplier';", "match": "supplier"}, {"file": "src\\components\\mobile\\MobileNavBar.tsx", "line": 156, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{(canCreateTasks || isSupplier) && (", "match": "Supplier"}, {"file": "src\\components\\mobile\\SimpleMobileNavBar.tsx", "line": 62, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = profile?.account_type === 'supplier';", "match": "Supplier"}, {"file": "src\\components\\mobile\\SimpleMobileNavBar.tsx", "line": 62, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = profile?.account_type === 'supplier';", "match": "supplier"}, {"file": "src\\components\\mobile\\SimpleMobileNavBar.tsx", "line": 115, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{(canCreateTasks || isSupplier) && (", "match": "Supplier"}, {"file": "src\\components\\organization\\OrganizationForm.tsx", "line": 157, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"supplier\">Supplier Business</SelectItem>", "match": "supplier"}, {"file": "src\\components\\organization\\OrganizationForm.tsx", "line": 157, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"supplier\">Supplier Business</SelectItem>", "match": "Supplier"}, {"file": "src\\components\\organization\\OrganizationForm.tsx", "line": 162, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Select the type of organization you're creating: an individual school, a Multi-Academy Trust, or a supplier business.", "match": "supplier"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 94, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id?: string;", "match": "supplier"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 95, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_name?: string;", "match": "supplier"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 144, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teachers: 0,", "match": "teacher"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 145, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "maintenance: 0,", "match": "maintenance"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 146, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "suppliers: 0,", "match": "supplier"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 310, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teachers: profilesData.filter(p => p.role === 'teacher').length,", "match": "teacher"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 310, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teachers: profilesData.filter(p => p.role === 'teacher').length,", "match": "teacher"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 311, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "maintenance: profilesData.filter(p => p.role === 'maintenance').length,", "match": "maintenance"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 311, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "maintenance: profilesData.filter(p => p.role === 'maintenance').length,", "match": "maintenance"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 312, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "suppliers: profilesData.filter(p => p.account_type === 'supplier').length,", "match": "supplier"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 312, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "suppliers: profilesData.filter(p => p.account_type === 'supplier').length,", "match": "supplier"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 747, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<div className=\"text-2xl font-bold text-blue-600\">{userStats.teachers}</div>", "match": "teacher"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 748, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<div className=\"text-sm text-gray-500\">Teachers</div>", "match": "Teacher"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 751, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<div className=\"text-2xl font-bold text-green-600\">{userStats.maintenance}</div>", "match": "maintenance"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 752, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<div className=\"text-sm text-gray-500\">Maintenance</div>", "match": "Maintenance"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 755, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<div className=\"text-2xl font-bold text-orange-600\">{userStats.suppliers}</div>", "match": "supplier"}, {"file": "src\\components\\organization\\OrganizationOverview.tsx", "line": 756, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<div className=\"text-sm text-gray-500\">Suppliers</div>", "match": "Supplier"}, {"file": "src\\components\\organization\\OrganizationSettings.tsx", "line": 132, "pattern": "Organization ID in console.log", "severity": "CRITICAL", "category": "ORGANIZATION_DATA", "content": "console.log('DEBUG: Organization type check:', {", "match": "console.log('DEBUG: Organization type check:', {\r\n          hasId: !!organizationId,\r\n          type"}, {"file": "src\\components\\organization\\OrganizationSettings.tsx", "line": 132, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log('DEBUG: Organization type check:', {", "match": "console.log('DEBUG: Organization type check:', {\r\n          hasId: !!organizationId,\r\n          type"}, {"file": "src\\components\\organization\\UserManagement.tsx", "line": 54, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const [inviteRole, setInviteRole] = useState<UserRole>('teacher');", "match": "teacher"}, {"file": "src\\components\\organization\\UserManagement.tsx", "line": 55, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const [editRole, setEditRole] = useState<UserRole>('teacher');", "match": "teacher"}, {"file": "src\\components\\organization\\UserManagement.tsx", "line": 244, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "set<PERSON>n<PERSON><PERSON><PERSON><PERSON>('teacher');", "match": "teacher"}, {"file": "src\\components\\organization\\UserManagement.tsx", "line": 522, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: 'teacher' // Default role for CSV imports", "match": "teacher"}, {"file": "src\\components\\organization\\UserManagement.tsx", "line": 686, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"teacher\">Teacher</SelectItem>", "match": "teacher"}, {"file": "src\\components\\organization\\UserManagement.tsx", "line": 686, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"teacher\">Teacher</SelectItem>", "match": "Teacher"}, {"file": "src\\components\\organization\\UserManagement.tsx", "line": 687, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"support\">Support Staff</SelectItem>", "match": "support\">Support Staff</SelectItem>\r\n                          <SelectItem value=\"maintenance\">Maint"}, {"file": "src\\components\\organization\\UserManagement.tsx", "line": 840, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"teacher\">Teacher</SelectItem>", "match": "teacher"}, {"file": "src\\components\\organization\\UserManagement.tsx", "line": 840, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"teacher\">Teacher</SelectItem>", "match": "Teacher"}, {"file": "src\\components\\organization\\UserManagement.tsx", "line": 841, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"support\">Support Staff</SelectItem>", "match": "support\">Support Staff</SelectItem>\r\n                                    <SelectItem value=\"maintena"}, {"file": "src\\components\\organization\\UserManagement.tsx", "line": 104, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log('DEBUG UserManagement: Fetching organization data for: completed');", "match": "console.log('DEBUG UserManagement: Fetching organization data for: completed')"}, {"file": "src\\components\\organization\\UserManagementForm.tsx", "line": 48, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const [inviteRole, setInviteRole] = useState<UserRole>('teacher');", "match": "teacher"}, {"file": "src\\components\\organization\\UserManagementForm.tsx", "line": 49, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const [editRole, setEditRole] = useState<UserRole>('teacher');", "match": "teacher"}, {"file": "src\\components\\organization\\UserManagementForm.tsx", "line": 190, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "set<PERSON>n<PERSON><PERSON><PERSON><PERSON>('teacher');", "match": "teacher"}, {"file": "src\\components\\organization\\UserManagementForm.tsx", "line": 441, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: 'teacher' // Default role for CSV imports", "match": "teacher"}, {"file": "src\\components\\organization\\UserManagementForm.tsx", "line": 558, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"teacher\">Teacher</SelectItem>", "match": "teacher"}, {"file": "src\\components\\organization\\UserManagementForm.tsx", "line": 558, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"teacher\">Teacher</SelectItem>", "match": "Teacher"}, {"file": "src\\components\\organization\\UserManagementForm.tsx", "line": 559, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"support\">Support Staff</SelectItem>", "match": "support\">Support Staff</SelectItem>\r\n                        <SelectItem value=\"maintenance\">Mainten"}, {"file": "src\\components\\organization\\UserManagementForm.tsx", "line": 685, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"teacher\">Teacher</SelectItem>", "match": "teacher"}, {"file": "src\\components\\organization\\UserManagementForm.tsx", "line": 685, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"teacher\">Teacher</SelectItem>", "match": "Teacher"}, {"file": "src\\components\\organization\\UserManagementForm.tsx", "line": 686, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"support\">Support Staff</SelectItem>", "match": "support\">Support Staff</SelectItem>\r\n                                  <SelectItem value=\"maintenanc"}, {"file": "src\\components\\organization\\UserManagementForm.tsx", "line": 93, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log('DEBUG UserManagement: Fetching organization data for: completed');", "match": "console.log('DEBUG UserManagement: Fetching organization data for: completed')"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 65, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "We couldn't find your organization details. Please contact support.", "match": "support.\n          </CardDescription>\n        </CardHeader>\n        <CardFooter className=\"flex just"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 90, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "case 'teacher':", "match": "teacher"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 93, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "title: 'Welcome, Teacher!',", "match": "Teacher"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 94, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: `As a teacher at ${organization.name}, you can create tasks for maintenance and support, and track their progress.`,", "match": "teacher"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 94, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: `As a teacher at ${organization.name}, you can create tasks for maintenance and support, and track their progress.`,", "match": "maintenance"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 94, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: `As a teacher at ${organization.name}, you can create tasks for maintenance and support, and track their progress.`,", "match": "support, and track their progress.`,\n          actions: [\n            { label: 'Create a Task', path"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 165, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Post tasks to the marketplace for suppliers</li>", "match": "supplier"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 170, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{userRole === 'teacher' && (", "match": "teacher"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 172, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Create maintenance and support requests</li>", "match": "maintenance"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 172, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Create maintenance and support requests</li>", "match": "support requests</li>\n                <li>Track the status of your requests</li>\n                <li"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 178, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{userRole === 'maintenance' && (", "match": "maintenance"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 186, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{userRole === 'support' && (", "match": "support' && (\n              <>\n                <li>View support requests assigned to you</li>\n      "}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 194, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{userRole === 'supplier' && (", "match": "supplier"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 202, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{!['admin', 'teacher', 'maintenance', 'support', 'supplier'].includes(userRole) && (", "match": "teacher"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 202, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{!['admin', 'teacher', 'maintenance', 'support', 'supplier'].includes(userRole) && (", "match": "maintenance"}, {"file": "src\\components\\organization\\WelcomeToOrganization.tsx", "line": 202, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{!['admin', 'teacher', 'maintenance', 'support', 'supplier'].includes(userRole) && (", "match": "support', 'supplier'].includes(userRole)"}, {"file": "src\\components\\profile\\ProfileBadges.tsx", "line": 44, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = accountType === 'supplier';", "match": "Supplier"}, {"file": "src\\components\\profile\\ProfileBadges.tsx", "line": 44, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = accountType === 'supplier';", "match": "supplier"}, {"file": "src\\components\\profile\\ProfileBadges.tsx", "line": 115, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplierBadges = [", "match": "supplier"}, {"file": "src\\components\\profile\\ProfileBadges.tsx", "line": 126, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "id: 'reliable-supplier',", "match": "supplier"}, {"file": "src\\components\\profile\\ProfileBadges.tsx", "line": 127, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "name: 'Reliable Supplier',", "match": "Supplier"}, {"file": "src\\components\\profile\\ProfileBadges.tsx", "line": 135, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "id: 'top-supplier',", "match": "supplier"}, {"file": "src\\components\\profile\\ProfileBadges.tsx", "line": 136, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "name: 'Top Supplier',", "match": "Supplier"}, {"file": "src\\components\\profile\\ProfileBadges.tsx", "line": 149, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "...(isSupplier ? supplierBadges : [])", "match": "Supplier"}, {"file": "src\\components\\profile\\ProfileBadges.tsx", "line": 149, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "...(isSupplier ? supplierBadges : [])", "match": "supplier"}, {"file": "src\\components\\profile\\ProfileCard.tsx", "line": 40, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-gray-600\">{profile.job_title || (profile.account_type === 'school' ? 'School Staff' : 'Supplier')}</p>", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWAChatList.tsx", "line": 269, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "(userProfile?.account_type === 'supplier' && task.visibility === 'public')", "match": "supplier"}, {"file": "src\\components\\pwa\\PWAChatView.tsx", "line": 43, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Custom Input UI component for the PWA chat with proper image support", "match": "support\nconst CustomInputUI = (props: any)"}, {"file": "src\\components\\pwa\\PWAChatView.tsx", "line": 61, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Use the default MessageInput with file upload support */}", "match": "support */}\n        <MessageInput\n          {...props}\n          focus\n          acceptedFiles={['im"}, {"file": "src\\components\\pwa\\PWAChatView.tsx", "line": 269, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// GetStream Chat Component with proper image support", "match": "support\n            <Chat client={client} theme=\"messaging light\">\n              <StreamChannel chan"}, {"file": "src\\components\\pwa\\PWADashboard.tsx", "line": 134, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".or(`supplier_id.eq.${user.id},admin_id.eq.${user.id}`)", "match": "supplier"}, {"file": "src\\components\\pwa\\PWAGetStreamChatList.tsx", "line": 158, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Fetch channels function with pagination support", "match": "support\n  const fetchChannels = async (client: StreamChat, page = 0, append = false)"}, {"file": "src\\components\\pwa\\PWAGetStreamChatList.tsx", "line": 270, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// 2. User is a supplier and task is public", "match": "supplier"}, {"file": "src\\components\\pwa\\PWAGetStreamChatList.tsx", "line": 273, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "(userProfile?.account_type === 'supplier' && task.visibility === 'public')", "match": "supplier"}, {"file": "src\\components\\pwa\\PWAInstallButton.tsx", "line": 41, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supportsBeforeInstallPrompt: 'BeforeInstallPromptEvent' in window || 'onbeforeinstallprompt' in window,", "match": "supportsBeforeInstallPrompt: 'BeforeInstallPromptEvent' in window || 'onbeforeinstallprompt' in wind"}, {"file": "src\\components\\pwa\\PWAMarketplace.tsx", "line": 58, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, isSupplier, profile, userRole } = useAuth();", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWAMarketplace.tsx", "line": 250, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "}, [user, isSupplier]);", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWAMarketplace.tsx", "line": 252, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Show friendly message for non-suppliers", "match": "supplier"}, {"file": "src\\components\\pwa\\PWAMarketplace.tsx", "line": 253, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!isSupplier) {", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWAMarketplace.tsx", "line": 258, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "message=\"The marketplace is where professional suppliers can view and bid on tasks that need external support.\"", "match": "supplier"}, {"file": "src\\components\\pwa\\PWAMarketplace.tsx", "line": 258, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "message=\"The marketplace is where professional suppliers can view and bid on tasks that need external support.\"", "match": "support.\"\r\n          howItWorks=\"When your school posts tasks that need external help, they appear h"}, {"file": "src\\components\\pwa\\PWASimplifiedTimeline.tsx", "line": 18, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'interest',       // Suppliers have expressed interest", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWASimplifiedTimeline.tsx", "line": 19, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'questions',      // Discussion phase between admin and suppliers", "match": "supplier"}, {"file": "src\\components\\pwa\\PWASimplifiedTimeline.tsx", "line": 20, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'offer',          // Suppliers have submitted offers", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 34, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, profile, userRole, isAdmin, isSupplier, isMaintenance } = useAuth();", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 34, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, profile, userRole, isAdmin, isSupplier, isMaintenance } = useAuth();", "match": "Maintenance"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 102, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// If user is a supplier, check if they have an offer for this task", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 103, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (isSupplier && taskData.visibility === 'public') {", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 199, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Actions for suppliers", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 201, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For suppliers expressing interest, we don't change task status", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 205, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check if there's already a chat thread for this task and supplier", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 210, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('supplier_id', user.id)", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 223, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: user.id,", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 305, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!additionalData?.offerId || !additionalData?.supplierId) {", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 311, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "assigned_to: additionalData.supplierId,", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 486, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// SUPPLIER ACTIONS", "match": "SUPPLIER"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 487, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (isSupplier && isExternalTask) {", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 488, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For open tasks, suppliers can express interest", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 490, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Only show if the supplier hasn't already expressed interest", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 501, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For tasks in interest or questions phase, suppliers can submit offers", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 511, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For assigned tasks (to this supplier), they can start work", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActions.tsx", "line": 521, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For in-progress tasks (assigned to this supplier), they can mark as completed", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 45, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, profile, userRole, isAdmin, isSupplier } = useAuth();", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 123, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// If user is a supplier, check if they have an offer for this task", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 124, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (isSupplier && taskData.visibility === 'public') {", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 263, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Actions for suppliers", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 265, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For suppliers expressing interest, we don't change task status", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 269, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check if there's already a chat thread for this task and supplier", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 274, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('supplier_id', user.id)", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 287, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: user.id,", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 369, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!additionalData?.offerId || !additionalData?.supplierId) {", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 375, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "assigned_to: additionalData.supplierId,", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 549, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// SUPPLIER ACTIONS", "match": "SUPPLIER"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 550, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (isSupplier && isExternalTask) {", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 551, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For open tasks, suppliers can express interest", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 553, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Only show if the supplier hasn't already expressed interest", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 564, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For tasks in interest or questions phase, suppliers can submit offers", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 574, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For assigned tasks (to this supplier), they can start work", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsClean.tsx", "line": 584, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For in-progress tasks (assigned to this supplier), they can mark as completed", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 54, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, profile, userRole, isAdmin, isSupplier } = useAuth();", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 140, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// If user is a supplier, check if they have an offer for this task", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 141, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (isSupplier && taskData.visibility === 'public') {", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 285, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "status: 'open', // For public visibility, we keep it as 'open' until a supplier offer is accepted", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 329, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "? 'Task has been made public and is now visible to suppliers'", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 433, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Actions for suppliers", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 435, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For suppliers expressing interest, we don't change task status", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 447, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Create the channel with the task creator and the supplier", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 457, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "text: `${user.email || 'A supplier'} has expressed interest in this task.`,", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 572, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!additionalData?.offerId || !additionalData?.supplierId) {", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 578, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "assigned_to: additionalData.supplierId,", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 767, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// SUPPLIER ACTIONS", "match": "SUPPLIER"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 768, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (isSupplier && isExternalTask) {", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 769, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For open tasks, suppliers can express interest", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 771, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Only show if the supplier hasn't already expressed interest", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 782, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For tasks in interest or questions phase, suppliers can submit offers", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 792, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For assigned tasks (to this supplier), they can start work", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 802, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For in-progress tasks (assigned to this supplier), they can mark as completed", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 914, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Assign \"{task?.title}\" to a staff member or make it available to suppliers.", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 938, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Make Public for Suppliers", "match": "Supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 958, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"maintenance\">Maintenance</SelectItem>", "match": "maintenance"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 958, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"maintenance\">Maintenance</SelectItem>", "match": "Maintenance"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 960, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"support\">Support</SelectItem>", "match": "support\">Support</SelectItem>\r\n                      <SelectItem value=\"it\">IT</SelectItem>\r\n       "}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 1000, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Exclude teachers from assignable users", "match": "teacher"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 1001, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (user.role === 'teacher') return false;", "match": "teacher"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 1030, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Set a budget for suppliers to bid on this task", "match": "supplier"}, {"file": "src\\components\\pwa\\PWATaskActionsFinal.tsx", "line": 183, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log('[PWATaskActionsFinal] Organization users: completed');", "match": "console.log('[PWATaskActionsFinal] Organization users: completed')"}, {"file": "src\\components\\pwa\\PWATaskList.tsx", "line": 224, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// These are typically tasks created by teachers that need admin review", "match": "teacher"}, {"file": "src\\components\\pwa\\PWATaskList.tsx", "line": 186, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log('PWATaskList - Fetched user-specific tasks: completed');", "match": "console.log('PWATaskList - Fetched user-specific tasks: completed')"}, {"file": "src\\components\\pwa\\PWATaskStatusFilter.tsx", "line": 33, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{ value: 'admin_review', label: 'Awaiting Admin Review', icon: AlertCircle, description: 'Tasks created by teachers that need admin review' },", "match": "teacher"}, {"file": "src\\components\\stripe\\EnhancedPaymentProcessor.tsx", "line": 114, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Payment functionality is currently disabled. Please contact support for assistance.", "match": "support for assistance.\r\n        </AlertDescription>\r\n      </Alert>\r\n    )"}, {"file": "src\\components\\stripe\\EnhancedPaymentProcessor.tsx", "line": 367, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "The supplier will be notified and the funds will be transferred once the task is completed.", "match": "supplier"}, {"file": "src\\components\\stripe\\PaymentProcessor.tsx", "line": 157, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Thank you for your payment. The supplier will be notified and will begin work on your task.", "match": "supplier"}, {"file": "src\\components\\stripe\\PaymentProcessor.tsx", "line": 176, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Pay for your task to release funds to the supplier", "match": "supplier"}, {"file": "src\\components\\stripe\\SimplePaymentProcessor.tsx", "line": 121, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "The supplier will be notified and the funds will be transferred once the task is completed.", "match": "supplier"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 22, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "support_type: z.string().min(1, { message: 'Please select a support type' }),", "match": "support_type: z.string()"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 22, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "support_type: z.string().min(1, { message: 'Please select a support type' }),", "match": "support type' })"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 28, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const SupportRequestForm = () => {", "match": "SupportRequestForm = ()"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "support_type: '',", "match": "support_type: '',\n      message: '',\n    },\n  })"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 148, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('SupportRequestForm - Error fetching organization:', err);", "match": "SupportRequestForm - Error fetching organization:', err)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 159, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('SupportRequestForm - Error populating form:', error);", "match": "SupportRequestForm - Error populating form:', error)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 178, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Format the data for the support request", "match": "support request\n      const supportRequest: any = {\n        name: data.name,\n        email: data.ema"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 203, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supportRequest.email = user.email || '';", "match": "supportRequest.email = user.email || '';\n        }\n\n        // We're not adding user_id as it doesn'"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 218, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// First, insert the support request into the database", "match": "support request into the database\n          const result = await supabaseClient\n            .from('s"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 221, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".insert(supportRequest)", "match": "supportRequest)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 225, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('SupportRequestForm - Error inserting support request:', result.error);", "match": "SupportRequestForm - Error inserting support request:', result.error)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 229, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "name: supportRequest.name,", "match": "supportRequest.name,\n              email: supportRequest.email,\n              organization: supportR"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 241, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".from('support_requests')", "match": "support_requests')"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 246, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('SupportRequestForm - Simplified request failed:', retryResult.error);", "match": "SupportRequestForm - Simplified request failed:', retryResult.error)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 254, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "await sendSupportEmail(supportRequest);", "match": "SupportEmail(supportRequest)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 278, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "await sendSupportEmail(supportRequest);", "match": "SupportEmail(supportRequest)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 298, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('SupportRequestForm - Exception during database operation:', dbError);", "match": "SupportRequestForm - Exception during database operation:', dbError)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 299, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "logSupportRequest(supportRequest);", "match": "SupportRequest(supportRequest)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 306, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "logSupportRequest(supportRequest);", "match": "SupportRequest(supportRequest)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 313, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('SupportRequestForm - Error submitting support request:', error);", "match": "SupportRequestForm - <PERSON><PERSON><PERSON> submitting support request:', error)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 316, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "setSubmitError('There was an error submitting your support request. Please try again later.');", "match": "support request. Please try again later.')"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 319, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.warn('SupportRequestForm - SUPPORT REQUEST FAILED:');", "match": "SupportRequestForm - SUPPORT REQUEST FAILED:')"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 335, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Support Request Details (Console Only):", "match": "Support Request Details (Console Only)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 337, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Name: ${supportRequest.name}", "match": "supportRequest.name}\n        Email: ${supportRequest.email}\n        Organization: ${supportRequest.o"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 350, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Helper function to send support email via Edge Function", "match": "support email via Edge Function\n  const sendSupportEmail = async (supportRequest: any)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 363, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "from: 'ClassTasker Support <<EMAIL>>',", "match": "Support <<EMAIL>>',\n        to: '<EMAIL>',\n        subject: `Su"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 380, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { data, error } = await supabaseClient.functions.invoke('support-email-sender', {", "match": "support-email-sender', {\n        body: emailPayload\n      })"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 388, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('SupportRequestForm - Error sending email via Edge Function:', error);", "match": "SupportRequestForm - Error sending email via Edge Function:', error)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 397, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('SupportRequestForm - Exception sending email via Edge Function:', error);", "match": "SupportRequestForm - Exception sending email via Edge Function:', error)"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 405, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<CardTitle className=\"text-2xl\">Contact Support</CardTitle>", "match": "Support</CardTitle>\n        <CardDescription>\n          Fill out this form to get help from our supp"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 416, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Thank you for contacting us. We've received your support request and will get back to you shortly at {form.getValues('email')}. Our support team has been notified of your request.", "match": "support team has been notified of your request.\n            </AlertDescription>\n          </Alert>\n "}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 480, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "name=\"support_type\"", "match": "support_type\"\n                render={({ field })"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 483, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<FormLabel>Support Type</FormLabel>", "match": "Support Type</FormLabel>\n                    <Select onValueChange={field.onChange} defaultValue={fi"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 537, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "\"Submit Support Request\"", "match": "Support Request\"\n                )"}, {"file": "src\\components\\support\\SupportRequestForm.tsx", "line": 547, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<a href=\"mailto:<EMAIL>\" className=\"text-blue-600 hover:underline\">", "match": "<EMAIL>\" className=\"text-blue-600 hover:underline\">\n            support@classtasker."}, {"file": "src\\components\\tasks\\AdminReviewStatus.tsx", "line": 12, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* This is shown to teachers when they view their own tasks that are pending admin review", "match": "teacher"}, {"file": "src\\components\\tasks\\AdminReviewStatus.tsx", "line": 36, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Assigned to internal maintenance staff</li>", "match": "maintenance"}, {"file": "src\\components\\tasks\\AdminReviewStatus.tsx", "line": 37, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Posted to the marketplace for external suppliers</li>", "match": "supplier"}, {"file": "src\\components\\tasks\\DirectDatabaseCheck.tsx", "line": 60, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isMaintenance: profileData?.role === 'maintenance'", "match": "Maintenance"}, {"file": "src\\components\\tasks\\DirectDatabaseCheck.tsx", "line": 60, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isMaintenance: profileData?.role === 'maintenance'", "match": "maintenance"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 40, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, isSupplier, isAdmin, isSupport, isMaintenance, userRole, profile } = useAuth();", "match": "Supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 40, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, isSupplier, isAdmin, isSupport, isMaintenance, userRole, profile } = useAuth();", "match": "Support, isMaintenance, userRole, profile } = useAuth()"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 83, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// If user is a supplier, only show threads where they are the supplier", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 83, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// If user is a supplier, only show threads where they are the supplier", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 88, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier:supplier_id(id, first_name, last_name, email)", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 88, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier:supplier_id(id, first_name, last_name, email)", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 97, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "query = query.eq('supplier_id', user.id);", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 211, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplierName = thread.supplier?.first_name && thread.supplier?.last_name", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 211, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplierName = thread.supplier?.first_name && thread.supplier?.last_name", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 211, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplierName = thread.supplier?.first_name && thread.supplier?.last_name", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 212, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "? `${thread.supplier.first_name} ${thread.supplier.last_name}`", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 212, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "? `${thread.supplier.first_name} ${thread.supplier.last_name}`", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 213, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": thread.supplier?.email?.[0] || 'Supplier';", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 213, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": thread.supplier?.email?.[0] || 'Supplier';", "match": "Supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 225, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{supplierName.substring(0, 2).toUpperCase()}", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 228, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span className=\"truncate max-w-[100px]\">{supplierName}</span>", "match": "supplier"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 240, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check if user is a teacher based on profile role", "match": "teacher"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 241, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isTeacher = userRole === 'teacher';", "match": "Teacher"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 241, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isTeacher = userRole === 'teacher';", "match": "teacher"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 243, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Allow viewing and sending messages for all roles EXCEPT teachers", "match": "teacher"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 244, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Teachers should not be able to chat with service providers", "match": "Teacher"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 246, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// User is admin, support, maintenance, or supplier", "match": "support, maintenance, or supplier\n    isAdmin || isSupport || isMaintenance || isSupplier ||\n    // "}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 250, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Special case: if the teacher is the task owner AND an admin has already", "match": "teacher"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 252, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "(isTeacher && user.id === taskOwnerId && messages.length > 0)", "match": "Teacher"}, {"file": "src\\components\\tasks\\GetStreamTaskChat.tsx", "line": 287, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isTeacher ? (", "match": "Teacher"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 38, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'open': 'Task is open for offers from suppliers',", "match": "supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 39, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'interest': 'Suppliers have expressed interest in this task',", "match": "Supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 40, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'questions': 'Suppliers are asking questions about this task',", "match": "Supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 41, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'offer': 'Suppliers have submitted offers for this task',", "match": "Supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 42, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'assigned': 'Task has been assigned to a supplier',", "match": "supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 43, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'in_progress': 'Supp<PERSON> is working on the task',", "match": "Supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 44, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'completed': 'Task has been marked as completed by the supplier',", "match": "supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 112, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'open': 'Suppliers: Express interest in this task to start a conversation with the admin.',", "match": "Supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 113, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'interest': 'Suppliers and Admin: Discuss task requirements and details before submitting formal offers.',", "match": "Supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 114, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'questions': 'Suppliers: Submit formal offers after discussing requirements. Admin: Review conversations and wait for offers.',", "match": "Supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 115, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'offer': 'Admin: Review the offers from suppliers and select one to assign the task to. Suppliers: Wait for admin decision.',", "match": "supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 115, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'offer': 'Admin: Review the offers from suppliers and select one to assign the task to. Suppliers: Wait for admin decision.',", "match": "Supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 116, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'assigned': 'Supplier: Accept the assignment and start work on the task.',", "match": "Supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 117, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'in_progress': 'Supplier: Complete the work and mark the task as \"Completed\" when finished.',", "match": "Supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 133, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'open': 'Admin: Review this task and decide whether to assign it internally or make it public for suppliers.',", "match": "supplier"}, {"file": "src\\components\\tasks\\HorizontalTaskTimeline.tsx", "line": 134, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'admin_review': 'Admin: Review this task and decide whether to assign it internally or make it public for suppliers.',", "match": "supplier"}, {"file": "src\\components\\tasks\\InternalTaskActions.tsx", "line": 35, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isMaintenance = profile?.role === 'maintenance';", "match": "Maintenance"}, {"file": "src\\components\\tasks\\InternalTaskActions.tsx", "line": 35, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isMaintenance = profile?.role === 'maintenance';", "match": "maintenance"}, {"file": "src\\components\\tasks\\InternalTaskActions.tsx", "line": 36, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupport = profile?.role === 'support';", "match": "Support = profile?.role === 'support';\r\n  const isAssignableStaff = isMaintenance || isSupport;\r\n  c"}, {"file": "src\\components\\tasks\\InternalTaskActions.tsx", "line": 44, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// 2. Internal tasks viewed by assigned maintenance/support staff", "match": "maintenance"}, {"file": "src\\components\\tasks\\InternalTaskActions.tsx", "line": 44, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// 2. Internal tasks viewed by assigned maintenance/support staff", "match": "support staff\r\n  // 3. Any task assigned to maintenance/support staff who is viewing it\r\n  const sho"}, {"file": "src\\components\\tasks\\InternalTaskActions.tsx", "line": 119, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('supplier_id', task.assigned_to || '')", "match": "supplier"}, {"file": "src\\components\\tasks\\OfferCard.tsx", "line": 107, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Supplier suggested a different price. Reason:", "match": "Supplier"}, {"file": "src\\components\\tasks\\OfferCard.tsx", "line": 152, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Message Supplier", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 178, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "interface SupplierActionsProps {", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 185, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const SupplierActions = ({", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 203, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, isSupplier } = useAuth();", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 257, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!user || !isSupplier) {", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 258, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.warn(\"Cannot express interest - user not logged in or not a supplier\", {", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 260, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupplier", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 317, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Create the channel with the task creator and the supplier", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 335, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "text: `${user.email || 'A supplier'} has expressed interest in this task.`,", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 395, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!user || !isSupplier) {", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 396, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.warn(\"Cannot submit offer - user not logged in or not a supplier\", {", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 398, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupplier", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 501, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Function to mark a task as completed by the supplier", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 503, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!user || !isSupplier) {", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 504, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.warn(\"Cannot mark task as completed - user not logged in or not a supplier\", {", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 506, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupplier", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 545, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('supplier_id', user.id)", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 592, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Only show for suppliers when task has public visibility AND is in a relevant status", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 598, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Then check if the task is in a status where supplier actions are relevant", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 606, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Only show supplier actions for external tasks in relevant statuses", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 643, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Function to mark a task as in progress by the supplier", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 645, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!user || !isSupplier) {", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 646, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.warn(\"Cannot start task - user not logged in or not a supplier\", {", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 648, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupplier", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 692, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('supplier_id', user.id)", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 739, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Render the \"Start Task\" button for suppliers when the task is assigned to them", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 741, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (task?.status === 'assigned' && isSupplier && user && task.assigned_to === user.id) {", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 769, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Render the \"Mark as Completed\" button for suppliers when the task is in progress", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 771, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (task?.status === 'in_progress' && isSupplier && user && task.assigned_to === user.id) {", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 827, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<div id=\"supplier-actions\" className=\"space-y-4\">", "match": "supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 836, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<h2 className=\"text-lg font-semibold\">Supplier Actions</h2>", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 1079, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "export default SupplierActions;", "match": "Supplier"}, {"file": "src\\components\\tasks\\SupplierActions.tsx", "line": 659, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log(\"Starting task (marking as in_progress):\", task.id);", "match": "console.log(\"Starting task (marking as in_progress)"}, {"file": "src\\components\\tasks\\TaskActions.tsx", "line": 21, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierActions from './SupplierActions';", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskActions.tsx", "line": 21, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierActions from './SupplierActions';", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskActions.tsx", "line": 88, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = profile.account_type === 'supplier';", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskActions.tsx", "line": 88, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = profile.account_type === 'supplier';", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskActions.tsx", "line": 89, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isMaintenance = userRole === 'maintenance';", "match": "Maintenance"}, {"file": "src\\components\\tasks\\TaskActions.tsx", "line": 89, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isMaintenance = userRole === 'maintenance';", "match": "maintenance"}, {"file": "src\\components\\tasks\\TaskActions.tsx", "line": 103, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupplier,", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskActions.tsx", "line": 147, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "title: 'Supplier Actions',", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskActions.tsx", "line": 150, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SupplierActions", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskActions.tsx", "line": 244, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "This task has been submitted by a teacher and requires admin review before assignment.", "match": "teacher"}, {"file": "src\\components\\tasks\\TaskCard.tsx", "line": 7, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import TaskCardSupplierActions from \"./TaskCardSupplierActions\";", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCard.tsx", "line": 7, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import TaskCardSupplierActions from \"./TaskCardSupplierActions\";", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCard.tsx", "line": 25, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "fullTask?: Task; // Optional full task object for supplier actions", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskCard.tsx", "line": 194, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Show supplier actions for public tasks */}", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskCard.tsx", "line": 196, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<TaskCardSupplierActions task={fullTask} />", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 12, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "interface TaskCardSupplierActionsProps {", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 17, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const TaskCardSupplierActions = ({ task, onClick }: TaskCardSupplierActionsProps) => {", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 17, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const TaskCardSupplierActions = ({ task, onClick }: TaskCardSupplierActionsProps) => {", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 21, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, isSupplier } = useAuth();", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 25, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Only show for suppliers and public tasks", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 26, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!isSupplier || task.visibility !== 'public') {", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 35, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!user || !isSupplier) {", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 36, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.warn(\"Cannot accept job - user not logged in or not a supplier\", {", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 38, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupplier", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 57, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!user || !isSupplier) {", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 58, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.warn(\"Cannot submit counter offer - user not logged in or not a supplier\", {", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 60, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupplier", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCardSupplierActions.tsx", "line": 169, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "export default TaskCardSupplierActions;", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCompletionActions.tsx", "line": 29, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Note: 'assigned' status is now handled by the supplier in SupplierActions component", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskCompletionActions.tsx", "line": 29, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Note: 'assigned' status is now handled by the supplier in SupplierActions component", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskCompletionActions.tsx", "line": 149, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('supplier_id', task.assigned_to || '')", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskCompletionActions.tsx", "line": 258, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Pay for the completed task to release funds to the supplier.", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskCompletionActions.tsx", "line": 50, "pattern": "Authentication data in console.log", "severity": "HIGH", "category": "AUTH_DATA", "content": "console.log('Handling task status change:', {", "match": "console.log('Handling task status change:', {\r\n        taskId: task.id,\r\n        currentStatus: task"}, {"file": "src\\components\\tasks\\TaskCompletionActions.tsx", "line": 50, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log('Handling task status change:', {", "match": "console.log('Handling task status change:', {\r\n        taskId: task.id,\r\n        currentStatus: task"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 14, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'interest',       // Suppliers have expressed interest", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 15, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'questions',      // Discussion phase between admin and suppliers", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 16, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'offer',          // Suppliers have submitted offers", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 26, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'open': 'Task has been created by the task creator and is awaiting interest from suppliers',", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 27, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'interest': 'Suppliers have expressed interest in this task and may submit offers',", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 28, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'questions': 'Suppliers are discussing requirements with the admin before submitting formal offers',", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 29, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'offer': 'Suppliers have submitted formal offers for this task, awaiting admin review',", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 30, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'assigned': 'Task has been assigned to a supplier and is ready to start',", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 31, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'in_progress': 'Task has been accepted by the supplier and work is currently being performed',", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 32, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'completed': 'Work has been marked as finished by the supplier and is awaiting admin approval',", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 156, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: `Task is now ${payload.new.visibility === 'public' ? 'visible to suppliers' : 'internal only'}`,", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 327, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Suppliers: Express interest in this task to start a conversation with the admin.", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 332, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Suppliers and Admin: Discuss task requirements and details before submitting formal offers.", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 337, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Suppliers: Submit formal offers after discussing requirements. Admin: Review conversations and wait for offers.", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 342, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Admin: Review the offers from suppliers and select one to assign the task to. Suppliers: Wait for admin decision.", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 342, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Admin: Review the offers from suppliers and select one to assign the task to. Suppliers: Wait for admin decision.", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 347, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Supplier: Accept the assignment and start work on the task.", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskDetailTimeline.tsx", "line": 352, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Supplier: Complete the work and mark the task as \"Completed\" when finished.", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskFlowExplanation.tsx", "line": 28, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Understanding the task process for suppliers", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskFlowExplanation.tsx", "line": 102, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>If another supplier is chosen, you'll be notified</li>", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskOwnerOfferActions.tsx", "line": 70, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// ONLY admins should be able to manage offers - teachers should not manage offers", "match": "teacher"}, {"file": "src\\components\\tasks\\TaskOwnerOfferActions.tsx", "line": 163, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "This task has been assigned to a supplier and is {task.status === 'in_progress' ? 'in progress' : 'pending start'}.", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskOwnerOfferActions.tsx", "line": 174, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "This task has been {task.status === 'completed' ? 'marked as completed by the supplier' : 'confirmed as completed'}.", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskOwnerOfferActions.tsx", "line": 76, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log('TaskOwnerOfferActions render: completed');", "match": "console.log('TaskOwnerOfferActions render: completed')"}, {"file": "src\\components\\tasks\\TaskOwnerOfferActions.tsx", "line": 98, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log('TaskOwnerOfferActions: User CAN manage offers completed');", "match": "console.log('TaskOwnerOfferActions: User CAN manage offers completed')"}, {"file": "src\\components\\tasks\\TaskOwnerOfferActions.tsx", "line": 135, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log('TaskOwnerOfferActions: Pending offers:', {", "match": "console.log('TaskOwnerOfferActions: Pending offers:', {\r\n    pendingOffers,\r\n    count: pendingOffer"}, {"file": "src\\components\\tasks\\TaskPaymentActions.tsx", "line": 69, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Pay for the completed task to release funds to the supplier.", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskStatusTimeline.tsx", "line": 7, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'interest',       // Suppliers have expressed interest", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskStatusTimeline.tsx", "line": 8, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'questions',      // Discussion phase between admin and suppliers", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskStatusTimeline.tsx", "line": 9, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'offer',          // Suppliers have submitted offers", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskTimeline.tsx", "line": 27, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'open': 'Task is open for offers from suppliers',", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskTimeline.tsx", "line": 28, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'interest': 'Suppliers have expressed interest in this task',", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskTimeline.tsx", "line": 29, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'questions': 'Suppliers are asking questions about this task',", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskTimeline.tsx", "line": 30, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'offer': 'Suppliers have submitted offers for this task',", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskTimeline.tsx", "line": 31, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'assigned': 'Task has been assigned to a supplier',", "match": "supplier"}, {"file": "src\\components\\tasks\\TaskTimeline.tsx", "line": 32, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'in_progress': 'Supp<PERSON> is working on the task',", "match": "Supplier"}, {"file": "src\\components\\tasks\\TaskTimeline.tsx", "line": 33, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'completed': 'Task has been marked as completed by the supplier',", "match": "supplier"}, {"file": "src\\constants\\roles.ts", "line": 13, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "SUPPLIER: 'supplier',", "match": "SUPPLIER"}, {"file": "src\\constants\\roles.ts", "line": 13, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "SUPPLIER: 'supplier',", "match": "supplier"}, {"file": "src\\constants\\roles.ts", "line": 19, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "MAINTENANCE: 'maintenance',", "match": "MAINTENANCE"}, {"file": "src\\constants\\roles.ts", "line": 19, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "MAINTENANCE: 'maintenance',", "match": "maintenance"}, {"file": "src\\constants\\roles.ts", "line": 20, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "SUPPORT: 'support',", "match": "SUPPORT: 'support',\n  C<PERSON>AN<PERSON>: 'cleaner',\n  IT: 'it',\n  TEACHER: 'teacher',\n  FINANCE: 'finance',\n  "}, {"file": "src\\constants\\roles.ts", "line": 107, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "case ROLES.MAINTENANCE: return 'Maintenance Staff';", "match": "MAINTENANCE"}, {"file": "src\\constants\\roles.ts", "line": 107, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "case ROLES.MAINTENANCE: return 'Maintenance Staff';", "match": "Maintenance"}, {"file": "src\\constants\\roles.ts", "line": 108, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "case ROLES.SUPPORT: return 'Support Worker';", "match": "SUPPORT: return 'Support Worker';\n    case ROLES.CLEANER: return 'Cleaning Staff';\n    case ROLES.IT"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 43, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupplier: boolean;", "match": "Supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 45, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isTeacher: boolean;", "match": "Teacher"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 46, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupport: boolean;", "match": "Support: boolean;\r\n  isMaintenance: boolean;\r\n  isSiteAdmin: boolean;\r\n  organizationId: string | nu"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 110, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = !!profile && profile.account_type === 'supplier';", "match": "Supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 110, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = !!profile && profile.account_type === 'supplier';", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 118, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isTeacher = userRole === 'teacher';", "match": "Teacher"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 118, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isTeacher = userRole === 'teacher';", "match": "teacher"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 119, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupport = userRole === 'support';", "match": "Support = userRole === 'support';\r\n  const isMaintenance = userRole === 'maintenance';\r\n\r\n  // Site "}, {"file": "src\\contexts\\AuthContext.tsx", "line": 157, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check if the user needs to be redirected to supplier onboarding", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 160, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For suppliers, redirect to supplier onboarding if they haven't completed it", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 160, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For suppliers, redirect to supplier onboarding if they haven't completed it", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 161, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (profile.account_type === 'supplier') {", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 162, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplierOnboardingComplete =", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 171, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "navigate('/supplier/onboarding');", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 577, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check if we need to redirect to organization setup or supplier onboarding", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 579, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const redirectToSupplierOnboarding = localStorage.getItem('redirectToSupplierOnboarding') === 'true';", "match": "Supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 579, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const redirectToSupplierOnboarding = localStorage.getItem('redirectToSupplierOnboarding') === 'true';", "match": "Supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 591, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "localStorage.removeItem('redirectToSupplierOnboarding');", "match": "Supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 592, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "navigate('/supplier/onboarding', { replace: true });", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 718, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// If accountType is 'supplier', role should also be 'supplier'", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 718, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// If accountType is 'supplier', role should also be 'supplier'", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 861, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (accountType === 'supplier') {", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 1066, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// If role is 'supplier', account_type should also be 'supplier'", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 1066, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// If role is 'supplier', account_type should also be 'supplier'", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 1123, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const role = invitation.role || 'teacher';", "match": "teacher"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 1268, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const accountType = result.role === 'supplier' ? 'supplier' : 'school';", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 1268, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const accountType = result.role === 'supplier' ? 'supplier' : 'school';", "match": "supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 1311, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupplier,", "match": "Supplier"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 1313, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "is<PERSON><PERSON>er,", "match": "Teacher"}, {"file": "src\\contexts\\AuthContext.tsx", "line": 1314, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupport,", "match": "Support,\r\n        isMaintenance,\r\n        isSiteAdmin,\r\n        organizationId,\r\n        userRole,\r\n"}, {"file": "src\\hooks\\fixed-use-offers.ts", "line": 130, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": userData.user?.email?.split('@')[0] || 'A supplier';", "match": "supplier"}, {"file": "src\\hooks\\fixed-use-offers.ts", "line": 294, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: \"You've accepted the offer. The task has been assigned to the supplier.\",", "match": "supplier"}, {"file": "src\\hooks\\use-conversations.ts", "line": 124, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log(`[useConversations] Found ${userTasks.length} conversations`);", "match": "console.log(`[useConversations] Found ${userTasks.length} conversations`)"}, {"file": "src\\hooks\\use-getstream-chat.ts", "line": 423, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log('[useGetStreamChat] Channel query response:', {", "match": "console.log('[useGetStreamChat] Channel query response:', {\r\n            channelId: taskChannel.id,\r"}, {"file": "src\\hooks\\use-offers.ts", "line": 113, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": userData.user?.email?.split('@')[0] || 'A supplier';", "match": "supplier"}, {"file": "src\\hooks\\use-offers.ts", "line": 262, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: \"You've accepted the offer. The task has been assigned to the supplier.\",", "match": "supplier"}, {"file": "src\\hooks\\use-organization-users.ts", "line": 90, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Debug: Check if maintenance and support staff are being retrieved", "match": "maintenance"}, {"file": "src\\hooks\\use-organization-users.ts", "line": 90, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Debug: Check if maintenance and support staff are being retrieved", "match": "support staff are being retrieved\r\n      const maintenanceUsers = mappedData.filter(user => user.rol"}, {"file": "src\\hooks\\use-organization-users.ts", "line": 13, "pattern": "Profile data in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log('useOrganizationUsers - profile: completed');", "match": "console.log('useOrganizationUsers - profile: completed')"}, {"file": "src\\hooks\\use-organization-users.ts", "line": 80, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(`Retrieved ${mappedData.length} users for organization`);", "match": "console.log(`Retrieved ${mappedData.length} users for organization`)"}, {"file": "src\\hooks\\use-tasks.ts", "line": 52, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: \"Your task has been posted and is now visible to suppliers.\",", "match": "supplier"}, {"file": "src\\hooks\\useRolePermissions.ts", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "return userRole === 'admin' || userRole === 'teacher' || userRole === 'maintenance' || userRole === 'support';", "match": "teacher"}, {"file": "src\\hooks\\useRolePermissions.ts", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "return userRole === 'admin' || userRole === 'teacher' || userRole === 'maintenance' || userRole === 'support';", "match": "maintenance"}, {"file": "src\\hooks\\useRolePermissions.ts", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "return userRole === 'admin' || userRole === 'teacher' || userRole === 'maintenance' || userRole === 'support';", "match": "support';\n\n      case 'handle_tasks':\n        return userRole === 'admin' || userRole === 'maintenan"}, {"file": "src\\integrations\\getstream\\client.ts", "line": 735, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check for members from different organizations (excluding suppliers)", "match": "supplier"}, {"file": "src\\integrations\\getstream\\client.ts", "line": 737, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "profile.account_type !== 'supplier' &&", "match": "supplier"}, {"file": "src\\integrations\\getstream\\client.ts", "line": 1418, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log('[getAllUserTaskChannels] Getting all task channels for user: completed');", "match": "console.log('[getAllUserTaskChannels] Getting all task channels for user: completed')"}, {"file": "src\\integrations\\getstream\\client.ts", "line": 1449, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log(`[getAllUserTaskChannels] Found ${userTasks?.length || 0} tasks for user`);", "match": "console.log(`[getAllUserTaskChannels] Found ${userTasks?.length || 0} tasks for user`)"}, {"file": "src\\integrations\\getstream\\client.ts", "line": 1513, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log(`[getAllUserTaskChannels] Successfully created/verified ${channels.length} channels for user ${userId}`);", "match": "console.log(`[getAllUserTaskChannels] Successfully created/verified ${channels.length} channels for "}, {"file": "src\\integrations\\getstream\\client.ts", "line": 1561, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log(`[syncUserChannelsWithTasks] Found ${streamChannels.length} GetStream channels for user`);", "match": "console.log(`[syncUserChannelsWithTasks] Found ${streamChannels.length} GetStream channels for user`"}, {"file": "src\\integrations\\getstream\\client.ts", "line": 1587, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log(`[syncUserChannelsWithTasks] Found ${orphanedChannels.length} orphaned channels`);", "match": "console.log(`[syncUserChannelsWithTasks] Found ${orphanedChannels.length} orphaned channels`)"}, {"file": "src\\integrations\\getstream\\client.ts", "line": 1600, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log(`[syncUserChannelsWithTasks] Found ${missingChannels.length} missing channels`);", "match": "console.log(`[syncUserChannelsWithTasks] Found ${missingChannels.length} missing channels`)"}, {"file": "src\\integrations\\getstream\\client.ts", "line": 1622, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log('[syncUserChannelsWithTasks] Sync completed successfully');", "match": "console.log('[syncUserChannelsWithTasks] Sync completed successfully')"}, {"file": "src\\integrations\\supabase\\types.ts", "line": 183, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "create_supplier_organization: {", "match": "supplier"}, {"file": "src\\layouts\\MobileAppLayout.tsx", "line": 87, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = profile?.account_type === 'supplier';", "match": "Supplier"}, {"file": "src\\layouts\\MobileAppLayout.tsx", "line": 87, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = profile?.account_type === 'supplier';", "match": "supplier"}, {"file": "src\\layouts\\MobileAppLayout.tsx", "line": 146, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{(canCreateTasks || isSupplier) && (", "match": "Supplier"}, {"file": "src\\layouts\\MobileLayout.tsx", "line": 52, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = profile?.account_type === 'supplier';", "match": "Supplier"}, {"file": "src\\layouts\\MobileLayout.tsx", "line": 52, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isSupplier = profile?.account_type === 'supplier';", "match": "supplier"}, {"file": "src\\layouts\\MobileLayout.tsx", "line": 109, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{(canCreateTasks || isSupplier) && (", "match": "Supplier"}, {"file": "src\\pages\\admin\\OrganizationManagement.tsx", "line": 141, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "case 'supplier':", "match": "supplier"}, {"file": "src\\pages\\admin\\OrganizationManagement.tsx", "line": 142, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "return <Badge variant=\"outline\">Supplier</Badge>;", "match": "Supplier"}, {"file": "src\\pages\\admin\\RoleManagement.tsx", "line": 232, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"teacher\">Teacher</SelectItem>", "match": "teacher"}, {"file": "src\\pages\\admin\\RoleManagement.tsx", "line": 232, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"teacher\">Teacher</SelectItem>", "match": "Teacher"}, {"file": "src\\pages\\admin\\RoleManagement.tsx", "line": 233, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"maintenance\">Maintenance</SelectItem>", "match": "maintenance"}, {"file": "src\\pages\\admin\\RoleManagement.tsx", "line": 233, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"maintenance\">Maintenance</SelectItem>", "match": "Maintenance"}, {"file": "src\\pages\\admin\\RoleManagement.tsx", "line": 234, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"support\">Support</SelectItem>", "match": "support\">Support</SelectItem>\n                        <SelectItem value=\"supplier\">Supplier</SelectI"}, {"file": "src\\pages\\admin\\SecurityDashboard.tsx", "line": 181, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "currentRole: 'teacher',", "match": "teacher"}, {"file": "src\\pages\\admin\\SiteAdminDashboard.tsx", "line": 22, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "totalSuppliers: 0", "match": "Supplier"}, {"file": "src\\pages\\admin\\SiteAdminDashboard.tsx", "line": 57, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Get total suppliers", "match": "supplier"}, {"file": "src\\pages\\admin\\SiteAdminDashboard.tsx", "line": 58, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { count: supplierCount, error: supplierError } = await supabase", "match": "supplier"}, {"file": "src\\pages\\admin\\SiteAdminDashboard.tsx", "line": 58, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { count: supplierCount, error: supplierError } = await supabase", "match": "supplier"}, {"file": "src\\pages\\admin\\SiteAdminDashboard.tsx", "line": 61, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('account_type', 'supplier');", "match": "supplier"}, {"file": "src\\pages\\admin\\SiteAdminDashboard.tsx", "line": 63, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (userError || orgError || taskError || supplierError) {", "match": "supplier"}, {"file": "src\\pages\\admin\\SiteAdminDashboard.tsx", "line": 71, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "totalSuppliers: supplierCount || 0", "match": "Supplier"}, {"file": "src\\pages\\admin\\SiteAdminDashboard.tsx", "line": 71, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "totalSuppliers: supplierCount || 0", "match": "supplier"}, {"file": "src\\pages\\admin\\SiteAdminDashboard.tsx", "line": 143, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<CardTitle className=\"text-sm font-medium text-gray-500\">Suppliers</CardTitle>", "match": "Supplier"}, {"file": "src\\pages\\admin\\SiteAdminDashboard.tsx", "line": 149, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isLoading ? '...' : stats.totalSuppliers}", "match": "Supplier"}, {"file": "src\\pages\\AdminTasks.tsx", "line": 24, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Review and assign tasks created by teachers", "match": "teacher"}, {"file": "src\\pages\\AdminUsers.tsx", "line": 468, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"teacher\">Teacher</SelectItem>", "match": "teacher"}, {"file": "src\\pages\\AdminUsers.tsx", "line": 468, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"teacher\">Teacher</SelectItem>", "match": "Teacher"}, {"file": "src\\pages\\AdminUsers.tsx", "line": 469, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SelectItem value=\"support\">Support</SelectItem>", "match": "support\">Support</SelectItem>\r\n                            <SelectItem value=\"maintenance\">Maintenan"}, {"file": "src\\pages\\api\\admin-tasks.js", "line": 35, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log(`[API Route] Fetching tasks for admin user: ${user_id}`);", "match": "console.log(`[API Route] Fetching tasks for admin user: ${user_id}`)"}, {"file": "src\\pages\\api\\fix-task-messages.js", "line": 40, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For internal tasks, remove supplier-related messages", "match": "supplier"}, {"file": "src\\pages\\api\\fix-task-messages.js", "line": 42, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'Task has been assigned to a supplier.',", "match": "supplier"}, {"file": "src\\pages\\api\\fix-task-messages.js", "line": 43, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'Task is now public and visible to suppliers.'", "match": "supplier"}, {"file": "src\\pages\\api\\fix-task-messages.js", "line": 49, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'Internal task has been assigned to maintenance staff.'", "match": "maintenance"}, {"file": "src\\pages\\api\\submit-support-request.js", "line": 1, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// API route for submitting support requests", "match": "support requests\nimport { createClient } from '@supabase/supabase-js';\n\n// Initialize Supabase with "}, {"file": "src\\pages\\api\\submit-support-request.js", "line": 28, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { name, email, organization, organization_id, support_type, message } = req.body;", "match": "support_type, message } = req.body;\n\n    // Validate required fields\n    if (!name || !email || !sup"}, {"file": "src\\pages\\api\\submit-support-request.js", "line": 35, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Insert the support request into the database", "match": "support request into the database\n    const { data, error } = await supabase\n      .from('support_re"}, {"file": "src\\pages\\api\\submit-support-request.js", "line": 43, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "support_type,", "match": "support_type,\n        message,\n        status: 'new'\n      })"}, {"file": "src\\pages\\api\\submit-support-request.js", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('Error inserting support request:', error);", "match": "support request:', error)"}, {"file": "src\\pages\\api\\submit-support-request.js", "line": 51, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "return res.status(500).json({ error: 'Failed to submit support request' });", "match": "support request' })"}, {"file": "src\\pages\\api\\submit-support-request.js", "line": 62, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('Error processing support request:', error);", "match": "support request:', error)"}, {"file": "src\\pages\\Contact.tsx", "line": 45, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Support */}", "match": "Support */}\n                <div>\n                  <h3 className=\"text-lg font-medium text-gray-900"}, {"file": "src\\pages\\Contact.tsx", "line": 83, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Our mission is to simplify maintenance, IT, and administrative tasks, allowing schools to focus on what matters most: education.", "match": "maintenance"}, {"file": "src\\pages\\Dashboard.tsx", "line": 41, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupplier,", "match": "Supplier"}, {"file": "src\\pages\\Dashboard.tsx", "line": 44, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "is<PERSON><PERSON>er,", "match": "Teacher"}, {"file": "src\\pages\\Dashboard.tsx", "line": 45, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isMaintenance,", "match": "Maintenance"}, {"file": "src\\pages\\Dashboard.tsx", "line": 46, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupport", "match": "Support\r\n  } = useAuth()"}, {"file": "src\\pages\\Dashboard.tsx", "line": 147, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (is<PERSON>eacher) {", "match": "Teacher"}, {"file": "src\\pages\\Dashboard.tsx", "line": 148, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For teachers", "match": "teacher"}, {"file": "src\\pages\\Dashboard.tsx", "line": 175, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isMaintenance || isSupport) {", "match": "Maintenance"}, {"file": "src\\pages\\Dashboard.tsx", "line": 175, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isMaintenance || isSupport) {", "match": "Support)"}, {"file": "src\\pages\\Dashboard.tsx", "line": 176, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For maintenance/support staff - focus on assigned tasks", "match": "maintenance"}, {"file": "src\\pages\\Dashboard.tsx", "line": 176, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For maintenance/support staff - focus on assigned tasks", "match": "support staff - focus on assigned tasks\r\n        setDashboardStats({\r\n          activeTasks: 0, // N"}, {"file": "src\\pages\\Dashboard.tsx", "line": 187, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isSupplier) {", "match": "Supplier"}, {"file": "src\\pages\\Dashboard.tsx", "line": 188, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For suppliers", "match": "supplier"}, {"file": "src\\pages\\Dashboard.tsx", "line": 198, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "activeTasks: 0, // Not relevant for suppliers", "match": "supplier"}, {"file": "src\\pages\\Dashboard.tsx", "line": 209, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "}, [userTasks, userOffers, isSchool, isSupplier, isTeacher, isAdmin, isMaintenance, isSupport]);", "match": "Supplier"}, {"file": "src\\pages\\Dashboard.tsx", "line": 209, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "}, [userTasks, userOffers, isSchool, isSupplier, isTeacher, isAdmin, isMaintenance, isSupport]);", "match": "Teacher"}, {"file": "src\\pages\\Dashboard.tsx", "line": 209, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "}, [userTasks, userOffers, isSchool, isSupplier, isTeacher, isAdmin, isMaintenance, isSupport]);", "match": "Maintenance"}, {"file": "src\\pages\\Dashboard.tsx", "line": 209, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "}, [userTasks, userOffers, isSchool, isSupplier, isTeacher, isAdmin, isMaintenance, isSupport]);", "match": "Support])"}, {"file": "src\\pages\\Dashboard.tsx", "line": 244, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isTeacher={isTeacher}", "match": "Teacher"}, {"file": "src\\pages\\Dashboard.tsx", "line": 244, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isTeacher={isTeacher}", "match": "Teacher"}, {"file": "src\\pages\\Dashboard.tsx", "line": 246, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isMaintenance={isMaintenance}", "match": "Maintenance"}, {"file": "src\\pages\\Dashboard.tsx", "line": 246, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isMaintenance={isMaintenance}", "match": "Maintenance"}, {"file": "src\\pages\\Dashboard.tsx", "line": 247, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupport={isSupport}", "match": "Support={isSupport}\r\n          isSupplier={isSupplier}\r\n          pendingReviewCount={dashboardStats"}, {"file": "src\\pages\\Dashboard.tsx", "line": 260, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isMaintenance ? (", "match": "Maintenance"}, {"file": "src\\pages\\Dashboard.tsx", "line": 263, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier ? (", "match": "Supplier"}, {"file": "src\\pages\\Dashboard.tsx", "line": 269, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isAdmin && !isSupplier ? (", "match": "Supplier"}, {"file": "src\\pages\\Dashboard.tsx", "line": 287, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isTeacher={isTeacher}", "match": "Teacher"}, {"file": "src\\pages\\Dashboard.tsx", "line": 287, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isTeacher={isTeacher}", "match": "Teacher"}, {"file": "src\\pages\\Dashboard.tsx", "line": 289, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isMaintenance={isMaintenance}", "match": "Maintenance"}, {"file": "src\\pages\\Dashboard.tsx", "line": 289, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isMaintenance={isMaintenance}", "match": "Maintenance"}, {"file": "src\\pages\\Dashboard.tsx", "line": 290, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupport={isSupport}", "match": "Support={isSupport}\r\n              isSupplier={isSupplier}\r\n              isLoading={isLoadingUserTa"}, {"file": "src\\pages\\EmergencyTaskActions.tsx", "line": 64, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isMaintenance: profileData?.role === 'maintenance'", "match": "Maintenance"}, {"file": "src\\pages\\EmergencyTaskActions.tsx", "line": 64, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isMaintenance: profileData?.role === 'maintenance'", "match": "maintenance"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 34, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, profile, isAdmin, isSupplier } = useAuth();", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 278, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "to={isSupplier ? \"/tasks\" : \"/dashboard?tab=my-tasks\"}", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 282, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier ? \"Back to Tasks\" : \"Back to My Tasks\"}", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 320, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "to={isSupplier ? \"/tasks\" : \"/dashboard?tab=my-tasks\"}", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 324, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier ? \"Back to Tasks\" : \"Back to My Tasks\"}", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 334, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Button onClick={() => navigate(isSupplier ? '/tasks' : '/dashboard?tab=my-tasks')}>", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 335, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier ? \"Return to Tasks\" : \"Return to My Tasks\"}", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 347, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "to={isSupplier ? \"/tasks\" : \"/dashboard?tab=my-tasks\"}", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 351, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier ? \"Back to Tasks\" : \"Back to My Tasks\"}", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 361, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Button onClick={() => navigate(isSupplier ? '/tasks' : '/dashboard?tab=my-tasks')}>", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 362, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier ? \"Return to Tasks\" : \"Return to My Tasks\"}", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 372, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "to={isSupplier ? \"/tasks\" : \"/dashboard?tab=my-tasks\"}", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 376, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier ? \"Back to Tasks\" : \"Back to My Tasks\"}", "match": "Supplier"}, {"file": "src\\pages\\EnhancedTask.tsx", "line": 503, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Communicate with {isInternalTask(task) ? 'staff' : 'suppliers'} about this task", "match": "supplier"}, {"file": "src\\pages\\fix-task-completion-for-admins.jsx", "line": 10, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "{console.log('Task Completion Debug:', {", "match": "console.log('Task Completion Debug:', {\r\n        isTaskOwner,\r\n        taskStatus: task?.status,\r\n  "}, {"file": "src\\pages\\fix-task-completion-for-admins.jsx", "line": 39, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "{console.log('Task Completion Debug:', {", "match": "console.log('Task Completion Debug:', {\r\n        isTaskOwner,\r\n        isAdmin,\r\n        canManageCo"}, {"file": "src\\pages\\FixedTask.tsx", "line": 22, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierActions from '@/components/tasks/SupplierActions';", "match": "Supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 22, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierActions from '@/components/tasks/SupplierActions';", "match": "Supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 49, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, isSupplier, isSchool } = useAuth();", "match": "Supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 58, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { isAdmin, isTeacher, isSupport, isMaintenance, profile } = useAuth();", "match": "Teacher"}, {"file": "src\\pages\\FixedTask.tsx", "line": 58, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { isAdmin, isTeacher, isSupport, isMaintenance, profile } = useAuth();", "match": "Support, isMaintenance, profile } = useAuth()"}, {"file": "src\\pages\\FixedTask.tsx", "line": 136, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: `Task is now ${payload.new.visibility === 'public' ? 'visible to suppliers' : 'internal only'}`,", "match": "supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 238, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!isSupplier) {", "match": "Supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 242, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: \"Only suppliers can submit offers on tasks.\",", "match": "supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 334, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: \"Offer accepted successfully. The task has been assigned to the supplier.\",", "match": "supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 347, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('supplier_id', acceptedOffer.user_id)", "match": "supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 634, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Supplier actions */}", "match": "Supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 635, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{(task?.status === 'open' || task?.status === 'assigned' || userOffer) && isSupplier && (", "match": "Supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 636, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SupplierActions", "match": "Supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 662, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Note for teachers who created the task but can't manage offers */}", "match": "teacher"}, {"file": "src\\pages\\FixedTask.tsx", "line": 677, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* For task owners (except teachers) */}", "match": "teacher"}, {"file": "src\\pages\\FixedTask.tsx", "line": 678, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isTaskOwner && !isTeacher && (task?.status === 'assigned' || (offers && offers.length > 0)) && (", "match": "Teacher"}, {"file": "src\\pages\\FixedTask.tsx", "line": 683, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<MessageSquare className=\"mr-2 h-4 w-4\" /> Message Supplier", "match": "Supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 687, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* For teachers */}", "match": "teacher"}, {"file": "src\\pages\\FixedTask.tsx", "line": 688, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isTaskOwner && isTeacher && (task?.status === 'assigned' || (offers && offers.length > 0)) && (", "match": "Teacher"}, {"file": "src\\pages\\FixedTask.tsx", "line": 698, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* For suppliers with offers */}", "match": "supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 699, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier && userOffer && (", "match": "Supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 708, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* For suppliers without offers */}", "match": "supplier"}, {"file": "src\\pages\\FixedTask.tsx", "line": 709, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier && task?.status === 'open' && !userOffer && (", "match": "Supplier"}, {"file": "src\\pages\\Help.tsx", "line": 12, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupportRequestForm from '@/components/support/SupportRequestForm';", "match": "SupportRequestForm from '@/components/support/SupportRequestForm';\r\nimport {\r\n  Search,\r\n  BookOpen,"}, {"file": "src\\pages\\Help.tsx", "line": 214, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Tips and guides for maintenance and service providers", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 355, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Can't find what you're looking for? Our support team is here to help.", "match": "support team is here to help.\r\n                  </CardDescription>\r\n                </CardHeader>\r\n"}, {"file": "src\\pages\\Help.tsx", "line": 361, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "onClick={() => setActiveSection('support-request')}", "match": "support-request')"}, {"file": "src\\pages\\Help.tsx", "line": 363, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Contact Support", "match": "Support\r\n                  </Button>\r\n                </CardContent>\r\n              </Card>\r\n       "}, {"file": "src\\pages\\Help.tsx", "line": 435, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Schools can post a wide variety of maintenance and service tasks on ClassTasker, including:", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 438, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Building maintenance and repairs</li>", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 440, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Grounds maintenance and landscaping</li>", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 441, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>IT support and equipment installation</li>", "match": "support and equipment installation</li>\r\n                        <li>Cleaning services</li>\r\n       "}, {"file": "src\\pages\\Help.tsx", "line": 648, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Posting your first task on ClassTasker is simple. Follow these steps to get your maintenance or service needs addressed quickly and efficiently.", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 669, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Choose the most appropriate category for your task (e.g., Plumbing, Electrical, Painting, IT Support).", "match": "Support)"}, {"file": "src\\pages\\Help.tsx", "line": 772, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Finding the right service provider for your school's maintenance and service needs is crucial. ClassTasker makes it easy to find qualified, reliable professionals who can complete your tasks to a high standard.", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 877, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Set up recurring maintenance tasks with providers you trust", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 1023, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Schools pay the agreed price for the task plus a small service fee of 5% to cover payment processing and platform maintenance.", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 1071, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "A well-written task description is essential for attracting the right service providers and ensuring your maintenance or service needs are met accurately. Follow these guidelines to create effective task descriptions that get results.", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 1243, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Setting an appropriate budget for your maintenance and service tasks is crucial for attracting qualified providers and ensuring value for money. This guide will help you establish realistic budgets for your school's tasks.", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 1256, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Contact local suppliers for rough estimates</li>", "match": "supplier"}, {"file": "src\\pages\\Help.tsx", "line": 1258, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Check industry pricing guides for common maintenance tasks</li>", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 1276, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Tasks requiring certified professionals (e.g., electricians, gas engineers) will typically cost more than general maintenance tasks.", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 1376, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Here are some general guidelines for common school maintenance tasks (prices may vary by region and specific requirements):", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 1404, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<td className=\"py-2 px-4 border-b\">Grounds Maintenance</td>", "match": "Maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 1721, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span className=\"font-medium\">Contact ClassTasker support if needed</span> - If you can't resolve the issues directly with the provider", "match": "support if needed</span> - If you can't resolve the issues directly with the provider\r\n             "}, {"file": "src\\pages\\Help.tsx", "line": 1979, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Attach photos of similar work you've completed, relevant certifications, or other documents that support your offer.", "match": "support your offer.\r\n                          </p>\r\n                        </div>\r\n               "}, {"file": "src\\pages\\Help.tsx", "line": 2110, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Offer clear documentation of the work completed, including any warranties, maintenance instructions, or certificates.", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 2284, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "ClassTasker's escrow system protects both you and the client. The client's payment is secured before you begin work, and you're guaranteed payment once the work is approved. If any disputes arise, ClassTasker's support team can help mediate.", "match": "support team can help mediate.\r\n                      </AlertDescription>\r\n                    </Ale"}, {"file": "src\\pages\\Help.tsx", "line": 2475, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Control who can see your contact details. Options include \"Only ClassTasker Support,\" \"Schools I've worked with,\" or \"All platform users.\"", "match": "Support,\" \"Schools I've worked with,\" or \"All platform users.\"\r\n                          </p>\r\n    "}, {"file": "src\\pages\\Help.tsx", "line": 2540, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "All communications through ClassTasker's messaging system are private between you and the other party. ClassTasker staff can access messages only for support purposes or if required by law.", "match": "support purposes or if required by law.\r\n                          </p>\r\n                        </d"}, {"file": "src\\pages\\Help.tsx", "line": 2810, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Review your recent login activity in the \"Security\" section. If you notice any suspicious activity, change your password immediately and contact ClassTasker support.", "match": "support.\r\n                          </p>\r\n                        </div>\r\n                      </di"}, {"file": "src\\pages\\Help.tsx", "line": 2830, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Your account type (school/educational institution or service provider) determines what features are available to you. If you need to change your account type, contact ClassTasker support.", "match": "support.\r\n                          </p>\r\n                        </div>\r\n\r\n                        "}, {"file": "src\\pages\\Help.tsx", "line": 2892, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Setting up your organization on ClassTasker allows you to manage multiple users, centralize billing, and streamline maintenance tasks across your educational institution. This guide walks you through the process of creating and configuring your organization.", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 3003, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Define custom roles beyond the default ones (admin, teacher, maintenance staff) and set specific permissions for each role.", "match": "teacher"}, {"file": "src\\pages\\Help.tsx", "line": 3003, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Define custom roles beyond the default ones (admin, teacher, maintenance staff) and set specific permissions for each role.", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 3033, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Create standardized task templates for common maintenance requests</li>", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 3035, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Configure detailed reporting to track maintenance spending and patterns</li>", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 3038, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "A well-configured organization setup will streamline your maintenance management process, improve accountability, and provide valuable insights into your facility management operations.", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 3113, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span className=\"font-medium\">Teacher</span>", "match": "Teacher"}, {"file": "src\\pages\\Help.tsx", "line": 3122, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span className=\"font-medium\">Maintenance Staff</span>", "match": "Maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 3124, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Can view and update internal maintenance tasks. Cannot create tasks for external service providers or access billing information.", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 3248, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Set up ClassTasker as a supplier in your finance system</li>", "match": "supplier"}, {"file": "src\\pages\\Help.tsx", "line": 3309, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "ClassTasker's standard payment terms are 30 days from the invoice date. Late payments may affect your ability to post new tasks. If you require different payment terms, please contact our support team to discuss options.", "match": "support team to discuss options.\r\n                      </AlertDescription>\r\n                    </A"}, {"file": "src\\pages\\Help.tsx", "line": 3339, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Task Type Analysis - Spending broken down by type of maintenance task</li>", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 3352, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "To set maintenance budgets for your organization:", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 3425, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "For convenient, secure payments, ClassTasker supports:", "match": "supports:\r\n                          </p>\r\n                          <ul className=\"list-disc pl-5 m"}, {"file": "src\\pages\\Help.tsx", "line": 3545, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Understanding the fee structure on ClassTasker helps you budget effectively for your maintenance and service needs. This guide explains all fees associated with using the platform.", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 3655, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Priority support</li>", "match": "support</li>\r\n                            <li>Advanced reporting features</li>\r\n                    "}, {"file": "src\\pages\\Help.tsx", "line": 3773, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "ClassTasker's support team will review the request, considering both parties' input and any supporting evidence.", "match": "support team will review the request, considering both parties' input and any supporting evidence.\r\n"}, {"file": "src\\pages\\Help.tsx", "line": 3908, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "For credit and debit card payments, we support 3D Secure (Verified by Visa, Mastercard SecureCode, etc.), which adds an additional layer of authentication for online transactions.", "match": "support 3D Secure (Verified by Visa, Mastercard SecureCode, etc.)"}, {"file": "src\\pages\\Help.tsx", "line": 4009, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Contact ClassTasker support <NAME_EMAIL></li>", "match": "support <NAME_EMAIL></li>\r\n                        <li>Change your passwo"}, {"file": "src\\pages\\Help.tsx", "line": 4106, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Create and manage templates for common maintenance tasks:", "match": "maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 4151, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Teacher/Staff - Can create and manage their own tasks</li>", "match": "Teacher"}, {"file": "src\\pages\\Help.tsx", "line": 4222, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Maintenance spending comparisons across schools</li>", "match": "Maintenance"}, {"file": "src\\pages\\Help.tsx", "line": 4273, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Support Request Form */}", "match": "Support Request Form */}\r\n                {activeSection === 'support-request' && (\r\n               "}, {"file": "src\\pages\\Help.tsx", "line": 4309, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "activeSection !== 'support-request' && (", "match": "support-request' && (\r\n                  <div className=\"p-6 text-center\">\r\n                    <h3 "}, {"file": "src\\pages\\HowItWorks.tsx", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"mb-4\">Assign maintenance tasks to your internal staff, track progress, and ensure timely completion.</p>", "match": "maintenance"}, {"file": "src\\pages\\HowItWorks.tsx", "line": 62, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span>Prioritize urgent maintenance needs</span>", "match": "maintenance"}, {"file": "src\\pages\\HowItWorks.tsx", "line": 78, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"mb-4\">Find, hire, and manage qualified external contractors for specialized maintenance tasks.</p>", "match": "maintenance"}, {"file": "src\\pages\\HowItWorks.tsx", "line": 134, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"mb-4\">Generate comprehensive reports and gain insights into your maintenance operations.</p>", "match": "maintenance"}, {"file": "src\\pages\\HowItWorks.tsx", "line": 170, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Efficiently manage tasks assigned to your internal maintenance staff", "match": "maintenance"}, {"file": "src\\pages\\HowItWorks.tsx", "line": 182, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-gray-600 text-sm mt-1\">Teachers and staff can create maintenance requests that are automatically routed to administrators.</p>", "match": "Teacher"}, {"file": "src\\pages\\HowItWorks.tsx", "line": 182, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-gray-600 text-sm mt-1\">Teachers and staff can create maintenance requests that are automatically routed to administrators.</p>", "match": "maintenance"}, {"file": "src\\pages\\HowItWorks.tsx", "line": 189, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-gray-600 text-sm mt-1\">Administrators can assign tasks to specific maintenance staff members based on skills and availability.</p>", "match": "maintenance"}, {"file": "src\\pages\\HowItWorks.tsx", "line": 208, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-gray-600 text-sm mt-1\">Set priority levels for urgent maintenance needs.</p>", "match": "maintenance"}, {"file": "src\\pages\\HowItWorks.tsx", "line": 222, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-gray-600 text-sm mt-1\">Plan maintenance work around school hours and events.</p>", "match": "maintenance"}, {"file": "src\\pages\\HowItWorks.tsx", "line": 237, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Find and hire qualified contractors for specialized maintenance tasks", "match": "maintenance"}, {"file": "src\\pages\\HowItWorks.tsx", "line": 323, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-gray-600 text-sm mt-1\">Mark tasks as completed and upload supporting documentation for audit purposes.</p>", "match": "supporting documentation for audit purposes.</p>\r\n                          </div>\r\n                "}, {"file": "src\\pages\\HowItWorks.tsx", "line": 463, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p>All payments are made to ClassTasker using your school's preferred payment methods, including BACS, within 15 days upon invoice. We support batch payment processing and manage all supplier payments on your behalf, ensuring contractors are paid promptly once work is completed satisfactorily. This streamlined approach simplifies your financial processes while protecting both schools and contractors.</p>", "match": "support batch payment processing and manage all supplier payments on your behalf, ensuring contracto"}, {"file": "src\\pages\\Index.tsx", "line": 49, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "title: 'Landscaping & School Garden Maintenance',", "match": "Maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: 'Regular maintenance required for our school garden and grounds. Tasks include mowing, pruning, weeding, and seasonal planting.',", "match": "maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 75, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "title: 'School Garden Maintenance',", "match": "Maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 81, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "title: 'Sports Field Maintenance',", "match": "Maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 87, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "title: 'Electrical System Maintenance',", "match": "Maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 127, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Maintenance", "match": "Maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 154, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Link to=\"/register\">Become a Supplier</Link>", "match": "Supplier"}, {"file": "src\\pages\\Index.tsx", "line": 176, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "alt=\"School maintenance\"", "match": "maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 275, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Create maintenance tasks and assign them internally to your staff or externally to qualified suppliers based on your needs.", "match": "maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 275, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Create maintenance tasks and assign them internally to your staff or externally to qualified suppliers based on your needs.", "match": "supplier"}, {"file": "src\\pages\\Index.tsx", "line": 309, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Assign roles to your staff, manage external suppliers, and ensure everyone has the right access to complete their tasks.", "match": "supplier"}, {"file": "src\\pages\\Index.tsx", "line": 326, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Monitor progress, generate reports for audits, and maintain a complete history of all maintenance and compliance activities.", "match": "maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 347, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span>Assign tasks to your maintenance staff, teachers, or support team</span>", "match": "maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 347, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span>Assign tasks to your maintenance staff, teachers, or support team</span>", "match": "teacher"}, {"file": "src\\pages\\Index.tsx", "line": 347, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span>Assign tasks to your maintenance staff, teachers, or support team</span>", "match": "support team</span>\n                  </li>\n                  <li className=\"flex items-start\">\n    "}, {"file": "src\\pages\\Index.tsx", "line": 414, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "We're specifically designed to address the unique maintenance challenges faced by educational institutions.", "match": "maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 423, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<h3 className=\"text-lg font-semibold mb-2\">Verified Suppliers</h3>", "match": "Supplier"}, {"file": "src\\pages\\Index.tsx", "line": 425, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "All our suppliers are thoroughly vetted for safety and reliability.", "match": "supplier"}, {"file": "src\\pages\\Index.tsx", "line": 445, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Our review system ensures only the best suppliers remain on our platform.", "match": "supplier"}, {"file": "src\\pages\\Index.tsx", "line": 476, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Simplify Your School Maintenance", "match": "Maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 479, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Ready to Simplify Your School Maintenance?", "match": "Maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 482, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Use Classtasker to manage your maintenance needs, ensure compliance, and save money.", "match": "maintenance"}, {"file": "src\\pages\\Index.tsx", "line": 489, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Link to=\"/register\">Become a Supplier</Link>", "match": "Supplier"}, {"file": "src\\pages\\InternalTask.tsx", "line": 252, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('supplier_id', task.assigned_to || '')", "match": "supplier"}, {"file": "src\\pages\\InternalTask.tsx", "line": 450, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isMaintenance = profile?.role === 'maintenance';", "match": "Maintenance"}, {"file": "src\\pages\\InternalTask.tsx", "line": 450, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isMaintenance = profile?.role === 'maintenance';", "match": "maintenance"}, {"file": "src\\pages\\InternalTask.tsx", "line": 524, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Tag size={16} className=\"mr-1\" /> Internal Maintenance Task", "match": "Maintenance"}, {"file": "src\\pages\\InternalTask.tsx", "line": 573, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{task.category || 'Maintenance'}", "match": "Maintenance"}, {"file": "src\\pages\\InternalTask.tsx", "line": 780, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p><strong>Is Maintenance:</strong> {isMaintenance ? 'Yes' : 'No'}</p>", "match": "Maintenance"}, {"file": "src\\pages\\InternalTask.tsx", "line": 780, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p><strong>Is Maintenance:</strong> {isMaintenance ? 'Yes' : 'No'}</p>", "match": "Maintenance"}, {"file": "src\\pages\\MessagesSimple.tsx", "line": 226, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-sm mt-1\">Start a conversation by messaging a task owner or supplier</p>", "match": "supplier"}, {"file": "src\\pages\\mobile\\GetStreamChatList.tsx", "line": 151, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "(userProfile?.account_type === 'supplier' && task.visibility === 'public')", "match": "supplier"}, {"file": "src\\pages\\mobile\\GetStreamChatList.tsx", "line": 290, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "(userProfile?.account_type === 'supplier' && task.visibility === 'public')", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 25, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: string;", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 34, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier?: {", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 109, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Fetch chat threads where the user is a participant (either as admin or supplier)", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 126, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id,", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 131, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier:supplier_id(id, first_name, last_name, email)", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 131, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier:supplier_id(id, first_name, last_name, email)", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 133, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".or(`supplier_id.eq.${user.id},admin_id.eq.${user.id}`)", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 350, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".or(`supplier_id.eq.${user.id},admin_id.eq.${user.id}`)", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 588, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "If the problem persists, try logging out and back in, or contact support.", "match": "support.\n            </p>\n          </div>\n        )"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 654, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{thread.supplier?.first_name ? (", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 656, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{thread.supplier.first_name.charAt(0)}{thread.supplier.last_name?.charAt(0) || ''}", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 656, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{thread.supplier.first_name.charAt(0)}{thread.supplier.last_name?.charAt(0) || ''}", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 678, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{thread.supplier && user?.id !== thread.supplier_id ? (", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 678, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{thread.supplier && user?.id !== thread.supplier_id ? (", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 680, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{thread.supplier.first_name || thread.supplier.email?.[0]?.split('@')[0] || 'Supplier'}:", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 680, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{thread.supplier.first_name || thread.supplier.email?.[0]?.split('@')[0] || 'Supplier'}:", "match": "supplier"}, {"file": "src\\pages\\mobile\\MobileChatList.tsx", "line": 680, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{thread.supplier.first_name || thread.supplier.email?.[0]?.split('@')[0] || 'Supplier'}:", "match": "Supplier"}, {"file": "src\\pages\\mobile\\OptimizedChatList.tsx", "line": 76, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".or(`supplier_id.eq.${user.id},admin_id.eq.${user.id}`)", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 24, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: string;", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 33, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier?: {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 82, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Fetch chat threads where the user is a participant (either as admin or supplier)", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 92, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 97, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier:supplier_id!inner(id, first_name, last_name, email)", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 97, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier:supplier_id!inner(id, first_name, last_name, email)", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 99, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".or(`supplier_id.eq.${user.id},admin_id.eq.${user.id}`)", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 185, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: thread.supplier_id,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 185, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: thread.supplier_id,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 194, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier: thread.supplier,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 194, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier: thread.supplier,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 210, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: thread.supplier_id,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 210, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: thread.supplier_id,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 219, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier: thread.supplier,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleGetStreamChatList.tsx", "line": 219, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier: thread.supplier,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 25, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: string;", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 34, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier?: {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 129, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Fetch chat threads where the user is a participant (either as admin or supplier)", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 139, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 144, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier:supplier_id!inner(id, first_name, last_name, email)", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 144, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier:supplier_id!inner(id, first_name, last_name, email)", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 146, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".or(`supplier_id.eq.${user.id},admin_id.eq.${user.id}`)", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 227, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Get the first item from task and supplier arrays if they exist", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 232, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Handle supplier data properly", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 233, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "let supplierData = null;", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 234, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (Array.isArray(thread.supplier) && thread.supplier.length > 0) {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 234, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (Array.isArray(thread.supplier) && thread.supplier.length > 0) {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 235, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplier = thread.supplier[0];", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 235, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplier = thread.supplier[0];", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 236, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplierData = {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 237, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "id: supplier.id || '',", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 238, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "first_name: supplier.first_name || '',", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 239, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "last_name: supplier.last_name || '',", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 240, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "email: supplier.email || []", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 242, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (thread.supplier && typeof thread.supplier === 'object' && !Array.isArray(thread.supplier)) {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 242, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (thread.supplier && typeof thread.supplier === 'object' && !Array.isArray(thread.supplier)) {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 242, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (thread.supplier && typeof thread.supplier === 'object' && !Array.isArray(thread.supplier)) {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 243, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Use type assertion to handle the supplier object", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 244, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplier = thread.supplier as any;", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 244, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplier = thread.supplier as any;", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 245, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplierData = {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 246, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "id: supplier.id || '',", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 247, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "first_name: supplier.first_name || '',", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 248, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "last_name: supplier.last_name || '',", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 249, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "email: supplier.email || []", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 261, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: thread.supplier_id,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 261, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: thread.supplier_id,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 270, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier: supplierData,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 270, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier: supplierData,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 279, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Get the first item from task and supplier arrays if they exist", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 284, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Handle supplier data properly", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 285, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "let supplierData = null;", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 286, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (Array.isArray(thread.supplier) && thread.supplier.length > 0) {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 286, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (Array.isArray(thread.supplier) && thread.supplier.length > 0) {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 287, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplier = thread.supplier[0];", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 287, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplier = thread.supplier[0];", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 288, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplierData = {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 289, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "id: supplier.id || '',", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 290, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "first_name: supplier.first_name || '',", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 291, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "last_name: supplier.last_name || '',", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 292, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "email: supplier.email || []", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 294, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (thread.supplier && typeof thread.supplier === 'object' && !Array.isArray(thread.supplier)) {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 294, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (thread.supplier && typeof thread.supplier === 'object' && !Array.isArray(thread.supplier)) {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 294, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (thread.supplier && typeof thread.supplier === 'object' && !Array.isArray(thread.supplier)) {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 295, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Use type assertion to handle the supplier object", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 296, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplier = thread.supplier as any;", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 296, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplier = thread.supplier as any;", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 297, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplierData = {", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 298, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "id: supplier.id || '',", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 299, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "first_name: supplier.first_name || '',", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 300, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "last_name: supplier.last_name || '',", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 301, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "email: supplier.email || []", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 313, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: thread.supplier_id,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 313, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_id: thread.supplier_id,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 322, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier: supplierData,", "match": "supplier"}, {"file": "src\\pages\\mobile\\SimpleMobileChatList.tsx", "line": 322, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier: supplierData,", "match": "supplier"}, {"file": "src\\pages\\Plans.tsx", "line": 55, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<FeatureItem included text=\"Standard support\" />", "match": "support\" />\n                  <FeatureItem included={false} text=\"Multi-school dashboard\" />\n       "}, {"file": "src\\pages\\Plans.tsx", "line": 261, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p>All payments are made to ClassTasker using your school's preferred payment methods, including BACS, within 15 days upon invoice. We support batch payment processing and manage all supplier payments on your behalf, ensuring contractors are paid promptly once work is completed satisfactorily. This streamlined approach simplifies your financial processes while protecting both schools and contractors.</p>", "match": "support batch payment processing and manage all supplier payments on your behalf, ensuring contracto"}, {"file": "src\\pages\\PostTask.tsx", "line": 198, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// This ensures teachers can see their pending tasks in the dashboard", "match": "teacher"}, {"file": "src\\pages\\PostTask.tsx", "line": 204, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// If user is not logged in or is a supplier, show access denied", "match": "supplier"}, {"file": "src\\pages\\PostTask.tsx", "line": 229, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "You don't have permission to create tasks. Only teachers, admins, maintenance, and support staff can create tasks.", "match": "teacher"}, {"file": "src\\pages\\PostTask.tsx", "line": 229, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "You don't have permission to create tasks. Only teachers, admins, maintenance, and support staff can create tasks.", "match": "maintenance"}, {"file": "src\\pages\\PostTask.tsx", "line": 229, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "You don't have permission to create tasks. Only teachers, admins, maintenance, and support staff can create tasks.", "match": "support staff can create tasks.\r\n            </AlertDescription>\r\n          </Alert>\r\n        </div>"}, {"file": "src\\pages\\PostTask.tsx", "line": 244, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Describe your task in detail to get the best responses from suppliers.", "match": "supplier"}, {"file": "src\\pages\\PostTask.tsx", "line": 273, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Include all details suppliers will need to know, such as specific requirements, materials needed, or any special instructions.", "match": "supplier"}, {"file": "src\\pages\\PostTask.tsx", "line": 442, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Upload up to 5 images to help describe the task. Images will be visible to administrators and suppliers.", "match": "supplier"}, {"file": "src\\pages\\PostTask.tsx", "line": 450, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Your task will be reviewed by an administrator who will either assign it to internal staff or make it available to external suppliers.", "match": "supplier"}, {"file": "src\\pages\\PostTask.tsx", "line": 472, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Specify the building and room to help suppliers locate the task precisely</li>", "match": "supplier"}, {"file": "src\\pages\\PostTask.tsx", "line": 474, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Add photos to help suppliers understand the task better</li>", "match": "supplier"}, {"file": "src\\pages\\Profile.tsx", "line": 32, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Valid tabs: 'about' and 'business' for suppliers", "match": "supplier"}, {"file": "src\\pages\\Profile.tsx", "line": 34, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (profile?.account_type === 'supplier') {", "match": "supplier"}, {"file": "src\\pages\\Profile.tsx", "line": 212, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Business profile editing view for suppliers", "match": "supplier"}, {"file": "src\\pages\\Profile.tsx", "line": 213, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (isEditingBusiness && isOwnProfile && profile.account_type === 'supplier') {", "match": "supplier"}, {"file": "src\\pages\\Profile.tsx", "line": 312, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{profile.account_type === 'supplier' && (", "match": "supplier"}, {"file": "src\\pages\\Profile.tsx", "line": 321, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{profile.account_type === 'supplier' && (", "match": "supplier"}, {"file": "src\\pages\\pwa\\PWAContact.tsx", "line": 7, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupportRequestForm from '@/components/support/SupportRequestForm';", "match": "SupportRequestForm from '@/components/support/SupportRequestForm';\n\nconst PWAContact: React.FC = ()"}, {"file": "src\\pages\\pwa\\PWAContact.tsx", "line": 19, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<h1 className=\"text-lg font-semibold ml-2\">Contact Support</h1>", "match": "Support</h1>\n        </div>\n\n        {/* Support Request Form */}\n        <Card className=\"mb-6\">\n  "}, {"file": "src\\pages\\pwa\\PWAContact.tsx", "line": 90, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Our mission is to simplify maintenance, IT, and administrative tasks, allowing schools to focus on what matters most: education.", "match": "maintenance"}, {"file": "src\\pages\\pwa\\PWAHelp.tsx", "line": 72, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "ClassTasker is a platform that connects schools and educational institutions with qualified maintenance and service providers.", "match": "maintenance"}, {"file": "src\\pages\\pwa\\PWAHelp.tsx", "line": 112, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "If you're not satisfied with the completed work, you can request revisions from the service provider. If issues persist, our support team can help mediate the situation. Our goal is to ensure that all parties are satisfied with the outcome.", "match": "support team can help mediate the situation. Our goal is to ensure that all parties are satisfied wi"}, {"file": "src\\pages\\pwa\\PWAHelp.tsx", "line": 133, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Contact Support", "match": "Support\n                </Button>\n              </CardContent>\n            </Card>\n          </TabsC"}, {"file": "src\\pages\\pwa\\PWAHelp.tsx", "line": 274, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Contact Support", "match": "Support\n                  </Button>\n                </CardContent>\n              </Card>\n           "}, {"file": "src\\pages\\pwa\\PWAPostTask.tsx", "line": 248, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// This ensures teachers can see their pending tasks in the dashboard", "match": "teacher"}, {"file": "src\\pages\\pwa\\PWAPostTask.tsx", "line": 267, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": \"You don't have permission to create tasks. Only teachers, admins, maintenance, and support staff can create tasks.\"}", "match": "teacher"}, {"file": "src\\pages\\pwa\\PWAPostTask.tsx", "line": 267, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": \"You don't have permission to create tasks. Only teachers, admins, maintenance, and support staff can create tasks.\"}", "match": "maintenance"}, {"file": "src\\pages\\pwa\\PWAPostTask.tsx", "line": 267, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": \"You don't have permission to create tasks. Only teachers, admins, maintenance, and support staff can create tasks.\"}", "match": "support staff can create tasks.\"}\n            </AlertDescription>\n          </Alert>\n          <Butt"}, {"file": "src\\pages\\pwa\\PWAPostTask.tsx", "line": 315, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Include all details suppliers will need to know.", "match": "supplier"}, {"file": "src\\pages\\pwa\\PWAPostTask.tsx", "line": 534, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Your task will be reviewed by an administrator who will either assign it to internal staff or make it available to external suppliers.", "match": "supplier"}, {"file": "src\\pages\\pwa\\PWAPrivacyPolicy.tsx", "line": 71, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>Provide customer support</li>", "match": "support</li>\n              </ul>\n            </section>\n\n            <section>\n              <h3 cla"}, {"file": "src\\pages\\pwa\\PWAProfile.tsx", "line": 189, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-gray-600\">{profile.job_title || (profile.account_type === 'school' ? 'School Staff' : 'Supplier')}</p>", "match": "Supplier"}, {"file": "src\\pages\\pwa\\PWAProfile.tsx", "line": 247, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Help & Support Section */}", "match": "Support Section */}\r\n          <Card>\r\n            <CardContent className=\"p-4\">\r\n              <h3 "}, {"file": "src\\pages\\pwa\\PWASettings.tsx", "line": 174, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Coming soon - Dark mode support", "match": "support\n              </p>\n            </CardContent>\n          </Card>\n\n          {/* Privacy & Sec"}, {"file": "src\\pages\\pwa\\PWATermsAndPrivacy.tsx", "line": 207, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Contact Support", "match": "Support\n          </Button>\n        </div>\n      </div>\n    </PWAMobileLayout>\n  )"}, {"file": "src\\pages\\Register.tsx", "line": 151, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Set up your {accountType === 'school' ? 'school' : 'supplier'} profile</li>", "match": "supplier"}, {"file": "src\\pages\\Register.tsx", "line": 275, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<div className={`rounded-full p-2 ${accountType === 'supplier' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100'}`}>", "match": "supplier"}, {"file": "src\\pages\\Register.tsx", "line": 279, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<RadioGroupItem value=\"supplier\" id=\"supplier\" className=\"sr-only\" />", "match": "supplier"}, {"file": "src\\pages\\Register.tsx", "line": 279, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<RadioGroupItem value=\"supplier\" id=\"supplier\" className=\"sr-only\" />", "match": "supplier"}, {"file": "src\\pages\\Register.tsx", "line": 280, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Label htmlFor=\"supplier\" className=\"font-medium block\">", "match": "supplier"}, {"file": "src\\pages\\Register.tsx", "line": 281, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Supplier Account", "match": "Supplier"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 59, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-sm text-gray-600\">Create a maintenance task and get quotes from qualified suppliers</p>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 59, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-sm text-gray-600\">Create a maintenance task and get quotes from qualified suppliers</p>", "match": "supplier"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 66, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-sm text-gray-600\">Monitor progress and communicate with suppliers through our platform</p>", "match": "supplier"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 91, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Communicating with suppliers effectively</li>", "match": "supplier"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 92, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Managing multiple maintenance projects</li>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 110, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Managing caretaker and maintenance team access</li>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 111, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Teacher task creation permissions</li>", "match": "Teacher"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 127, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Ensuring supplier qualifications and insurance</li>", "match": "supplier"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 131, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Emergency maintenance protocols</li>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 147, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Tracking maintenance expenditure</li>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 149, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Annual maintenance planning</li>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 165, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Scheduling maintenance during school holidays</li>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 166, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Emergency vs. routine maintenance priorities</li>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 169, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Planned preventive maintenance schedules</li>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 184, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Maintenance request templates</li>", "match": "Maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 185, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Supplier evaluation forms</li>", "match": "Supplier"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 202, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Free templates and guides to help you manage school maintenance effectively", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 208, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<h4 className=\"font-semibold mb-2\">Maintenance Request Template</h4>", "match": "Maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 209, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-sm text-gray-600 mb-3\">Standardized form for submitting maintenance requests</p>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 216, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<h4 className=\"font-semibold mb-2\">Supplier Evaluation Checklist</h4>", "match": "Supplier"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 217, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-sm text-gray-600 mb-3\">Criteria for assessing and selecting maintenance suppliers</p>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 217, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-sm text-gray-600 mb-3\">Criteria for assessing and selecting maintenance suppliers</p>", "match": "supplier"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 224, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<h4 className=\"font-semibold mb-2\">Annual Maintenance Planner</h4>", "match": "Maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 225, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-sm text-gray-600 mb-3\">Template for planning maintenance activities throughout the year</p>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 233, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-sm text-gray-600 mb-3\">Essential safety requirements for school maintenance work</p>", "match": "maintenance"}, {"file": "src\\pages\\SchoolResources.tsx", "line": 243, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Support Section */}", "match": "Support Section */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Need Additional Sup"}, {"file": "src\\pages\\StripeConnectPage.tsx", "line": 408, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<td className=\"py-2 px-2\">{formatCurrency(payment.supplier_amount)}</td>", "match": "supplier"}, {"file": "src\\pages\\SupplierFAQ.tsx", "line": 24, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const SupplierFAQ = () => {", "match": "Supplier"}, {"file": "src\\pages\\SupplierFAQ.tsx", "line": 38, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "question: \"How do I become a Classtasker supplier?\",", "match": "supplier"}, {"file": "src\\pages\\SupplierFAQ.tsx", "line": 39, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "answer: \"To become a supplier, you need to register on our platform, complete the verification process (including DBS checks and insurance verification), and set up your profile with your skills and service areas. Once approved, you can start browsing and bidding on available jobs.\"", "match": "supplier"}, {"file": "src\\pages\\SupplierFAQ.tsx", "line": 74, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "answer: \"Classtasker charges a small service fee on completed jobs to maintain the platform and provide support services. The fee structure is transparent and shown before you accept any job.\"", "match": "support services. The fee structure is transparent and shown before you accept any job.\"\n    },\n    "}, {"file": "src\\pages\\SupplierFAQ.tsx", "line": 138, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<h1 className=\"text-4xl font-bold mb-4\">Supplier FAQ</h1>", "match": "Supplier"}, {"file": "src\\pages\\SupplierFAQ.tsx", "line": 140, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Find answers to common questions about working as a Classtasker supplier", "match": "supplier"}, {"file": "src\\pages\\SupplierFAQ.tsx", "line": 153, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "\"Support\": HelpCircle", "match": "Support\": HelpCircle\n            };\n            const Icon = icons[category as keyof typeof icons] |"}, {"file": "src\\pages\\SupplierFAQ.tsx", "line": 186, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "\"Support\": HelpCircle", "match": "Support\": HelpCircle\n                  };\n                  const Icon = icons[category as keyof typ"}, {"file": "src\\pages\\SupplierFAQ.tsx", "line": 233, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Can't find the answer you're looking for? Our support team is here to help.", "match": "support team is here to help.\n            </CardDescription>\n          </CardHeader>\n          <Card"}, {"file": "src\\pages\\SupplierFAQ.tsx", "line": 264, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "export default SupplierFAQ;", "match": "Supplier"}, {"file": "src\\pages\\SupplierOnboarding.tsx", "line": 16, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const SupplierOnboarding: React.FC = () => {", "match": "Supplier"}, {"file": "src\\pages\\SupplierOnboarding.tsx", "line": 43, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check if user is a supplier", "match": "supplier"}, {"file": "src\\pages\\SupplierOnboarding.tsx", "line": 44, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (profile && profile.account_type !== 'supplier') {", "match": "supplier"}, {"file": "src\\pages\\SupplierOnboarding.tsx", "line": 118, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Create a supplier organization using the secure function", "match": "supplier"}, {"file": "src\\pages\\SupplierOnboarding.tsx", "line": 119, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { data, error } = await supabase.rpc('create_supplier_organization', {", "match": "supplier"}, {"file": "src\\pages\\SupplierOnboarding.tsx", "line": 153, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('Error creating supplier organization:', error);", "match": "supplier"}, {"file": "src\\pages\\SupplierOnboarding.tsx", "line": 178, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<CardTitle>Supplier Onboarding</CardTitle>", "match": "Supplier"}, {"file": "src\\pages\\SupplierOnboarding.tsx", "line": 384, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "export default SupplierOnboarding;", "match": "Supplier"}, {"file": "src\\pages\\SupplierResources.tsx", "line": 21, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const SupplierResources = () => {", "match": "Supplier"}, {"file": "src\\pages\\SupplierResources.tsx", "line": 30, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<h1 className=\"text-4xl font-bold mb-4\">Supplier Resources</h1>", "match": "Supplier"}, {"file": "src\\pages\\SupplierResources.tsx", "line": 32, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Essential guides, tools, and information to help you succeed as a Classtasker supplier", "match": "supplier"}, {"file": "src\\pages\\SupplierResources.tsx", "line": 45, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Link to=\"/supplier-faq\">", "match": "supplier"}, {"file": "src\\pages\\SupplierResources.tsx", "line": 47, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Supplier FAQ", "match": "Supplier"}, {"file": "src\\pages\\SupplierResources.tsx", "line": 57, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Link to=\"/register?type=supplier\">", "match": "supplier"}, {"file": "src\\pages\\SupplierResources.tsx", "line": 75, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Setting up your supplier profile</li>", "match": "supplier"}, {"file": "src\\pages\\SupplierResources.tsx", "line": 135, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<li>• Tax considerations for suppliers</li>", "match": "supplier"}, {"file": "src\\pages\\SupplierResources.tsx", "line": 252, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<h4 className=\"font-semibold mb-2\">Supplier Handbook</h4>", "match": "Supplier"}, {"file": "src\\pages\\SupplierResources.tsx", "line": 307, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Support Section */}", "match": "Support Section */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Need Help or Suppor"}, {"file": "src\\pages\\SupplierResources.tsx", "line": 342, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "export default SupplierResources;", "match": "Supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 20, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const SupplierSignup = () => {", "match": "Supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 29, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<h1 className=\"text-4xl font-bold mb-4\">Become a Classtasker Supplier</h1>", "match": "Supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 31, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Join our network of trusted maintenance professionals and grow your business by connecting with schools across the UK", "match": "maintenance"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 34, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Link to=\"/register?type=supplier\">", "match": "supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 35, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Join as a Supplier", "match": "Supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 54, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Access a consistent stream of maintenance work from schools in your area. Build long-term relationships with educational institutions.", "match": "maintenance"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 136, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Getting started as a Classtasker supplier is simple and straightforward", "match": "supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 146, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-sm text-gray-600\">Create your supplier profile with your skills, qualifications, and service areas</p>", "match": "supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 160, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p className=\"text-sm text-gray-600\">View available maintenance tasks from schools and submit competitive quotes</p>", "match": "maintenance"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 176, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<CardTitle>Supplier Requirements</CardTitle>", "match": "Supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 178, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "To ensure quality and safety, all suppliers must meet our verification requirements", "match": "supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 226, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'Cleaning Services', 'Grounds Maintenance', 'Fire Safety', 'Security Systems',", "match": "Maintenance"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 227, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'Catering Equipment', 'IT & AV Equipment', 'Playground Equipment', 'General Maintenance'", "match": "Maintenance"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 242, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Join thousands of suppliers already working with schools through Classtasker", "match": "supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 250, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Create your supplier account today and start browsing available opportunities in your area.", "match": "supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 253, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Link to=\"/register?type=supplier\">", "match": "supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 254, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Create Supplier Account", "match": "Supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 268, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<span className=\"text-sm\"><EMAIL></span>", "match": "supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 271, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<Link to=\"/supplier-faq\">", "match": "supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 272, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "View Supplier FAQ", "match": "Supplier"}, {"file": "src\\pages\\SupplierSignup.tsx", "line": 285, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "export default SupplierSignup;", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 21, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierActions from '@/components/tasks/SupplierActions';", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 21, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "import SupplierActions from '@/components/tasks/SupplierActions';", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, isSupplier, isSchool, isAdmin, isMaintenance, profile } = useAuth();", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { user, isSupplier, isSchool, isAdmin, isMaintenance, profile } = useAuth();", "match": "Maintenance"}, {"file": "src\\pages\\Task.tsx", "line": 152, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "}, [task, offers, showMessages, hasMessages, isAdmin, isTaskOwner, isSupplier, isSchool]);", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 176, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!isSupplier) {", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 180, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: \"Only suppliers can submit offers on tasks.\",", "match": "supplier"}, {"file": "src\\pages\\Task.tsx", "line": 262, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: \"The offer has been accepted successfully. The task has been assigned to the supplier.\",", "match": "supplier"}, {"file": "src\\pages\\Task.tsx", "line": 631, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Message Supplier", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 659, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Task Flow Explanation for Suppliers */}", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 660, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier && (", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 688, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Show supplier actions for tasks in any discussion stage or with public visibility */}", "match": "supplier"}, {"file": "src\\pages\\Task.tsx", "line": 698, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupplier,", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 705, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// And only for suppliers", "match": "supplier"}, {"file": "src\\pages\\Task.tsx", "line": 706, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "isSupplier &&", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 722, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Only show SupplierActions for external (public) tasks and suppliers */}", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 722, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Only show SupplierActions for external (public) tasks and suppliers */}", "match": "supplier"}, {"file": "src\\pages\\Task.tsx", "line": 723, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{(task?.visibility === 'public' && isSupplier && (", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 731, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<SupplierActions", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 739, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{task?.status === 'open' && !isSupplier && user && (", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 754, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{user && !isSupplier && (", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 757, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<AlertTitle>Suppliers Only</AlertTitle>", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 759, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "Only supplier accounts can make offers on tasks.", "match": "supplier"}, {"file": "src\\pages\\Task.tsx", "line": 764, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{user && isSupplier && !hasSubmittedOffer && (", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 809, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{user && isSupplier && hasSubmittedOffer && (", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 842, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p>Is Maintenance Role: {profile?.role === 'maintenance' ? 'Yes' : 'No'}</p>", "match": "Maintenance"}, {"file": "src\\pages\\Task.tsx", "line": 842, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p>Is Maintenance Role: {profile?.role === 'maintenance' ? 'Yes' : 'No'}</p>", "match": "maintenance"}, {"file": "src\\pages\\Task.tsx", "line": 960, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Internal Task Actions - Show for internal tasks OR for maintenance staff assigned to the task */}", "match": "maintenance"}, {"file": "src\\pages\\Task.tsx", "line": 964, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// 2. Internal tasks viewed by assigned maintenance staff", "match": "maintenance"}, {"file": "src\\pages\\Task.tsx", "line": 965, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// 3. Any task assigned to maintenance staff who is viewing it", "match": "maintenance"}, {"file": "src\\pages\\Task.tsx", "line": 968, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "(task?.visibility === 'internal' && isMaintenance && String(task?.assigned_to) === String(user?.id)) ||", "match": "Maintenance"}, {"file": "src\\pages\\Task.tsx", "line": 969, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "(isMaintenance && String(task?.assigned_to) === String(user?.id))", "match": "Maintenance"}, {"file": "src\\pages\\Task.tsx", "line": 977, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "? 'Actions for this internal maintenance task'", "match": "maintenance"}, {"file": "src\\pages\\Task.tsx", "line": 978, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": 'Maintenance staff actions for this task'}", "match": "Maintenance"}, {"file": "src\\pages\\Task.tsx", "line": 1075, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<MessageSquare className=\"mr-2 h-4 w-4\" /> Message Supplier", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 1079, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Message button for suppliers with accepted offers */}", "match": "supplier"}, {"file": "src\\pages\\Task.tsx", "line": 1080, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{isSupplier && task?.status === 'assigned' && !showMessages && !hasMessages && !isChecking &&", "match": "Supplier"}, {"file": "src\\pages\\Task.tsx", "line": 592, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "{console.log('Task.tsx offer conditions:', {", "match": "console.log('Task.tsx offer conditions:', {\r\n                                displayStatus,\r\n       "}, {"file": "src\\pages\\TestInternalTaskActions.tsx", "line": 152, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p><strong>Is Maintenance:</strong> {profile?.role === 'maintenance' ? 'Yes' : 'No'}</p>", "match": "Maintenance"}, {"file": "src\\pages\\TestInternalTaskActions.tsx", "line": 152, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p><strong>Is Maintenance:</strong> {profile?.role === 'maintenance' ? 'Yes' : 'No'}</p>", "match": "maintenance"}, {"file": "src\\pages\\TestInternalTaskActions.tsx", "line": 251, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{task.status === 'assigned' && 'The task is assigned to a maintenance worker. They should click \"Start Work\" to begin.'}", "match": "maintenance"}, {"file": "src\\pages\\TestInternalTaskActions.tsx", "line": 252, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{task.status === 'in_progress' && 'The task is in progress. The maintenance worker should click \"Mark as Completed\" when done.'}", "match": "maintenance"}, {"file": "src\\pages\\TestInternalTaskActions.tsx", "line": 322, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p><strong>Is Maintenance:</strong> {profile?.role === 'maintenance' ? 'Yes' : 'No'}</p>", "match": "Maintenance"}, {"file": "src\\pages\\TestInternalTaskActions.tsx", "line": 322, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "<p><strong>Is Maintenance:</strong> {profile?.role === 'maintenance' ? 'Yes' : 'No'}</p>", "match": "maintenance"}, {"file": "src\\pages\\TestTaskInvoiceFlow.tsx", "line": 25, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "category: 'Maintenance',", "match": "Maintenance"}, {"file": "src\\routes\\PWARoutes.tsx", "line": 95, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{/* Help and Support routes */}", "match": "Support routes */}\n            <Route path=\"/help\" element={<PWAHelp />} />\n            <Route path="}, {"file": "src\\scripts\\check-invitation-data.js", "line": 68, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// <NAME_EMAIL> specifically", "match": "teacher"}, {"file": "src\\scripts\\check-invitation-data.js", "line": 101, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// <NAME_EMAIL> specifically", "match": "teacher"}, {"file": "src\\scripts\\check-org-users.js", "line": 198, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: profile.role || userInfo.metadata.role || 'teacher',", "match": "teacher"}, {"file": "src\\scripts\\check-org-users.js", "line": 88, "pattern": "Authentication data in console.log", "severity": "HIGH", "category": "AUTH_DATA", "content": "console.log(`Found ${usersWithOrg.length} auth users with this organization in their metadata`);", "match": "console.log(`Found ${usersWithOrg.length} auth users with this organization in their metadata`)"}, {"file": "src\\scripts\\check-org-users.js", "line": 88, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(`Found ${usersWithOrg.length} auth users with this organization in their metadata`);", "match": "console.log(`Found ${usersWithOrg.length} auth users with this organization in their metadata`)"}, {"file": "src\\scripts\\check-organization-users.js", "line": 87, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check for maintenance and support staff", "match": "maintenance"}, {"file": "src\\scripts\\check-organization-users.js", "line": 87, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check for maintenance and support staff", "match": "support staff\r\n      const maintenanceStaff = profiles.filter(p => p.role === 'maintenance')"}, {"file": "src\\scripts\\check-staff-roles.js", "line": 38, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const maintenanceStaff = profiles.filter(p => p.role === 'maintenance');", "match": "maintenance"}, {"file": "src\\scripts\\check-staff-roles.js", "line": 38, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const maintenanceStaff = profiles.filter(p => p.role === 'maintenance');", "match": "maintenance"}, {"file": "src\\scripts\\check-tasks-table.js", "line": 64, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// First get the user <NAME_EMAIL>", "match": "teacher"}, {"file": "src\\scripts\\check-tasks-table.js", "line": 66, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { data: teacherProfiles, error: teacherError } = await supabase", "match": "teacher"}, {"file": "src\\scripts\\check-tasks-table.js", "line": 66, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { data: teacherProfiles, error: teacherError } = await supabase", "match": "teacher"}, {"file": "src\\scripts\\check-tasks-table.js", "line": 69, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".contains('email', ['<EMAIL>']);", "match": "teacher"}, {"file": "src\\scripts\\check-tasks-table.js", "line": 71, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (teacherError) {", "match": "teacher"}, {"file": "src\\scripts\\check-tasks-table.js", "line": 72, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('Error fetching teacher profile:', teacherError);", "match": "teacher"}, {"file": "src\\scripts\\check-tasks-table.js", "line": 72, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('Error fetching teacher profile:', teacherError);", "match": "teacher"}, {"file": "src\\scripts\\check-tasks-table.js", "line": 81, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Get tasks created by this teacher", "match": "teacher"}, {"file": "src\\scripts\\check-tasks-table.js", "line": 82, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { data: teacherTasks, error: tasksError } = await supabase", "match": "teacher"}, {"file": "src\\scripts\\check-tasks-table.js", "line": 85, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('user_id', teacherProfile.id)", "match": "teacher"}, {"file": "src\\scripts\\check-user-data.js", "line": 55, "pattern": "Authentication data in console.log", "severity": "HIGH", "category": "AUTH_DATA", "content": "console.log('=== Auth User Data ===');", "match": "console.log('=== Auth User Data ===')"}, {"file": "src\\scripts\\check-user-data.js", "line": 55, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log('=== Auth User Data ===');", "match": "console.log('=== Auth User Data ===')"}, {"file": "src\\scripts\\check-user-metadata.js", "line": 38, "pattern": "Authentication data in console.log", "severity": "HIGH", "category": "AUTH_DATA", "content": "console.log(`Found ${authData.users.length} total auth users`);", "match": "console.log(`Found ${authData.users.length} total auth users`)"}, {"file": "src\\scripts\\check-user-metadata.js", "line": 38, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(`Found ${authData.users.length} total auth users`);", "match": "console.log(`Found ${authData.users.length} total auth users`)"}, {"file": "src\\scripts\\check-user-metadata.js", "line": 91, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log('\\nUser metadata check completed.');", "match": "console.log('\\nUser metadata check completed.')"}, {"file": "src\\scripts\\cleanup-unauthorized-chats.ts", "line": 142, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Supplier with public task", "match": "Supplier"}, {"file": "src\\scripts\\cleanup-unauthorized-chats.ts", "line": 143, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "(memberProfile.account_type === 'supplier' && task.visibility === 'public')", "match": "supplier"}, {"file": "src\\scripts\\cleanup-unauthorized-chats.ts", "line": 78, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log(`\\n🔍 Processing channel: ${channelId} (Task: ${taskId})`);", "match": "console.log(`\\n🔍 Processing channel: ${channelId} (Task: ${taskId})"}, {"file": "src\\scripts\\cleanup-unauthorized-chats.ts", "line": 107, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log(`   Task: \"${task.title}\" (Org: [ID], Visibility: ${task.visibility})`);", "match": "console.log(`   Task: \"${task.title}\" (Org: [ID], Visibility: ${task.visibility})"}, {"file": "src\\scripts\\cleanup-unauthorized-chats.ts", "line": 210, "pattern": "Error with potential user data in console.log", "severity": "MEDIUM", "category": "ERROR_DATA", "content": "result.errors.forEach(error => console.log(`    - ${error}`));", "match": "console.log(`    - ${error}`)"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 129, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{ email: '<EMAIL>', orgIndex: 0, role: 'teacher', accountType: 'school' },", "match": "teacher"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 129, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{ email: '<EMAIL>', orgIndex: 0, role: 'teacher', accountType: 'school' },", "match": "teacher"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 130, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{ email: '<EMAIL>', orgIndex: 0, role: 'maintenance', accountType: 'school' },", "match": "maintenance"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 130, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{ email: '<EMAIL>', orgIndex: 0, role: 'maintenance', accountType: 'school' },", "match": "maintenance"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 132, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{ email: '<EMAIL>', orgIndex: 1, role: 'teacher', accountType: 'school' },", "match": "teacher"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 132, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{ email: '<EMAIL>', orgIndex: 1, role: 'teacher', accountType: 'school' },", "match": "teacher"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 134, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{ email: '<EMAIL>', orgIndex: -1, role: 'supplier', accountType: 'supplier' }", "match": "supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 134, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{ email: '<EMAIL>', orgIndex: -1, role: 'supplier', accountType: 'supplier' }", "match": "supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 134, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "{ email: '<EMAIL>', orgIndex: -1, role: 'supplier', accountType: 'supplier' }", "match": "supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 194, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const creator = this.users.find(u => u.organizationId === org.id && u.role === 'teacher');", "match": "teacher"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 240, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "await this.testSupplierAccess();", "match": "Supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 297, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const maintenanceB = this.users.find(u => u.role === 'maintenance' && u.organizationId === this.organizations[1].id)!;", "match": "maintenance"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 297, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const maintenanceB = this.users.find(u => u.role === 'maintenance' && u.organizationId === this.organizations[1].id)!;", "match": "maintenance"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 300, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Try to assign task from Org A to maintenance user in Org B", "match": "maintenance"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 304, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "assigned_to: maintenanceB.id,", "match": "maintenance"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 339, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const teacherB = this.users.find(u => u.role === 'teacher' && u.organizationId === this.organizations[1].id)!;", "match": "teacher"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 339, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const teacherB = this.users.find(u => u.role === 'teacher' && u.organizationId === this.organizations[1].id)!;", "match": "teacher"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 346, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "sender_id: teacherB.id,", "match": "teacher"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 378, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Create a public task for supplier testing", "match": "supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 384, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "title: 'Public Task for Supplier Test',", "match": "Supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 385, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: 'This task should be visible to suppliers',", "match": "supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 398, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "testName: 'Supplier access to public tasks',", "match": "Supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 407, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Test: Supplier should be able to see public tasks", "match": "Supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 408, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplier = this.users.find(u => u.accountType === 'supplier')!;", "match": "supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 408, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplier = this.users.find(u => u.accountType === 'supplier')!;", "match": "supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 420, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "testName: 'Supplier access to public tasks',", "match": "Supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 424, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "details: allowed ? 'Supplier correctly has access to public tasks' : 'Issue: Supplier blocked from public tasks'", "match": "Supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 424, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "details: allowed ? 'Supplier correctly has access to public tasks' : 'Issue: Supplier blocked from public tasks'", "match": "Supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 429, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "testName: 'Supplier access to public tasks',", "match": "Supplier"}, {"file": "src\\scripts\\comprehensive-security-test.ts", "line": 433, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "details: 'Supplier access to public tasks was blocked',", "match": "Supplier"}, {"file": "src\\scripts\\create-public-task.js", "line": 28, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('role', 'teacher')", "match": "teacher"}, {"file": "src\\scripts\\create-test-invitation.js", "line": 57, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: 'teacher',", "match": "teacher"}, {"file": "src\\scripts\\create-test-invitation.js", "line": 148, "pattern": "Profile data in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log('\\nUpdated user profile:');", "match": "console.log('\\nUpdated user profile:')"}, {"file": "src\\scripts\\create-test-task.js", "line": 28, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('role', 'teacher')", "match": "teacher"}, {"file": "src\\scripts\\fix-metadata-invitations.js", "line": 35, "pattern": "Authentication data in console.log", "severity": "HIGH", "category": "AUTH_DATA", "content": "console.log(`Found ${authData.users.length} users in auth.users`);", "match": "console.log(`Found ${authData.users.length} users in auth.users`)"}, {"file": "src\\scripts\\fix-metadata-invitations.js", "line": 35, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(`Found ${authData.users.length} users in auth.users`);", "match": "console.log(`Found ${authData.users.length} users in auth.users`)"}, {"file": "src\\scripts\\fix-metadata-invitations.js", "line": 48, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(`Found ${usersWithInvitations.length} users with invitations in their metadata`);", "match": "console.log(`Found ${usersWithInvitations.length} users with invitations in their metadata`)"}, {"file": "src\\scripts\\fix-profile-emails.js", "line": 76, "pattern": "Profile data in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log(`Updating profile ${profile.id} with email ${authUser.email}`);", "match": "console.log(`Updating profile ${profile.id} with email ${authUser.email}`)"}, {"file": "src\\scripts\\fix-profile-emails.js", "line": 48, "pattern": "Authentication data in console.log", "severity": "HIGH", "category": "AUTH_DATA", "content": "console.log(`Found ${authUsers.users.length} total auth users`);", "match": "console.log(`Found ${authUsers.users.length} total auth users`)"}, {"file": "src\\scripts\\fix-profile-emails.js", "line": 76, "pattern": "Authentication data in console.log", "severity": "HIGH", "category": "AUTH_DATA", "content": "console.log(`Updating profile ${profile.id} with email ${authUser.email}`);", "match": "console.log(`Updating profile ${profile.id} with email ${authUser.email}`)"}, {"file": "src\\scripts\\fix-user-organization.js", "line": 163, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: 'teacher'", "match": "teacher"}, {"file": "src\\scripts\\fix-user-organization.js", "line": 188, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: 'teacher',", "match": "teacher"}, {"file": "src\\scripts\\fix-user-organization.js", "line": 207, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log('User metadata updated successfully');", "match": "console.log('User metadata updated successfully')"}, {"file": "src\\scripts\\manage-profiles.js", "line": 221, "pattern": "Profile data in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log(`Found ${profiles.length} profiles and ${users.length} users`);", "match": "console.log(`Found ${profiles.length} profiles and ${users.length} users`)"}, {"file": "src\\scripts\\migrate-existing-organizations.js", "line": 191, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: metadata.role || profile.role || 'teacher' // Use existing role or default to 'teacher'", "match": "teacher"}, {"file": "src\\scripts\\migrate-existing-organizations.js", "line": 191, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: metadata.role || profile.role || 'teacher' // Use existing role or default to 'teacher'", "match": "teacher"}, {"file": "src\\scripts\\populate-email-column.js", "line": 35, "pattern": "Authentication data in console.log", "severity": "HIGH", "category": "AUTH_DATA", "content": "console.log(`Found ${authData.users.length} total auth users`);", "match": "console.log(`Found ${authData.users.length} total auth users`)"}, {"file": "src\\scripts\\populate-email-column.js", "line": 35, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(`Found ${authData.users.length} total auth users`);", "match": "console.log(`Found ${authData.users.length} total auth users`)"}, {"file": "src\\scripts\\run-sql.js", "line": 120, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: 'teacher'", "match": "teacher"}, {"file": "src\\scripts\\security-audit.ts", "line": 139, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".neq('account_type', 'supplier'); // Suppliers might not have organization_id", "match": "supplier"}, {"file": "src\\scripts\\security-audit.ts", "line": 139, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".neq('account_type', 'supplier'); // Suppliers might not have organization_id", "match": "Supplier"}, {"file": "src\\scripts\\security-audit.ts", "line": 145, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: `Found ${profilesWithoutOrg.length} non-supplier profiles without organization_id`,", "match": "supplier"}, {"file": "src\\scripts\\security-audit.ts", "line": 189, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "AND p.account_type != 'supplier'", "match": "supplier"}, {"file": "src\\scripts\\set-admin-role.js", "line": 33, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log('User role set to admin successfully');", "match": "console.log('User role set to admin"}, {"file": "src\\scripts\\test-fixed-query.js", "line": 75, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check for maintenance and support staff", "match": "maintenance"}, {"file": "src\\scripts\\test-fixed-query.js", "line": 75, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check for maintenance and support staff", "match": "support staff\r\n      const maintenanceUsers = mappedData.filter(user => user.role === 'maintenance')"}, {"file": "src\\scripts\\test-fixed-query.js", "line": 82, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log(`Maintenance staff: ${maintenanceUsers.length}`);", "match": "Maintenance"}, {"file": "src\\scripts\\test-fixed-query.js", "line": 82, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log(`Maintenance staff: ${maintenanceUsers.length}`);", "match": "maintenance"}, {"file": "src\\scripts\\test-fixed-query.js", "line": 97, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log(`Support staff: ${supportUsers.length}`);", "match": "Support staff: ${supportUsers.length}`)"}, {"file": "src\\scripts\\test-fixed-query.js", "line": 57, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(`Query returned ${data.length} users`);", "match": "console.log(`Query returned ${data.length} users`)"}, {"file": "src\\scripts\\test-organization-users-query.js", "line": 62, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check for maintenance and support staff", "match": "maintenance"}, {"file": "src\\scripts\\test-organization-users-query.js", "line": 62, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check for maintenance and support staff", "match": "support staff\r\n      const maintenanceUsers = data.filter(user => user.role === 'maintenance')"}, {"file": "src\\scripts\\test-organization-users-query.js", "line": 69, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log(`Maintenance staff: ${maintenanceUsers.length}`);", "match": "Maintenance"}, {"file": "src\\scripts\\test-organization-users-query.js", "line": 69, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log(`Maintenance staff: ${maintenanceUsers.length}`);", "match": "maintenance"}, {"file": "src\\scripts\\test-organization-users-query.js", "line": 84, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log(`Support staff: ${supportUsers.length}`);", "match": "Support staff: ${supportUsers.length}`)"}, {"file": "src\\scripts\\test-organization-users-query.js", "line": 57, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(`Query returned ${data.length} users`);", "match": "console.log(`Query returned ${data.length} users`)"}, {"file": "src\\scripts\\test-role-system.js", "line": 36, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "email: '<EMAIL>',", "match": "teacher"}, {"file": "src\\scripts\\test-role-system.js", "line": 38, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: 'teacher',", "match": "teacher"}, {"file": "src\\scripts\\test-role-system.js", "line": 40, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: 'Teacher'", "match": "Teacher"}, {"file": "src\\scripts\\test-role-system.js", "line": 43, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "email: '<EMAIL>',", "match": "maintenance"}, {"file": "src\\scripts\\test-role-system.js", "line": 45, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: 'maintenance',", "match": "maintenance"}, {"file": "src\\scripts\\test-role-system.js", "line": 47, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description: 'Maintenance Staff'", "match": "Maintenance"}, {"file": "src\\scripts\\test-role-system.js", "line": 50, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "email: '<EMAIL>',", "match": "<EMAIL>',\n    password: 'Test123!',\n    role: 'support',\n    is_site_admin: false,\n "}, {"file": "src\\scripts\\test-role-system.js", "line": 166, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "account_type: testUser.role === 'supplier' ? 'supplier' : 'school',", "match": "supplier"}, {"file": "src\\scripts\\test-role-system.js", "line": 166, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "account_type: testUser.role === 'supplier' ? 'supplier' : 'school',", "match": "supplier"}, {"file": "src\\scripts\\test-role-system.js", "line": 168, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "organization_id: testUser.role !== 'supplier' ? organizationId : null", "match": "supplier"}, {"file": "src\\scripts\\test-role-system.js", "line": 192, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "organization_id: testUser.role !== 'supplier' ? organizationId : null", "match": "supplier"}, {"file": "src\\scripts\\test-task-policies.js", "line": 28, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".in('role', ['admin', 'teacher', 'maintenance', 'support'])", "match": "teacher"}, {"file": "src\\scripts\\test-task-policies.js", "line": 28, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".in('role', ['admin', 'teacher', 'maintenance', 'support'])", "match": "maintenance"}, {"file": "src\\scripts\\test-task-policies.js", "line": 28, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".in('role', ['admin', 'teacher', 'maintenance', 'support'])", "match": "support'])"}, {"file": "src\\scripts\\test-task-policies.js", "line": 56, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Get a teacher user", "match": "teacher"}, {"file": "src\\scripts\\test-task-policies.js", "line": 57, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const teacherUser = users.find(user => user.role === 'teacher');", "match": "teacher"}, {"file": "src\\scripts\\test-task-policies.js", "line": 57, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const teacherUser = users.find(user => user.role === 'teacher');", "match": "teacher"}, {"file": "src\\scripts\\test-task-policies.js", "line": 58, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (!teacherUser) {", "match": "teacher"}, {"file": "src\\scripts\\test-task-policies.js", "line": 59, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('No teacher user found');", "match": "teacher"}, {"file": "src\\scripts\\test-task-policies.js", "line": 77, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Create a client with the teacher's auth", "match": "teacher"}, {"file": "src\\scripts\\test-task-policies.js", "line": 78, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const teacherClient = supabase.auth.setSession({", "match": "teacher"}, {"file": "src\\scripts\\test-task-policies.js", "line": 94, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "user_id: teacherUser.id", "match": "teacher"}, {"file": "src\\scripts\\test-task-policies.js", "line": 155, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { data: teacherTasks, error: teacherError } = await supabase", "match": "teacher"}, {"file": "src\\scripts\\test-task-policies.js", "line": 155, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { data: teacherTasks, error: teacherError } = await supabase", "match": "teacher"}, {"file": "src\\scripts\\test-task-policies.js", "line": 158, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ".eq('user_id', teacherUser.id);", "match": "teacher"}, {"file": "src\\scripts\\test-task-policies.js", "line": 160, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (teacherError) {", "match": "teacher"}, {"file": "src\\scripts\\update-email-arrays.js", "line": 35, "pattern": "Authentication data in console.log", "severity": "HIGH", "category": "AUTH_DATA", "content": "console.log(`Found ${authData.users.length} total auth users`);", "match": "console.log(`Found ${authData.users.length} total auth users`)"}, {"file": "src\\scripts\\update-email-arrays.js", "line": 35, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(`Found ${authData.users.length} total auth users`);", "match": "console.log(`Found ${authData.users.length} total auth users`)"}, {"file": "src\\scripts\\update-profile-emails.js", "line": 48, "pattern": "Authentication data in console.log", "severity": "HIGH", "category": "AUTH_DATA", "content": "console.log(`Found ${authData.users.length} total auth users`);", "match": "console.log(`Found ${authData.users.length} total auth users`)"}, {"file": "src\\scripts\\update-profile-emails.js", "line": 48, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(`Found ${authData.users.length} total auth users`);", "match": "console.log(`Found ${authData.users.length} total auth users`)"}, {"file": "src\\scripts\\update-supplier-organization-schema.js", "line": 12, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const sqlPath = path.resolve('sql/update_supplier_organization_schema.sql');", "match": "supplier"}, {"file": "src\\scripts\\update-supplier-organization-schema.js", "line": 28, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "sql: \"SELECT proname FROM pg_proc WHERE proname = 'create_supplier_organization';\"", "match": "supplier"}, {"file": "src\\scripts\\update-supplier-organization-schema.js", "line": 41, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error('Function create_supplier_organization was not created');", "match": "supplier"}, {"file": "src\\scripts\\update-supplier-organization-schema.js", "line": 49, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "updateSupplierOrganizationSchema();", "match": "Supplier"}, {"file": "src\\scripts\\update-task-schema.js", "line": 119, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Teacher view policy", "match": "Teacher"}, {"file": "src\\scripts\\update-task-schema.js", "line": 120, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { error: teacher<PERSON><PERSON><PERSON><PERSON><PERSON>r } = await supabase.rpc('create_policy', {", "match": "teacher"}, {"file": "src\\scripts\\update-task-schema.js", "line": 121, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "policy_name: 'Teachers can view their own tasks',", "match": "Teacher"}, {"file": "src\\scripts\\update-task-schema.js", "line": 127, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (teacher<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) {", "match": "teacher"}, {"file": "src\\scripts\\update-task-schema.js", "line": 135, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Maintenance/support view policy", "match": "Maintenance"}, {"file": "src\\scripts\\update-task-schema.js", "line": 135, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Maintenance/support view policy", "match": "support view policy\r\n    const { error: maintenanceViewError } = await supabase.rpc('create_policy',"}, {"file": "src\\scripts\\update-task-schema.js", "line": 143, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (maintenanceViewError) {", "match": "maintenance"}, {"file": "src\\scripts\\update-task-schema.js", "line": 151, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Supplier view policy", "match": "Supplier"}, {"file": "src\\scripts\\update-task-schema.js", "line": 152, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const { error: supplierViewError } = await supabase.rpc('create_policy', {", "match": "supplier"}, {"file": "src\\scripts\\update-task-schema.js", "line": 153, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "policy_name: 'Suppliers can view public tasks',", "match": "Supplier"}, {"file": "src\\scripts\\update-task-schema.js", "line": 161, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "AND account_type = 'supplier'", "match": "supplier"}, {"file": "src\\scripts\\update-task-schema.js", "line": 166, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "if (supplierViewError) {", "match": "supplier"}, {"file": "src\\scripts\\update-task-schema.js", "line": 176, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "policy_name: 'Teachers and admins can create tasks',", "match": "Teacher"}, {"file": "src\\scripts\\update-task-schema.js", "line": 183, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "AND (role = 'teacher' OR role = 'admin')", "match": "teacher"}, {"file": "src\\scripts\\update-tasks-default-visibility.js", "line": 81, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "user_id: '4a82d673-eee5-4ddf-b455-395ce3a73459' // Teacher user ID", "match": "Teacher"}, {"file": "src\\scripts\\update-tasks-schema.js", "line": 124, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "CREATE POLICY \"Teachers can view their own tasks\"", "match": "Teacher"}, {"file": "src\\scripts\\update-tasks-schema.js", "line": 131, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "CREATE POLICY \"Maintenance and support can view assigned tasks\"", "match": "Maintenance"}, {"file": "src\\scripts\\update-tasks-schema.js", "line": 131, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "CREATE POLICY \"Maintenance and support can view assigned tasks\"", "match": "support can view assigned tasks\" \r\n        ON public.tasks\r\n        FOR SELECT\r\n        USING (\r\n   "}, {"file": "src\\scripts\\update-tasks-schema.js", "line": 139, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "CREATE POLICY \"Suppliers can view public tasks\"", "match": "Supplier"}, {"file": "src\\scripts\\update-tasks-schema.js", "line": 147, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "AND account_type = 'supplier'", "match": "supplier"}, {"file": "src\\scripts\\update-tasks-schema.js", "line": 152, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "CREATE POLICY \"Teachers and admins can create tasks\"", "match": "Teacher"}, {"file": "src\\scripts\\update-tasks-schema.js", "line": 159, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "AND (role = 'teacher' OR role = 'admin')", "match": "teacher"}, {"file": "src\\scripts\\update-test-user.js", "line": 78, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: 'teacher'", "match": "teacher"}, {"file": "src\\scripts\\update-test-user.js", "line": 108, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: 'teacher',", "match": "teacher"}, {"file": "src\\scripts\\update-test-user.js", "line": 92, "pattern": "Profile data in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log(`Updated profile for ${testUser.email}:`);", "match": "console.log(`Updated profile for ${testUser.email}:`)"}, {"file": "src\\scripts\\update-test-user.js", "line": 127, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log('Updated user metadata successfully');", "match": "console.log('Updated user metadata successfully')"}, {"file": "src\\scripts\\update-user-organization.js", "line": 266, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "rl.question('Enter user role (default: teacher): ', (answer) => {", "match": "teacher"}, {"file": "src\\scripts\\update-user-organization.js", "line": 267, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "resolve(answer || 'teacher');", "match": "teacher"}, {"file": "src\\scripts\\update-user-organization.js", "line": 158, "pattern": "Profile data in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log(`Successfully updated profile for user ${user.email}`);", "match": "console.log(`Successfully updated profile for user ${user.email}`)"}, {"file": "src\\scripts\\update-user-organization.js", "line": 193, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(`Successfully updated metadata for user ${user.email}`);", "match": "console.log(`Successfully updated metadata for user ${user.email}`)"}, {"file": "src\\scripts\\update-user-roles.js", "line": 25, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "email: '<EMAIL>',", "match": "maintenance"}, {"file": "src\\scripts\\update-user-roles.js", "line": 26, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: 'maintenance'", "match": "maintenance"}, {"file": "src\\scripts\\update-user-roles.js", "line": 29, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "email: '<EMAIL>',", "match": "<EMAIL>',\r\n    role: 'support'\r\n  },\r\n  {\r\n    email: '<EMAIL>"}, {"file": "src\\scripts\\update-user-roles.js", "line": 75, "pattern": "Authentication data in console.log", "severity": "HIGH", "category": "AUTH_DATA", "content": "console.log(`Found ${authData.users.length} total auth users`);", "match": "console.log(`Found ${authData.users.length} total auth users`)"}, {"file": "src\\scripts\\update-user-roles.js", "line": 75, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(`Found ${authData.users.length} total auth users`);", "match": "console.log(`Found ${authData.users.length} total auth users`)"}, {"file": "src\\scripts\\verify-invitations.js", "line": 103, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role_param: 'teacher'", "match": "teacher"}, {"file": "src\\server\\index.js", "line": 93, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "message: `Unsupported email provider: ${config.provider}`", "match": "supported email provider: ${config.provider}`\r\n      })"}, {"file": "src\\services\\fixedTaskService.ts", "line": 53, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Unfortunately, Supabase JS client doesn't support transactions directly", "match": "support transactions directly\r\n    // So we'll do our best with sequential operations\r\n\r\n    // 1. F"}, {"file": "src\\services\\organizationService.ts", "line": 311, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: profile.role || 'teacher',", "match": "teacher"}, {"file": "src\\services\\organizationService.ts", "line": 372, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "role: acceptedUser<PERSON><PERSON>s[invitation.email] || invitation.role || 'teacher',", "match": "teacher"}, {"file": "src\\services\\organizationService.ts", "line": 975, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.log('DEBUG: Current user role check - isAdmin: completed');", "match": "console.log('DEBUG: Current user role check - isAdmin"}, {"file": "src\\services\\organizationService.ts", "line": 1011, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check if the current user is a teacher", "match": "teacher"}, {"file": "src\\services\\organizationService.ts", "line": 1044, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log('DEBUG: Successfully removed user from organization in database');", "match": "console.log('DEBUG: Successfully removed user from organization in database')"}, {"file": "src\\services\\stripeService.ts", "line": 48, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_amount: number;", "match": "supplier"}, {"file": "src\\services\\stripeService.ts", "line": 373, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Calculate the platform fee and supplier amount", "match": "supplier"}, {"file": "src\\services\\stripeService.ts", "line": 376, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplierAmountInCents = amountInCents - platformFeeInCents;", "match": "supplier"}, {"file": "src\\services\\stripeService.ts", "line": 388, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_amount: supplierAmountInCents / 100, // Convert back to dollars", "match": "supplier"}, {"file": "src\\services\\stripeService.ts", "line": 388, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_amount: supplierAmountInCents / 100, // Convert back to dollars", "match": "supplier"}, {"file": "src\\services\\systemMessageService.ts", "line": 20, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For internal tasks, remove supplier-related messages", "match": "supplier"}, {"file": "src\\services\\systemMessageService.ts", "line": 22, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'Task has been assigned to a supplier.',", "match": "supplier"}, {"file": "src\\services\\systemMessageService.ts", "line": 23, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'Task is now public and visible to suppliers.',", "match": "supplier"}, {"file": "src\\services\\systemMessageService.ts", "line": 25, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'A supplier has expressed interest in this task.',", "match": "supplier"}, {"file": "src\\services\\systemMessageService.ts", "line": 27, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'Supp<PERSON> has marked this task as completed.',", "match": "Supplier"}, {"file": "src\\services\\systemMessageService.ts", "line": 34, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'Internal task has been assigned to maintenance staff.',", "match": "maintenance"}, {"file": "src\\services\\systemMessageService.ts", "line": 35, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'Maintenance work has started on this task.',", "match": "Maintenance"}, {"file": "src\\services\\systemMessageService.ts", "line": 36, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "'Maintenance staff has marked this task as completed.'", "match": "Maintenance"}, {"file": "src\\services\\systemMessageService.ts", "line": 101, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "message = 'Internal task has been assigned to maintenance staff.';", "match": "maintenance"}, {"file": "src\\services\\systemMessageService.ts", "line": 104, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "message = 'Maintenance work has started on this task.';", "match": "Maintenance"}, {"file": "src\\services\\systemMessageService.ts", "line": 107, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "message = 'Maintenance staff has marked this task as completed.';", "match": "Maintenance"}, {"file": "src\\services\\systemMessageService.ts", "line": 122, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "message = 'A supplier has expressed interest in this task.';", "match": "supplier"}, {"file": "src\\services\\systemMessageService.ts", "line": 128, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "message = 'Task has been assigned to a supplier.';", "match": "supplier"}, {"file": "src\\services\\systemMessageService.ts", "line": 134, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "message = 'Supplier has marked this task as completed.';", "match": "Supplier"}, {"file": "src\\services\\systemMessageService.ts", "line": 158, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isMaintenanceStaff = userData.role === 'maintenance';", "match": "Maintenance"}, {"file": "src\\services\\systemMessageService.ts", "line": 158, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isMaintenanceStaff = userData.role === 'maintenance';", "match": "maintenance"}, {"file": "src\\services\\systemMessageService.ts", "line": 162, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": isMaintenanceStaff ? 'maintenance staff' : 'a supplier';", "match": "Maintenance"}, {"file": "src\\services\\systemMessageService.ts", "line": 162, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": isMaintenanceStaff ? 'maintenance staff' : 'a supplier';", "match": "maintenance"}, {"file": "src\\services\\systemMessageService.ts", "line": 162, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": isMaintenanceStaff ? 'maintenance staff' : 'a supplier';", "match": "supplier"}, {"file": "src\\services\\taskFactory.ts", "line": 58, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Special case for admin visibility tasks created by teachers", "match": "teacher"}, {"file": "src\\services\\taskService.ts", "line": 377, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (accountType === 'supplier') {", "match": "supplier"}, {"file": "src\\services\\taskService.ts", "line": 378, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Suppliers can only see public tasks in early stages", "match": "Supplier"}, {"file": "src\\services\\taskService.ts", "line": 384, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (userRole === 'maintenance' || userRole === 'support') {", "match": "maintenance"}, {"file": "src\\services\\taskService.ts", "line": 384, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (userRole === 'maintenance' || userRole === 'support') {", "match": "support')"}, {"file": "src\\services\\taskService.ts", "line": 388, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (userRole === 'teacher') {", "match": "teacher"}, {"file": "src\\services\\taskService.ts", "line": 389, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Teachers can see their own tasks (regardless of visibility) or public tasks within their organization", "match": "Teacher"}, {"file": "src\\services\\taskService.ts", "line": 772, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// For public visibility, we keep it as 'open' until a supplier offer is accepted", "match": "supplier"}, {"file": "src\\services\\taskService.ts", "line": 813, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "? 'Task is now public and visible to suppliers.'", "match": "supplier"}, {"file": "src\\services\\taskService.ts", "line": 883, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "text: 'This internal maintenance task has been assigned to you. You can use this chat to communicate with the administrator.',", "match": "maintenance"}, {"file": "src\\services\\taskService.ts", "line": 1255, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Ensure visibility is public since this is a supplier assignment", "match": "supplier"}, {"file": "src\\services\\taskService.ts", "line": 1279, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Update chat membership to include the supplier", "match": "supplier"}, {"file": "src\\services\\taskService.ts", "line": 143, "pattern": "Task data in console.log", "severity": "MEDIUM", "category": "BUSINESS_DATA", "content": "console.log('DEBUG: Final task data to insert: completed');", "match": "console.log('DEBUG: Final task data to insert: completed')"}, {"file": "src\\services\\taskService.ts", "line": 143, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log('DEBUG: Final task data to insert: completed');", "match": "console.log('DEBUG: Final task data to insert: completed')"}, {"file": "src\\services\\taskService.ts", "line": 1005, "pattern": "Database data in console.log", "severity": "MEDIUM", "category": "DATABASE_DATA", "content": "console.log(\"createOffer: Submitting offer to database:\", offerWithUserId);", "match": "console.log(\"createOffer: Submitting offer to database:\", offerWithUserId)"}, {"file": "src\\tests\\security\\organization-isolation.test.ts", "line": 33, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teacher1: { id: '', email: '<EMAIL>', organization: 'org1', role: 'teacher' },", "match": "teacher"}, {"file": "src\\tests\\security\\organization-isolation.test.ts", "line": 33, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teacher1: { id: '', email: '<EMAIL>', organization: 'org1', role: 'teacher' },", "match": "teacher"}, {"file": "src\\tests\\security\\organization-isolation.test.ts", "line": 33, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teacher1: { id: '', email: '<EMAIL>', organization: 'org1', role: 'teacher' },", "match": "teacher"}, {"file": "src\\tests\\security\\organization-isolation.test.ts", "line": 35, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teacher2: { id: '', email: '<EMAIL>', organization: 'org2', role: 'teacher' }", "match": "teacher"}, {"file": "src\\tests\\security\\organization-isolation.test.ts", "line": 35, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teacher2: { id: '', email: '<EMAIL>', organization: 'org2', role: 'teacher' }", "match": "teacher"}, {"file": "src\\tests\\security\\organization-isolation.test.ts", "line": 35, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teacher2: { id: '', email: '<EMAIL>', organization: 'org2', role: 'teacher' }", "match": "teacher"}, {"file": "src\\tests\\security\\organization-isolation.test.ts", "line": 39, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "task1: { id: '', title: 'Task from School A', organization: 'org1', creator: 'teacher1' },", "match": "teacher"}, {"file": "src\\tests\\security\\organization-isolation.test.ts", "line": 40, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "task2: { id: '', title: 'Task from School B', organization: 'org2', creator: 'teacher2' }", "match": "teacher"}, {"file": "src\\types\\organization.ts", "line": 1, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "export type UserRole = 'admin' | 'teacher' | 'support' | 'supplier' | 'maintenance';", "match": "teacher"}, {"file": "src\\types\\organization.ts", "line": 1, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "export type UserRole = 'admin' | 'teacher' | 'support' | 'supplier' | 'maintenance';", "match": "supplier"}, {"file": "src\\types\\organization.ts", "line": 1, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "export type UserRole = 'admin' | 'teacher' | 'support' | 'supplier' | 'maintenance';", "match": "maintenance"}, {"file": "src\\types\\organization.ts", "line": 3, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "export type OrganizationType = 'school' | 'trust' | 'supplier';", "match": "supplier"}, {"file": "src\\types\\supabase.ts", "line": 8, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "organization_type: 'school' | 'trust' | 'supplier';", "match": "supplier"}, {"file": "src\\types\\supabase.ts", "line": 22, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "organization_type: 'school' | 'trust' | 'supplier';", "match": "supplier"}, {"file": "src\\types\\supabase.ts", "line": 36, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "organization_type?: 'school' | 'trust' | 'supplier';", "match": "supplier"}, {"file": "src\\types\\tasks.ts", "line": 43, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "assigned_role: string; // Role of the assigned staff (maintenance, support, IT, etc.)", "match": "maintenance"}, {"file": "src\\types\\tasks.ts", "line": 43, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "assigned_role: string; // Role of the assigned staff (maintenance, support, IT, etc.)", "match": "support, IT, etc.)"}, {"file": "src\\types\\tasks.ts", "line": 55, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "assigned_to?: string | null; // ID of supplier (if assigned)", "match": "supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 31, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* 3. <PERSON><PERSON> Creates & Assigns to Supplier (2 members): Admin + Supplier", "match": "Supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 31, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* 3. <PERSON><PERSON> Creates & Assigns to Supplier (2 members): Admin + Supplier", "match": "Supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 32, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* 4. Teacher Creates, <PERSON><PERSON> Self-Assigns (2 members): Teacher + <PERSON><PERSON>", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 32, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* 4. Teacher Creates, <PERSON><PERSON> Self-Assigns (2 members): Teacher + <PERSON><PERSON>", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 33, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* 5. Teacher Creates, <PERSON>min Assigns to Internal Staff (3 members): Teacher + <PERSON>min + Staff", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 33, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* 5. Teacher Creates, <PERSON>min Assigns to Internal Staff (3 members): Teacher + <PERSON>min + Staff", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 34, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* 6. Teacher Creates, <PERSON><PERSON> Assigns to Supplier (3 members): Teacher + <PERSON><PERSON> + Supplier", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 34, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* 6. Teacher Creates, <PERSON><PERSON> Assigns to Supplier (3 members): Teacher + <PERSON><PERSON> + Supplier", "match": "Supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 34, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* 6. Teacher Creates, <PERSON><PERSON> Assigns to Supplier (3 members): Teacher + <PERSON><PERSON> + Supplier", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 34, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* 6. Teacher Creates, <PERSON><PERSON> Assigns to Supplier (3 members): Teacher + <PERSON><PERSON> + Supplier", "match": "Supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 53, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isAssigneeSupplier = assigneeId ? await isUserSupplier(assigneeId) : false;", "match": "Supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 53, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isAssigneeSupplier = assigneeId ? await isUserSupplier(assigneeId) : false;", "match": "Supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 66, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isAssigneeSupplier) {", "match": "Supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 67, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Admin assigned to supplier", "match": "supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 68, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "scenario = 'admin_assigned_to_supplier';", "match": "supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 69, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description = 'Admin created and assigned to supplier';", "match": "supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 82, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Teacher created the task", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 84, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// No assignment yet - just teacher (awaiting admin review)", "match": "teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 85, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "scenario = 'teacher_created_pending_review';", "match": "teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 86, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description = 'Teacher created task, awaiting admin review';", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 88, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Teacher created, admin self-assigned", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 89, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "scenario = 'teacher_created_admin_self_assigned';", "match": "teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 90, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description = 'Teacher created task, admin self-assigned';", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 94, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "} else if (isAssigneeSupplier) {", "match": "Supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 95, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Teacher created, admin assigned to supplier", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 95, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Teacher created, admin assigned to supplier", "match": "supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 96, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "scenario = 'teacher_created_admin_assigned_to_supplier';", "match": "teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 96, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "scenario = 'teacher_created_admin_assigned_to_supplier';", "match": "supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 97, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description = 'Teacher created task, admin assigned to supplier';", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 97, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description = 'Teacher created task, admin assigned to supplier';", "match": "supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 105, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Teacher created, admin assigned to internal staff", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 106, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "scenario = 'teacher_created_admin_assigned_to_staff';", "match": "teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 107, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "description = 'Teacher created task, admin assigned to internal staff';", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 156, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* Helper function to check if a user is a supplier", "match": "supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 158, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "async function isUserSupplier(userId: string): Promise<boolean> {", "match": "Supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 166, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "return profile?.account_type === 'supplier';", "match": "supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 168, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "console.error(`[ChatMembership] Error checking if user ${userId} is supplier:`, error);", "match": "supplier"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 201, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Teacher created the task, so an admin must have assigned it", "match": "Teacher"}, {"file": "src\\utils\\chatMembershipUtils.ts", "line": 224, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* Helper function to find the admin who assigned a teacher's task", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 252, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* @param teacherId - The teacher user ID", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 252, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* @param teacherId - The teacher user ID", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 253, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* @param supplierId - The supplier user ID", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 253, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "* @param supplierId - The supplier user ID", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 258, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teacherId: string,", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 259, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplierId: string", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 284, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "user_id: teacherId,", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 331, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "channel = await createOrUpdateTaskChannel(taskId, 'Test Task for GetStream Simulation', [adminId, teacherId], true);", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 356, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Connect as supplier and add to channel", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 357, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "await connectUser(supplierId, 'Supplier User', supplierId);", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 357, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "await connectUser(supplierId, 'Supplier User', supplierId);", "match": "Supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 357, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "await connectUser(supplierId, 'Supplier User', supplierId);", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 358, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "channel = await createOrUpdateTaskChannel(taskId, 'Test Task for GetStream Simulation', [adminId, teacherId, supplierId], true);", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 358, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "channel = await createOrUpdateTaskChannel(taskId, 'Test Task for GetStream Simulation', [adminId, teacherId, supplierId], true);", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 360, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "// Check channel membership after supplier interest", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 361, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "results.stages.supplier_interest = {", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 374, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "user_id: supplierId,", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 420, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "assigned_to: supplierId,", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 499, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "await connectUser(teacherId, 'Teacher User', teacherId);", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 499, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "await connectUser(teacherId, 'Teacher User', teacherId);", "match": "Teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 499, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "await connectUser(teacherId, 'Teacher User', teacherId);", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 500, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const teacherChannels = await getUserChannels(teacherId);", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 500, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const teacherChannels = await getUserChannels(teacherId);", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 501, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const teacherCanSeeChannel = teacherChannels.some(c => c.id === channel?.id);", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 501, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const teacherCanSeeChannel = teacherChannels.some(c => c.id === channel?.id);", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 503, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "await connectUser(supplierId, 'Supplier User', supplierId);", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 503, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "await connectUser(supplierId, 'Supplier User', supplierId);", "match": "Supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 503, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "await connectUser(supplierId, 'Supplier User', supplierId);", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 504, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplierChannels = await getUserChannels(supplierId);", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 504, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplierChannels = await getUserChannels(supplierId);", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 505, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplierCanSeeChannel = supplierChannels.some(c => c.id === channel?.id);", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 505, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const supplierCanSeeChannel = supplierChannels.some(c => c.id === channel?.id);", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 509, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teacher_can_see_channel: teacherCanSeeChannel,", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 509, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teacher_can_see_channel: teacherCanSeeChannel,", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 510, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_can_see_channel: supplierCanSeeChannel,", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 510, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_can_see_channel: supplierCanSeeChannel,", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 512, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teacher_channels_count: teacherChannels.length,", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 512, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "teacher_channels_count: teacherChannels.length,", "match": "teacher"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 513, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_channels_count: supplierChannels.length", "match": "supplier"}, {"file": "src\\utils\\getStreamDebug.ts", "line": 513, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "supplier_channels_count: supplierChannels.length", "match": "supplier"}, {"file": "src\\utils\\streamSystemMessages.ts", "line": 78, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "message = 'A supplier has expressed interest in this task.';", "match": "supplier"}, {"file": "src\\utils\\streamSystemMessages.ts", "line": 86, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": 'Task has been assigned to a supplier.';", "match": "supplier"}, {"file": "src\\utils\\streamSystemMessages.ts", "line": 90, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "? 'Maintenance work has started on this task.'", "match": "Maintenance"}, {"file": "src\\utils\\streamSystemMessages.ts", "line": 91, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": '<PERSON><PERSON><PERSON> has started work on this task.';", "match": "Supplier"}, {"file": "src\\utils\\streamSystemMessages.ts", "line": 95, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "? 'Maintenance staff has marked this task as completed.'", "match": "Maintenance"}, {"file": "src\\utils\\streamSystemMessages.ts", "line": 96, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": 'Su<PERSON><PERSON> has marked this task as completed.';", "match": "Supplier"}, {"file": "src\\utils\\streamSystemMessages.ts", "line": 122, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isMaintenanceStaff = userData.role === 'maintenance';", "match": "Maintenance"}, {"file": "src\\utils\\streamSystemMessages.ts", "line": 122, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": "const isMaintenanceStaff = userData.role === 'maintenance';", "match": "maintenance"}, {"file": "src\\utils\\streamSystemMessages.ts", "line": 126, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": isMaintenanceStaff ? 'maintenance staff' : 'a supplier';", "match": "Maintenance"}, {"file": "src\\utils\\streamSystemMessages.ts", "line": 126, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": isMaintenanceStaff ? 'maintenance staff' : 'a supplier';", "match": "maintenance"}, {"file": "src\\utils\\streamSystemMessages.ts", "line": 126, "pattern": "User role in console.log", "severity": "HIGH", "category": "USER_DATA", "content": ": isMaintenanceStaff ? 'maintenance staff' : 'a supplier';", "match": "supplier"}], "summary": {"CRITICAL": 1, "HIGH": 1388, "MEDIUM": 48, "LOW": 0}}