// CommonJS wrapper to start the ESM server
const { exec } = require('child_process');
const path = require('path');

console.log('Starting server...');

// Use the --experimental-specifier-resolution=node flag
const server = exec('node --experimental-specifier-resolution=node --experimental-modules src/server/index.js', {
  stdio: 'inherit',
  env: process.env
});

server.stdout.on('data', (data) => {
  console.log(data);
});

server.stderr.on('data', (data) => {
  console.error(data);
});

server.on('error', (err) => {
  console.error('Failed to start server:', err);
});

process.on('SIGINT', () => {
  server.kill('SIGINT');
  process.exit(0);
});
