# Stripe API Server Documentation

## Overview

This server provides a real implementation of the Stripe API for the ClassTasker application. It handles:

1. **Stripe Connect** - For onboarding suppliers and managing their accounts
2. **Direct Payments** - For processing payments directly through the platform
3. **Invoicing** - For generating and sending invoices to customers

## Running the Server

The server can be started using the following command:

```bash
npm run stripe-server
```

Or you can start both the development server and the Stripe API server using:

```bash
npm run dev:all
```

## Implementation Details

### Database Integration

The server integrates with the Supabase database to:

1. Verify that Stripe accounts exist in the database before making API calls
2. Update the database with the latest status from Stripe
3. Handle errors gracefully when accounts don't exist

### Security Measures

The server includes several security measures:

1. **Database Verification** - Ensures that only accounts in the database can be accessed
2. **Error Handling** - Provides appropriate error messages without exposing sensitive information
3. **Logging** - Logs detailed error information for debugging purposes

### API Endpoints

The server provides the following API endpoints:

#### Stripe Connect

- `GET /api/stripe-connect/account-status/:accountId` - Get the status of a Stripe Connect account
- `POST /api/stripe-connect/dashboard-link` - Generate a dashboard link for a Stripe Connect account
- `DELETE /api/stripe-connect/delete-account/:accountId` - Delete a Stripe Connect account

#### Payments

- `POST /api/stripe-connect/create-payment-intent` - Create a payment intent for direct payments
- `POST /api/stripe-connect/confirm-payment-intent` - Confirm a payment intent

#### Invoicing

- `POST /api/stripe-connect/create-invoice` - Create an invoice for a payment
- `POST /api/stripe-invoice/:id/send` - Send an invoice email

## Configuration

The server uses the following environment variables:

- `STRIPE_SECRET_KEY` - The Stripe API key for the platform account
- `STRIPE_API_PORT` - The port to run the server on (default: 3001)
- `STRIPE_CONNECT_EXPRESS_REFRESH_URL` - The URL to redirect to when refreshing the Stripe Connect onboarding process
- `STRIPE_CONNECT_EXPRESS_RETURN_URL` - The URL to redirect to when completing the Stripe Connect onboarding process

## Important Notes

1. **No Hardcoded Values** - The server does not use any hardcoded Stripe account IDs. All account IDs are fetched from the database.
2. **Error Handling** - The server includes robust error handling to ensure a smooth user experience even when errors occur.
3. **Direct Payments** - The server is configured for direct payments through the platform, not Stripe Connect payments.
