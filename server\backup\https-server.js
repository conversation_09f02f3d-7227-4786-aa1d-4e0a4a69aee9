import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import fs from 'fs';
import https from 'https';
import path from 'path';
import { fileURLToPath } from 'url';
import stripeConnectRouter from './api/stripe-connect.js';
import stripeWebhookRouter from './api/stripe-webhook.js';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Express app
const app = express();

// Enable CORS for all routes
app.use(cors({
  origin: '*',
  credentials: true
}));

// Parse JSON request bodies
app.use(express.json({ limit: '10mb' }));

// Use the Stripe Connect router
app.use('/api/stripe-connect', stripeConnectRouter);

// Use the Stripe Webhook router
app.use('/api/stripe-webhook', stripeWebhookRouter);

// Generate self-signed certificate
const generateSelfSignedCert = async () => {
  const certPath = path.join(__dirname, 'cert.pem');
  const keyPath = path.join(__dirname, 'key.pem');
  
  if (!fs.existsSync(certPath) || !fs.existsSync(keyPath)) {
    console.log('Generating self-signed certificate...');
    
    // Since we're using ESM, we need to use dynamic import for child_process
    const childProcess = await import('child_process');
    const { execSync } = childProcess;
    
    try {
      execSync(`openssl req -x509 -newkey rsa:4096 -keyout ${keyPath} -out ${certPath} -days 365 -nodes -subj "/CN=localhost"`);
      console.log('Self-signed certificate generated successfully');
    } catch (error) {
      console.error('Error generating self-signed certificate:', error);
      process.exit(1);
    }
  }
  
  return {
    cert: fs.readFileSync(certPath),
    key: fs.readFileSync(keyPath)
  };
};

// Start the server
const PORT = process.env.PORT || 3000;

try {
  const { cert, key } = await generateSelfSignedCert();
  
  const httpsServer = https.createServer({ key, cert }, app);
  
  httpsServer.listen(PORT, () => {
    console.log(`Stripe Connect API server listening on port ${PORT} (HTTPS)`);
    
    // Update the environment variables with the HTTPS URL
    const httpsUrl = `https://localhost:${PORT}`;
    
    // Update the .env file
    const envPath = path.join(__dirname, '..', '.env');
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    envContent = envContent
      .replace(/STRIPE_CONNECT_EXPRESS_RETURN_URL=.*/g, `STRIPE_CONNECT_EXPRESS_RETURN_URL=${httpsUrl}/api/stripe-connect`)
      .replace(/STRIPE_CONNECT_EXPRESS_REFRESH_URL=.*/g, `STRIPE_CONNECT_EXPRESS_REFRESH_URL=${httpsUrl}/api/stripe-connect`);
    
    fs.writeFileSync(envPath, envContent);
    
    // Update the .env.local file
    const envLocalPath = path.join(__dirname, '..', '.env.local');
    let envLocalContent = fs.readFileSync(envLocalPath, 'utf8');
    
    envLocalContent = envLocalContent
      .replace(/VITE_API_URL=.*/g, `VITE_API_URL=${httpsUrl}`)
      .replace(/VITE_STRIPE_CONNECT_EXPRESS_RETURN_URL=.*/g, `VITE_STRIPE_CONNECT_EXPRESS_RETURN_URL=${httpsUrl}/api/stripe-connect`)
      .replace(/VITE_STRIPE_CONNECT_EXPRESS_REFRESH_URL=.*/g, `VITE_STRIPE_CONNECT_EXPRESS_REFRESH_URL=${httpsUrl}/api/stripe-connect`);
    
    fs.writeFileSync(envLocalPath, envLocalContent);
    
    console.log(`\nEnvironment variables updated with HTTPS URL: ${httpsUrl}`);
    console.log(`Return URL: ${httpsUrl}/api/stripe-connect`);
    console.log(`Refresh URL: ${httpsUrl}/api/stripe-connect`);
  });
} catch (error) {
  console.error('Error starting HTTPS server:', error);
  process.exit(1);
}