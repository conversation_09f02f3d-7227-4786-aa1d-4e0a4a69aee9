// Last restarted: 2025-04-21T17:30:24.836Z
// Simple Express server for Stripe Connect API
import express from 'express';
import dotenv from 'dotenv';
import cors from 'cors';
import Stripe from 'stripe';
import stripeWebhookRouter from './api/stripe-webhook.js';
import stripeConnectRouter from './api/stripe-connect.js';
import stripeInvoiceRouter from './api/stripe-invoice.js';

// Load environment variables
import path from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Try different paths for the .env file
const envPaths = [
  path.resolve(__dirname, '.env'),
  path.resolve(process.cwd(), 'server/.env'),
  path.resolve(process.cwd(), '.env')
];

let envLoaded = false;
for (const envPath of envPaths) {
  console.log('Trying to load environment variables from:', envPath);
  const result = dotenv.config({ path: envPath });
  if (!result.error) {
    console.log('Successfully loaded environment variables from:', envPath);
    envLoaded = true;
    break;
  }
}

if (!envLoaded) {
  console.error('Failed to load environment variables from any of the paths');
}

// Log environment variables for debugging (without exposing sensitive data)
console.log('Environment variables loaded');

// Check if required environment variables are set
const requiredEnvVars = [
  'STRIPE_SECRET_KEY',
  'STRIPE_PUBLIC_KEY',
  'SUPABASE_URL',
  'SUPABASE_SERVICE_ROLE_KEY'
];

const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error('ERROR: Missing required environment variables:', missingEnvVars.join(', '));
  process.exit(1);
}

// Log non-sensitive environment variables
console.log('PORT:', process.env.PORT || '3001 (default)');
console.log('API mode:', process.env.STRIPE_SECRET_KEY?.startsWith('sk_test') ? 'TEST' : 'LIVE');

const app = express();
const PORT = process.env.STRIPE_API_PORT || 3001;

// Enable CORS for all routes
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// Add OPTIONS handling for preflight requests
app.options('/*', cors());

// Stripe webhook route - needs raw body for signature verification
app.use('/api/stripe-webhook', stripeWebhookRouter);

// Stripe Connect API routes
app.use('/api/stripe-connect', express.json(), (req, res, next) => {
  console.log(`Received ${req.method} request to ${req.originalUrl}`);
  console.log('Request body:', req.body);
  next();
}, stripeConnectRouter);

// Stripe Invoice API routes
app.use('/api/stripe-invoice', express.json(), (req, res, next) => {
  console.log(`Received ${req.method} request to ${req.originalUrl}`);
  console.log('Request body:', req.body);
  next();
}, stripeInvoiceRouter);

// Direct endpoint for sending invoice emails
app.post('/api/send-invoice-email/:id', express.json(), async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'Invoice ID is required' });
    }

    console.log('Sending email for invoice', id);

    // Initialize Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

    // Send the invoice email
    const sentInvoice = await stripe.invoices.sendInvoice(id);

    return res.status(200).json({ sent: true, invoice: sentInvoice });
  } catch (error) {
    console.error('Error sending invoice email:', error);
    return res.status(500).json({ error: 'Failed to send invoice email' });
  }
});

// Add a test route for the create-payment-intent endpoint
app.post('/api/test-payment-intent', express.json(), (req, res) => {
  console.log('Received test payment intent request');
  console.log('Request body:', req.body);
  res.json({ success: true, message: 'Test payment intent endpoint working' });
});

// Simple route for testing
app.get('/', (req, res) => {
  res.json({ status: 'Stripe Connect API server is running' });
});

app.listen(PORT, () => {
  console.log(`Stripe Connect API server listening on port ${PORT}`);
});
