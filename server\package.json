{"name": "class-tasker-connect-server", "version": "1.0.0", "description": "Server for Class Tasker Connect", "type": "module", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "stripe": "^14.8.0"}, "devDependencies": {"nodemon": "^3.0.2"}}