/**
 * GetStream Token API Route
 * 
 * This API route generates a token for GetStream Chat authentication.
 */

import express from 'express';
import { StreamChat } from 'stream-chat';
import { createClient } from '@supabase/supabase-js';

const router = express.Router();

// Initialize GetStream client
const streamApiKey = process.env.GETSTREAM_API_KEY;
const streamApiSecret = process.env.GETSTREAM_API_SECRET;
const serverClient = StreamChat.getInstance(streamApiKey, streamApiSecret);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Generate a token for a user
 * POST /api/getstream-token
 */
router.post('/', async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    console.log('Generating token for user:', userId);
    
    // Generate a token for the user
    const token = serverClient.createToken(userId);
    
    console.log('Token generated successfully');
    
    res.json({ token });
  } catch (error) {
    console.error('Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

export default router;
