/**
 * GetStream API Routes
 *
 * This file contains routes for GetStream integration
 */

import express from 'express';
import { StreamChat } from 'stream-chat';
import { createClient } from '@supabase/supabase-js';

const router = express.Router();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Initialize GetStream client
const streamApiKey = process.env.GETSTREAM_API_KEY;
const streamApiSecret = process.env.GETSTREAM_API_SECRET;
const serverClient = StreamChat.getInstance(streamApiKey, streamApiSecret);

/**
 * Generate a token for a user
 * POST /api/getstream/token
 */
router.post('/token', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    console.log('Generating token for user:', userId);

    // Generate a token for the user
    const token = serverClient.createToken(userId);

    console.log('Token generated successfully');

    res.json({ token });
  } catch (error) {
    console.error('Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
});

/**
 * Create a channel for a task
 * POST /api/getstream/channels
 */
router.post('/channels', async (req, res) => {
  try {
    const { taskId, taskTitle, members, userId } = req.body;

    if (!taskId || !taskTitle || !userId) {
      return res.status(400).json({ error: 'Task ID, title, and user ID are required' });
    }

    console.log('Creating channel for task:', taskId, 'by user:', userId);

    // Validate that the user has permission to create a channel for this task
    // and that all members belong to the same organization
    const { data: task, error: taskError } = await supabase
      .from('tasks')
      .select(`
        id,
        title,
        user_id,
        assigned_to,
        organization_id,
        visibility
      `)
      .eq('id', taskId)
      .single();

    if (taskError || !task) {
      console.error('Task not found or error:', taskError);
      return res.status(404).json({ error: 'Task not found' });
    }

    // Get the requesting user's profile
    const { data: userProfile, error: userError } = await supabase
      .from('profiles')
      .select('organization_id, role, account_type')
      .eq('id', userId)
      .single();

    if (userError || !userProfile) {
      console.error('User profile not found:', userError);
      return res.status(403).json({ error: 'User not authorized' });
    }

    // Validate organization access
    if (task.organization_id && userProfile.organization_id !== task.organization_id) {
      console.error('User organization mismatch:', userProfile.organization_id, 'vs task org:', task.organization_id);
      return res.status(403).json({ error: 'Access denied: Not a member of task organization' });
    }

    // Validate that all provided members belong to the same organization
    if (members && Array.isArray(members) && members.length > 0) {
      const { data: memberProfiles, error: memberError } = await supabase
        .from('profiles')
        .select('id, organization_id')
        .in('id', members);

      if (memberError) {
        console.error('Error fetching member profiles:', memberError);
        return res.status(500).json({ error: 'Failed to validate members' });
      }

      // Check that all members belong to the same organization as the task
      const invalidMembers = memberProfiles.filter(profile =>
        task.organization_id && profile.organization_id !== task.organization_id
      );

      if (invalidMembers.length > 0) {
        console.error('Invalid members found:', invalidMembers);
        return res.status(403).json({ error: 'Some members do not belong to the task organization' });
      }
    }

    // Create a channel
    const channelId = `task-${taskId}`;

    // Use provided members or create an empty array
    const channelMembers = members && Array.isArray(members) ? members : [];

    const channel = serverClient.channel('messaging', channelId, {
      name: taskTitle,
      members: channelMembers,
      task_id: taskId,
      organization_id: task.organization_id, // Store organization ID in channel metadata
    });

    await channel.create();

    console.log('Channel created successfully:', channelId);

    res.json({
      channelId,
      channel: channel.id,
      members: channel.state.members
    });
  } catch (error) {
    console.error('Error creating channel:', error);
    res.status(500).json({ error: 'Failed to create channel' });
  }
});

/**
 * Migrate a task's chat to GetStream
 * POST /api/getstream/migrate-task-chat
 */
router.post('/migrate-task-chat', async (req, res) => {
  try {
    const { taskId, taskTitle } = req.body;

    if (!taskId || !taskTitle) {
      return res.status(400).json({ error: 'Task ID and title are required' });
    }

    console.log('Migrating chat for task:', taskId);

    // Create a channel
    const channelId = `task-${taskId}`;
    const channel = serverClient.channel('messaging', channelId, {
      name: taskTitle,
      task_id: taskId,
    });

    await channel.create();

    console.log('Channel created successfully:', channelId);

    // Mark the task as migrated in Supabase
    const { error: updateError } = await supabase
      .from('tasks')
      .update({
        chat_migrated_to_stream: true,
        getstream_channel_id: channelId
      })
      .eq('id', taskId);

    if (updateError) {
      console.error('Error updating task:', updateError);
      return res.status(500).json({ error: 'Failed to update task' });
    }

    console.log('Task marked as migrated:', taskId);

    res.json({
      success: true,
      channelId,
      channel: channel.id
    });
  } catch (error) {
    console.error('Error migrating task chat:', error);
    res.status(500).json({ error: 'Failed to migrate task chat' });
  }
});

/**
 * Add a member to a channel
 * POST /api/getstream/channels/:channelId/members
 */
router.post('/channels/:channelId/members', async (req, res) => {
  try {
    const { channelId } = req.params;
    const { userId, requestingUserId } = req.body;

    if (!userId || !requestingUserId) {
      return res.status(400).json({ error: 'User ID and requesting user ID are required' });
    }

    // Extract task ID from channel ID (format: task-{taskId})
    const taskId = channelId.replace('task-', '');

    // Get the task details to validate organization
    const { data: task, error: taskError } = await supabase
      .from('tasks')
      .select('id, organization_id, user_id, assigned_to')
      .eq('id', taskId)
      .single();

    if (taskError || !task) {
      console.error('Task not found for channel:', channelId, taskError);
      return res.status(404).json({ error: 'Task not found for this channel' });
    }

    // Get both users' profiles to validate organization membership
    const { data: userProfiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, organization_id, role, account_type')
      .in('id', [userId, requestingUserId]);

    if (profileError || !userProfiles || userProfiles.length !== 2) {
      console.error('Error fetching user profiles:', profileError);
      return res.status(403).json({ error: 'User profiles not found' });
    }

    const userToAdd = userProfiles.find(p => p.id === userId);
    const requestingUser = userProfiles.find(p => p.id === requestingUserId);

    // Validate that the requesting user has permission to add members
    if (task.organization_id && requestingUser.organization_id !== task.organization_id) {
      console.error('Requesting user not in task organization');
      return res.status(403).json({ error: 'Access denied: Not a member of task organization' });
    }

    // Validate that the user being added belongs to the same organization
    if (task.organization_id && userToAdd.organization_id !== task.organization_id) {
      console.error('User to add not in task organization');
      return res.status(403).json({ error: 'Cannot add user: Not a member of task organization' });
    }

    // Get the channel
    const channel = serverClient.channel('messaging', channelId);

    // Add the member
    await channel.addMembers([userId]);

    console.log('Member added successfully to channel:', channelId, 'user:', userId);

    res.json({ success: true });
  } catch (error) {
    console.error('Error adding member to channel:', error);
    res.status(500).json({ error: 'Failed to add member to channel' });
  }
});

/**
 * Remove a member from a channel
 * DELETE /api/getstream/channels/:channelId/members/:userId
 */
router.delete('/channels/:channelId/members/:userId', async (req, res) => {
  try {
    const { channelId, userId } = req.params;

    // Get the channel
    const channel = serverClient.channel('messaging', channelId);

    // Remove the member
    await channel.removeMembers([userId]);

    res.json({ success: true });
  } catch (error) {
    console.error('Error removing member from channel:', error);
    res.status(500).json({ error: 'Failed to remove member from channel' });
  }
});

/**
 * Delete a channel
 * DELETE /api/getstream/channels/:channelId
 */
router.delete('/channels/:channelId', async (req, res) => {
  try {
    const { channelId } = req.params;

    // Get the channel
    const channel = serverClient.channel('messaging', channelId);

    // Delete the channel
    await channel.delete();

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting channel:', error);
    res.status(500).json({ error: 'Failed to delete channel' });
  }
});

export default router;
