// Simple script to start the server with environment variables
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// SECURITY: Load environment variables from .env file instead of hardcoding
// Never hardcode API keys or secrets in source code
require('dotenv').config();

// Validate required environment variables
if (!process.env.STRIPE_SECRET_KEY) {
  console.error('SECURITY ERROR: STRIPE_SECRET_KEY not found in environment variables');
  process.exit(1);
}

if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.error('SECURITY ERROR: SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  process.exit(1);
}

// Set default values for non-sensitive variables
process.env.SUPABASE_URL = process.env.SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
process.env.PORT = process.env.PORT || '3001';

// Import and run the server
import('./index.js').catch(err => {
  console.error('Error starting server:', err);
  process.exit(1);
});

console.log('Server started with environment variables set programmatically');
