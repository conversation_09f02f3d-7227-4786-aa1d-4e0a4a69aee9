-- SQL script to add policies allowing admins to manage all offers
-- This fixes the issue where admins can't see accept/reject buttons for offers on tasks they don't own

-- Enable RLS on the offers table if not already enabled
ALTER TABLE offers ENABLE ROW LEVEL SECURITY;

-- Drop any existing admin policies to avoid conflicts
DROP POLICY IF EXISTS allow_admins_to_view_all_offers ON offers;
DROP POLICY IF EXISTS allow_admins_to_update_all_offers ON offers;
DROP POLICY IF EXISTS allow_admins_to_delete_all_offers ON offers;

-- Create a policy to allow admins to view all offers
CREATE POLICY allow_admins_to_view_all_offers
ON offers
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

-- Create a policy to allow admins to update all offers
CREATE POLICY allow_admins_to_update_all_offers
ON offers
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

-- Create a policy to allow admins to delete all offers
CREATE POLICY allow_admins_to_delete_all_offers
ON offers
FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

-- Add a comment explaining the purpose of these policies
COMMENT ON TABLE offers IS 'Offers made by suppliers for tasks. Admins can manage all offers, while regular users can only manage their own offers or offers for tasks they own.';