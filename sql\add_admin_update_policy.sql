-- SQL script to add a policy allowing admins to update any task
-- This fixes the issue where admins can't mark tasks as complete

-- Create a function to check if the user is an admin
CREATE OR REPLACE FUNCTION public.check_admin_role()
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop any existing admin update policy to avoid conflicts
DROP POLICY IF EXISTS "Ad<PERSON> can update any task" ON public.tasks;

-- Create a policy to allow admins to update any task
CREATE POLICY "Ad<PERSON> can update any task"
ON public.tasks
FOR UPDATE
USING (check_admin_role())
WITH CHECK (check_admin_role());

-- Ensure the tasks table has RLS enabled
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
