-- Add building and room columns to the tasks table
-- This script adds new columns to better describe specific locations within schools

-- Check if the building column exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'tasks'
    AND column_name = 'building'
    AND table_schema = 'public'
  ) THEN
    -- Add the building column
    ALTER TABLE public.tasks
    ADD COLUMN building TEXT;
    
    -- Add a comment
    COMMENT ON COLUMN public.tasks.building IS 'Building name or identifier within the school';
  END IF;
END $$;

-- Check if the room column exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'tasks'
    AND column_name = 'room'
    AND table_schema = 'public'
  ) THEN
    -- Add the room column
    ALTER TABLE public.tasks
    ADD COLUMN room TEXT;
    
    -- Add a comment
    COMMENT ON COLUMN public.tasks.room IS 'Classroom or room identifier within the building';
  END IF;
END $$;

-- Verify the changes
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default, 
    pg_catalog.col_description(format('%I.%I', table_schema, table_name)::regclass::oid, ordinal_position) as column_comment
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'tasks'
    AND column_name IN ('building', 'room')
ORDER BY 
    ordinal_position;
