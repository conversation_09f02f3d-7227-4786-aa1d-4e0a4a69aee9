-- SQL script to add email column to profiles table and populate it with emails from auth.users
-- Run this in the Supabase SQL Editor

-- Add email column to profiles table if it doesn't exist
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS email TEXT;

-- Update profiles with emails from auth.users
-- This requires a function that can access auth.users
CREATE OR REPLACE FUNCTION update_profile_emails()
R<PERSON><PERSON>NS void AS $$
DECLARE
    profile_record RECORD;
    auth_email TEXT;
BEGIN
    FOR profile_record IN SELECT id FROM public.profiles LOOP
        -- Get email from auth.users
        SELECT email INTO auth_email FROM auth.users WHERE id = profile_record.id;

        -- Update profile with email
        IF auth_email IS NOT NULL THEN
            UPDATE public.profiles SET email = auth_email WHERE id = profile_record.id;
            RAISE NOTICE 'Updated profile % with email %', profile_record.id, auth_email;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Execute the function
SELECT update_profile_emails();

-- Create a trigger to keep emails in sync
CREATE OR REPLACE FUNCTION sync_profile_email()
<PERSON><PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    -- When a new user is created in auth.users, update the corresponding profile
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE public.profiles
        SET email = NEW.email
        WHERE id = NEW.id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS sync_user_email_to_profile ON auth.users;

-- Create the trigger
CREATE TRIGGER sync_user_email_to_profile
AFTER INSERT OR UPDATE OF email ON auth.users
FOR EACH ROW
EXECUTE FUNCTION sync_profile_email();

-- Verify the updates
SELECT id, email, organization_id, role FROM public.profiles;
