-- Add GetStream-related columns to the tasks table

-- Check if the chat_migrated_to_stream column exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'tasks'
    AND column_name = 'chat_migrated_to_stream'
    AND table_schema = 'public'
  ) THEN
    -- Add the chat_migrated_to_stream column
    ALTER TABLE public.tasks
    ADD COLUMN chat_migrated_to_stream BOOLEAN DEFAULT FALSE;
    
    -- Add an index for better performance
    CREATE INDEX IF NOT EXISTS tasks_chat_migrated_to_stream_idx ON public.tasks(chat_migrated_to_stream);
    
    -- Add a comment
    COMMENT ON COLUMN public.tasks.chat_migrated_to_stream IS 'Indicates if the task chat has been migrated to GetStream';
  END IF;
END $$;

-- Check if the getstream_channel_id column exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'tasks'
    AND column_name = 'getstream_channel_id'
    AND table_schema = 'public'
  ) THEN
    -- Add the getstream_channel_id column
    ALTER TABLE public.tasks
    ADD COLUMN getstream_channel_id TEXT;
    
    -- Add an index for better performance
    CREATE INDEX IF NOT EXISTS tasks_getstream_channel_id_idx ON public.tasks(getstream_channel_id);
    
    -- Add a comment
    COMMENT ON COLUMN public.tasks.getstream_channel_id IS 'The GetStream channel ID for this task';
  END IF;
END $$;

-- Check if the stream_message_id column exists in task_messages
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'task_messages'
    AND column_name = 'stream_message_id'
    AND table_schema = 'public'
  ) THEN
    -- Add the stream_message_id column
    ALTER TABLE public.task_messages
    ADD COLUMN stream_message_id TEXT;
    
    -- Add an index for better performance
    CREATE INDEX IF NOT EXISTS task_messages_stream_message_id_idx ON public.task_messages(stream_message_id);
    
    -- Add a comment
    COMMENT ON COLUMN public.task_messages.stream_message_id IS 'The GetStream message ID for this message';
  END IF;
END $$;

-- Create a function to migrate a task's chat to GetStream
CREATE OR REPLACE FUNCTION public.migrate_task_chat_to_getstream(task_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Mark the task as migrated
  UPDATE public.tasks
  SET chat_migrated_to_stream = TRUE,
      getstream_channel_id = 'task-' || task_id_param::TEXT
  WHERE id = task_id_param;
  
  RETURN TRUE;
END;
$$;

-- Add comment
COMMENT ON FUNCTION public.migrate_task_chat_to_getstream IS 'Marks a task as having its chat migrated to GetStream';
