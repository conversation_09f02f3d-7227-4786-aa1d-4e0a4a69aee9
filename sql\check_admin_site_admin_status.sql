-- Check the site admin <NAME_EMAIL>
SELECT 
  id,
  email,
  role,
  is_site_admin,
  organization_id,
  created_at,
  updated_at
FROM 
  profiles
WHERE 
  email = ARRAY['<EMAIL>'];

-- Check if the is_site_admin function exists
SELECT 
  routine_name,
  routine_type,
  data_type
FROM 
  information_schema.routines
WHERE 
  routine_name = 'is_site_admin'
  AND routine_schema = 'public';

-- Check if the has_role function exists
SELECT 
  routine_name,
  routine_type,
  data_type
FROM 
  information_schema.routines
WHERE 
  routine_name = 'has_role'
  AND routine_schema = 'public';

-- Check the RLS policies on the profiles table
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  tablename = 'profiles'
ORDER BY
  policyname;
