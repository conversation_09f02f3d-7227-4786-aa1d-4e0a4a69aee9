-- <NAME_EMAIL> has site admin privileges
SELECT 
  id,
  email,
  role,
  is_site_admin,
  organization_id
FROM 
  profiles
WHERE 
  email = ARRAY['<EMAIL>'];

-- Check if the is_site_admin function exists
SELECT 
  routine_name,
  routine_type,
  data_type
FROM 
  information_schema.routines
WHERE 
  routine_name = 'is_site_admin'
  AND routine_schema = 'public';

-- Test the is_site_admin function
DO $$
DECLARE
  admin_id UUID;
  result BOOLEAN;
BEGIN
  -- Get the <NAME_EMAIL>
  SELECT id INTO admin_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  -- Set the current <NAME_EMAIL>
  IF admin_id IS NOT NULL THEN
    -- This won't actually work in the SQL editor, but it's a good check
    RAISE NOTICE 'Admin ID: %', admin_id;
  ELSE
    RAISE NOTICE 'Admin user not found';
  END IF;
END $$;
