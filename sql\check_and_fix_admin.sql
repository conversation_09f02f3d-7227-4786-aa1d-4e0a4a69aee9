-- SQL script to check and <NAME_EMAIL> user
-- Run this in the Supabase SQL Editor

-- Check the current <NAME_EMAIL>
SELECT 
  id,
  email,
  role,
  is_site_admin,
  organization_id
FROM 
  profiles
WHERE 
  email = ARRAY['<EMAIL>'];

-- <NAME_EMAIL> is a site admin
UPDATE profiles
SET is_site_admin = true
WHERE email = ARRAY['<EMAIL>']
AND role = 'admin';

-- Verify the update
SELECT 
  id,
  email,
  role,
  is_site_admin,
  organization_id
FROM 
  profiles
WHERE 
  email = ARRAY['<EMAIL>'];

-- Check if the user exists in auth.users
SELECT 
  id,
  email,
  email_confirmed_at,
  last_sign_in_at,
  raw_user_meta_data
FROM 
  auth.users
WHERE 
  email = '<EMAIL>';

-- If the user doesn't exist in auth.users, create it
DO $$
DECLARE
  admin_id UUID;
BEGIN
  -- <NAME_EMAIL> exists in auth.users
  SELECT id INTO admin_id
  FROM auth.users
  WHERE email = '<EMAIL>';
  
  IF admin_id IS NULL THEN
    -- Create the user in auth.users
    INSERT INTO auth.users (
      id,
      email,
      email_confirmed_at,
      raw_user_meta_data
    )
    VALUES (
      gen_random_uuid(),
      '<EMAIL>',
      now(),
      '{"name": "Admin User", "account_type": "school"}'
    )
    RETURNING id INTO admin_id;
    
    -- Create the profile
    INSERT INTO profiles (
      id,
      email,
      first_name,
      last_name,
      role,
      is_site_admin,
      account_type
    )
    VALUES (
      admin_id,
      ARRAY['<EMAIL>'],
      'Admin',
      'User',
      'admin',
      true,
      'school'
    );
    
    RAISE NOTICE 'Created admin user with ID: %', admin_id;
  ELSE
    RAISE NOTICE 'Admin user already exists with ID: %', admin_id;
  END IF;
END $$;
