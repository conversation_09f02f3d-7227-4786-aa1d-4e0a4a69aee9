-- SQL script to check the RLS policies for the tasks table
-- Run this in the Supabase SQL Editor

-- List all policies for the tasks table
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  tablename = 'tasks';

-- Check if <PERSON><PERSON> is enabled for the tasks table
SELECT
  relname,
  relrowsecurity
FROM
  pg_class
WHERE
  relname = 'tasks';

-- Create a function to check task policies
CREATE OR REPLACE FUNCTION check_task_policies()
RETURNS TEXT AS $$
DECLARE
  result TEXT := '';
BEGIN
  -- Check if R<PERSON> is enabled
  SELECT
    CASE WHEN relrowsecurity THEN 'RLS is enabled for tasks table'
    ELSE 'RLS is NOT enabled for tasks table'
    END
  INTO result
  FROM pg_class
  WHERE relname = 'tasks';
  
  result := result || E'\n\n';
  
  -- Count policies
  SELECT
    result || 'Number of policies for tasks table: ' || COUNT(*)::TEXT
  INTO result
  FROM pg_policies
  WHERE tablename = 'tasks';
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Call the function
SELECT check_task_policies();
