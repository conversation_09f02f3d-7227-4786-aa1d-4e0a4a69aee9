-- Comprehensive fix for RLS policies to eliminate recursion issues
-- This script uses SECURITY DEFINER functions to avoid recursion while maintaining security

-- First, drop all existing policies on the profiles table
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Trust admins can view profiles in schools under their trust" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Public profiles access" ON profiles;
DROP POLICY IF EXISTS "Authenticated users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Authenticated users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can access all profiles" ON profiles;
DROP POLICY IF EXISTS "Block anonymous access to profiles" ON profiles;
DROP POLICY IF EXISTS "service_role_profiles_all" ON profiles;
DROP POLICY IF EXISTS "auth_profiles_own" ON profiles;
DROP POLICY IF EXISTS "admin_profiles_org_select" ON profiles;
DROP POLICY IF EXISTS "admin_profiles_org_update" ON profiles;
DROP POLICY IF EXISTS "auth_profiles_view_all" ON profiles;
DROP POLICY IF EXISTS "auth_profiles_view_limited" ON profiles;
DROP POLICY IF EXISTS "admin_view_org_profiles" ON profiles;
DROP POLICY IF EXISTS "auth_profiles_view_org" ON profiles;
DROP POLICY IF EXISTS "admin_update_org_profiles" ON profiles;
DROP POLICY IF EXISTS "block_anon_access" ON profiles;

-- Drop existing functions if they exist
DROP FUNCTION IF EXISTS check_admin_for_org(uuid);
DROP FUNCTION IF EXISTS get_user_organization_id();
DROP FUNCTION IF EXISTS is_same_organization(uuid);

-- 1. Create a function to get the current user's organization_id
-- This function runs with elevated privileges and bypasses RLS
CREATE OR REPLACE FUNCTION get_user_organization_id()
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  org_id uuid;
BEGIN
  -- First try to get from JWT claims if available
  org_id := (auth.jwt() ->> 'organization_id')::uuid;
  
  -- If not in JWT, query the database
  IF org_id IS NULL THEN
    SELECT organization_id INTO org_id
    FROM profiles
    WHERE id = auth.uid();
  END IF;
  
  RETURN org_id;
END;
$$;

-- 2. Create a function to check if a user is an admin for an organization
CREATE OR REPLACE FUNCTION check_admin_for_org(org_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  is_admin boolean;
  user_role text;
  user_org_id uuid;
BEGIN
  -- Get the user's role and organization_id
  SELECT role, organization_id
  INTO user_role, user_org_id
  FROM profiles
  WHERE id = auth.uid();
  
  -- Check if user is an admin and in the specified organization
  is_admin := (user_role = 'admin' AND user_org_id = org_id);
  
  RETURN COALESCE(is_admin, false);
END;
$$;

-- 3. Create a function to check if a profile is in the same organization as the current user
CREATE OR REPLACE FUNCTION is_same_organization(profile_org_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_org_id uuid;
BEGIN
  -- Get the current user's organization_id
  SELECT organization_id INTO user_org_id
  FROM profiles
  WHERE id = auth.uid();
  
  -- Check if the organizations match
  RETURN user_org_id = profile_org_id;
END;
$$;

-- Now create new, non-recursive policies using these functions

-- 1. Service role can access all profiles
CREATE POLICY "service_role_profiles_all"
ON profiles
FOR ALL
TO service_role
USING (true);

-- 2. Authenticated users can view and update their own profile
CREATE POLICY "auth_profiles_own"
ON profiles
FOR ALL
TO authenticated
USING (auth.uid() = id);

-- 3. Authenticated users can view profiles in their organization
-- This uses the is_same_organization function to avoid recursion
CREATE POLICY "auth_profiles_view_org"
ON profiles
FOR SELECT
TO authenticated
USING (
  is_same_organization(organization_id)
);

-- 4. Authenticated users with admin role can update profiles in their organization
-- This uses the check_admin_for_org function to avoid recursion
CREATE POLICY "admin_update_org_profiles"
ON profiles
FOR UPDATE
TO authenticated
USING (
  check_admin_for_org(organization_id)
);

-- 5. Block anonymous access
CREATE POLICY "block_anon_access"
ON profiles
FOR ALL
TO anon
USING (false);

-- Create a view for public profile information
CREATE OR REPLACE VIEW public_profiles AS
SELECT 
  id,
  first_name,
  last_name,
  role,
  organization_id,
  created_at
FROM profiles;

-- Grant access to the view
GRANT SELECT ON public_profiles TO anon, authenticated;

-- Add comment explaining the fix
COMMENT ON TABLE profiles IS 'User profile information with non-recursive RLS policies using SECURITY DEFINER functions';