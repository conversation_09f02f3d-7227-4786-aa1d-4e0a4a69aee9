-- Create a SQL function to handle offer acceptance
CREATE OR REPLACE FUNCTION public.accept_offer(task_id_param UUID, offer_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  offer_record RECORD;
  task_record RECORD;
BEGIN
  -- Check if the task exists and is in 'open' status
  SELECT * INTO task_record
  FROM public.tasks
  WHERE id = task_id_param;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Task not found with ID: %', task_id_param;
    RETURN FALSE;
  END IF;
  
  IF task_record.status != 'open' THEN
    RAISE EXCEPTION 'Task is not in open status. Current status: %', task_record.status;
    RETURN FALSE;
  END IF;
  
  -- Check if the offer exists
  SELECT * INTO offer_record
  FROM public.offers
  WHERE id = offer_id_param AND task_id = task_id_param;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Offer not found with ID: % for task: %', offer_id_param, task_id_param;
    RETURN FALSE;
  END IF;
  
  -- Update the offer status to 'accepted'
  UPDATE public.offers
  SET status = 'accepted'
  WHERE id = offer_id_param;
  
  -- Update the task status to 'assigned' and set assigned_to
  UPDATE public.tasks
  SET 
    status = 'assigned',
    assigned_to = offer_record.user_id,
    visibility = 'public'
  WHERE id = task_id_param;
  
  -- Reject all other offers for this task
  UPDATE public.offers
  SET status = 'rejected'
  WHERE task_id = task_id_param
  AND id != offer_id_param;
  
  RETURN TRUE;
END;
$$;
