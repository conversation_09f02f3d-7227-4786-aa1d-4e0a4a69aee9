-- Create a function to allow admins to update tasks
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION public.update_task_as_admin(
  task_id UUID,
  new_status TEXT,
  admin_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_admin BOOLEAN;
BEGIN
  -- Check if the user is an admin
  SELECT EXISTS (
    SELECT 1 
    FROM public.profiles 
    WHERE id = admin_id 
    AND role = 'admin'
  ) INTO is_admin;
  
  -- If not an admin, return false
  IF NOT is_admin THEN
    RETURN FALSE;
  END IF;
  
  -- Update the task
  UPDATE public.tasks
  SET 
    status = new_status,
    payment_status = CASE 
      WHEN new_status = 'pending_payment' THEN 'pending'
      ELSE payment_status
    END,
    updated_at = NOW()
  WHERE id = task_id;
  
  RETURN TRUE;
END;
$$;
