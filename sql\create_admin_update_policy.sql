-- Create a policy to allow admins to update tasks
DROP POLICY IF EXISTS "Ad<PERSON> can update any task" ON public.tasks;

CREATE POLICY "Admins can update any task"
ON public.tasks
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 
    FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 
    FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
);
