-- Create the chat_threads table for task-specific conversations
CREATE TABLE IF NOT EXISTS public.chat_threads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
  supplier_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  admin_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'interest',
  has_offer BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  last_message_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS chat_threads_task_id_idx ON public.chat_threads(task_id);
CREATE INDEX IF NOT EXISTS chat_threads_supplier_id_idx ON public.chat_threads(supplier_id);
CREATE INDEX IF NOT EXISTS chat_threads_admin_id_idx ON public.chat_threads(admin_id);
CREATE INDEX IF NOT EXISTS chat_threads_status_idx ON public.chat_threads(status);
CREATE INDEX IF NOT EXISTS chat_threads_last_message_at_idx ON public.chat_threads(last_message_at);

-- Enable RLS
ALTER TABLE public.chat_threads ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Allow authenticated users to view threads they are part of
CREATE POLICY "Users can view their own chat threads" 
ON public.chat_threads
FOR SELECT
USING (
  auth.uid() IN (supplier_id, admin_id)
);

-- Allow suppliers to create threads for tasks
CREATE POLICY "Suppliers can create chat threads" 
ON public.chat_threads
FOR INSERT
WITH CHECK (
  auth.uid() = supplier_id
  AND EXISTS (
    SELECT 1 FROM public.tasks
    WHERE id = task_id
    AND status = 'open'
  )
);

-- Allow users to update threads they are part of
CREATE POLICY "Users can update their own chat threads" 
ON public.chat_threads
FOR UPDATE
USING (
  auth.uid() IN (supplier_id, admin_id)
);

-- Add thread_id column to task_messages if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'task_messages'
    AND column_name = 'thread_id'
    AND table_schema = 'public'
  ) THEN
    ALTER TABLE public.task_messages
    ADD COLUMN thread_id UUID REFERENCES public.chat_threads(id) ON DELETE CASCADE;
    
    CREATE INDEX IF NOT EXISTS task_messages_thread_id_idx ON public.task_messages(thread_id);
  END IF;
END $$;

-- Update task_messages policies to include thread_id
DROP POLICY IF EXISTS "Task participants can read messages" ON public.task_messages;
CREATE POLICY "Task participants can read messages" 
ON public.task_messages
FOR SELECT
USING (
  auth.uid() IN (
    SELECT user_id FROM public.tasks WHERE id = task_id
    UNION
    SELECT user_id FROM public.offers WHERE task_id = task_messages.task_id
    UNION
    SELECT supplier_id FROM public.chat_threads WHERE id = thread_id
    UNION
    SELECT admin_id FROM public.chat_threads WHERE id = thread_id
  )
);

-- Update insert policy for task_messages
DROP POLICY IF EXISTS "Users can send messages for their tasks or offers" ON public.task_messages;
CREATE POLICY "Users can send messages for their tasks or offers"
ON public.task_messages
FOR INSERT
WITH CHECK (
  auth.uid() = sender_id
  AND (
    auth.uid() IN (
      SELECT user_id FROM public.tasks WHERE id = task_id
      UNION
      SELECT user_id FROM public.offers WHERE task_id = task_messages.task_id
    )
    OR
    (thread_id IS NOT NULL AND auth.uid() IN (
      SELECT supplier_id FROM public.chat_threads WHERE id = thread_id
      UNION
      SELECT admin_id FROM public.chat_threads WHERE id = thread_id
    ))
  )
);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_chat_threads_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update timestamps
DROP TRIGGER IF EXISTS update_chat_threads_updated_at ON public.chat_threads;
CREATE TRIGGER update_chat_threads_updated_at
BEFORE UPDATE ON public.chat_threads
FOR EACH ROW
EXECUTE FUNCTION public.update_chat_threads_updated_at();

-- Add comment
COMMENT ON TABLE public.chat_threads IS 'Stores chat threads between suppliers and admins for tasks';
