-- Create compliance_tasks table for storing recurring compliance tasks
CREATE TABLE IF NOT EXISTS public.compliance_tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  cycle TEXT NOT NULL, -- 'daily', 'weekly', 'monthly', 'annually'
  last_completed_at TIMESTAMPTZ,
  next_due_date TIMESTAMPTZ NOT NULL,
  created_by UUID NOT NULL REFERENCES auth.users(id),
  parent_task_id UUID REFERENCES public.compliance_tasks(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create compliance_attachments table for storing proof documents
CREATE TABLE IF NOT EXISTS public.compliance_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  compliance_task_id UUID NOT NULL REFERENCES public.compliance_tasks(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_path TEXT NOT NULL,
  uploaded_by UUID NOT NULL REFERENCES auth.users(id),
  uploaded_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create compliance_completions table to track completion history
CREATE TABLE IF NOT EXISTS public.compliance_completions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  compliance_task_id UUID NOT NULL REFERENCES public.compliance_tasks(id) ON DELETE CASCADE,
  completed_by UUID NOT NULL REFERENCES auth.users(id),
  completed_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  notes TEXT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS compliance_tasks_organization_id_idx ON public.compliance_tasks(organization_id);
CREATE INDEX IF NOT EXISTS compliance_tasks_next_due_date_idx ON public.compliance_tasks(next_due_date);
CREATE INDEX IF NOT EXISTS compliance_attachments_compliance_task_id_idx ON public.compliance_attachments(compliance_task_id);
CREATE INDEX IF NOT EXISTS compliance_completions_compliance_task_id_idx ON public.compliance_completions(compliance_task_id);

-- Enable RLS
ALTER TABLE public.compliance_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.compliance_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.compliance_completions ENABLE ROW LEVEL SECURITY;

-- Create policies for compliance_tasks
CREATE POLICY "Organization admins can manage compliance tasks"
ON public.compliance_tasks
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND role = 'admin'
    AND organization_id = public.compliance_tasks.organization_id
  )
);

-- Create policies for compliance_attachments
CREATE POLICY "Organization admins can manage compliance attachments"
ON public.compliance_attachments
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.compliance_tasks
    JOIN public.profiles ON public.profiles.organization_id = public.compliance_tasks.organization_id
    WHERE public.profiles.id = auth.uid()
    AND public.profiles.role = 'admin'
    AND public.compliance_attachments.compliance_task_id = public.compliance_tasks.id
  )
);

-- Create policies for compliance_completions
CREATE POLICY "Organization admins can manage compliance completions"
ON public.compliance_completions
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.compliance_tasks
    JOIN public.profiles ON public.profiles.organization_id = public.compliance_tasks.organization_id
    WHERE public.profiles.id = auth.uid()
    AND public.profiles.role = 'admin'
    AND public.compliance_completions.compliance_task_id = public.compliance_tasks.id
  )
);
