-- Create a function to execute arbitrary SQL (for admin use only)
CREATE OR REPLACE FUNCTION exec_sql(sql TEXT)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
BEGIN
  -- Only allow superusers or service_role to execute this function
  IF NOT (SELECT usesuper FROM pg_user WHERE usename = current_user) 
     AND current_setting('request.jwt.claims', true)::jsonb->>'role' != 'service_role' THEN
    RAISE EXCEPTION 'Permission denied: only superusers or service_role can execute arbitrary SQL';
  END IF;

  -- Execute the SQL and capture the result
  EXECUTE 'SELECT to_jsonb(t) FROM (' || sql || ') t' INTO result;
  RETURN result;
EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object('error', SQLERRM, 'detail', SQLSTATE);
END;
$$;
