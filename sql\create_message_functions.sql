-- Create a function to retrieve task messages with sender information
CREATE OR <PERSON><PERSON>LACE FUNCTION get_task_messages_with_sender(task_id_param UUID)
RETURNS TABLE (
  id UUID,
  task_id UUID,
  sender_id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  first_name TEXT,
  last_name TEXT
)
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    tm.id,
    tm.task_id,
    tm.sender_id,
    tm.content,
    tm.created_at,
    p.first_name,
    p.last_name
  FROM 
    task_messages tm
  LEFT JOIN 
    profiles p ON tm.sender_id = p.id
  WHERE 
    tm.task_id = task_id_param
  ORDER BY 
    tm.created_at ASC;
END;
$$ LANGUAGE plpgsql;
