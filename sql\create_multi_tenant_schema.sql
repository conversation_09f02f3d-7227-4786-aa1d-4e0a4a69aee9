-- Create a new table for organizations (schools)
CREATE TABLE IF NOT EXISTS public.organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  address TEXT,
  city TEXT,
  state TEXT,
  zip TEXT,
  phone TEXT,
  website TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add organization_id to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES public.organizations(id);
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS role TEXT;

-- Create a new table for user invitations
CREATE TABLE IF NOT EXISTS public.user_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL,
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  role TEXT NOT NULL,
  invited_by UUID NOT NULL REFERENCES auth.users(id),
  status TEXT NOT NULL DEFAULT 'pending',
  token TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  expires_at TIMESTAMPTZ NOT NULL DEFAULT (now() + interval '7 days'),
  UNIQUE(email, organization_id)
);

-- Enable RLS on new tables
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;

-- RLS policies for organizations
CREATE POLICY "Organization members can view their organization"
ON public.organizations
FOR SELECT
USING (
  id IN (
    SELECT organization_id FROM public.profiles WHERE id = auth.uid()
  )
);

CREATE POLICY "Only admins can update their organization"
ON public.organizations
FOR UPDATE
USING (
  id IN (
    SELECT organization_id FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- RLS policies for user_invitations
CREATE POLICY "Organization admins can view invitations"
ON public.user_invitations
FOR SELECT
USING (
  organization_id IN (
    SELECT organization_id FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

CREATE POLICY "Organization admins can create invitations"
ON public.user_invitations
FOR INSERT
WITH CHECK (
  organization_id IN (
    SELECT organization_id FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) AND
  invited_by = auth.uid()
);

CREATE POLICY "Organization admins can update invitations"
ON public.user_invitations
FOR UPDATE
USING (
  organization_id IN (
    SELECT organization_id FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Update RLS policies for tasks
CREATE POLICY "Organization members can view their organization's tasks"
ON public.tasks
FOR SELECT
USING (
  user_id IN (
    SELECT p.id FROM public.profiles p
    WHERE p.organization_id = (
      SELECT organization_id FROM public.profiles WHERE id = auth.uid()
    )
  )
);

-- Function to create an invitation and send email
CREATE OR REPLACE FUNCTION invite_user(
  email_param TEXT,
  organization_id_param UUID,
  role_param TEXT
)
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
  invitation_id UUID;
  token TEXT;
BEGIN
  -- Generate a random token
  token := encode(gen_random_bytes(32), 'hex');
  
  -- Create the invitation
  INSERT INTO public.user_invitations (
    email, 
    organization_id, 
    role, 
    invited_by, 
    token
  )
  VALUES (
    email_param,
    organization_id_param,
    role_param,
    auth.uid(),
    token
  )
  RETURNING id INTO invitation_id;
  
  -- In a real implementation, you would send an email here
  -- For now, we'll just return the invitation ID
  
  RETURN invitation_id;
END;
$$ LANGUAGE plpgsql;

-- Function to accept an invitation
CREATE OR REPLACE FUNCTION accept_invitation(
  token_param TEXT,
  user_id_param UUID
)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
  invitation RECORD;
BEGIN
  -- Find the invitation
  SELECT * INTO invitation
  FROM public.user_invitations
  WHERE token = token_param
  AND status = 'pending'
  AND expires_at > now();
  
  -- If invitation not found or expired
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Update the user's profile
  UPDATE public.profiles
  SET 
    organization_id = invitation.organization_id,
    role = invitation.role
  WHERE id = user_id_param;
  
  -- Mark invitation as accepted
  UPDATE public.user_invitations
  SET status = 'accepted'
  WHERE id = invitation.id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to get all users in an organization with their roles
CREATE OR REPLACE FUNCTION get_organization_users(org_id UUID)
RETURNS TABLE (
  user_id UUID,
  email TEXT,
  first_name TEXT,
  last_name TEXT,
  role TEXT,
  created_at TIMESTAMPTZ
)
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id as user_id,
    u.email,
    p.first_name,
    p.last_name,
    p.role,
    p.created_at
  FROM 
    profiles p
  JOIN
    auth.users u ON p.id = u.id
  WHERE 
    p.organization_id = org_id
  ORDER BY 
    p.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get all pending invitations for an organization
CREATE OR REPLACE FUNCTION get_organization_invitations(org_id UUID)
RETURNS TABLE (
  id UUID,
  email TEXT,
  role TEXT,
  status TEXT,
  created_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ
)
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    i.id,
    i.email,
    i.role,
    i.status,
    i.created_at,
    i.expires_at
  FROM 
    user_invitations i
  WHERE 
    i.organization_id = org_id
  ORDER BY 
    i.created_at DESC;
END;
$$ LANGUAGE plpgsql;
