-- SQL script to create triggers for automatic notifications

-- First, create a function that will be called by the message trigger
CREATE OR REPLACE FUNCTION create_message_notification()
RETURNS TRIGGER AS $$
DECLARE
  task_owner_id UUID;
  task_title TEXT;
  sender_name TEXT;
BEGIN
  -- Get task owner and title
  SELECT user_id, title INTO task_owner_id, task_title
  FROM public.tasks
  WHERE id = NEW.task_id;
  
  -- Only create notification if sender is not the task owner
  IF NEW.sender_id <> task_owner_id THEN
    -- Get sender name
    SELECT 
      CASE 
        WHEN first_name IS NOT NULL AND last_name IS NOT NULL THEN
          first_name || ' ' || last_name
        ELSE
          'A user'
      END INTO sender_name
    FROM public.profiles
    WHERE id = NEW.sender_id;
    
    -- Create notification
    INSERT INTO public.notifications (
      user_id,
      type,
      message,
      related_id,
      related_type,
      read,
      email_sent
    ) VALUES (
      task_owner_id,
      'message',
      'You have received a new message from ' || sender_name || ' regarding "' || task_title || '".',
      NEW.task_id::text,
      'message',
      false,
      false
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the message trigger
DROP TRIGGER IF EXISTS task_message_notification_trigger ON public.task_messages;
CREATE TRIGGER task_message_notification_trigger
AFTER INSERT ON public.task_messages
FOR EACH ROW
EXECUTE FUNCTION create_message_notification();

-- Create a function that will be called by the offer trigger
CREATE OR REPLACE FUNCTION create_offer_notification()
RETURNS TRIGGER AS $$
DECLARE
  task_owner_id UUID;
  task_title TEXT;
  provider_name TEXT;
BEGIN
  -- Get task owner and title
  SELECT user_id, title INTO task_owner_id, task_title
  FROM public.tasks
  WHERE id = NEW.task_id;
  
  -- Get provider name
  SELECT 
    CASE 
      WHEN first_name IS NOT NULL AND last_name IS NOT NULL THEN
        first_name || ' ' || last_name
      ELSE
        'A supplier'
    END INTO provider_name
  FROM public.profiles
  WHERE id = NEW.user_id;
  
  -- Create notification
  INSERT INTO public.notifications (
    user_id,
    type,
    message,
    related_id,
    related_type,
    read,
    email_sent
  ) VALUES (
    task_owner_id,
    'offer',
    provider_name || ' has made an offer of £' || NEW.amount::text || ' on your task "' || task_title || '".',
    NEW.task_id::text,
    'offer',
    false,
    false
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the offer trigger
DROP TRIGGER IF EXISTS offer_notification_trigger ON public.offers;
CREATE TRIGGER offer_notification_trigger
AFTER INSERT ON public.offers
FOR EACH ROW
EXECUTE FUNCTION create_offer_notification();
