-- Create a trigger to automatically create profiles for new users
CREATE OR REPLACE FUNCTION public.create_profile_for_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if a profile already exists for this user
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = NEW.id) THEN
    -- Create a new profile
    INSERT INTO public.profiles (
      id,
      email,
      account_type,
      first_name,
      last_name,
      created_at,
      updated_at
    ) VALUES (
      NEW.id,
      ARRAY[NEW.email],
      COALESCE(NEW.raw_user_meta_data->>'account_type', 'school'),
      COALESCE(split_part(COALESCE(NEW.raw_user_meta_data->>'name', ''), ' ', 1), ''),
      COALESCE(substring(COALESCE(NEW.raw_user_meta_data->>'name', '') from position(' ' in COALESCE(NEW.raw_user_meta_data->>'name', '')) + 1), ''),
      NEW.created_at,
      NEW.created_at
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS create_profile_trigger ON auth.users;

-- Create the trigger
CREATE TRIGGER create_profile_trigger
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.create_profile_for_new_user();
