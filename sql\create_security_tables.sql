-- Create security events table
CREATE TABLE IF NOT EXISTS public.security_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    type TEXT NOT NULL CHECK (type IN ('auth_failure', 'permission_denied', 'suspicious_activity', 'data_access', 'admin_action')),
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    user_email TEXT,
    action TEXT NOT NULL,
    resource TEXT,
    details JSONB,
    ip_address TEXT,
    user_agent TEXT,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create security incidents table for tracking critical events
CREATE TABLE IF NOT EXISTS public.security_incidents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event_type TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    description TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    user_email TEXT,
    ip_address TEXT,
    status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'false_positive')),
    assigned_to UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    resolution_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    resolved_at TIMESTAMPTZ
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_security_events_type ON public.security_events(type);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON public.security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON public.security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_timestamp ON public.security_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_security_events_user_email ON public.security_events(user_email);

CREATE INDEX IF NOT EXISTS idx_security_incidents_status ON public.security_incidents(status);
CREATE INDEX IF NOT EXISTS idx_security_incidents_severity ON public.security_incidents(severity);
CREATE INDEX IF NOT EXISTS idx_security_incidents_created_at ON public.security_incidents(created_at DESC);

-- Enable Row Level Security
ALTER TABLE public.security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_incidents ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for security events (only site admins can access)
CREATE POLICY "Site admins can view all security events" ON public.security_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_site_admin = true
        )
    );

CREATE POLICY "Site admins can insert security events" ON public.security_events
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_site_admin = true
        )
    );

-- Create RLS policies for security incidents (only site admins can access)
CREATE POLICY "Site admins can view all security incidents" ON public.security_incidents
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_site_admin = true
        )
    );

CREATE POLICY "Site admins can manage security incidents" ON public.security_incidents
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_site_admin = true
        )
    );

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_security_events_updated_at 
    BEFORE UPDATE ON public.security_events 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_security_incidents_updated_at 
    BEFORE UPDATE ON public.security_incidents 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to get security statistics
CREATE OR REPLACE FUNCTION get_security_statistics()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_events', (SELECT COUNT(*) FROM public.security_events),
        'critical_events', (SELECT COUNT(*) FROM public.security_events WHERE severity = 'critical'),
        'high_events', (SELECT COUNT(*) FROM public.security_events WHERE severity = 'high'),
        'medium_events', (SELECT COUNT(*) FROM public.security_events WHERE severity = 'medium'),
        'low_events', (SELECT COUNT(*) FROM public.security_events WHERE severity = 'low'),
        'events_last_24h', (
            SELECT COUNT(*) FROM public.security_events 
            WHERE timestamp > NOW() - INTERVAL '24 hours'
        ),
        'events_last_7d', (
            SELECT COUNT(*) FROM public.security_events 
            WHERE timestamp > NOW() - INTERVAL '7 days'
        ),
        'open_incidents', (SELECT COUNT(*) FROM public.security_incidents WHERE status = 'open'),
        'total_incidents', (SELECT COUNT(*) FROM public.security_incidents),
        'auth_failures_today', (
            SELECT COUNT(*) FROM public.security_events 
            WHERE type = 'auth_failure' 
            AND timestamp > CURRENT_DATE
        ),
        'suspicious_activities_today', (
            SELECT COUNT(*) FROM public.security_events 
            WHERE type = 'suspicious_activity' 
            AND timestamp > CURRENT_DATE
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get recent security events
CREATE OR REPLACE FUNCTION get_recent_security_events(limit_count INTEGER DEFAULT 50)
RETURNS TABLE (
    id UUID,
    type TEXT,
    severity TEXT,
    user_email TEXT,
    action TEXT,
    resource TEXT,
    details JSONB,
    ip_address TEXT,
    timestamp TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        se.id,
        se.type,
        se.severity,
        se.user_email,
        se.action,
        se.resource,
        se.details,
        se.ip_address,
        se.timestamp
    FROM public.security_events se
    ORDER BY se.timestamp DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT SELECT, INSERT ON public.security_events TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.security_incidents TO authenticated;
GRANT EXECUTE ON FUNCTION get_security_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION get_recent_security_events(INTEGER) TO authenticated;

-- Create a view for security dashboard
CREATE OR REPLACE VIEW public.security_dashboard_view AS
SELECT 
    se.id,
    se.type,
    se.severity,
    se.user_email,
    se.action,
    se.resource,
    se.ip_address,
    se.timestamp,
    CASE 
        WHEN se.severity = 'critical' THEN 1
        WHEN se.severity = 'high' THEN 2
        WHEN se.severity = 'medium' THEN 3
        WHEN se.severity = 'low' THEN 4
        ELSE 5
    END as severity_order
FROM public.security_events se
ORDER BY se.timestamp DESC;

-- Grant access to the view
GRANT SELECT ON public.security_dashboard_view TO authenticated;

-- Add RLS policy for the view
CREATE POLICY "Site admins can view security dashboard" ON public.security_dashboard_view
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_site_admin = true
        )
    );

-- Enable RLS on the view
ALTER VIEW public.security_dashboard_view SET (security_barrier = true);

COMMENT ON TABLE public.security_events IS 'Stores all security events for monitoring and analysis';
COMMENT ON TABLE public.security_incidents IS 'Tracks critical security incidents that require investigation';
COMMENT ON FUNCTION get_security_statistics() IS 'Returns comprehensive security statistics for the dashboard';
COMMENT ON FUNCTION get_recent_security_events(INTEGER) IS 'Returns recent security events with optional limit';
COMMENT ON VIEW public.security_dashboard_view IS 'Optimized view for security dashboard with severity ordering';
