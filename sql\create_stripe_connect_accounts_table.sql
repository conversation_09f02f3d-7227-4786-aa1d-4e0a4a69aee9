-- Create the stripe_connect_accounts table
CREATE TABLE IF NOT EXISTS stripe_connect_accounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES profiles(id),
  stripe_account_id TEXT NOT NULL,
  account_status TEXT NOT NULL,
  charges_enabled BOOLEAN DEFAULT FALSE,
  payouts_enabled BOOLEAN DEFAULT FALSE,
  details_submitted BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id),
  UNIQUE(stripe_account_id)
);

-- Create an index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS stripe_connect_accounts_user_id_idx ON stripe_connect_accounts(user_id);

-- Create an index on stripe_account_id for faster lookups
CREATE INDEX IF NOT EXISTS stripe_connect_accounts_stripe_account_id_idx ON stripe_connect_accounts(stripe_account_id);

-- Add RLS policies
ALTER TABLE stripe_connect_accounts ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to view their own stripe connect accounts
CREATE POLICY stripe_connect_accounts_select_policy ON stripe_connect_accounts
  FOR SELECT
  USING (auth.uid() = user_id);

-- Allow authenticated users to insert their own stripe connect accounts
CREATE POLICY stripe_connect_accounts_insert_policy ON stripe_connect_accounts
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Allow authenticated users to update their own stripe connect accounts
CREATE POLICY stripe_connect_accounts_update_policy ON stripe_connect_accounts
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Allow service role to access all stripe connect accounts
CREATE POLICY stripe_connect_accounts_service_policy ON stripe_connect_accounts
  FOR ALL
  USING (auth.role() = 'service_role');

-- Add a comment to the table
COMMENT ON TABLE stripe_connect_accounts IS 'Stores the mapping between users and their Stripe Connect account IDs';
