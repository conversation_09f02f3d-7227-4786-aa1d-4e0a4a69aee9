-- Create the stripe_customers table
CREATE TABLE IF NOT EXISTS stripe_customers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES profiles(id),
  stripe_customer_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id),
  UNIQUE(stripe_customer_id)
);

-- Create an index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS stripe_customers_user_id_idx ON stripe_customers(user_id);

-- Create an index on stripe_customer_id for faster lookups
CREATE INDEX IF NOT EXISTS stripe_customers_stripe_customer_id_idx ON stripe_customers(stripe_customer_id);

-- Add RLS policies
ALTER TABLE stripe_customers ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to view their own stripe customers
CREATE POLICY stripe_customers_select_policy ON stripe_customers
  FOR SELECT
  USING (auth.uid() = user_id);

-- Allow authenticated users to insert their own stripe customers
CREATE POLICY stripe_customers_insert_policy ON stripe_customers
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Allow authenticated users to update their own stripe customers
CREATE POLICY stripe_customers_update_policy ON stripe_customers
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Allow service role to access all stripe customers
CREATE POLICY stripe_customers_service_policy ON stripe_customers
  FOR ALL
  USING (auth.role() = 'service_role');

-- Add a comment to the table
COMMENT ON TABLE stripe_customers IS 'Stores the mapping between users and their Stripe customer IDs';
