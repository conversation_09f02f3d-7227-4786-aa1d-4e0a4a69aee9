-- Create the stripe_customers table
CREATE TABLE IF NOT EXISTS public.stripe_customers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  customer_id TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(user_id),
  UNIQUE(customer_id)
);

-- Enable RLS
ALTER TABLE public.stripe_customers ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY stripe_customers_select_policy ON public.stripe_customers
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY stripe_customers_insert_policy ON public.stripe_customers
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY stripe_customers_update_policy ON public.stripe_customers
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY stripe_customers_delete_policy ON public.stripe_customers
  FOR DELETE
  USING (auth.uid() = user_id);

CREATE POLICY stripe_customers_service_policy ON public.stripe_customers
  FOR ALL
  USING (auth.role() = 'service_role');

-- Create trigger for updating timestamps
CREATE TRIGGER update_stripe_customers_updated_at
BEFORE UPDATE ON public.stripe_customers
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Add comment
COMMENT ON TABLE public.stripe_customers IS 'Stores the mapping between users and their Stripe customer IDs';
