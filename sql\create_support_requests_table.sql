-- <PERSON><PERSON> script to create the support_requests table for tracking support requests

-- Check if the support_requests table exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.tables
    WHERE table_name = 'support_requests'
    AND table_schema = 'public'
  ) THEN
    -- Create the support_requests table
    CREATE TABLE public.support_requests (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name TEXT NOT NULL,
      email TEXT NOT NULL,
      organization TEXT,
      organization_id UUID REFERENCES public.organizations(id),
      support_type TEXT NOT NULL,
      message TEXT NOT NULL,
      status TEXT NOT NULL DEFAULT 'new',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
      resolved_at TIMESTAMP WITH TIME ZONE,
      resolved_by UUID REFERENCES auth.users(id),
      notes TEXT
    );

    -- Add comments to the table and columns
    COMMENT ON TABLE public.support_requests IS 'Table to store support requests from users';
    COMMENT ON COLUMN public.support_requests.id IS 'Unique identifier for the support request';
    COMMENT ON COLUMN public.support_requests.name IS 'Name of the person submitting the request';
    COMMENT ON COLUMN public.support_requests.email IS 'Email of the person submitting the request';
    COMMENT ON COLUMN public.support_requests.organization IS 'Name of the organization';
    COMMENT ON COLUMN public.support_requests.organization_id IS 'Reference to the organization ID if available';
    COMMENT ON COLUMN public.support_requests.support_type IS 'Type of support request (account, billing, technical, etc.)';
    COMMENT ON COLUMN public.support_requests.message IS 'Content of the support request';
    COMMENT ON COLUMN public.support_requests.status IS 'Current status of the request (new, in-progress, resolved, etc.)';
    COMMENT ON COLUMN public.support_requests.created_at IS 'Timestamp when the request was created';
    COMMENT ON COLUMN public.support_requests.updated_at IS 'Timestamp when the request was last updated';
    COMMENT ON COLUMN public.support_requests.resolved_at IS 'Timestamp when the request was resolved';
    COMMENT ON COLUMN public.support_requests.resolved_by IS 'User ID of the staff member who resolved the request';
    COMMENT ON COLUMN public.support_requests.notes IS 'Internal notes about the support request';

    -- Create indexes for better performance
    CREATE INDEX support_requests_email_idx ON public.support_requests(email);
    CREATE INDEX support_requests_organization_id_idx ON public.support_requests(organization_id);
    CREATE INDEX support_requests_status_idx ON public.support_requests(status);
    CREATE INDEX support_requests_created_at_idx ON public.support_requests(created_at);

    -- Set up RLS policies
    ALTER TABLE public.support_requests ENABLE ROW LEVEL SECURITY;

    -- Only site admins can view all support requests
    CREATE POLICY "Site admins can view all support requests"
    ON public.support_requests
    FOR SELECT
    TO authenticated
    USING (
      EXISTS (
        SELECT 1
        FROM profiles
        WHERE id = auth.uid() AND is_site_admin = true
      )
    );

    -- Users can view their own support requests
    CREATE POLICY "Users can view their own support requests"
    ON public.support_requests
    FOR SELECT
    TO authenticated
    USING (
      email = (
        SELECT email
        FROM auth.users
        WHERE id = auth.uid()
      )
    );

    -- Only site admins can update support requests
    CREATE POLICY "Site admins can update support requests"
    ON public.support_requests
    FOR UPDATE
    TO authenticated
    USING (
      EXISTS (
        SELECT 1
        FROM profiles
        WHERE id = auth.uid() AND is_site_admin = true
      )
    );

    -- Users can create support requests
    CREATE POLICY "Users can create support requests"
    ON public.support_requests
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

    -- Set up triggers for updated_at
    CREATE OR REPLACE FUNCTION public.handle_updated_at()
    RETURNS TRIGGER AS $$
    BEGIN
      NEW.updated_at = now();
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    CREATE TRIGGER set_updated_at
    BEFORE UPDATE ON public.support_requests
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

    RAISE NOTICE 'Created support_requests table with RLS policies and triggers';
  ELSE
    RAISE NOTICE 'support_requests table already exists';
  END IF;
END $$;

-- Verify the table was created
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'support_requests'
ORDER BY 
    ordinal_position;

-- Verify RLS policies
SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM
    pg_policies
WHERE
    tablename = 'support_requests'
ORDER BY
    policyname;
