-- SQL script to create a trigger for task assignment notifications

-- Create a function that will be called by the task assignment trigger
CREATE OR REPLACE FUNCTION create_task_assignment_notification()
RETURNS TRIGGER AS $$
DECLARE
  task_title TEXT;
  assigner_name TEXT;
BEGIN
  -- Only proceed if assigned_to has changed and is not null
  IF (OLD.assigned_to IS DISTINCT FROM NEW.assigned_to) AND (NEW.assigned_to IS NOT NULL) THEN
    -- Get task title
    SELECT title INTO task_title
    FROM public.tasks
    WHERE id = NEW.id;
    
    -- Get assigner name (assuming the last updater is the assigner)
    -- This is a simplification; in reality, we don't know who made the assignment
    -- So we'll use a generic message
    
    -- Create notification for the assigned user
    INSERT INTO public.notifications (
      user_id,
      type,
      message,
      related_id,
      related_type,
      read,
      email_sent
    ) VALUES (
      NEW.assigned_to,
      'task_update',
      'You have been assigned to the task "' || task_title || '".',
      NEW.id,
      'task',
      false,
      false
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the task assignment trigger
DROP TRIGGER IF EXISTS task_assignment_notification_trigger ON public.tasks;
CREATE TRIGGER task_assignment_notification_trigger
AFTER UPDATE ON public.tasks
FOR EACH ROW
EXECUTE FUNCTION create_task_assignment_notification();
