-- Create the task_comments table
CREATE TABLE IF NOT EXISTS public.task_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- <PERSON>reate indexes
CREATE INDEX IF NOT EXISTS task_comments_task_id_idx ON public.task_comments(task_id);
CREATE INDEX IF NOT EXISTS task_comments_user_id_idx ON public.task_comments(user_id);

-- Enable RLS
ALTER TABLE public.task_comments ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Allow authenticated users to view comments on tasks they can see
CREATE POLICY task_comments_select_policy ON public.task_comments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.tasks t
      WHERE t.id = task_id
      AND (
        -- Task creator can see comments
        t.user_id = auth.uid()
        -- Task assignee can see comments
        OR t.assigned_to = auth.uid()
        -- Users in the same organization can see comments on internal tasks
        OR (
          t.visibility = 'internal'
          AND EXISTS (
            SELECT 1 FROM public.profiles p1, public.profiles p2
            WHERE p1.id = auth.uid()
            AND p2.id = t.user_id
            AND p1.organization_id = p2.organization_id
          )
        )
        -- Anyone can see comments on public tasks
        OR t.is_public = true
      )
    )
  );

-- Allow authenticated users to insert comments on tasks they can see
CREATE POLICY task_comments_insert_policy ON public.task_comments
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id
    AND EXISTS (
      SELECT 1 FROM public.tasks t
      WHERE t.id = task_id
      AND (
        -- Task creator can add comments
        t.user_id = auth.uid()
        -- Task assignee can add comments
        OR t.assigned_to = auth.uid()
        -- Users in the same organization can add comments on internal tasks
        OR (
          t.visibility = 'internal'
          AND EXISTS (
            SELECT 1 FROM public.profiles p1, public.profiles p2
            WHERE p1.id = auth.uid()
            AND p2.id = t.user_id
            AND p1.organization_id = p2.organization_id
          )
        )
      )
    )
  );

-- Allow authenticated users to update their own comments
CREATE POLICY task_comments_update_policy ON public.task_comments
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Allow authenticated users to delete their own comments
CREATE POLICY task_comments_delete_policy ON public.task_comments
  FOR DELETE
  USING (auth.uid() = user_id);

-- Allow service role to access all comments
CREATE POLICY task_comments_service_policy ON public.task_comments
  FOR ALL
  USING (auth.role() = 'service_role');

-- Create trigger for updating timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_task_comments_updated_at
BEFORE UPDATE ON public.task_comments
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Add comment
COMMENT ON TABLE public.task_comments IS 'Stores comments on tasks';
