-- Create the task_messages table
CREATE TABLE IF NOT EXISTS public.task_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  message TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  read_at TIMESTAMPTZ
);

-- Create indexes
CREATE INDEX IF NOT EXISTS task_messages_task_id_idx ON public.task_messages(task_id);
CREATE INDEX IF NOT EXISTS task_messages_sender_id_idx ON public.task_messages(sender_id);

-- Enable RLS
ALTER TABLE public.task_messages ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Allow authenticated users to view messages on tasks they can see
CREATE POLICY task_messages_select_policy ON public.task_messages
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.tasks t
      WHERE t.id = task_id
      AND (
        -- Task creator can see messages
        t.user_id = auth.uid()
        -- Task assignee can see messages
        OR t.assigned_to = auth.uid()
        -- Users in the same organization can see messages on internal tasks
        OR (
          t.visibility = 'internal'
          AND EXISTS (
            SELECT 1 FROM public.profiles p1, public.profiles p2
            WHERE p1.id = auth.uid()
            AND p2.id = t.user_id
            AND p1.organization_id = p2.organization_id
          )
        )
      )
    )
  );

-- Allow authenticated users to insert messages on tasks they can see
CREATE POLICY task_messages_insert_policy ON public.task_messages
  FOR INSERT
  WITH CHECK (
    auth.uid() = sender_id
    AND EXISTS (
      SELECT 1 FROM public.tasks t
      WHERE t.id = task_id
      AND (
        -- Task creator can add messages
        t.user_id = auth.uid()
        -- Task assignee can add messages
        OR t.assigned_to = auth.uid()
        -- Users in the same organization can add messages on internal tasks
        OR (
          t.visibility = 'internal'
          AND EXISTS (
            SELECT 1 FROM public.profiles p1, public.profiles p2
            WHERE p1.id = auth.uid()
            AND p2.id = t.user_id
            AND p1.organization_id = p2.organization_id
          )
        )
      )
    )
  );

-- Allow authenticated users to update read_at for messages they can see
CREATE POLICY task_messages_update_policy ON public.task_messages
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.tasks t
      WHERE t.id = task_id
      AND (
        -- Task creator can update read_at
        t.user_id = auth.uid()
        -- Task assignee can update read_at
        OR t.assigned_to = auth.uid()
      )
    )
  );

-- Allow service role to access all messages
CREATE POLICY task_messages_service_policy ON public.task_messages
  FOR ALL
  USING (auth.role() = 'service_role');

-- Add comment
COMMENT ON TABLE public.task_messages IS 'Stores messages related to tasks';
