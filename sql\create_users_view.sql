-- Create a view to expose user data safely
CREATE OR REPLACE VIEW public.users AS
SELECT
  id,
  email,
  created_at,
  last_sign_in_at,
  raw_user_meta_data
FROM
  auth.users;

-- Grant access to the authenticated users
ALTER VIEW public.users OWNER TO authenticated;
GRANT SELECT ON public.users TO authenticated;

-- Create a policy to allow users to see only their own data unless they are admin
CREATE POLICY "Users can see their own data" ON public.users
  FOR SELECT
  USING (
    auth.uid() = id OR 
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    )
  );

-- Enable RLS on the view
ALTER VIEW public.users ENABLE ROW LEVEL SECURITY;
