-- SQL script to diagnose issues with message sending

-- Check if the task_messages table has the correct structure
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'task_messages'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check existing triggers on the task_messages table
SELECT trigger_name, event_manipulation, action_statement
FROM information_schema.triggers
WHERE event_object_table = 'task_messages'
AND event_object_schema = 'public';

-- Check the definition of the create_message_notification function
SELECT pg_get_functiondef('public.create_message_notification'::regproc);

-- Check recent messages to see if they're being created properly
SELECT tm.id, tm.task_id, tm.sender_id, tm.content, tm.created_at,
       t.title as task_title, t.user_id as task_owner_id,
       p.first_name || ' ' || p.last_name as sender_name
FROM public.task_messages tm
JOIN public.tasks t ON tm.task_id = t.id
JOIN public.profiles p ON tm.sender_id = p.id
ORDER BY tm.created_at DESC
LIMIT 10;

-- Check if there are any errors in the PostgreSQL logs related to the trigger
-- (This can only be checked by a database administrator with access to the logs)

-- Test if a direct insert works (for diagnostic purposes only)
-- DO $$
-- DECLARE
--   test_task_id UUID;
--   test_sender_id UUID;
-- BEGIN
--   -- Get a test task ID
--   SELECT id INTO test_task_id FROM public.tasks LIMIT 1;
--   
--   -- Get a test sender ID (admin user)
--   SELECT id INTO test_sender_id FROM public.profiles WHERE role = 'admin' LIMIT 1;
--   
--   -- Try to insert a test message
--   INSERT INTO public.task_messages (task_id, sender_id, content)
--   VALUES (test_task_id, test_sender_id, 'This is a test message from the diagnostic script');
--   
--   RAISE NOTICE 'Test message inserted successfully';
-- EXCEPTION
--   WHEN OTHERS THEN
--     RAISE NOTICE 'Error inserting test message: %', SQLERRM;
-- END;
-- $$;
