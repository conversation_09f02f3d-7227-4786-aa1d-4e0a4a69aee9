-- End-to-End Role-Based Access Control Test Script (Fixed)
-- This script tests the entire role-based access control system
-- Run this in the Supabase SQL Editor

-- Step 1: Verify the is_site_admin function
DO $$
DECLARE
  result BOOLEAN;
BEGIN
  -- Test the is_site_admin function
  -- Note: This will return false because we're not authenticated as a site admin
  result := is_site_admin();
  RAISE NOTICE 'is_site_admin() = %', result;
END $$;

-- Step 2: Verify the has_role function
DO $$
DECLARE
  result BOOLEAN;
BEGIN
  -- Test the has_role function
  -- Note: This will return false because we're not authenticated as any role
  result := has_role('admin');
  RAISE NOTICE 'has_role(''admin'') = %', result;
END $$;

-- Step 3: Create test users for each role if they don't exist
DO $$
DECLARE
  org_id UUID;
  site_admin_id UUID := gen_random_uuid();
  org_admin_id UUID := gen_random_uuid();
  teacher_id UUID := gen_random_uuid();
  maintenance_id UUID := gen_random_uuid();
  support_id UUID := gen_random_uuid();
  supplier_id UUID := gen_random_uuid();
BEGIN
  -- Get an organization ID
  SELECT id INTO org_id
  FROM organizations
  LIMIT 1;
  
  IF org_id IS NULL THEN
    -- Create a test organization if none exists
    INSERT INTO organizations (name, type, address, city, postal_code, country)
    VALUES ('Test Organization', 'school', '123 Test St', 'Test City', 'TE1 1ST', 'United Kingdom')
    RETURNING id INTO org_id;
    
    RAISE NOTICE 'Created test organization with ID: %', org_id;
  END IF;
  
  -- Create test users if they don't exist
  -- Site Admin
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    -- Insert into auth.users with explicit ID and account_type in metadata
    INSERT INTO auth.users (id, email, email_confirmed_at, raw_user_meta_data)
    VALUES (
      site_admin_id, 
      '<EMAIL>', 
      now(), 
      '{"name": "Site Admin", "account_type": "school"}'
    );
    
    -- Update the profile with additional fields
    UPDATE profiles
    SET 
      email = ARRAY['<EMAIL>'],
      first_name = 'Site',
      last_name = 'Admin',
      role = 'admin',
      is_site_admin = true,
      organization_id = org_id
    WHERE id = site_admin_id;
    
    RAISE NOTICE 'Created site admin test user with ID: %', site_admin_id;
  ELSE
    -- Get existing user ID
    SELECT id INTO site_admin_id FROM auth.users WHERE email = '<EMAIL>';
    
    -- Update the profile to ensure it has the correct values
    UPDATE profiles
    SET 
      role = 'admin',
      is_site_admin = true,
      organization_id = org_id
    WHERE id = site_admin_id;
    
    RAISE NOTICE 'Using existing site admin user with ID: %', site_admin_id;
  END IF;
  
  -- Organization Admin
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    -- Insert into auth.users with explicit ID and account_type in metadata
    INSERT INTO auth.users (id, email, email_confirmed_at, raw_user_meta_data)
    VALUES (
      org_admin_id, 
      '<EMAIL>', 
      now(), 
      '{"name": "Org Admin", "account_type": "school"}'
    );
    
    -- Update the profile with additional fields
    UPDATE profiles
    SET 
      email = ARRAY['<EMAIL>'],
      first_name = 'Org',
      last_name = 'Admin',
      role = 'admin',
      is_site_admin = false,
      organization_id = org_id
    WHERE id = org_admin_id;
    
    RAISE NOTICE 'Created organization admin test user with ID: %', org_admin_id;
  ELSE
    -- Get existing user ID
    SELECT id INTO org_admin_id FROM auth.users WHERE email = '<EMAIL>';
    
    -- Update the profile to ensure it has the correct values
    UPDATE profiles
    SET 
      role = 'admin',
      is_site_admin = false,
      organization_id = org_id
    WHERE id = org_admin_id;
    
    RAISE NOTICE 'Using existing org admin user with ID: %', org_admin_id;
  END IF;
  
  -- Teacher
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    -- Insert into auth.users with explicit ID and account_type in metadata
    INSERT INTO auth.users (id, email, email_confirmed_at, raw_user_meta_data)
    VALUES (
      teacher_id, 
      '<EMAIL>', 
      now(), 
      '{"name": "Teacher User", "account_type": "school"}'
    );
    
    -- Update the profile with additional fields
    UPDATE profiles
    SET 
      email = ARRAY['<EMAIL>'],
      first_name = 'Teacher',
      last_name = 'User',
      role = 'teacher',
      is_site_admin = false,
      organization_id = org_id
    WHERE id = teacher_id;
    
    RAISE NOTICE 'Created teacher test user with ID: %', teacher_id;
  ELSE
    -- Get existing user ID
    SELECT id INTO teacher_id FROM auth.users WHERE email = '<EMAIL>';
    
    -- Update the profile to ensure it has the correct values
    UPDATE profiles
    SET 
      role = 'teacher',
      is_site_admin = false,
      organization_id = org_id
    WHERE id = teacher_id;
    
    RAISE NOTICE 'Using existing teacher user with ID: %', teacher_id;
  END IF;
  
  -- Maintenance
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    -- Insert into auth.users with explicit ID and account_type in metadata
    INSERT INTO auth.users (id, email, email_confirmed_at, raw_user_meta_data)
    VALUES (
      maintenance_id, 
      '<EMAIL>', 
      now(), 
      '{"name": "Maintenance User", "account_type": "school"}'
    );
    
    -- Update the profile with additional fields
    UPDATE profiles
    SET 
      email = ARRAY['<EMAIL>'],
      first_name = 'Maintenance',
      last_name = 'User',
      role = 'maintenance',
      is_site_admin = false,
      organization_id = org_id
    WHERE id = maintenance_id;
    
    RAISE NOTICE 'Created maintenance test user with ID: %', maintenance_id;
  ELSE
    -- Get existing user ID
    SELECT id INTO maintenance_id FROM auth.users WHERE email = '<EMAIL>';
    
    -- Update the profile to ensure it has the correct values
    UPDATE profiles
    SET 
      role = 'maintenance',
      is_site_admin = false,
      organization_id = org_id
    WHERE id = maintenance_id;
    
    RAISE NOTICE 'Using existing maintenance user with ID: %', maintenance_id;
  END IF;
  
  -- Support
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    -- Insert into auth.users with explicit ID and account_type in metadata
    INSERT INTO auth.users (id, email, email_confirmed_at, raw_user_meta_data)
    VALUES (
      support_id, 
      '<EMAIL>', 
      now(), 
      '{"name": "Support User", "account_type": "school"}'
    );
    
    -- Update the profile with additional fields
    UPDATE profiles
    SET 
      email = ARRAY['<EMAIL>'],
      first_name = 'Support',
      last_name = 'User',
      role = 'support',
      is_site_admin = false,
      organization_id = org_id
    WHERE id = support_id;
    
    RAISE NOTICE 'Created support test user with ID: %', support_id;
  ELSE
    -- Get existing user ID
    SELECT id INTO support_id FROM auth.users WHERE email = '<EMAIL>';
    
    -- Update the profile to ensure it has the correct values
    UPDATE profiles
    SET 
      role = 'support',
      is_site_admin = false,
      organization_id = org_id
    WHERE id = support_id;
    
    RAISE NOTICE 'Using existing support user with ID: %', support_id;
  END IF;
  
  -- Supplier
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    -- Insert into auth.users with explicit ID and account_type in metadata
    INSERT INTO auth.users (id, email, email_confirmed_at, raw_user_meta_data)
    VALUES (
      supplier_id, 
      '<EMAIL>', 
      now(), 
      '{"name": "Supplier User", "account_type": "supplier"}'
    );
    
    -- Update the profile with additional fields
    UPDATE profiles
    SET 
      email = ARRAY['<EMAIL>'],
      first_name = 'Supplier',
      last_name = 'User',
      role = 'supplier',
      is_site_admin = false
    WHERE id = supplier_id;
    
    RAISE NOTICE 'Created supplier test user with ID: %', supplier_id;
  ELSE
    -- Get existing user ID
    SELECT id INTO supplier_id FROM auth.users WHERE email = '<EMAIL>';
    
    -- Update the profile to ensure it has the correct values
    UPDATE profiles
    SET 
      role = 'supplier',
      is_site_admin = false
    WHERE id = supplier_id;
    
    RAISE NOTICE 'Using existing supplier user with ID: %', supplier_id;
  END IF;
END $$;

-- Step 4: Create test tasks
DO $$
DECLARE
  teacher_id UUID;
  admin_id UUID;
  maintenance_id UUID;
BEGIN
  -- Get user IDs
  SELECT id INTO teacher_id FROM profiles WHERE email = ARRAY['<EMAIL>'] LIMIT 1;
  SELECT id INTO admin_id FROM profiles WHERE email = ARRAY['<EMAIL>'] LIMIT 1;
  SELECT id INTO maintenance_id FROM profiles WHERE email = ARRAY['<EMAIL>'] LIMIT 1;
  
  -- Create test tasks
  -- Teacher task (admin visibility)
  IF NOT EXISTS (SELECT 1 FROM tasks WHERE title = 'RBAC Test: Teacher Task') THEN
    INSERT INTO tasks (
      title, 
      description, 
      status, 
      visibility, 
      user_id,
      location,
      category,
      budget,
      due_date
    )
    VALUES (
      'RBAC Test: Teacher Task',
      'This is a test task created by a teacher for RBAC testing',
      'pending',
      'admin',
      teacher_id,
      'Test Location',
      'Test Category',
      100,
      NOW() + INTERVAL '7 days'
    );
    
    RAISE NOTICE 'Created teacher test task';
  END IF;
  
  -- Admin task (internal visibility)
  IF NOT EXISTS (SELECT 1 FROM tasks WHERE title = 'RBAC Test: Internal Task') THEN
    INSERT INTO tasks (
      title, 
      description, 
      status, 
      visibility, 
      user_id,
      assigned_to,
      location,
      category,
      budget,
      due_date
    )
    VALUES (
      'RBAC Test: Internal Task',
      'This is an internal test task assigned to maintenance staff',
      'assigned',
      'internal',
      admin_id,
      maintenance_id,
      'Test Location',
      'Test Category',
      200,
      NOW() + INTERVAL '14 days'
    );
    
    RAISE NOTICE 'Created internal test task';
  END IF;
  
  -- Public task
  IF NOT EXISTS (SELECT 1 FROM tasks WHERE title = 'RBAC Test: Public Task') THEN
    INSERT INTO tasks (
      title, 
      description, 
      status, 
      visibility, 
      user_id,
      location,
      category,
      budget,
      due_date
    )
    VALUES (
      'RBAC Test: Public Task',
      'This is a public test task visible to suppliers',
      'open',
      'public',
      admin_id,
      'Test Location',
      'Test Category',
      300,
      NOW() + INTERVAL '21 days'
    );
    
    RAISE NOTICE 'Created public test task';
  END IF;
END $$;

-- Step 5: Test task visibility for each role
DO $$
DECLARE
  site_admin_id UUID;
  org_admin_id UUID;
  teacher_id UUID;
  maintenance_id UUID;
  supplier_id UUID;
  
  task_count INT;
  visible_tasks TEXT;
BEGIN
  -- Get user IDs
  SELECT id INTO site_admin_id FROM profiles WHERE email = ARRAY['<EMAIL>'] LIMIT 1;
  SELECT id INTO org_admin_id FROM profiles WHERE email = ARRAY['<EMAIL>'] LIMIT 1;
  SELECT id INTO teacher_id FROM profiles WHERE email = ARRAY['<EMAIL>'] LIMIT 1;
  SELECT id INTO maintenance_id FROM profiles WHERE email = ARRAY['<EMAIL>'] LIMIT 1;
  SELECT id INTO supplier_id FROM profiles WHERE email = ARRAY['<EMAIL>'] LIMIT 1;
  
  -- Test site admin access
  SET LOCAL ROLE authenticated;
  SET LOCAL "request.jwt.claims" TO '{"sub": "' || site_admin_id || '", "role": "authenticated"}';
  
  SELECT COUNT(*) INTO task_count FROM tasks;
  SELECT string_agg(title, ', ') INTO visible_tasks FROM tasks;
  
  RAISE NOTICE 'Site admin can see % tasks: %', task_count, visible_tasks;
  
  -- Test org admin access
  SET LOCAL ROLE authenticated;
  SET LOCAL "request.jwt.claims" TO '{"sub": "' || org_admin_id || '", "role": "authenticated"}';
  
  SELECT COUNT(*) INTO task_count FROM tasks;
  SELECT string_agg(title, ', ') INTO visible_tasks FROM tasks;
  
  RAISE NOTICE 'Org admin can see % tasks: %', task_count, visible_tasks;
  
  -- Test teacher access
  SET LOCAL ROLE authenticated;
  SET LOCAL "request.jwt.claims" TO '{"sub": "' || teacher_id || '", "role": "authenticated"}';
  
  SELECT COUNT(*) INTO task_count FROM tasks;
  SELECT string_agg(title, ', ') INTO visible_tasks FROM tasks;
  
  RAISE NOTICE 'Teacher can see % tasks: %', task_count, visible_tasks;
  
  -- Test maintenance access
  SET LOCAL ROLE authenticated;
  SET LOCAL "request.jwt.claims" TO '{"sub": "' || maintenance_id || '", "role": "authenticated"}';
  
  SELECT COUNT(*) INTO task_count FROM tasks;
  SELECT string_agg(title, ', ') INTO visible_tasks FROM tasks;
  
  RAISE NOTICE 'Maintenance staff can see % tasks: %', task_count, visible_tasks;
  
  -- Test supplier access
  SET LOCAL ROLE authenticated;
  SET LOCAL "request.jwt.claims" TO '{"sub": "' || supplier_id || '", "role": "authenticated"}';
  
  SELECT COUNT(*) INTO task_count FROM tasks;
  SELECT string_agg(title, ', ') INTO visible_tasks FROM tasks;
  
  RAISE NOTICE 'Supplier can see % tasks: %', task_count, visible_tasks;
  
  -- Reset role
  RESET ROLE;
  RESET "request.jwt.claims";
END $$;
