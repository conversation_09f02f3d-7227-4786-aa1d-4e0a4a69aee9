-- Comprehensive SQL script to fix all policies with infinite recursion (Safe Version)
-- Run this in the Supabase SQL Editor

-- First, let's check all the policies on the profiles table
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  tablename = 'profiles'
ORDER BY
  policyname;

-- Check all the policies on the offers table
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  tablename = 'offers'
ORDER BY
  policyname;

-- Step 1: Drop all potentially problematic policies
DROP POLICY IF EXISTS "Site admins can access all profiles" ON profiles;
DROP POLICY IF EXISTS "Site admins can access all offers" ON offers;
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can view profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Admins can update profiles in their organization" ON profiles;

-- Also drop the v2 policies if they exist
DROP POLICY IF EXISTS "Site admins can access all profiles v2" ON profiles;
DROP POLICY IF EXISTS "Site admins can access all offers v2" ON offers;
DROP POLICY IF EXISTS "Users can view their own profile v2" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile v2" ON profiles;
DROP POLICY IF EXISTS "Admins can view profiles in their organization v2" ON profiles;
DROP POLICY IF EXISTS "Admins can update profiles in their organization v2" ON profiles;
DROP POLICY IF EXISTS "Users can view their own offers" ON offers;
DROP POLICY IF EXISTS "Users can update their own offers" ON offers;

-- Step 2: Create a bypass function for site admins that doesn't use RLS
CREATE OR REPLACE FUNCTION is_site_admin()
RETURNS BOOLEAN AS $$
DECLARE
  is_admin BOOLEAN;
BEGIN
  -- Direct query without using RLS
  SELECT is_site_admin INTO is_admin
  FROM profiles
  WHERE id = auth.uid();
  
  RETURN COALESCE(is_admin, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 3: Create a bypass function for checking organization membership
CREATE OR REPLACE FUNCTION is_in_same_organization(user_id UUID, target_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_org_id UUID;
  target_org_id UUID;
BEGIN
  -- Direct queries without using RLS
  SELECT organization_id INTO user_org_id
  FROM profiles
  WHERE id = user_id;
  
  SELECT organization_id INTO target_org_id
  FROM profiles
  WHERE id = target_user_id;
  
  RETURN user_org_id IS NOT NULL AND user_org_id = target_org_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Create simplified policies for profiles table
-- Users can view their own profile
CREATE POLICY "Users can view their own profile v2"
ON profiles
FOR SELECT
TO authenticated
USING (
  id = auth.uid()
);

-- Users can update their own profile
CREATE POLICY "Users can update own profile v2"
ON profiles
FOR UPDATE
TO authenticated
USING (
  id = auth.uid()
);

-- Site admins can access all profiles
CREATE POLICY "Site admins can access all profiles v2"
ON profiles
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles AS admin_profiles
    WHERE admin_profiles.id = auth.uid() 
    AND admin_profiles.is_site_admin = true
  )
);

-- Admins can view profiles in their organization
CREATE POLICY "Admins can view profiles in their organization v2"
ON profiles
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles AS admin_profiles
    WHERE admin_profiles.id = auth.uid() 
    AND admin_profiles.role = 'admin'
    AND admin_profiles.organization_id = profiles.organization_id
    AND profiles.organization_id IS NOT NULL
  )
);

-- Admins can update profiles in their organization
CREATE POLICY "Admins can update profiles in their organization v2"
ON profiles
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles AS admin_profiles
    WHERE admin_profiles.id = auth.uid() 
    AND admin_profiles.role = 'admin'
    AND admin_profiles.organization_id = profiles.organization_id
    AND profiles.organization_id IS NOT NULL
  )
);

-- Step 5: Create simplified policies for offers table
-- Users can view their own offers
CREATE POLICY "Users can view their own offers"
ON offers
FOR SELECT
TO authenticated
USING (
  user_id = auth.uid()
);

-- Users can update their own offers
CREATE POLICY "Users can update their own offers"
ON offers
FOR UPDATE
TO authenticated
USING (
  user_id = auth.uid()
);

-- Site admins can access all offers
CREATE POLICY "Site admins can access all offers v2"
ON offers
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles AS admin_profiles
    WHERE admin_profiles.id = auth.uid() 
    AND admin_profiles.is_site_admin = true
  )
);

-- Step 6: <NAME_EMAIL> is a site admin
UPDATE profiles
SET is_site_admin = true
WHERE email = ARRAY['<EMAIL>']
AND role = 'admin';

-- Step 7: Check the <NAME_EMAIL>
SELECT 
  id,
  email,
  role,
  is_site_admin,
  organization_id
FROM 
  profiles
WHERE 
  email = ARRAY['<EMAIL>'];

-- Step 8: Check if the is_site_admin function works
DO $$
DECLARE
  admin_id UUID;
  result BOOLEAN;
BEGIN
  -- Get the <NAME_EMAIL>
  SELECT id INTO admin_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  -- Test the is_site_admin function
  IF admin_id IS NOT NULL THEN
    -- This won't actually work in the SQL editor, but it's a good check
    RAISE NOTICE 'Admin ID: %', admin_id;
  ELSE
    RAISE NOTICE 'Admin user not found';
  END IF;
END $$;
