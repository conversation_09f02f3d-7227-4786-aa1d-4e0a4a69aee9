-- SQL script to fix the infinite recursion in profiles policies
-- Run this in the Supabase SQL Editor

-- First, let's check the existing policies on the profiles table
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  tablename = 'profiles'
ORDER BY
  policyname;

-- Drop the problematic policies that might be causing recursion
DROP POLICY IF EXISTS "Site admins can access all profiles" ON profiles;

-- Create a simpler site admin policy that doesn't use the is_site_admin function
-- This avoids the recursion by not calling a function that might query the profiles table again
CREATE POLICY "Site admins can access all profiles v2"
ON profiles
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles AS admin_profiles
    WHERE admin_profiles.id = auth.uid() 
    AND admin_profiles.is_site_admin = true
  )
);

-- Check if the is_site_admin function is causing recursion
SELECT routine_definition
FROM information_schema.routines
WHERE routine_name = 'is_site_admin'
AND routine_schema = 'public';

-- Redefine the is_site_admin function to avoid recursion
CREATE OR REPLACE FUNCTION is_site_admin()
RETURNS BOOLEAN AS $$
DECLARE
  is_admin BOOLEAN;
BEGIN
  -- Direct query without using RLS
  SELECT is_site_admin INTO is_admin
  FROM profiles
  WHERE id = auth.uid();
  
  RETURN COALESCE(is_admin, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix the offers policies that might be causing issues
DROP POLICY IF EXISTS "Site admins can access all offers" ON offers;

-- Create a simpler site admin policy for offers
CREATE POLICY "Site admins can access all offers v2"
ON offers
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles AS admin_profiles
    WHERE admin_profiles.id = auth.uid() 
    AND admin_profiles.is_site_admin = true
  )
);

-- Check the <NAME_EMAIL>
SELECT 
  id,
  email,
  role,
  is_site_admin,
  organization_id
FROM 
  profiles
WHERE 
  email = ARRAY['<EMAIL>'];

-- <NAME_EMAIL> is a site admin
UPDATE profiles
SET is_site_admin = true
WHERE email = ARRAY['<EMAIL>']
AND role = 'admin';
