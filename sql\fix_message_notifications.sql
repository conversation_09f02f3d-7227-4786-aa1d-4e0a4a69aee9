-- <PERSON><PERSON> script to fix the message notification trigger that's preventing admins from sending messages

-- Fix the message notification function
CREATE OR REPLACE FUNCTION create_message_notification()
RETURNS TRIGGER AS $$
DECLARE
  task_owner_id UUID;
  task_title TEXT;
  sender_name TEXT;
  recipient_id UUID;
  recipient_count INTEGER := 0;
BEGIN
  -- Get task owner and title
  SELECT user_id, title INTO task_owner_id, task_title
  FROM public.tasks
  WHERE id = NEW.task_id;
  
  -- Get sender name
  SELECT 
    CASE 
      WHEN first_name IS NOT NULL AND last_name IS NOT NULL THEN
        first_name || ' ' || last_name
      ELSE
        'A user'
    END INTO sender_name
  FROM public.profiles
  WHERE id = NEW.sender_id;
  
  -- If sender is the task owner, notify suppliers who have made offers
  IF NEW.sender_id = task_owner_id THEN
    -- Find suppliers who have made offers on this task
    FOR recipient_id IN 
      SELECT DISTINCT user_id 
      FROM public.offers 
      WHERE task_id = NEW.task_id
    LOOP
      recipient_count := recipient_count + 1;
      
      -- Create notification for each supplier
      INSERT INTO public.notifications (
        user_id,
        type,
        message,
        related_id,
        related_type,
        read,
        email_sent
      ) VALUES (
        recipient_id,
        'message',
        'You have received a new message from ' || sender_name || ' regarding "' || task_title || '".',
        NEW.task_id::text,
        'message',
        false,
        false
      );
    END LOOP;
    
    -- If no suppliers found, don't block the message
    -- This is just for notifications, the message itself should still be created
  
  -- If sender is not the task owner, notify the task owner
  ELSIF NEW.sender_id <> task_owner_id THEN
    -- Create notification for task owner
    INSERT INTO public.notifications (
      user_id,
      type,
      message,
      related_id,
      related_type,
      read,
      email_sent
    ) VALUES (
      task_owner_id,
      'message',
      'You have received a new message from ' || sender_name || ' regarding "' || task_title || '".',
      NEW.task_id::text,
      'message',
      false,
      false
    );
  END IF;
  
  -- Always return NEW to allow the message to be created
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make sure the trigger is properly set up
DROP TRIGGER IF EXISTS task_message_notification_trigger ON public.task_messages;
CREATE TRIGGER task_message_notification_trigger
AFTER INSERT ON public.task_messages
FOR EACH ROW
EXECUTE FUNCTION create_message_notification();
