-- SQL script to fix the message notification trigger with proper UUID casting

-- First, check the data type of related_id in the notifications table
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'notifications' 
AND column_name = 'related_id';

-- Fix the message notification function with proper <PERSON><PERSON><PERSON> casting
CREATE OR REPLACE FUNCTION create_message_notification()
RETURNS TRIGGER AS $$
DECLARE
  task_owner_id UUID;
  task_title TEXT;
  sender_name TEXT;
  recipient_id UUID;
  recipient_count INTEGER := 0;
BEGIN
  -- Get task owner and title
  SELECT user_id, title INTO task_owner_id, task_title
  FROM public.tasks
  WHERE id = NEW.task_id;
  
  -- Get sender name
  SELECT 
    CASE 
      WHEN first_name IS NOT NULL AND last_name IS NOT NULL THEN
        first_name || ' ' || last_name
      ELSE
        'A user'
    END INTO sender_name
  FROM public.profiles
  WHERE id = NEW.sender_id;
  
  -- If sender is the task owner, notify suppliers who have made offers
  IF NEW.sender_id = task_owner_id THEN
    -- Find suppliers who have made offers on this task
    FOR recipient_id IN 
      SELECT DISTINCT user_id 
      FROM public.offers 
      WHERE task_id = NEW.task_id
    LOOP
      recipient_count := recipient_count + 1;
      
      -- Create notification for each supplier with proper UUID casting
      INSERT INTO public.notifications (
        user_id,
        type,
        message,
        related_id,
        related_type,
        read,
        email_sent
      ) VALUES (
        recipient_id,
        'message',
        'You have received a new message from ' || sender_name || ' regarding "' || task_title || '".',
        NEW.task_id, -- No casting needed, already UUID
        'message',
        false,
        false
      );
    END LOOP;
    
    -- If no suppliers found, don't block the message
    -- This is just for notifications, the message itself should still be created
  
  -- If sender is not the task owner, notify the task owner
  ELSIF NEW.sender_id <> task_owner_id THEN
    -- Create notification for task owner with proper UUID casting
    INSERT INTO public.notifications (
      user_id,
      type,
      message,
      related_id,
      related_type,
      read,
      email_sent
    ) VALUES (
      task_owner_id,
      'message',
      'You have received a new message from ' || sender_name || ' regarding "' || task_title || '".',
      NEW.task_id, -- No casting needed, already UUID
      'message',
      false,
      false
    );
  END IF;
  
  -- Always return NEW to allow the message to be created
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make sure the trigger is properly set up
DROP TRIGGER IF EXISTS task_message_notification_trigger ON public.task_messages;
CREATE TRIGGER task_message_notification_trigger
AFTER INSERT ON public.task_messages
FOR EACH ROW
EXECUTE FUNCTION create_message_notification();

-- Also fix the offer notification function with proper UUID casting
CREATE OR REPLACE FUNCTION create_offer_notification()
RETURNS TRIGGER AS $$
DECLARE
  task_owner_id UUID;
  task_title TEXT;
  provider_name TEXT;
BEGIN
  -- Get task owner and title
  SELECT user_id, title INTO task_owner_id, task_title
  FROM public.tasks
  WHERE id = NEW.task_id;
  
  -- Get provider name
  SELECT 
    CASE 
      WHEN first_name IS NOT NULL AND last_name IS NOT NULL THEN
        first_name || ' ' || last_name
      ELSE
        'A supplier'
    END INTO provider_name
  FROM public.profiles
  WHERE id = NEW.user_id;
  
  -- Create notification with proper UUID casting
  INSERT INTO public.notifications (
    user_id,
    type,
    message,
    related_id,
    related_type,
    read,
    email_sent
  ) VALUES (
    task_owner_id,
    'offer',
    provider_name || ' has made an offer of £' || NEW.amount::text || ' on your task "' || task_title || '".',
    NEW.task_id, -- No casting needed, already UUID
    'offer',
    false,
    false
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the offer trigger
DROP TRIGGER IF EXISTS offer_notification_trigger ON public.offers;
CREATE TRIGGER offer_notification_trigger
AFTER INSERT ON public.offers
FOR EACH ROW
EXECUTE FUNCTION create_offer_notification();

-- Fix the offer update notification function with proper UUID casting
CREATE OR REPLACE FUNCTION create_offer_update_notification()
RETURNS TRIGGER AS $$
DECLARE
  task_title TEXT;
  owner_name TEXT;
BEGIN
  -- Only proceed if status has changed
  IF OLD.status = NEW.status THEN
    RETURN NEW;
  END IF;
  
  -- Get task title
  SELECT title INTO task_title
  FROM public.tasks
  WHERE id = NEW.task_id;
  
  -- Get task owner name
  SELECT 
    CASE 
      WHEN first_name IS NOT NULL AND last_name IS NOT NULL THEN
        first_name || ' ' || last_name
      ELSE
        'The task owner'
    END INTO owner_name
  FROM public.profiles p
  JOIN public.tasks t ON p.id = t.user_id
  WHERE t.id = NEW.task_id;
  
  -- Create appropriate notification based on new status with proper UUID casting
  IF NEW.status = 'accepted' THEN
    -- Notify supplier that their offer was accepted
    INSERT INTO public.notifications (
      user_id,
      type,
      message,
      related_id,
      related_type,
      read,
      email_sent
    ) VALUES (
      NEW.user_id,
      'offer',
      'Your offer of £' || NEW.amount::text || ' on "' || task_title || '" has been accepted!',
      NEW.task_id, -- No casting needed, already UUID
      'offer',
      false,
      false
    );
  ELSIF NEW.status = 'rejected' THEN
    -- Notify supplier that their offer was rejected
    INSERT INTO public.notifications (
      user_id,
      type,
      message,
      related_id,
      related_type,
      read,
      email_sent
    ) VALUES (
      NEW.user_id,
      'offer',
      'Your offer of £' || NEW.amount::text || ' on "' || task_title || '" has been declined.',
      NEW.task_id, -- No casting needed, already UUID
      'offer',
      false,
      false
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for offer status updates
DROP TRIGGER IF EXISTS offer_update_notification_trigger ON public.offers;
CREATE TRIGGER offer_update_notification_trigger
AFTER UPDATE ON public.offers
FOR EACH ROW
EXECUTE FUNCTION create_offer_update_notification();
