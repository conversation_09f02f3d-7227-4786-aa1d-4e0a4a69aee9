-- SQL script to fix the offers table policies
-- Run this in the Supabase SQL Editor

-- First, let's check all the policies on the offers table
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  tablename = 'offers'
ORDER BY
  policyname;

-- Drop all existing policies on the offers table
DO $$
DECLARE
  policy_name TEXT;
BEGIN
  FOR policy_name IN (
    SELECT policyname 
    FROM pg_policies 
    WHERE tablename = 'offers'
  ) LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON offers', policy_name);
    RAISE NOTICE 'Dropped policy: %', policy_name;
  END LOOP;
END $$;

-- Create simple policies for the offers table that don't rely on complex functions

-- 1. Users can view their own offers
CREATE POLICY "Users can view their own offers"
ON offers
FOR SELECT
TO authenticated
USING (
  user_id = auth.uid()
);

-- 2. Users can update their own offers
CREATE POLICY "Users can update their own offers"
ON offers
FOR UPDATE
TO authenticated
USING (
  user_id = auth.uid()
);

-- 3. Users can insert their own offers
CREATE POLICY "Users can insert their own offers"
ON offers
FOR INSERT
TO authenticated
WITH CHECK (
  user_id = auth.uid()
);

-- 4. Users can delete their own offers
CREATE POLICY "Users can delete their own offers"
ON offers
FOR DELETE
TO authenticated
USING (
  user_id = auth.uid()
);

-- 5. Task owners can view offers for their tasks
CREATE POLICY "Task owners can view offers for their tasks"
ON offers
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM tasks
    WHERE tasks.id = offers.task_id
    AND tasks.user_id = auth.uid()
  )
);

-- 6. Site admins can access all offers (without using functions)
CREATE POLICY "Site admins can access all offers v2"
ON offers
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles
    WHERE profiles.id = auth.uid() 
    AND profiles.is_site_admin = true
  )
);

-- 7. Organization admins can view offers for tasks in their organization
CREATE POLICY "Organization admins can view offers"
ON offers
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles admin_profile
    JOIN tasks ON tasks.id = offers.task_id
    JOIN profiles task_owner_profile ON task_owner_profile.id = tasks.user_id
    WHERE admin_profile.id = auth.uid()
    AND admin_profile.role = 'admin'
    AND admin_profile.organization_id = task_owner_profile.organization_id
    AND admin_profile.organization_id IS NOT NULL
  )
);

-- Check if the policies were created successfully
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  tablename = 'offers'
ORDER BY
  policyname;
