-- <PERSON><PERSON> script to fix infinite recursion in profiles table policies

-- First, let's drop all existing policies on the profiles table
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can view profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Trust admins can view profiles in schools under their trust" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Public profiles access" ON profiles;
DROP POLICY IF EXISTS "Authenticated users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Authenticated users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can access all profiles" ON profiles;
DROP POLICY IF EXISTS "Block anonymous access to profiles" ON profiles;

-- Now, let's create simple, non-recursive policies

-- 1. Service role can access all profiles
CREATE POLICY "service_role_profiles_all"
ON profiles
FOR ALL
TO service_role
USING (true);

-- 2. Authenticated users can view and update their own profile
CREATE POLICY "auth_profiles_own"
ON profiles
FOR ALL
TO authenticated
USING (auth.uid() = id);

-- 3. Authenticated users with admin role can view profiles in their organization
-- This is the policy that likely caused recursion, so we'll simplify it
CREATE POLICY "admin_profiles_org_select"
ON profiles
FOR SELECT
TO authenticated
USING (
  -- Get the admin's organization_id directly from their JWT claims if available
  (auth.jwt() ->> 'organization_id')::uuid = organization_id
  OR
  -- Otherwise, use a direct comparison without a subquery
  EXISTS (
    SELECT 1 
    FROM profiles admin_profile
    WHERE admin_profile.id = auth.uid()
    AND admin_profile.role = 'admin'
    AND admin_profile.organization_id = profiles.organization_id
    AND admin_profile.id != profiles.id  -- Prevent self-reference
  )
);

-- 4. Authenticated users with admin role can update profiles in their organization
CREATE POLICY "admin_profiles_org_update"
ON profiles
FOR UPDATE
TO authenticated
USING (
  -- Get the admin's organization_id directly from their JWT claims if available
  (auth.jwt() ->> 'organization_id')::uuid = organization_id
  OR
  -- Otherwise, use a direct comparison without a subquery
  EXISTS (
    SELECT 1 
    FROM profiles admin_profile
    WHERE admin_profile.id = auth.uid()
    AND admin_profile.role = 'admin'
    AND admin_profile.organization_id = profiles.organization_id
    AND admin_profile.id != profiles.id  -- Prevent self-reference
  )
);

-- 5. Create a view for public profile information
CREATE OR REPLACE VIEW public_profiles AS
SELECT 
  id,
  first_name,
  last_name,
  role,
  organization_id,
  created_at
FROM profiles;

-- Grant access to the view
GRANT SELECT ON public_profiles TO anon, authenticated;
