-- SQL file to fix RLS policies for all tables

-- =============================================
-- Profiles Table
-- =============================================

-- First, ensure RLS is enabled
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can view profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update profiles in their organization" ON profiles;

-- Create proper policies
-- 1. Users can view their own profile
CREATE POLICY "Users can view their own profile"
ON profiles FOR SELECT
USING (auth.uid() = id);

-- 2. Users can update their own profile
CREATE POLICY "Users can update their own profile"
ON profiles FOR UPDATE
USING (auth.uid() = id);

-- 3. <PERSON><PERSON> can view profiles in their organization
CREATE POLICY "Admins can view profiles in their organization"
ON profiles FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() 
    AND role = 'admin' 
    AND organization_id = profiles.organization_id
  )
);

-- 4. Trust admins can view profiles in schools under their trust
CREATE POLICY "Trust admins can view profiles in schools under their trust"
ON profiles FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles p
    JOIN organizations o ON p.organization_id = o.id
    JOIN organizations school ON profiles.organization_id = school.id
    WHERE p.id = auth.uid() 
    AND p.role = 'admin' 
    AND o.organization_type = 'trust'
    AND school.parent_organization_id = p.organization_id
  )
);

-- 5. Admins can update profiles in their organization
CREATE POLICY "Admins can update profiles in their organization"
ON profiles FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() 
    AND role = 'admin' 
    AND organization_id = profiles.organization_id
  )
);

-- =============================================
-- Organizations Table
-- =============================================

-- First, ensure RLS is enabled
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own organization" ON organizations;
DROP POLICY IF EXISTS "Trust members can view schools in their trust" ON organizations;
DROP POLICY IF EXISTS "Admins can update their own organization" ON organizations;
DROP POLICY IF EXISTS "Trust admins can update schools in their trust" ON organizations;
DROP POLICY IF EXISTS "Authenticated users can create organizations" ON organizations;

-- Create proper policies
-- 1. Users can view their own organization
CREATE POLICY "Users can view their own organization"
ON organizations FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND organization_id = organizations.id
  )
);

-- 2. Trust members can view schools in their trust
CREATE POLICY "Trust members can view schools in their trust"
ON organizations FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles p
    JOIN organizations o ON p.organization_id = o.id
    WHERE p.id = auth.uid() 
    AND o.id = organizations.parent_organization_id
  )
);

-- 3. Admins can update their own organization
CREATE POLICY "Admins can update their own organization"
ON organizations FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin' AND organization_id = organizations.id
  )
);

-- 4. Trust admins can update schools in their trust
CREATE POLICY "Trust admins can update schools in their trust"
ON organizations FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM profiles p
    JOIN organizations o ON p.organization_id = o.id
    WHERE p.id = auth.uid() 
    AND p.role = 'admin' 
    AND o.organization_type = 'trust'
    AND organizations.parent_organization_id = p.organization_id
  )
);

-- 5. Authenticated users can create organizations
CREATE POLICY "Authenticated users can create organizations"
ON organizations FOR INSERT
WITH CHECK (auth.role() = 'authenticated');

-- =============================================
-- User Invitations Table
-- =============================================

-- First, ensure RLS is enabled
ALTER TABLE user_invitations ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view invitations by token" ON user_invitations;
DROP POLICY IF EXISTS "Admins can view invitations for their organization" ON user_invitations;
DROP POLICY IF EXISTS "Trust admins can view invitations for schools in their trust" ON user_invitations;
DROP POLICY IF EXISTS "Admins can create invitations for their organization" ON user_invitations;
DROP POLICY IF EXISTS "Trust admins can create invitations for schools in their trust" ON user_invitations;
DROP POLICY IF EXISTS "Users can update invitations they've been invited with" ON user_invitations;

-- Create proper policies
-- 1. Users can view invitations by token (for accepting invitations)
CREATE POLICY "Users can view invitations by token"
ON user_invitations FOR SELECT
USING (
  -- Only allow viewing invitations by token for authenticated users
  auth.role() = 'authenticated'
);

-- 2. Admins can view invitations for their organization
CREATE POLICY "Admins can view invitations for their organization"
ON user_invitations FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin' AND organization_id = user_invitations.organization_id
  )
);

-- 3. Trust admins can view invitations for schools in their trust
CREATE POLICY "Trust admins can view invitations for schools in their trust"
ON user_invitations FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles p
    JOIN organizations o ON p.organization_id = o.id
    JOIN organizations school ON user_invitations.organization_id = school.id
    WHERE p.id = auth.uid() 
    AND p.role = 'admin' 
    AND o.organization_type = 'trust'
    AND school.parent_organization_id = p.organization_id
  )
);

-- 4. Admins can create invitations for their organization
CREATE POLICY "Admins can create invitations for their organization"
ON user_invitations FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin' AND organization_id = user_invitations.organization_id
  )
);

-- 5. Trust admins can create invitations for schools in their trust
CREATE POLICY "Trust admins can create invitations for schools in their trust"
ON user_invitations FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles p
    JOIN organizations o ON p.organization_id = o.id
    JOIN organizations school ON user_invitations.organization_id = school.id
    WHERE p.id = auth.uid() 
    AND p.role = 'admin' 
    AND o.organization_type = 'trust'
    AND school.parent_organization_id = p.organization_id
  )
);

-- 6. Users can update invitations they've been invited with
CREATE POLICY "Users can update invitations they've been invited with"
ON user_invitations FOR UPDATE
USING (email = auth.email());

-- =============================================
-- Tasks Table
-- =============================================

-- First, ensure RLS is enabled
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own tasks" ON tasks;
DROP POLICY IF EXISTS "Users can view tasks assigned to them" ON tasks;
DROP POLICY IF EXISTS "Users can view public tasks" ON tasks;
DROP POLICY IF EXISTS "Users can view tasks in their organization" ON tasks;
DROP POLICY IF EXISTS "Users can update their own tasks" ON tasks;
DROP POLICY IF EXISTS "Users can update tasks assigned to them" ON tasks;

-- Create proper policies
-- 1. Users can view their own tasks
CREATE POLICY "Users can view their own tasks"
ON tasks FOR SELECT
USING (user_id = auth.uid());

-- 2. Users can view tasks assigned to them
CREATE POLICY "Users can view tasks assigned to them"
ON tasks FOR SELECT
USING (assigned_to = auth.uid());

-- 3. Users can view public tasks
CREATE POLICY "Users can view public tasks"
ON tasks FOR SELECT
USING (visibility = 'public');

-- 4. Users can view tasks in their organization
CREATE POLICY "Users can view tasks in their organization"
ON tasks FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles p1
    JOIN profiles p2 ON p1.organization_id = p2.organization_id
    WHERE p1.id = auth.uid() AND p2.id = tasks.user_id
  )
);

-- 5. Users can update their own tasks
CREATE POLICY "Users can update their own tasks"
ON tasks FOR UPDATE
USING (user_id = auth.uid());

-- 6. Users can update tasks assigned to them
CREATE POLICY "Users can update tasks assigned to them"
ON tasks FOR UPDATE
USING (assigned_to = auth.uid());

-- 7. Users can create tasks
CREATE POLICY "Users can create tasks"
ON tasks FOR INSERT
WITH CHECK (auth.role() = 'authenticated');

-- =============================================
-- Task Messages Table
-- =============================================

-- First, ensure RLS is enabled
ALTER TABLE task_messages ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view messages for their tasks" ON task_messages;
DROP POLICY IF EXISTS "Users can view messages for tasks assigned to them" ON task_messages;
DROP POLICY IF EXISTS "Users can view messages for public tasks" ON task_messages;
DROP POLICY IF EXISTS "Users can view messages for tasks in their organization" ON task_messages;
DROP POLICY IF EXISTS "Users can create messages for their tasks" ON task_messages;
DROP POLICY IF EXISTS "Users can create messages for tasks assigned to them" ON task_messages;
DROP POLICY IF EXISTS "Users can create messages for public tasks" ON task_messages;
DROP POLICY IF EXISTS "Users can create messages for tasks in their organization" ON task_messages;

-- Create proper policies
-- 1. Users can view messages for their tasks
CREATE POLICY "Users can view messages for their tasks"
ON task_messages FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM tasks
    WHERE tasks.id = task_messages.task_id AND tasks.user_id = auth.uid()
  )
);

-- 2. Users can view messages for tasks assigned to them
CREATE POLICY "Users can view messages for tasks assigned to them"
ON task_messages FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM tasks
    WHERE tasks.id = task_messages.task_id AND tasks.assigned_to = auth.uid()
  )
);

-- 3. Users can view messages for public tasks
CREATE POLICY "Users can view messages for public tasks"
ON task_messages FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM tasks
    WHERE tasks.id = task_messages.task_id AND tasks.visibility = 'public'
  )
);

-- 4. Users can view messages for tasks in their organization
CREATE POLICY "Users can view messages for tasks in their organization"
ON task_messages FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM tasks
    JOIN profiles p1 ON tasks.user_id = p1.id
    JOIN profiles p2 ON p1.organization_id = p2.organization_id
    WHERE tasks.id = task_messages.task_id AND p2.id = auth.uid()
  )
);

-- 5. Users can create messages for their tasks
CREATE POLICY "Users can create messages for their tasks"
ON task_messages FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM tasks
    WHERE tasks.id = task_messages.task_id AND tasks.user_id = auth.uid()
  )
);

-- 6. Users can create messages for tasks assigned to them
CREATE POLICY "Users can create messages for tasks assigned to them"
ON task_messages FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM tasks
    WHERE tasks.id = task_messages.task_id AND tasks.assigned_to = auth.uid()
  )
);

-- 7. Users can create messages for public tasks
CREATE POLICY "Users can create messages for public tasks"
ON task_messages FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM tasks
    WHERE tasks.id = task_messages.task_id AND tasks.visibility = 'public'
  )
);

-- 8. Users can create messages for tasks in their organization
CREATE POLICY "Users can create messages for tasks in their organization"
ON task_messages FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM tasks
    JOIN profiles p1 ON tasks.user_id = p1.id
    JOIN profiles p2 ON p1.organization_id = p2.organization_id
    WHERE tasks.id = task_messages.task_id AND p2.id = auth.uid()
  )
);
