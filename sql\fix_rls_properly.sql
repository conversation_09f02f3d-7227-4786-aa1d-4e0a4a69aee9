-- Proper fix for RLS policies without disabling security
-- This script fixes the infinite recursion issue while maintaining security

-- First, drop all existing policies on the profiles table
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Trust admins can view profiles in schools under their trust" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Public profiles access" ON profiles;
DROP POLICY IF EXISTS "Authenticated users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Authenticated users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can access all profiles" ON profiles;
DROP POLICY IF EXISTS "Block anonymous access to profiles" ON profiles;
DROP POLICY IF EXISTS "service_role_profiles_all" ON profiles;
DROP POLICY IF EXISTS "auth_profiles_own" ON profiles;
DROP POLICY IF EXISTS "admin_profiles_org_select" ON profiles;
DROP POLICY IF EXISTS "admin_profiles_org_update" ON profiles;
DROP POLICY IF EXISTS "auth_profiles_view_all" ON profiles;
DROP POLICY IF EXISTS "auth_profiles_view_limited" ON profiles;
DROP POLICY IF EXISTS "admin_view_org_profiles" ON profiles;

-- Create new, non-recursive policies

-- 1. Service role can access all profiles
CREATE POLICY "service_role_profiles_all"
ON profiles
FOR ALL
TO service_role
USING (true);

-- 2. Authenticated users can view and update their own profile
CREATE POLICY "auth_profiles_own"
ON profiles
FOR ALL
TO authenticated
USING (auth.uid() = id);

-- 3. Authenticated users can view profiles in their organization
-- This is a simplified policy that avoids recursion
CREATE POLICY "auth_profiles_view_org"
ON profiles
FOR SELECT
TO authenticated
USING (
  -- This checks if the user belongs to the same organization without causing recursion
  organization_id IN (
    SELECT p.organization_id 
    FROM profiles p 
    WHERE p.id = auth.uid()
  )
);

-- 4. Authenticated users with admin role can update profiles in their organization
-- We'll use a database function to avoid recursion
CREATE OR REPLACE FUNCTION check_admin_for_org(org_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_admin boolean;
BEGIN
  SELECT (role = 'admin' AND organization_id = org_id)
  INTO is_admin
  FROM profiles
  WHERE id = auth.uid();
  
  RETURN COALESCE(is_admin, false);
END;
$$;

-- Create policy using the function
CREATE POLICY "admin_update_org_profiles"
ON profiles
FOR UPDATE
TO authenticated
USING (
  check_admin_for_org(organization_id)
);

-- 5. Block anonymous access
CREATE POLICY "block_anon_access"
ON profiles
FOR ALL
TO anon
USING (false);
