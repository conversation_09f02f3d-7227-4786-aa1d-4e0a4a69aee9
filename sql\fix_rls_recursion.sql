-- SQL file to fix RLS policy recursion issues

-- =============================================
-- Profiles Table
-- =============================================

-- First, ensure RLS is enabled
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Trust admins can view profiles in schools under their trust" ON profiles;
DROP POLICY IF EXISTS "Admins can update profiles in their organization" ON profiles;

-- Create simpler policies
-- 1. Public access policy (very restrictive)
CREATE POLICY "Public profiles access"
ON profiles FOR SELECT
USING (false); -- No anonymous access by default

-- 2. Authenticated users can view their own profile
CREATE POLICY "Authenticated users can view their own profile"
ON profiles FOR SELECT
TO authenticated
USING (auth.uid() = id);

-- 3. Authenticated users can update their own profile
CREATE POLICY "Authenticated users can update their own profile"
ON profiles FOR UPDATE
TO authenticated
USING (auth.uid() = id);

-- 4. Service role can access all profiles
CREATE POLICY "Service role can access all profiles"
ON profiles
TO service_role
USING (true);

-- =============================================
-- Organizations Table
-- =============================================

-- First, ensure RLS is enabled
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own organization" ON organizations;
DROP POLICY IF EXISTS "Trust members can view schools in their trust" ON organizations;
DROP POLICY IF EXISTS "Admins can update their own organization" ON organizations;
DROP POLICY IF EXISTS "Trust admins can update schools in their trust" ON organizations;
DROP POLICY IF EXISTS "Authenticated users can create organizations" ON organizations;
DROP POLICY IF EXISTS "Public organizations access" ON organizations;

-- Create simpler policies
-- 1. Public access policy (very restrictive)
CREATE POLICY "Public organizations access"
ON organizations FOR SELECT
USING (false); -- No anonymous access by default

-- 2. Authenticated users can view organizations they belong to
CREATE POLICY "Authenticated users can view their organizations"
ON organizations FOR SELECT
TO authenticated
USING (
  id IN (
    SELECT organization_id FROM profiles
    WHERE profiles.id = auth.uid()
  )
);

-- 3. Authenticated users can create organizations
CREATE POLICY "Authenticated users can create organizations"
ON organizations FOR INSERT
TO authenticated
WITH CHECK (true);

-- 4. Authenticated users with admin role can update their organization
CREATE POLICY "Admins can update their organization"
ON organizations FOR UPDATE
TO authenticated
USING (
  id IN (
    SELECT organization_id FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
  )
);

-- 5. Service role can access all organizations
CREATE POLICY "Service role can access all organizations"
ON organizations
TO service_role
USING (true);

-- =============================================
-- User Invitations Table
-- =============================================

-- First, ensure RLS is enabled
ALTER TABLE user_invitations ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view invitations by token" ON user_invitations;
DROP POLICY IF EXISTS "Admins can view invitations for their organization" ON user_invitations;
DROP POLICY IF EXISTS "Trust admins can view invitations for schools in their trust" ON user_invitations;
DROP POLICY IF EXISTS "Admins can create invitations for their organization" ON user_invitations;
DROP POLICY IF EXISTS "Trust admins can create invitations for schools in their trust" ON user_invitations;
DROP POLICY IF EXISTS "Users can update invitations they've been invited with" ON user_invitations;
DROP POLICY IF EXISTS "Public invitations access" ON user_invitations;

-- Create simpler policies
-- 1. Public access policy (very restrictive)
CREATE POLICY "Public invitations access"
ON user_invitations FOR SELECT
USING (false); -- No anonymous access by default

-- 2. Authenticated users can view invitations for their email
CREATE POLICY "Authenticated users can view their invitations"
ON user_invitations FOR SELECT
TO authenticated
USING (email = auth.email());

-- 3. Authenticated users can update invitations for their email
CREATE POLICY "Authenticated users can update their invitations"
ON user_invitations FOR UPDATE
TO authenticated
USING (email = auth.email());

-- 4. Authenticated users with admin role can view and manage invitations for their organization
CREATE POLICY "Admins can manage invitations for their organization"
ON user_invitations
TO authenticated
USING (
  organization_id IN (
    SELECT organization_id FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
  )
);

-- 5. Service role can access all invitations
CREATE POLICY "Service role can access all invitations"
ON user_invitations
TO service_role
USING (true);

-- =============================================
-- Tasks Table
-- =============================================

-- First, ensure RLS is enabled
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own tasks" ON tasks;
DROP POLICY IF EXISTS "Users can view tasks assigned to them" ON tasks;
DROP POLICY IF EXISTS "Users can view public tasks" ON tasks;
DROP POLICY IF EXISTS "Users can view tasks in their organization" ON tasks;
DROP POLICY IF EXISTS "Users can update their own tasks" ON tasks;
DROP POLICY IF EXISTS "Users can update tasks assigned to them" ON tasks;
DROP POLICY IF EXISTS "Users can create tasks" ON tasks;
DROP POLICY IF EXISTS "Public tasks access" ON tasks;

-- Create simpler policies
-- 1. Public access policy (very restrictive)
CREATE POLICY "Public tasks access"
ON tasks FOR SELECT
USING (false); -- No anonymous access by default

-- 2. Authenticated users can view and manage their own tasks
CREATE POLICY "Authenticated users can manage their own tasks"
ON tasks
TO authenticated
USING (user_id = auth.uid());

-- 3. Authenticated users can view and update tasks assigned to them
CREATE POLICY "Authenticated users can manage tasks assigned to them"
ON tasks
TO authenticated
USING (assigned_to = auth.uid());

-- 4. Authenticated users can view public tasks
CREATE POLICY "Authenticated users can view public tasks"
ON tasks FOR SELECT
TO authenticated
USING (visibility = 'public');

-- 5. Authenticated users can create tasks
CREATE POLICY "Authenticated users can create tasks"
ON tasks FOR INSERT
TO authenticated
WITH CHECK (true);

-- 6. Service role can access all tasks
CREATE POLICY "Service role can access all tasks"
ON tasks
TO service_role
USING (true);

-- =============================================
-- Task Messages Table
-- =============================================

-- First, ensure RLS is enabled
ALTER TABLE task_messages ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view messages for their tasks" ON task_messages;
DROP POLICY IF EXISTS "Users can view messages for tasks assigned to them" ON task_messages;
DROP POLICY IF EXISTS "Users can view messages for public tasks" ON task_messages;
DROP POLICY IF EXISTS "Users can view messages for tasks in their organization" ON task_messages;
DROP POLICY IF EXISTS "Users can create messages for their tasks" ON task_messages;
DROP POLICY IF EXISTS "Users can create messages for tasks assigned to them" ON task_messages;
DROP POLICY IF EXISTS "Users can create messages for public tasks" ON task_messages;
DROP POLICY IF EXISTS "Users can create messages for tasks in their organization" ON task_messages;
DROP POLICY IF EXISTS "Public task_messages access" ON task_messages;

-- Create simpler policies
-- 1. Public access policy (very restrictive)
CREATE POLICY "Public task_messages access"
ON task_messages FOR SELECT
USING (false); -- No anonymous access by default

-- 2. Authenticated users can view and create messages for tasks they own
CREATE POLICY "Authenticated users can manage messages for their tasks"
ON task_messages
TO authenticated
USING (
  task_id IN (
    SELECT id FROM tasks
    WHERE tasks.user_id = auth.uid()
  )
);

-- 3. Authenticated users can view and create messages for tasks assigned to them
CREATE POLICY "Authenticated users can manage messages for tasks assigned to them"
ON task_messages
TO authenticated
USING (
  task_id IN (
    SELECT id FROM tasks
    WHERE tasks.assigned_to = auth.uid()
  )
);

-- 4. Authenticated users can view and create messages for public tasks
CREATE POLICY "Authenticated users can manage messages for public tasks"
ON task_messages
TO authenticated
USING (
  task_id IN (
    SELECT id FROM tasks
    WHERE tasks.visibility = 'public'
  )
);

-- 5. Service role can access all task messages
CREATE POLICY "Service role can access all task messages"
ON task_messages
TO service_role
USING (true);
