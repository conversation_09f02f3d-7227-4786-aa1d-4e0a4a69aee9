-- SQL script to fix the RLS policies for the tasks table
-- Run this in the Supabase SQL Editor

-- First, check if RLS is enabled
SELECT
  relname,
  relrowsecurity
FROM
  pg_class
WHERE
  relname = 'tasks';

-- Enable RLS if it's not already enabled
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Teachers and admins can create tasks" ON public.tasks;
DROP POLICY IF EXISTS "Ad<PERSON> can view all organization tasks" ON public.tasks;
DROP POLICY IF EXISTS "Teachers can view their own tasks" ON public.tasks;
DROP POLICY IF EXISTS "Maintenance and support can view assigned tasks" ON public.tasks;
DROP POLICY IF EXISTS "Suppliers can view public tasks" ON public.tasks;
DROP POLICY IF EXISTS "Ad<PERSON> can update any task" ON public.tasks;
DROP POLICY IF EXISTS "Task owners can update their own tasks" ON public.tasks;
DROP POLICY IF EXISTS "Ad<PERSON> can delete any task" ON public.tasks;
DROP POLICY IF EXISTS "Task owners can delete their own tasks" ON public.tasks;
DROP POLICY IF EXISTS "Organization members can view their organization's tasks" ON public.tasks;

-- Create new policies

-- Create policy for task creation
CREATE POLICY "Teachers and admins can create tasks"
ON public.tasks
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND (role = 'teacher' OR role = 'admin')
  )
);

-- Create policies for task visibility
CREATE POLICY "Admins can view all tasks"
ON public.tasks
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  )
);

-- Additional policy to ensure admins can see all tasks regardless of status
CREATE POLICY "Admins can view all tasks regardless of status"
ON public.tasks
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  )
);

CREATE POLICY "Teachers can view their own tasks"
ON public.tasks
FOR SELECT
USING (
  user_id = auth.uid()
);

CREATE POLICY "Maintenance and support can view assigned tasks"
ON public.tasks
FOR SELECT
USING (
  assigned_to = auth.uid()
);

CREATE POLICY "Staff can view open tasks"
ON public.tasks
FOR SELECT
USING (
  status = 'open'
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND (role = 'maintenance' OR role = 'support')
  )
);

CREATE POLICY "Suppliers can view public tasks"
ON public.tasks
FOR SELECT
USING (
  visibility = 'public'
  AND EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND account_type = 'supplier'
  )
);

-- Create policies for task updates
CREATE POLICY "Admins can update any task"
ON public.tasks
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  )
);

CREATE POLICY "Task owners can update their own tasks"
ON public.tasks
FOR UPDATE
USING (
  user_id = auth.uid()
);

-- Create policies for task deletion
CREATE POLICY "Admins can delete any task"
ON public.tasks
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND role = 'admin'
  )
);

CREATE POLICY "Task owners can delete their own tasks"
ON public.tasks
FOR DELETE
USING (
  user_id = auth.uid()
);

-- Verify the policies
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies
WHERE tablename = 'tasks'
ORDER BY policyname;
