-- <PERSON><PERSON> script to fix task security policies
-- This script addresses the security issue where users can see tasks from other organizations

-- First, drop the problematic policies
DROP POLICY IF EXISTS "Ad<PERSON> can view all tasks" ON public.tasks;
DROP POLICY IF EXISTS "Ad<PERSON> can view all tasks regardless of status" ON public.tasks;
DROP POLICY IF EXISTS "auth_tasks_select_public" ON public.tasks;
DROP POLICY IF EXISTS "Organization admins can view tasks" ON public.tasks;
DROP POLICY IF EXISTS "Suppliers can view public tasks" ON public.tasks;

-- Create new organization-aware policies

-- Organization admins can view tasks within their organization
CREATE POLICY "Organization admins can view tasks"
ON public.tasks
FOR SELECT
TO authenticated
USING (
  has_role('admin') AND
  (
    -- Handle tasks with organization_id
    (
      tasks.organization_id IS NOT NULL AND
      EXISTS (
        SELECT 1
        FROM profiles
        WHERE
          id = auth.uid() AND
          organization_id = tasks.organization_id
      )
    )
    -- Special case for legacy tasks with null organization_id
    -- Only site admins can see these
    OR (
      tasks.organization_id IS NULL AND
      EXISTS (
        SELECT 1
        FROM profiles
        WHERE id = auth.uid() AND is_site_admin = true
      )
    )
  )
);

-- Suppliers can only view public tasks within their organization
CREATE POLICY "Suppliers can view public tasks"
ON public.tasks
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM profiles p
    WHERE
      p.id = auth.uid() AND
      p.account_type = 'supplier' AND
      (
        -- Handle tasks with organization_id
        (tasks.organization_id IS NOT NULL AND p.organization_id = tasks.organization_id)
        -- Special case for legacy tasks with null organization_id
        -- Only site admins can see these
        OR (tasks.organization_id IS NULL AND p.is_site_admin = true)
      )
  ) AND
  visibility = 'public'
);

-- Authenticated users can only see public tasks within their organization
CREATE POLICY "auth_tasks_select_public"
ON public.tasks
FOR SELECT
TO authenticated
USING (
  (visibility = 'public' OR is_public = true) AND
  (
    -- Handle tasks with organization_id
    (
      tasks.organization_id IS NOT NULL AND
      EXISTS (
        SELECT 1
        FROM profiles
        WHERE
          id = auth.uid() AND
          organization_id = tasks.organization_id
      )
    )
    -- Special case for legacy tasks with null organization_id
    -- Only site admins can see these
    OR (
      tasks.organization_id IS NULL AND
      EXISTS (
        SELECT 1
        FROM profiles
        WHERE id = auth.uid() AND is_site_admin = true
      )
    )
  )
);

-- Site admins can access all tasks (keep this for super admins)
-- This policy already exists and is fine as is:
-- CREATE POLICY "Site admins can access all tasks" ON public.tasks FOR ALL USING (is_site_admin());

-- Add organization check to other policies that might need it

-- Update the policy for teachers to include organization check
DROP POLICY IF EXISTS "Teachers can view tasks" ON public.tasks;
CREATE POLICY "Teachers can view tasks"
ON public.tasks
FOR SELECT
TO authenticated
USING (
  has_role('teacher') AND
  (
    user_id = auth.uid() OR
    (
      visibility = 'public' AND
      (
        -- Handle tasks with organization_id
        (
          tasks.organization_id IS NOT NULL AND
          EXISTS (
            SELECT 1
            FROM profiles
            WHERE
              id = auth.uid() AND
              organization_id = tasks.organization_id
          )
        )
        -- Special case for legacy tasks with null organization_id
        -- Only site admins can see these
        OR (
          tasks.organization_id IS NULL AND
          EXISTS (
            SELECT 1
            FROM profiles
            WHERE id = auth.uid() AND is_site_admin = true
          )
        )
      )
    )
  )
);

-- Update staff policies to include organization check
DROP POLICY IF EXISTS "Staff can view assigned tasks" ON public.tasks;
CREATE POLICY "Staff can view assigned tasks"
ON public.tasks
FOR SELECT
TO authenticated
USING (
  (has_role('maintenance') OR has_role('support')) AND
  assigned_to = auth.uid() AND
  (
    -- Handle tasks with organization_id
    (
      tasks.organization_id IS NOT NULL AND
      EXISTS (
        SELECT 1
        FROM profiles
        WHERE
          id = auth.uid() AND
          organization_id = tasks.organization_id
      )
    )
    -- Special case for legacy tasks with null organization_id
    -- Only site admins can see these
    OR (
      tasks.organization_id IS NULL AND
      EXISTS (
        SELECT 1
        FROM profiles
        WHERE id = auth.uid() AND is_site_admin = true
      )
    )
  )
);

-- Update staff open tasks policy
DROP POLICY IF EXISTS "Staff can view open tasks" ON public.tasks;
CREATE POLICY "Staff can view open tasks"
ON public.tasks
FOR SELECT
TO authenticated
USING (
  (status = 'open') AND
  (has_role('maintenance') OR has_role('support')) AND
  (
    -- Handle tasks with organization_id
    (
      tasks.organization_id IS NOT NULL AND
      EXISTS (
        SELECT 1
        FROM profiles
        WHERE
          id = auth.uid() AND
          organization_id = tasks.organization_id
      )
    )
    -- Special case for legacy tasks with null organization_id
    -- Only site admins can see these
    OR (
      tasks.organization_id IS NULL AND
      EXISTS (
        SELECT 1
        FROM profiles
        WHERE id = auth.uid() AND is_site_admin = true
      )
    )
  )
);

-- Add a policy to handle tasks with null organization_id
-- Only site admins can see these
CREATE POLICY "Only site admins can see tasks with null organization_id"
ON public.tasks
FOR SELECT
TO authenticated
USING (
  tasks.organization_id IS NULL AND
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE id = auth.uid() AND is_site_admin = true
  )
);

-- Fix tasks with null organization_id by assigning them to the creator's organization
UPDATE public.tasks
SET organization_id = (
  SELECT organization_id
  FROM profiles
  WHERE id = tasks.user_id
)
WHERE organization_id IS NULL;

-- Verify the changes
SELECT
  schemaname,
  tablename,
  policyname,
  roles,
  cmd,
  qual
FROM
  pg_policies
WHERE
  tablename = 'tasks'
ORDER BY
  policyname;
