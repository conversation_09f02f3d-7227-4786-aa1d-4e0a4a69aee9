-- SQL script to fix the tasks visibility default value
-- Run this in the Supabase SQL Editor

-- Change the default value for the visibility column to 'admin'
ALTER TABLE public.tasks 
ALTER COLUMN visibility SET DEFAULT 'admin';

-- Update existing tasks created by teachers to have admin visibility
UPDATE public.tasks
SET visibility = 'admin'
WHERE visibility = 'public'
AND status = 'open'
AND user_id IN (
  SELECT id FROM public.profiles
  WHERE role = 'teacher'
);

-- Verify the changes
SELECT id, title, visibility, status, user_id
FROM public.tasks
ORDER BY created_at DESC
LIMIT 10;
