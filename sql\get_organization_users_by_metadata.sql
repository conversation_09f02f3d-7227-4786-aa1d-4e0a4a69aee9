-- Function to get all users with the same organization ID in their metadata
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION get_organization_users_by_metadata(org_id TEXT)
RETURNS TABLE (
  id UUID,
  email TEXT,
  role TEXT,
  created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    au.id,
    au.email,
    (au.raw_user_meta_data->>'role')::TEXT as role,
    au.created_at
  FROM 
    auth.users au
  WHERE 
    (au.raw_user_meta_data->'organization'->>'id')::TEXT = org_id;
END;
$$;
