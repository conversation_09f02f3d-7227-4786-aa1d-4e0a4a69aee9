-- SQL script to improve account_type handling
-- This script modifies the profile creation trigger to be more robust
-- and ensures account_type is properly set in the database

-- 1. Modify the profile creation trigger to handle account_type more robustly
CREATE OR REPLACE FUNCTION public.create_profile_for_new_user()
RETURNS TRIGGER AS $$
DECLARE
  account_type_value TEXT;
  role_value TEXT;
BEGIN
  -- Check if a profile already exists for this user
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = NEW.id) THEN
    -- Determine account_type based on metadata
    account_type_value := NEW.raw_user_meta_data->>'account_type';
    
    -- If account_type is not set in metadata, default to 'school'
    IF account_type_value IS NULL OR account_type_value = '' THEN
      account_type_value := 'school';
    END IF;
    
    -- Determine role based on metadata
    role_value := NEW.raw_user_meta_data->>'role';
    
    -- If role is 'supplier', ensure account_type is also 'supplier'
    -- This ensures consistency between role and account_type
    IF role_value = 'supplier' THEN
      account_type_value := 'supplier';
    END IF;
    
    -- Create a new profile
    INSERT INTO public.profiles (
      id,
      email,
      account_type,
      role,
      first_name,
      last_name,
      created_at,
      updated_at
    ) VALUES (
      NEW.id,
      ARRAY[NEW.email],
      account_type_value,
      role_value,
      COALESCE(split_part(COALESCE(NEW.raw_user_meta_data->>'name', ''), ' ', 1), ''),
      COALESCE(substring(COALESCE(NEW.raw_user_meta_data->>'name', '') from position(' ' in COALESCE(NEW.raw_user_meta_data->>'name', '')) + 1), ''),
      NEW.created_at,
      NEW.created_at
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Create a function to fix inconsistent account_type values
CREATE OR REPLACE FUNCTION public.fix_inconsistent_account_types()
RETURNS void AS $$
DECLARE
  profile_record RECORD;
BEGIN
  -- Find profiles where role is 'supplier' but account_type is not 'supplier'
  FOR profile_record IN 
    SELECT id, role, account_type 
    FROM public.profiles 
    WHERE role = 'supplier' AND account_type != 'supplier'
  LOOP
    -- Update the account_type to match the role
    UPDATE public.profiles
    SET 
      account_type = 'supplier',
      updated_at = NOW()
    WHERE id = profile_record.id;
    
    RAISE NOTICE 'Fixed account_type for user %: changed from % to supplier', 
      profile_record.id, profile_record.account_type;
  END LOOP;
  
  -- Find profiles where account_type is 'supplier' but role is not 'supplier'
  FOR profile_record IN 
    SELECT id, role, account_type 
    FROM public.profiles 
    WHERE account_type = 'supplier' AND (role IS NULL OR role != 'supplier')
  LOOP
    -- Update the role to match the account_type
    UPDATE public.profiles
    SET 
      role = 'supplier',
      updated_at = NOW()
    WHERE id = profile_record.id;
    
    RAISE NOTICE 'Fixed role for user %: changed from % to supplier', 
      profile_record.id, profile_record.role;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create a trigger to ensure consistency between role and account_type
CREATE OR REPLACE FUNCTION public.ensure_role_account_type_consistency()
RETURNS TRIGGER AS $$
BEGIN
  -- If role is being set to 'supplier', ensure account_type is also 'supplier'
  IF NEW.role = 'supplier' AND NEW.account_type != 'supplier' THEN
    NEW.account_type := 'supplier';
  END IF;
  
  -- If account_type is being set to 'supplier', ensure role is also 'supplier'
  IF NEW.account_type = 'supplier' AND NEW.role != 'supplier' THEN
    NEW.role := 'supplier';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS ensure_role_account_type_consistency_trigger ON public.profiles;

-- Create the trigger
CREATE TRIGGER ensure_role_account_type_consistency_trigger
BEFORE INSERT OR UPDATE ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION public.ensure_role_account_type_consistency();

-- 4. Run the fix function to correct any existing inconsistencies
SELECT fix_inconsistent_account_types();

-- 5. Add a comment to explain the relationship between role and account_type
COMMENT ON COLUMN public.profiles.account_type IS 'Distinguishes between school and supplier accounts. For supplier accounts, both account_type and role should be set to supplier. For school accounts, account_type is school and role can be admin, teacher, maintenance, or support.';
COMMENT ON COLUMN public.profiles.role IS 'For school accounts (account_type=school), role can be admin, teacher, maintenance, or support. For supplier accounts (account_type=supplier), role should be supplier.';
