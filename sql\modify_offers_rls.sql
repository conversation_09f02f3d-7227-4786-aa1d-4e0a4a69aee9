-- Enable RLS on the offers table if not already enabled
ALTER TABLE offers ENABLE ROW LEVEL SECURITY;

-- Drop any existing policy with the same name
DROP POLICY IF EXISTS allow_task_owners_to_update_offers ON offers;

-- Create a policy to allow task owners to update offers for their tasks
CREATE POLICY allow_task_owners_to_update_offers
ON offers
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM tasks
    WHERE tasks.id = offers.task_id
    AND tasks.user_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM tasks
    WHERE tasks.id = offers.task_id
    AND tasks.user_id = auth.uid()
  )
);

-- Ensure there's a policy for users to read their own offers
DROP POLICY IF EXISTS allow_users_to_read_own_offers ON offers;
CREATE POLICY allow_users_to_read_own_offers
ON offers
FOR SELECT
TO authenticated
USING (
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1
    FROM tasks
    WHERE tasks.id = offers.task_id
    AND tasks.user_id = auth.uid()
  )
);

-- Ensure there's a policy for users to create their own offers
DROP POLICY IF EXISTS allow_users_to_insert_own_offers ON offers;
CREATE POLICY allow_users_to_insert_own_offers
ON offers
FOR INSERT
TO authenticated
WITH CHECK (
  user_id = auth.uid()
);

-- Ensure there's a policy for users to delete their own offers
DROP POLICY IF EXISTS allow_users_to_delete_own_offers ON offers;
CREATE POLICY allow_users_to_delete_own_offers
ON offers
FOR DELETE
TO authenticated
USING (
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1
    FROM tasks
    WHERE tasks.id = offers.task_id
    AND tasks.user_id = auth.uid()
  )
);

-- Allow public read access to offers for public tasks
DROP POLICY IF EXISTS allow_public_read_offers_for_public_tasks ON offers;
CREATE POLICY allow_public_read_offers_for_public_tasks
ON offers
FOR SELECT
TO anon
USING (
  EXISTS (
    SELECT 1
    FROM tasks
    WHERE tasks.id = offers.task_id
    AND tasks.visibility = 'public'
  )
);
