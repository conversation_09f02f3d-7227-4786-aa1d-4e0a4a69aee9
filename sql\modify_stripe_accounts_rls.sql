-- Enable RLS on the stripe_accounts table if not already enabled
ALTER TABLE stripe_accounts ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies for stripe_accounts
DROP POLICY IF EXISTS allow_users_to_read_own_stripe_account ON stripe_accounts;
DROP POLICY IF EXISTS allow_users_to_read_supplier_stripe_account ON stripe_accounts;
DROP POLICY IF EXISTS allow_admins_to_read_all_stripe_accounts ON stripe_accounts;
DROP POLICY IF EXISTS allow_users_to_update_own_stripe_account ON stripe_accounts;
DROP POLICY IF EXISTS allow_users_to_insert_own_stripe_account ON stripe_accounts;

-- Create a policy to allow users to read their own Stripe account
CREATE POLICY allow_users_to_read_own_stripe_account
ON stripe_accounts
FOR SELECT
TO authenticated
USING (
  user_id = auth.uid()
);

-- Create a policy to allow users to read Stripe accounts of suppliers they're working with
CREATE POLICY allow_users_to_read_supplier_stripe_account
ON stripe_accounts
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM tasks
    WHERE tasks.assigned_to = stripe_accounts.user_id
    AND tasks.user_id = auth.uid()
  )
  OR
  EXISTS (
    SELECT 1
    FROM tasks
    WHERE tasks.user_id = stripe_accounts.user_id
    AND tasks.assigned_to = auth.uid()
  )
);

-- Create a policy to allow admins to read all Stripe accounts
CREATE POLICY allow_admins_to_read_all_stripe_accounts
ON stripe_accounts
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

-- Create a policy to allow users to update their own Stripe account
CREATE POLICY allow_users_to_update_own_stripe_account
ON stripe_accounts
FOR UPDATE
TO authenticated
USING (
  user_id = auth.uid()
)
WITH CHECK (
  user_id = auth.uid()
);

-- Create a policy to allow users to insert their own Stripe account
CREATE POLICY allow_users_to_insert_own_stripe_account
ON stripe_accounts
FOR INSERT
TO authenticated
WITH CHECK (
  user_id = auth.uid()
);

-- Create a policy to allow payment processing between users
CREATE POLICY allow_payment_processing
ON stripe_accounts
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM offers
    JOIN tasks ON offers.task_id = tasks.id
    WHERE (tasks.user_id = auth.uid() AND offers.user_id = stripe_accounts.user_id)
    OR (offers.user_id = auth.uid() AND tasks.user_id = stripe_accounts.user_id)
  )
);

-- Create a policy to allow service role to access all stripe accounts
CREATE POLICY allow_service_role_full_access
ON stripe_accounts
FOR ALL
TO service_role
USING (true)
WITH CHECK (true);
