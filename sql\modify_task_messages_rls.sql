-- Enable <PERSON><PERSON> on the task_messages table if not already enabled
ALTER TABLE task_messages ENA<PERSON>E ROW LEVEL SECURITY;

-- Drop any existing policies for task_messages
DROP POLICY IF EXISTS allow_task_participants_to_read_messages ON task_messages;
DROP POLICY IF EXISTS allow_task_participants_to_insert_messages ON task_messages;
DROP POLICY IF EXISTS allow_users_to_read_own_messages ON task_messages;
DROP POLICY IF EXISTS allow_users_to_insert_own_messages ON task_messages;

-- Create a policy to allow task participants (owner and assigned user) to read messages
CREATE POLICY allow_task_participants_to_read_messages
ON task_messages
FOR SELECT
TO authenticated
USING (
  -- User is the sender of the message
  sender_id = auth.uid() OR
  -- User is the owner of the task
  EXISTS (
    SELECT 1
    FROM tasks
    WHERE tasks.id = task_messages.task_id
    AND tasks.user_id = auth.uid()
  ) OR
  -- User is assigned to the task
  EXISTS (
    SELECT 1
    FROM tasks
    WHERE tasks.id = task_messages.task_id
    AND tasks.assigned_to = auth.uid()
  )
);

-- Create a policy to allow task participants to insert messages
CREATE POLICY allow_task_participants_to_insert_messages
ON task_messages
FOR INSERT
TO authenticated
WITH CHECK (
  -- User is the sender of the message (this should always be true)
  sender_id = auth.uid() AND
  (
    -- User is the owner of the task
    EXISTS (
      SELECT 1
      FROM tasks
      WHERE tasks.id = task_messages.task_id
      AND tasks.user_id = auth.uid()
    ) OR
    -- User is assigned to the task
    EXISTS (
      SELECT 1
      FROM tasks
      WHERE tasks.id = task_messages.task_id
      AND tasks.assigned_to = auth.uid()
    )
  )
);

-- Create a policy to allow admins to read all messages
DROP POLICY IF EXISTS allow_admins_to_read_all_messages ON task_messages;
CREATE POLICY allow_admins_to_read_all_messages
ON task_messages
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

-- Create a policy to allow support and maintenance staff to read messages for their tasks
DROP POLICY IF EXISTS allow_support_maintenance_to_read_messages ON task_messages;
CREATE POLICY allow_support_maintenance_to_read_messages
ON task_messages
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM profiles
    WHERE profiles.id = auth.uid()
    AND (profiles.role = 'support' OR profiles.role = 'maintenance')
  )
);
