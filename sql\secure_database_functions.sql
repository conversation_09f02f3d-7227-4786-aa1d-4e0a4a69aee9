-- SQL file to create secure database functions that replace direct service role key usage
-- These functions use SECURITY DEFINER to run with elevated privileges
-- while still enforcing proper authorization checks

-- Function to get all users in the system (admin only)
CREATE OR REPLACE FUNCTION get_all_users()
RETURNS TABLE (
  id UUID,
  email TEXT,
  created_at TIMESTAMPTZ,
  last_sign_in_at TIMESTAMPTZ,
  email_confirmed_at TIMESTAMPTZ,
  role TEXT,
  organization_id UUID,
  first_name TEXT,
  last_name TEXT
)
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the current user is an admin
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Access denied: Only admins can view all users';
  END IF;

  RETURN QUERY
  SELECT 
    u.id,
    u.email,
    u.created_at,
    u.last_sign_in_at,
    u.email_confirmed_at,
    p.role,
    p.organization_id,
    p.first_name,
    p.last_name
  FROM 
    auth.users u
  LEFT JOIN
    public.profiles p ON u.id = p.id
  ORDER BY 
    u.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to delete a user (admin only)
CREATE OR REPLACE FUNCTION delete_user(user_id_param UUID)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the current user is an admin
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Access denied: Only admins can delete users';
  END IF;

  -- Delete the user from auth.users (this will cascade to profiles)
  DELETE FROM auth.users WHERE id = user_id_param;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to invite a user to an organization
CREATE OR REPLACE FUNCTION invite_user_to_organization(
  email_param TEXT,
  organization_id_param UUID,
  role_param TEXT
)
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
  invitation_id UUID;
  token TEXT;
  current_user_org_id UUID;
BEGIN
  -- Get the current user's organization_id
  SELECT organization_id INTO current_user_org_id
  FROM public.profiles
  WHERE id = auth.uid();
  
  -- Check if the current user is an admin in the organization
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin' AND organization_id = organization_id_param
  ) THEN
    -- Check if the user is a trust admin and the target organization is a school in their trust
    IF NOT EXISTS (
      SELECT 1 FROM public.profiles p
      JOIN public.organizations o ON p.organization_id = o.id
      WHERE p.id = auth.uid() 
        AND p.role = 'admin' 
        AND o.organization_type = 'trust'
        AND organization_id_param IN (
          SELECT id FROM public.organizations 
          WHERE parent_organization_id = p.organization_id
        )
    ) THEN
      RAISE EXCEPTION 'Access denied: Only organization admins can invite users';
    END IF;
  END IF;

  -- Generate a random token
  token := encode(gen_random_bytes(32), 'hex');
  
  -- Create the invitation
  INSERT INTO public.user_invitations (
    email, 
    organization_id, 
    role, 
    invited_by, 
    token,
    status,
    expires_at
  )
  VALUES (
    email_param,
    organization_id_param,
    role_param,
    auth.uid(),
    token,
    'pending',
    NOW() + INTERVAL '7 days'
  )
  RETURNING id INTO invitation_id;
  
  RETURN invitation_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get all invitations for an organization
CREATE OR REPLACE FUNCTION get_organization_invitations_secure(org_id UUID)
RETURNS TABLE (
  id UUID,
  email TEXT,
  role TEXT,
  status TEXT,
  created_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ,
  invited_by UUID
)
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the current user is an admin in the organization
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin' AND organization_id = org_id
  ) THEN
    -- Check if the user is a trust admin and the target organization is a school in their trust
    IF NOT EXISTS (
      SELECT 1 FROM public.profiles p
      JOIN public.organizations o ON p.organization_id = o.id
      WHERE p.id = auth.uid() 
        AND p.role = 'admin' 
        AND o.organization_type = 'trust'
        AND org_id IN (
          SELECT id FROM public.organizations 
          WHERE parent_organization_id = p.organization_id
        )
    ) THEN
      RAISE EXCEPTION 'Access denied: Only organization admins can view invitations';
    END IF;
  END IF;

  RETURN QUERY
  SELECT 
    i.id,
    i.email,
    i.role,
    i.status,
    i.created_at,
    i.expires_at,
    i.invited_by
  FROM 
    user_invitations i
  WHERE 
    i.organization_id = org_id
  ORDER BY 
    i.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to update a user's role (admin only)
CREATE OR REPLACE FUNCTION update_user_role(user_id_param UUID, role_param TEXT)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
  target_org_id UUID;
  current_user_org_id UUID;
BEGIN
  -- Get the target user's organization_id
  SELECT organization_id INTO target_org_id
  FROM public.profiles
  WHERE id = user_id_param;
  
  -- Get the current user's organization_id
  SELECT organization_id INTO current_user_org_id
  FROM public.profiles
  WHERE id = auth.uid();
  
  -- Check if the current user is an admin in the same organization
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin' AND organization_id = target_org_id
  ) THEN
    -- Check if the user is a trust admin and the target user is in a school in their trust
    IF NOT EXISTS (
      SELECT 1 FROM public.profiles p
      JOIN public.organizations o ON p.organization_id = o.id
      JOIN public.organizations school ON school.id = target_org_id
      WHERE p.id = auth.uid() 
        AND p.role = 'admin' 
        AND o.organization_type = 'trust'
        AND school.parent_organization_id = p.organization_id
    ) THEN
      RAISE EXCEPTION 'Access denied: Only organization admins can update user roles';
    END IF;
  END IF;

  -- Update the user's role
  UPDATE public.profiles
  SET role = role_param
  WHERE id = user_id_param;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to create an organization (authenticated users only)
CREATE OR REPLACE FUNCTION create_organization(
  name_param TEXT,
  organization_type_param TEXT,
  parent_organization_id_param UUID DEFAULT NULL,
  address_param TEXT DEFAULT NULL,
  city_param TEXT DEFAULT NULL,
  state_param TEXT DEFAULT NULL,
  zip_param TEXT DEFAULT NULL,
  phone_param TEXT DEFAULT NULL,
  website_param TEXT DEFAULT NULL
)
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
  org_id UUID;
BEGIN
  -- Insert the new organization
  INSERT INTO public.organizations (
    name,
    organization_type,
    parent_organization_id,
    address,
    city,
    state,
    zip,
    phone,
    website,
    created_at,
    updated_at
  )
  VALUES (
    name_param,
    organization_type_param,
    parent_organization_id_param,
    address_param,
    city_param,
    state_param,
    zip_param,
    phone_param,
    website_param,
    NOW(),
    NOW()
  )
  RETURNING id INTO org_id;
  
  -- Update the current user's profile to be an admin of this organization
  UPDATE public.profiles
  SET 
    organization_id = org_id,
    role = 'admin'
  WHERE id = auth.uid();
  
  RETURN org_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update an organization (admin only)
CREATE OR REPLACE FUNCTION update_organization(
  org_id_param UUID,
  name_param TEXT DEFAULT NULL,
  organization_type_param TEXT DEFAULT NULL,
  parent_organization_id_param UUID DEFAULT NULL,
  address_param TEXT DEFAULT NULL,
  city_param TEXT DEFAULT NULL,
  state_param TEXT DEFAULT NULL,
  zip_param TEXT DEFAULT NULL,
  phone_param TEXT DEFAULT NULL,
  website_param TEXT DEFAULT NULL
)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the current user is an admin in the organization
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin' AND organization_id = org_id_param
  ) THEN
    -- Check if the user is a trust admin and the target organization is a school in their trust
    IF NOT EXISTS (
      SELECT 1 FROM public.profiles p
      JOIN public.organizations o ON p.organization_id = o.id
      WHERE p.id = auth.uid() 
        AND p.role = 'admin' 
        AND o.organization_type = 'trust'
        AND org_id_param IN (
          SELECT id FROM public.organizations 
          WHERE parent_organization_id = p.organization_id
        )
    ) THEN
      RAISE EXCEPTION 'Access denied: Only organization admins can update the organization';
    END IF;
  END IF;

  -- Update the organization
  UPDATE public.organizations
  SET 
    name = COALESCE(name_param, name),
    organization_type = COALESCE(organization_type_param, organization_type),
    parent_organization_id = COALESCE(parent_organization_id_param, parent_organization_id),
    address = COALESCE(address_param, address),
    city = COALESCE(city_param, city),
    state = COALESCE(state_param, state),
    zip = COALESCE(zip_param, zip),
    phone = COALESCE(phone_param, phone),
    website = COALESCE(website_param, website),
    updated_at = NOW()
  WHERE id = org_id_param;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to get all schools in a trust
CREATE OR REPLACE FUNCTION get_trust_schools(trust_id_param UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  organization_type TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  zip TEXT,
  phone TEXT,
  website TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the current user is a member of the trust
  IF NOT EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() AND organization_id = trust_id_param
  ) THEN
    RAISE EXCEPTION 'Access denied: Only trust members can view trust schools';
  END IF;

  RETURN QUERY
  SELECT 
    o.id,
    o.name,
    o.organization_type,
    o.address,
    o.city,
    o.state,
    o.zip,
    o.phone,
    o.website,
    o.created_at,
    o.updated_at
  FROM 
    public.organizations o
  WHERE 
    o.parent_organization_id = trust_id_param
  ORDER BY 
    o.name;
END;
$$ LANGUAGE plpgsql;

-- Function to create a task message (bypasses RLS)
CREATE OR REPLACE FUNCTION create_task_message(
  task_id_param UUID,
  sender_id_param UUID,
  content_param TEXT
)
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
  message_id UUID;
  task_org_id UUID;
  sender_org_id UUID;
  task_visibility TEXT;
  task_assigned_to UUID;
  task_user_id UUID;
BEGIN
  -- Get task information
  SELECT 
    p.organization_id, t.visibility, t.assigned_to, t.user_id
  INTO 
    task_org_id, task_visibility, task_assigned_to, task_user_id
  FROM 
    public.tasks t
  JOIN
    public.profiles p ON t.user_id = p.id
  WHERE 
    t.id = task_id_param;
  
  -- Get sender's organization_id
  SELECT organization_id INTO sender_org_id
  FROM public.profiles
  WHERE id = sender_id_param;
  
  -- Check if sender has permission to message this task
  IF sender_id_param = task_user_id THEN
    -- Task owner can always message
    NULL;
  ELSIF sender_id_param = task_assigned_to THEN
    -- Assigned user can always message
    NULL;
  ELSIF task_visibility = 'public' THEN
    -- Public tasks can be messaged by anyone
    NULL;
  ELSIF sender_org_id = task_org_id THEN
    -- Users in the same organization can message internal tasks
    NULL;
  ELSE
    RAISE EXCEPTION 'Access denied: You do not have permission to message this task';
  END IF;

  -- Insert the message
  INSERT INTO public.task_messages (
    task_id,
    sender_id,
    content,
    created_at
  )
  VALUES (
    task_id_param,
    sender_id_param,
    content_param,
    NOW()
  )
  RETURNING id INTO message_id;
  
  RETURN message_id;
END;
$$ LANGUAGE plpgsql;
