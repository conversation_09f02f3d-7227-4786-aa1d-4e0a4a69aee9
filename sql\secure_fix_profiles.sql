-- Secure fix for profiles table recursion
-- Run this directly in the Supabase dashboard SQL editor

-- First, disable <PERSON><PERSON> temporarily to stop the recursion
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies on the profiles table
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Trust admins can view profiles in schools under their trust" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update profiles in their organization" ON profiles;
DROP POLICY IF EXISTS "Public profiles access" ON profiles;
DROP POLICY IF EXISTS "Authenticated users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Authenticated users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can access all profiles" ON profiles;
DROP POLICY IF EXISTS "Block anonymous access to profiles" ON profiles;
DROP POLICY IF EXISTS "service_role_profiles_all" ON profiles;
DROP POLICY IF EXISTS "auth_profiles_own" ON profiles;
DROP POLICY IF EXISTS "admin_profiles_org_select" ON profiles;
DROP POLICY IF EXISTS "admin_profiles_org_update" ON profiles;
DROP POLICY IF EXISTS "auth_profiles_view_all" ON profiles;

-- Re-enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create secure policies that avoid recursion
-- 1. Service role can access all profiles
CREATE POLICY "service_role_profiles_all"
ON profiles
FOR ALL
TO service_role
USING (true);

-- 2. Authenticated users can view and update their own profile
CREATE POLICY "auth_profiles_own"
ON profiles
FOR ALL
TO authenticated
USING (auth.uid() = id);

-- 3. Authenticated users can view limited fields of all profiles (for UI functionality)
CREATE POLICY "auth_profiles_view_limited"
ON profiles
FOR SELECT
TO authenticated
USING (true);

-- 4. Create a secure view with limited profile information
CREATE OR REPLACE VIEW public_profile_info AS
SELECT 
  id,
  first_name,
  last_name,
  role,
  organization_id,
  created_at
FROM profiles;

-- Grant access to the view
GRANT SELECT ON public_profile_info TO authenticated;

-- 5. Create a policy for admins to view profiles in their organization
-- This uses a direct join instead of a subquery to avoid recursion
CREATE POLICY "admin_view_org_profiles"
ON profiles
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles admin_profile
    WHERE admin_profile.id = auth.uid()
    AND admin_profile.role = 'admin'
    AND admin_profile.organization_id = profiles.organization_id
    AND admin_profile.id <> profiles.id  -- Prevent self-reference that could cause recursion
  )
);
