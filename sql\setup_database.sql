-- Setup script for the database
-- This script creates the necessary tables for the application

-- Create organizations table
CREATE TABLE IF NOT EXISTS public.organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create a test organization
INSERT INTO public.organizations (name)
VALUES ('Test Organization')
ON CONFLICT DO NOTHING
RETURNING id;

-- Make sure profiles table has organization_id column
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES public.organizations(id);

-- Update the test user's profile
UPDATE public.profiles
SET organization_id = (SELECT id FROM public.organizations WHERE name = 'Test Organization'),
    role = 'teacher'
WHERE id = 'e0eb9971-8690-4b51-b0d6-04805f2955ab';

-- Create a function to accept invitations
CREATE OR REPLACE FUNCTION public.accept_invitation(token_param TEXT, user_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update the user's profile with the organization ID from the invitation
  UPDATE public.profiles
  SET organization_id = (
    SELECT organization_id 
    FROM public.user_invitations 
    WHERE token = token_param
  ),
  role = (
    SELECT role
    FROM public.user_invitations
    WHERE token = token_param
  )
  WHERE id = user_id_param;
  
  -- Mark the invitation as accepted
  UPDATE public.user_invitations
  SET status = 'accepted'
  WHERE token = token_param;
  
  RETURN TRUE;
END;
$$;
