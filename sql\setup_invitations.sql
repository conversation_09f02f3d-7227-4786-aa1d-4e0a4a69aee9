-- Setup script for the invitation system
-- This script creates the user_invitations table and related functions

-- Create a table for user invitations if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL,
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  role TEXT NOT NULL,
  invited_by UUID NOT NULL REFERENCES auth.users(id),
  status TEXT NOT NULL DEFAULT 'pending',
  token TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  expires_at TIMESTAMPTZ NOT NULL DEFAULT (now() + interval '7 days'),
  UNIQUE(email, organization_id)
);

-- Enable RLS on the user_invitations table
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;

-- RLS policies for user_invitations
CREATE POLICY "Organization admins can view invitations"
ON public.user_invitations
FOR SELECT
USING (
  organization_id IN (
    SELECT organization_id FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

CREATE POLICY "Organization admins can create invitations"
ON public.user_invitations
FOR INSERT
WITH CHECK (
  organization_id IN (
    SELECT organization_id FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  ) AND
  invited_by = auth.uid()
);

CREATE POLICY "Organization admins can update invitations"
ON public.user_invitations
FOR UPDATE
USING (
  organization_id IN (
    SELECT organization_id FROM public.profiles 
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Function to create an invitation and send email
CREATE OR REPLACE FUNCTION invite_user(
  email_param TEXT,
  organization_id_param UUID,
  role_param TEXT
)
RETURNS UUID
SECURITY DEFINER
AS $$
DECLARE
  invitation_id UUID;
  token TEXT;
BEGIN
  -- Generate a random token
  token := encode(gen_random_bytes(32), 'hex');
  
  -- Create the invitation
  INSERT INTO public.user_invitations (
    email, 
    organization_id, 
    role, 
    invited_by, 
    token
  )
  VALUES (
    email_param,
    organization_id_param,
    role_param,
    auth.uid(),
    token
  )
  RETURNING id INTO invitation_id;
  
  -- In a real implementation, you would send an email here
  -- For now, we'll just return the invitation ID
  
  RETURN invitation_id;
END;
$$ LANGUAGE plpgsql;

-- Update the accept_invitation function to handle the user_invitations table
CREATE OR REPLACE FUNCTION accept_invitation(
  token_param TEXT,
  user_id_param UUID
)
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
DECLARE
  invitation RECORD;
BEGIN
  -- Find the invitation
  SELECT * INTO invitation
  FROM public.user_invitations
  WHERE token = token_param
  AND status = 'pending'
  AND expires_at > now()
  LIMIT 1;
  
  -- If invitation not found or expired
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Update the user's profile
  UPDATE public.profiles
  SET 
    organization_id = invitation.organization_id,
    role = invitation.role
  WHERE id = user_id_param;
  
  -- Mark invitation as accepted
  UPDATE public.user_invitations
  SET status = 'accepted'
  WHERE id = invitation.id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
