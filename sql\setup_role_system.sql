-- SQL script to set up the enhanced role management system
-- Run this in the Supabase SQL Editor

-- 1. Add is_site_admin column to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS is_site_admin BOOLEAN DEFAULT false;

-- 2. Create a function to check if a user is a site admin
CREATE OR REPLACE FUNCTION is_site_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM public.profiles 
    WHERE id = auth.uid() 
    AND is_site_admin = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create a function to check if a user has a specific role
CREATE OR REPLACE FUNCTION has_role(role_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = role_name
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Create a function to check if a user is an admin for an organization
CREATE OR REPLACE FUNCTION is_admin_for_org(org_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
    AND organization_id = org_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create RLS policies for site admins

-- Allow site admins to access all profiles
DROP POLICY IF EXISTS "Site admins can access all profiles" ON profiles;
CREATE POLICY "Site admins can access all profiles"
ON profiles
FOR ALL
TO authenticated
USING (is_site_admin());

-- Allow site admins to access all organizations
DROP POLICY IF EXISTS "Site admins can access all organizations" ON organizations;
CREATE POLICY "Site admins can access all organizations"
ON organizations
FOR ALL
TO authenticated
USING (is_site_admin());

-- Allow site admins to access all tasks
DROP POLICY IF EXISTS "Site admins can access all tasks" ON tasks;
CREATE POLICY "Site admins can access all tasks"
ON tasks
FOR ALL
TO authenticated
USING (is_site_admin());

-- Allow site admins to access all offers
DROP POLICY IF EXISTS "Site admins can access all offers" ON offers;
CREATE POLICY "Site admins can access all offers"
ON offers
FOR ALL
TO authenticated
USING (is_site_admin());

-- Allow site admins to access all user invitations
DROP POLICY IF EXISTS "Site admins can access all invitations" ON user_invitations;
CREATE POLICY "Site admins can access all invitations"
ON user_invitations
FOR ALL
TO authenticated
USING (is_site_admin());

-- 6. Create organization-specific policies

-- Organization admins can view all profiles in their organization
DROP POLICY IF EXISTS "Organization admins can view profiles" ON profiles;
CREATE POLICY "Organization admins can view profiles"
ON profiles
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM public.profiles admin
    WHERE admin.id = auth.uid() 
    AND admin.role = 'admin'
    AND admin.organization_id = profiles.organization_id
  )
);

-- Organization admins can update profiles in their organization
DROP POLICY IF EXISTS "Organization admins can update profiles" ON profiles;
CREATE POLICY "Organization admins can update profiles"
ON profiles
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM public.profiles admin
    WHERE admin.id = auth.uid() 
    AND admin.role = 'admin'
    AND admin.organization_id = profiles.organization_id
  )
);

-- 7. Set initial site admin (change email as needed)
UPDATE profiles
SET is_site_admin = true
WHERE email = ARRAY['<EMAIL>']
AND role = 'admin';
