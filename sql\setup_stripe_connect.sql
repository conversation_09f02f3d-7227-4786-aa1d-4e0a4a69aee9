-- SQL script to set up Stripe Connect integration
-- Run this in the Supabase SQL Editor

-- Create stripe_accounts table to store connected accounts information
CREATE TABLE IF NOT EXISTS public.stripe_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  account_id TEXT NOT NULL,
  account_type TEXT NOT NULL DEFAULT 'express',
  charges_enabled BOOLEAN NOT NULL DEFAULT false,
  payouts_enabled BOOLEAN NOT NULL DEFAULT false,
  account_status TEXT NOT NULL DEFAULT 'pending',
  refresh_token TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(user_id),
  UNIQUE(account_id)
);

-- Create payments table to store payment information
CREATE TABLE IF NOT EXISTS public.payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
  offer_id UUID NOT NULL REFERENCES public.offers(id) ON DELETE CASCADE,
  payer_id UUID NOT NULL REFERENCES auth.users(id),
  payee_id UUID NOT NULL REFERENCES auth.users(id),
  payment_intent_id TEXT,
  transfer_id TEXT,
  amount DECIMAL(10, 2) NOT NULL,
  platform_fee DECIMAL(10, 2) NOT NULL,
  supplier_amount DECIMAL(10, 2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  currency TEXT NOT NULL DEFAULT 'gbp',
  metadata JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create invoices table to store invoice information
CREATE TABLE IF NOT EXISTS public.invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_id UUID NOT NULL REFERENCES public.payments(id) ON DELETE CASCADE,
  invoice_number TEXT NOT NULL,
  invoice_url TEXT,
  status TEXT NOT NULL DEFAULT 'pending',
  due_date TIMESTAMPTZ,
  paid_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(invoice_number)
);

-- Add RLS policies for stripe_accounts table
ALTER TABLE public.stripe_accounts ENABLE ROW LEVEL SECURITY;

-- Users can only view their own stripe account
CREATE POLICY "Users can view their own stripe account" 
ON public.stripe_accounts
FOR SELECT
USING (user_id = auth.uid());

-- Users can only update their own stripe account
CREATE POLICY "Users can update their own stripe account" 
ON public.stripe_accounts
FOR UPDATE
USING (user_id = auth.uid());

-- Only the system can insert stripe accounts (via service role)
CREATE POLICY "System can insert stripe accounts" 
ON public.stripe_accounts
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Add RLS policies for payments table
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

-- Users can view payments they are involved in
CREATE POLICY "Users can view payments they are involved in" 
ON public.payments
FOR SELECT
USING (payer_id = auth.uid() OR payee_id = auth.uid());

-- Only admins can insert payments
CREATE POLICY "Admins can insert payments" 
ON public.payments
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
);

-- Only admins can update payments
CREATE POLICY "Admins can update payments" 
ON public.payments
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
);

-- Add RLS policies for invoices table
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;

-- Users can view invoices for payments they are involved in
CREATE POLICY "Users can view invoices for their payments" 
ON public.invoices
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.payments
    WHERE payments.id = invoices.payment_id
    AND (payments.payer_id = auth.uid() OR payments.payee_id = auth.uid())
  )
);

-- Only admins can insert invoices
CREATE POLICY "Admins can insert invoices" 
ON public.invoices
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
);

-- Only admins can update invoices
CREATE POLICY "Admins can update invoices" 
ON public.invoices
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
);

-- Add task status for payment
ALTER TABLE public.tasks
ADD COLUMN IF NOT EXISTS payment_status TEXT DEFAULT 'unpaid';

-- Add stripe_account_id to profiles table
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS stripe_account_id TEXT;

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update timestamps
CREATE TRIGGER update_stripe_accounts_updated_at
BEFORE UPDATE ON public.stripe_accounts
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_payments_updated_at
BEFORE UPDATE ON public.payments
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_invoices_updated_at
BEFORE UPDATE ON public.invoices
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();