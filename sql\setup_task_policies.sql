-- SQL script to set up task-related RLS policies
-- Run this in the Supabase SQL Editor

-- 1. Create role-specific policies for tasks

-- Teachers can create tasks
DROP POLICY IF EXISTS "Teachers can create tasks" ON tasks;
CREATE POLICY "Teachers can create tasks"
ON tasks
FOR INSERT
TO authenticated
WITH CHECK (
  has_role('teacher') OR has_role('admin') OR is_site_admin()
);

-- Teachers can update their own tasks
DROP POLICY IF EXISTS "Teachers can update their own tasks" ON tasks;
CREATE POLICY "Teachers can update their own tasks"
ON tasks
FOR UPDATE
TO authenticated
USING (
  user_id = auth.uid() AND has_role('teacher')
);

-- Maintenance staff can update assigned tasks
DROP POLICY IF EXISTS "Maintenance staff can update assigned tasks" ON tasks;
CREATE POLICY "Maintenance staff can update assigned tasks"
ON tasks
FOR UPDATE
TO authenticated
USING (
  assigned_to = auth.uid() AND has_role('maintenance')
);

-- Support staff can update assigned tasks
DROP POLICY IF EXISTS "Support staff can update assigned tasks" ON tasks;
CREATE POLICY "Support staff can update assigned tasks"
ON tasks
FOR UPDATE
TO authenticated
USING (
  assigned_to = auth.uid() AND has_role('support')
);

-- Organization admins can update any task in their organization
DROP POLICY IF EXISTS "Organization admins can update tasks" ON tasks;
CREATE POLICY "Organization admins can update tasks"
ON tasks
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM public.profiles admin
    WHERE admin.id = auth.uid() 
    AND admin.role = 'admin'
    AND admin.organization_id = tasks.organization_id
  )
);

-- 2. Create role-based task view policies

-- Teachers can view their own tasks and public tasks in their organization
DROP POLICY IF EXISTS "Teachers can view tasks" ON tasks;
CREATE POLICY "Teachers can view tasks"
ON tasks
FOR SELECT
TO authenticated
USING (
  (
    has_role('teacher') AND 
    (
      user_id = auth.uid() OR 
      (
        visibility = 'public' AND
        organization_id = (
          SELECT organization_id 
          FROM profiles 
          WHERE id = auth.uid()
        )
      )
    )
  )
);

-- Maintenance and support staff can view tasks assigned to them
DROP POLICY IF EXISTS "Staff can view assigned tasks" ON tasks;
CREATE POLICY "Staff can view assigned tasks"
ON tasks
FOR SELECT
TO authenticated
USING (
  (
    (has_role('maintenance') OR has_role('support')) AND
    assigned_to = auth.uid()
  )
);

-- Organization admins can view all tasks in their organization
DROP POLICY IF EXISTS "Organization admins can view tasks" ON tasks;
CREATE POLICY "Organization admins can view tasks"
ON tasks
FOR SELECT
TO authenticated
USING (
  (
    has_role('admin') AND
    organization_id = (
      SELECT organization_id 
      FROM profiles 
      WHERE id = auth.uid()
    )
  )
);

-- Suppliers can view public tasks
DROP POLICY IF EXISTS "Suppliers can view public tasks" ON tasks;
CREATE POLICY "Suppliers can view public tasks"
ON tasks
FOR SELECT
TO authenticated
USING (
  (
    EXISTS (
      SELECT 1 
      FROM profiles p
      WHERE p.id = auth.uid() 
      AND p.account_type = 'supplier'
    ) AND
    visibility = 'public'
  )
);
