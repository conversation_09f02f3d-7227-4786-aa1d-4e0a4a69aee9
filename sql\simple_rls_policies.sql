-- Simple RLS policies to block anonymous access
-- Run this directly in the Supabase dashboard SQL editor

-- =============================================
-- Profiles Table
-- =============================================

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create a policy to block anonymous access
CREATE POLICY "Block anonymous access to profiles"
ON profiles
FOR SELECT
USING (auth.role() = 'authenticated');

-- =============================================
-- Organizations Table
-- =============================================

-- Enable RLS
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Create a policy to block anonymous access
CREATE POLICY "Block anonymous access to organizations"
ON organizations
FOR SELECT
USING (auth.role() = 'authenticated');

-- =============================================
-- User Invitations Table
-- =============================================

-- Enable RLS
ALTER TABLE user_invitations ENABLE ROW LEVEL SECURITY;

-- Create a policy to block anonymous access
CREATE POLICY "Block anonymous access to user_invitations"
ON user_invitations
FOR SELECT
USING (auth.role() = 'authenticated');

-- =============================================
-- Tasks Table
-- =============================================

-- Enable RLS
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;

-- Create a policy to block anonymous access
CREATE POLICY "Block anonymous access to tasks"
ON tasks
FOR SELECT
USING (auth.role() = 'authenticated');

-- =============================================
-- Task Messages Table
-- =============================================

-- Enable RLS
ALTER TABLE task_messages ENABLE ROW LEVEL SECURITY;

-- Create a policy to block anonymous access
CREATE POLICY "Block anonymous access to task_messages"
ON task_messages
FOR SELECT
USING (auth.role() = 'authenticated');
