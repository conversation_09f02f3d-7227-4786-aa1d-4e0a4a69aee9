-- Create a simplified version of the accept_invitation function
CREATE OR REPLACE FUNCTION public.accept_invitation(token_param TEXT, user_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  invitation RECORD;
  user_email TEXT;
BEGIN
  -- Find the invitation regardless of status
  SELECT * INTO invitation
  FROM public.user_invitations
  WHERE token = token_param;
  
  -- If invitation not found
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invitation not found with token: %', token_param;
    RETURN FALSE;
  END IF;
  
  -- Get the user's email
  SELECT email INTO user_email
  FROM auth.users
  WHERE id = user_id_param;
  
  -- If user not found
  IF user_email IS NULL THEN
    RAISE EXCEPTION 'User not found with ID: %', user_id_param;
    RETURN FALSE;
  END IF;
  
  -- Always update the profile with the organization ID and role
  UPDATE public.profiles
  SET 
    organization_id = invitation.organization_id,
    role = invitation.role,
    email = ARRAY[user_email]
  WHERE id = user_id_param;
  
  -- Always update the invitation status to accepted
  UPDATE public.user_invitations
  SET status = 'accepted'
  WHERE token = token_param;
  
  RETURN TRUE;
END;
$$;
