-- Strengthen Organization Security Policies
-- This script ensures complete organization-level data isolation

-- First, ensure all tables have organization_id columns where needed
ALTER TABLE public.tasks ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES public.organizations(id);
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES public.organizations(id);
ALTER TABLE public.task_messages ADD COLUMN IF NOT EXISTS organization_id UUID;
ALTER TABLE public.task_comments ADD COLUMN IF NOT EXISTS organization_id UUID;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tasks_organization_id ON public.tasks(organization_id);
CREATE INDEX IF NOT EXISTS idx_profiles_organization_id ON public.profiles(organization_id);
CREATE INDEX IF NOT EXISTS idx_task_messages_organization_id ON public.task_messages(organization_id);
CREATE INDEX IF NOT EXISTS idx_task_comments_organization_id ON public.task_comments(organization_id);

-- Drop existing policies to recreate them with stronger organization isolation
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Organization admins can view tasks" ON public.tasks;
DROP POLICY IF EXISTS "Teachers can view tasks" ON public.tasks;
DROP POLICY IF EXISTS "Staff can view assigned tasks" ON public.tasks;
DROP POLICY IF EXISTS "Suppliers can view public tasks" ON public.tasks;
DROP POLICY IF EXISTS "auth_tasks_select_public" ON public.tasks;

-- Profiles policies with organization isolation
CREATE POLICY "Users can view profiles in their organization"
ON public.profiles
FOR SELECT
TO authenticated
USING (
  -- Users can view their own profile
  id = auth.uid()
  OR
  -- Users can view profiles in their organization
  (
    organization_id IS NOT NULL AND
    organization_id = (
      SELECT organization_id
      FROM public.profiles
      WHERE id = auth.uid()
    )
  )
  OR
  -- Site admins can view all profiles
  EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE id = auth.uid() AND is_site_admin = true
  )
);

CREATE POLICY "Users can update their own profile"
ON public.profiles
FOR UPDATE
TO authenticated
USING (id = auth.uid())
WITH CHECK (id = auth.uid());

-- Tasks policies with strict organization isolation
CREATE POLICY "Organization members can view tasks in their organization"
ON public.tasks
FOR SELECT
TO authenticated
USING (
  -- Site admins can see all tasks
  EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE id = auth.uid() AND is_site_admin = true
  )
  OR
  -- Organization members can see tasks in their organization
  (
    organization_id IS NOT NULL AND
    organization_id = (
      SELECT organization_id
      FROM public.profiles
      WHERE id = auth.uid()
    )
    AND
    (
      -- Admins can see all tasks in their organization
      (
        EXISTS (
          SELECT 1
          FROM public.profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      )
      OR
      -- Teachers can see their own tasks or public tasks in their organization
      (
        EXISTS (
          SELECT 1
          FROM public.profiles
          WHERE id = auth.uid() AND role = 'teacher'
        )
        AND (user_id = auth.uid() OR visibility = 'public')
      )
      OR
      -- Maintenance/support staff can see tasks assigned to them
      (
        EXISTS (
          SELECT 1
          FROM public.profiles
          WHERE id = auth.uid() AND role IN ('maintenance', 'support')
        )
        AND assigned_to = auth.uid()
      )
    )
  )
  OR
  -- Suppliers can see public tasks (across organizations for marketplace functionality)
  (
    EXISTS (
      SELECT 1
      FROM public.profiles
      WHERE id = auth.uid() AND account_type = 'supplier'
    )
    AND visibility = 'public'
    AND status IN ('open', 'interest', 'questions', 'offer')
  )
);

CREATE POLICY "Users can create tasks in their organization"
ON public.tasks
FOR INSERT
TO authenticated
WITH CHECK (
  -- Users can only create tasks in their own organization
  organization_id = (
    SELECT organization_id
    FROM public.profiles
    WHERE id = auth.uid()
  )
  AND user_id = auth.uid()
);

CREATE POLICY "Users can update tasks in their organization"
ON public.tasks
FOR UPDATE
TO authenticated
USING (
  -- Site admins can update all tasks
  EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE id = auth.uid() AND is_site_admin = true
  )
  OR
  -- Task owners can update their own tasks
  user_id = auth.uid()
  OR
  -- Admins can update tasks in their organization
  (
    EXISTS (
      SELECT 1
      FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
    AND organization_id = (
      SELECT organization_id
      FROM public.profiles
      WHERE id = auth.uid()
    )
  )
  OR
  -- Assigned users can update their assigned tasks
  assigned_to = auth.uid()
)
WITH CHECK (
  -- Ensure organization_id doesn't change to a different organization
  organization_id = (
    SELECT organization_id
    FROM public.profiles
    WHERE id = auth.uid()
  )
  OR
  -- Site admins can change organization_id
  EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE id = auth.uid() AND is_site_admin = true
  )
  -- Additional check: if assigned_to is being set, ensure the assignee is in the same organization
  AND (
    assigned_to IS NULL
    OR assigned_to = auth.uid()
    OR EXISTS (
      SELECT 1
      FROM public.profiles p1, public.profiles p2
      WHERE p1.id = auth.uid()
      AND p2.id = assigned_to
      AND (
        p1.is_site_admin = true
        OR p1.organization_id = p2.organization_id
      )
    )
  )
);

-- Task messages policies with organization isolation
CREATE POLICY "Users can view messages in their organization"
ON public.task_messages
FOR SELECT
TO authenticated
USING (
  -- Site admins can see all messages
  EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE id = auth.uid() AND is_site_admin = true
  )
  OR
  -- Users can see messages for tasks in their organization
  EXISTS (
    SELECT 1
    FROM public.tasks t
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE t.id = task_id
    AND (
      t.organization_id = p.organization_id
      OR p.account_type = 'supplier' -- Suppliers can see messages for public tasks they're involved in
    )
  )
);

CREATE POLICY "Users can create messages in their organization"
ON public.task_messages
FOR INSERT
TO authenticated
WITH CHECK (
  sender_id = auth.uid()
  AND
  EXISTS (
    SELECT 1
    FROM public.tasks t
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE t.id = task_id
    AND (
      -- Task is in user's organization
      t.organization_id = p.organization_id
      OR
      -- Supplier can message public tasks
      (p.account_type = 'supplier' AND t.visibility = 'public')
    )
  )
);

-- Task comments policies with organization isolation
CREATE POLICY "Users can view comments in their organization"
ON public.task_comments
FOR SELECT
TO authenticated
USING (
  -- Site admins can see all comments
  EXISTS (
    SELECT 1
    FROM public.profiles
    WHERE id = auth.uid() AND is_site_admin = true
  )
  OR
  -- Users can see comments for tasks in their organization
  EXISTS (
    SELECT 1
    FROM public.tasks t
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE t.id = task_id
    AND t.organization_id = p.organization_id
  )
);

CREATE POLICY "Users can create comments in their organization"
ON public.task_comments
FOR INSERT
TO authenticated
WITH CHECK (
  user_id = auth.uid()
  AND
  EXISTS (
    SELECT 1
    FROM public.tasks t
    JOIN public.profiles p ON p.id = auth.uid()
    WHERE t.id = task_id
    AND t.organization_id = p.organization_id
  )
);

-- Function to automatically set organization_id for task messages and comments
CREATE OR REPLACE FUNCTION set_organization_id_from_task()
RETURNS TRIGGER AS $$
BEGIN
  -- Set organization_id based on the task's organization_id
  SELECT organization_id INTO NEW.organization_id
  FROM public.tasks
  WHERE id = NEW.task_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers to automatically set organization_id
DROP TRIGGER IF EXISTS set_task_message_organization_id ON public.task_messages;
CREATE TRIGGER set_task_message_organization_id
  BEFORE INSERT ON public.task_messages
  FOR EACH ROW
  EXECUTE FUNCTION set_organization_id_from_task();

DROP TRIGGER IF EXISTS set_task_comment_organization_id ON public.task_comments;
CREATE TRIGGER set_task_comment_organization_id
  BEFORE INSERT ON public.task_comments
  FOR EACH ROW
  EXECUTE FUNCTION set_organization_id_from_task();

-- Update existing records to have organization_id
UPDATE public.task_messages
SET organization_id = (
  SELECT organization_id
  FROM public.tasks
  WHERE tasks.id = task_messages.task_id
)
WHERE organization_id IS NULL;

UPDATE public.task_comments
SET organization_id = (
  SELECT organization_id
  FROM public.tasks
  WHERE tasks.id = task_comments.task_id
)
WHERE organization_id IS NULL;
