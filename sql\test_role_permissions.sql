-- SQL script to test role-based permissions
-- Run this in the Supabase SQL Editor

-- 1. Create a test organization if it doesn't exist
DO $$
DECLARE
  test_org_id UUID;
BEGIN
  -- Check if test organization exists
  SELECT id INTO test_org_id
  FROM organizations
  WHERE name = 'Test Organization'
  LIMIT 1;
  
  -- Create test organization if it doesn't exist
  IF test_org_id IS NULL THEN
    INSERT INTO organizations (name, type, address, city, postal_code, country)
    VALUES ('Test Organization', 'school', '123 Test St', 'Test City', 'TE1 1ST', 'United Kingdom')
    RETURNING id INTO test_org_id;
    
    RAISE NOTICE 'Created test organization with ID: %', test_org_id;
  ELSE
    RAISE NOTICE 'Using existing test organization with ID: %', test_org_id;
  END IF;
END $$;

-- 2. Create test tasks for each role
DO $$
DECLARE
  test_org_id UUID;
  site_admin_id UUID;
  org_admin_id UUID;
  teacher_id UUID;
  maintenance_id UUID;
  support_id UUID;
  supplier_id UUID;
BEGIN
  -- Get test organization ID
  SELECT id INTO test_org_id
  FROM organizations
  WHERE name = 'Test Organization'
  LIMIT 1;
  
  -- Get user IDs
  SELECT id INTO site_admin_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  SELECT id INTO org_admin_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  SELECT id INTO teacher_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  SELECT id INTO maintenance_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  SELECT id INTO support_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  SELECT id INTO supplier_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  -- Create test tasks if they don't exist
  -- Teacher task (admin visibility)
  IF NOT EXISTS (SELECT 1 FROM tasks WHERE title = 'Teacher Test Task') THEN
    INSERT INTO tasks (
      title, 
      description, 
      status, 
      visibility, 
      user_id, 
      organization_id
    )
    VALUES (
      'Teacher Test Task',
      'This is a test task created by a teacher',
      'pending',
      'admin',
      teacher_id,
      test_org_id
    );
    
    RAISE NOTICE 'Created teacher test task';
  END IF;
  
  -- Admin task (internal visibility)
  IF NOT EXISTS (SELECT 1 FROM tasks WHERE title = 'Admin Test Task') THEN
    INSERT INTO tasks (
      title, 
      description, 
      status, 
      visibility, 
      user_id, 
      organization_id,
      assigned_to
    )
    VALUES (
      'Admin Test Task',
      'This is a test task created by an admin and assigned to maintenance',
      'in_progress',
      'internal',
      org_admin_id,
      test_org_id,
      maintenance_id
    );
    
    RAISE NOTICE 'Created admin test task';
  END IF;
  
  -- Public task
  IF NOT EXISTS (SELECT 1 FROM tasks WHERE title = 'Public Test Task') THEN
    INSERT INTO tasks (
      title, 
      description, 
      status, 
      visibility, 
      user_id, 
      organization_id
    )
    VALUES (
      'Public Test Task',
      'This is a public test task visible to suppliers',
      'pending',
      'public',
      org_admin_id,
      test_org_id
    );
    
    RAISE NOTICE 'Created public test task';
  END IF;
END $$;

-- 3. Test role-based access to tasks
DO $$
DECLARE
  site_admin_id UUID;
  org_admin_id UUID;
  teacher_id UUID;
  maintenance_id UUID;
  support_id UUID;
  supplier_id UUID;
  
  teacher_task_id UUID;
  admin_task_id UUID;
  public_task_id UUID;
  
  task_count INT;
BEGIN
  -- Get user IDs
  SELECT id INTO site_admin_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  SELECT id INTO org_admin_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  SELECT id INTO teacher_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  SELECT id INTO maintenance_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  SELECT id INTO support_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  SELECT id INTO supplier_id
  FROM profiles
  WHERE email = ARRAY['<EMAIL>']
  LIMIT 1;
  
  -- Get task IDs
  SELECT id INTO teacher_task_id
  FROM tasks
  WHERE title = 'Teacher Test Task'
  LIMIT 1;
  
  SELECT id INTO admin_task_id
  FROM tasks
  WHERE title = 'Admin Test Task'
  LIMIT 1;
  
  SELECT id INTO public_task_id
  FROM tasks
  WHERE title = 'Public Test Task'
  LIMIT 1;
  
  -- Test site admin access (should see all tasks)
  SET LOCAL ROLE authenticated;
  SET LOCAL "request.jwt.claims" TO '{"sub": "' || site_admin_id || '", "role": "authenticated"}';
  
  SELECT COUNT(*) INTO task_count
  FROM tasks;
  
  RAISE NOTICE 'Site admin can see % tasks', task_count;
  
  -- Test org admin access (should see all tasks in their organization)
  SET LOCAL ROLE authenticated;
  SET LOCAL "request.jwt.claims" TO '{"sub": "' || org_admin_id || '", "role": "authenticated"}';
  
  SELECT COUNT(*) INTO task_count
  FROM tasks;
  
  RAISE NOTICE 'Org admin can see % tasks', task_count;
  
  -- Test teacher access (should see own tasks and public tasks)
  SET LOCAL ROLE authenticated;
  SET LOCAL "request.jwt.claims" TO '{"sub": "' || teacher_id || '", "role": "authenticated"}';
  
  SELECT COUNT(*) INTO task_count
  FROM tasks;
  
  RAISE NOTICE 'Teacher can see % tasks', task_count;
  
  -- Test maintenance access (should see assigned tasks)
  SET LOCAL ROLE authenticated;
  SET LOCAL "request.jwt.claims" TO '{"sub": "' || maintenance_id || '", "role": "authenticated"}';
  
  SELECT COUNT(*) INTO task_count
  FROM tasks;
  
  RAISE NOTICE 'Maintenance staff can see % tasks', task_count;
  
  -- Test supplier access (should see public tasks)
  SET LOCAL ROLE authenticated;
  SET LOCAL "request.jwt.claims" TO '{"sub": "' || supplier_id || '", "role": "authenticated"}';
  
  SELECT COUNT(*) INTO task_count
  FROM tasks;
  
  RAISE NOTICE 'Supplier can see % tasks', task_count;
  
  -- Reset role
  RESET ROLE;
  RESET "request.jwt.claims";
END $$;
