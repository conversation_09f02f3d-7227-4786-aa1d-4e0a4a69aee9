-- SQL script to test the role management system
-- Run this in the Supabase SQL Editor

-- 1. Test site admin functions
DO $$
DECLARE
  test_user_id UUID;
  result BOOLEAN;
BEGIN
  -- Get a user ID that is set as site admin
  SELECT id INTO test_user_id
  FROM profiles
  WHERE is_site_admin = true
  LIMIT 1;
  
  IF test_user_id IS NULL THEN
    RAISE NOTICE 'No site admin found for testing';
    RETURN;
  END IF;
  
  -- Test the is_site_admin function
  -- Note: This will return false because we're not authenticated as the test user
  result := is_site_admin();
  RAISE NOTICE 'is_site_admin() = %', result;
  
  -- Test the has_role function
  -- Note: This will return false because we're not authenticated as the test user
  result := has_role('admin');
  RAISE NOTICE 'has_role(''admin'') = %', result;
  
  RAISE NOTICE 'Site admin functions tested. Note: Expected false results because we are not authenticated as the test user.';
END $$;

-- 2. Create test users for each role if they don't exist
DO $$
DECLARE
  org_id UUID;
BEGIN
  -- Get an organization ID
  SELECT id INTO org_id
  FROM organizations
  LIMIT 1;
  
  IF org_id IS NULL THEN
    RAISE NOTICE 'No organization found for testing';
    RETURN;
  END IF;
  
  -- Create test users if they don't exist
  -- Site Admin
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    INSERT INTO auth.users (email, email_confirmed_at, raw_user_meta_data)
    VALUES ('<EMAIL>', now(), '{"name": "Site Admin"}');
    
    INSERT INTO profiles (id, email, first_name, last_name, role, is_site_admin, organization_id)
    VALUES (
      (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
      ARRAY['<EMAIL>'],
      'Site',
      'Admin',
      'admin',
      true,
      org_id
    );
    
    RAISE NOTICE 'Created site admin test user';
  END IF;
  
  -- Organization Admin
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    INSERT INTO auth.users (email, email_confirmed_at, raw_user_meta_data)
    VALUES ('<EMAIL>', now(), '{"name": "Org Admin"}');
    
    INSERT INTO profiles (id, email, first_name, last_name, role, is_site_admin, organization_id)
    VALUES (
      (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
      ARRAY['<EMAIL>'],
      'Org',
      'Admin',
      'admin',
      false,
      org_id
    );
    
    RAISE NOTICE 'Created organization admin test user';
  END IF;
  
  -- Teacher
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    INSERT INTO auth.users (email, email_confirmed_at, raw_user_meta_data)
    VALUES ('<EMAIL>', now(), '{"name": "Teacher User"}');
    
    INSERT INTO profiles (id, email, first_name, last_name, role, is_site_admin, organization_id)
    VALUES (
      (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
      ARRAY['<EMAIL>'],
      'Teacher',
      'User',
      'teacher',
      false,
      org_id
    );
    
    RAISE NOTICE 'Created teacher test user';
  END IF;
  
  -- Maintenance
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    INSERT INTO auth.users (email, email_confirmed_at, raw_user_meta_data)
    VALUES ('<EMAIL>', now(), '{"name": "Maintenance User"}');
    
    INSERT INTO profiles (id, email, first_name, last_name, role, is_site_admin, organization_id)
    VALUES (
      (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
      ARRAY['<EMAIL>'],
      'Maintenance',
      'User',
      'maintenance',
      false,
      org_id
    );
    
    RAISE NOTICE 'Created maintenance test user';
  END IF;
  
  -- Support
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    INSERT INTO auth.users (email, email_confirmed_at, raw_user_meta_data)
    VALUES ('<EMAIL>', now(), '{"name": "Support User"}');
    
    INSERT INTO profiles (id, email, first_name, last_name, role, is_site_admin, organization_id)
    VALUES (
      (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
      ARRAY['<EMAIL>'],
      'Support',
      'User',
      'support',
      false,
      org_id
    );
    
    RAISE NOTICE 'Created support test user';
  END IF;
  
  -- Supplier
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE email = '<EMAIL>') THEN
    INSERT INTO auth.users (email, email_confirmed_at, raw_user_meta_data)
    VALUES ('<EMAIL>', now(), '{"name": "Supplier User"}');
    
    INSERT INTO profiles (id, email, first_name, last_name, role, account_type, is_site_admin)
    VALUES (
      (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
      ARRAY['<EMAIL>'],
      'Supplier',
      'User',
      'supplier',
      'supplier',
      false
    );
    
    RAISE NOTICE 'Created supplier test user';
  END IF;
END $$;
