-- Update the accept_invitation function to handle both pending and accepted invitations
CREATE OR REPLACE FUNCTION public.accept_invitation(token_param TEXT, user_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  invitation RECORD;
  user_email TEXT;
BEGIN
  -- Find the invitation regardless of status
  SELECT * INTO invitation
  FROM public.user_invitations
  WHERE token = token_param
  AND expires_at > now();
  
  -- If invitation not found or expired
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Get the user's email
  SELECT email INTO user_email
  FROM auth.users
  WHERE id = user_id_param;
  
  -- Update the user's profile
  UPDATE public.profiles
  SET 
    organization_id = invitation.organization_id,
    role = invitation.role,
    email = ARRAY[user_email]
  WHERE id = user_id_param;
  
  -- Mark invitation as accepted if it's not already
  IF invitation.status != 'accepted' THEN
    UPDATE public.user_invitations
    SET status = 'accepted'
    WHERE id = invitation.id;
  END IF;
  
  RETURN TRUE;
END;
$$;
