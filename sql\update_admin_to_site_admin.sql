-- Update <EMAIL> to be a site admin
UPDATE profiles
SET is_site_admin = true
WHERE email = ARRAY['<EMAIL>']
AND role = 'admin';

-- Verify the update
SELECT 
  id,
  email,
  role,
  is_site_admin,
  organization_id
FROM 
  profiles
WHERE 
  email = ARRAY['<EMAIL>'];

-- Check if the is_site_admin function exists
SELECT 
  routine_name,
  routine_type,
  data_type
FROM 
  information_schema.routines
WHERE 
  routine_name = 'is_site_admin'
  AND routine_schema = 'public';
