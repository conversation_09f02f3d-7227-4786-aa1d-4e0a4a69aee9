-- Update all users' profiles with their emails from the Auth system

-- Create a temporary table to store the mapping between user IDs and emails
CREATE TEMP TABLE user_emails AS
SELECT id, email FROM auth.users;

-- Update the profiles table with the emails from the Auth system
UPDATE profiles p
SET 
  email = ARRAY[u.email]
FROM user_emails u
WHERE 
  p.id = u.id
  AND (p.email IS NULL OR p.email = '{}' OR p.email = '{null}');
