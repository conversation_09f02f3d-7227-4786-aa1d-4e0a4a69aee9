-- Update the invoices table to add stripe_invoice_id field

-- Check if stripe_invoice_id column exists, if not add it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'invoices' 
        AND column_name = 'stripe_invoice_id'
    ) THEN
        ALTER TABLE public.invoices
        ADD COLUMN stripe_invoice_id TEXT;
    END IF;
END $$;

-- Add index on stripe_invoice_id for faster lookups
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE tablename = 'invoices'
        AND indexname = 'invoices_stripe_invoice_id_idx'
    ) THEN
        CREATE INDEX invoices_stripe_invoice_id_idx ON public.invoices(stripe_invoice_id);
    END IF;
END $$;

-- Update RLS policies for invoices table
-- Users can view invoices for payments they are involved in
DROP POLICY IF EXISTS "Users can view invoices for their payments" ON public.invoices;
CREATE POLICY "Users can view invoices for their payments" 
ON public.invoices
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.payments
    WHERE payments.id = invoices.payment_id
    AND (payments.payer_id = auth.uid() OR payments.payee_id = auth.uid())
  )
);

-- Only service role can insert invoices
DROP POLICY IF EXISTS "Service role can insert invoices" ON public.invoices;
CREATE POLICY "Service role can insert invoices" 
ON public.invoices
FOR INSERT
WITH CHECK (
  auth.role() = 'service_role'
);

-- Only service role can update invoices
DROP POLICY IF EXISTS "Service role can update invoices" ON public.invoices;
CREATE POLICY "Service role can update invoices" 
ON public.invoices
FOR UPDATE
USING (
  auth.role() = 'service_role'
);
