-- SQL script to update triggers for notifications to include suppliers

-- Update the message notification function to also notify suppliers
CREATE OR REPLACE FUNCTION create_message_notification()
RETURNS TRIGGER AS $$
DECLARE
  task_owner_id UUID;
  task_title TEXT;
  sender_name TEXT;
  recipient_id UUID;
BEGIN
  -- Get task owner and title
  SELECT user_id, title INTO task_owner_id, task_title
  FROM public.tasks
  WHERE id = NEW.task_id;
  
  -- Get sender name
  SELECT 
    CASE 
      WHEN first_name IS NOT NULL AND last_name IS NOT NULL THEN
        first_name || ' ' || last_name
      ELSE
        'A user'
    END INTO sender_name
  FROM public.profiles
  WHERE id = NEW.sender_id;
  
  -- If sender is the task owner, notify suppliers who have made offers
  IF NEW.sender_id = task_owner_id THEN
    -- Find suppliers who have made offers on this task
    FOR recipient_id IN 
      SELECT DISTINCT user_id 
      FROM public.offers 
      WHERE task_id = NEW.task_id
    LOOP
      -- Create notification for each supplier
      INSERT INTO public.notifications (
        user_id,
        type,
        message,
        related_id,
        related_type,
        read,
        email_sent
      ) VALUES (
        recipient_id,
        'message',
        'You have received a new message from ' || sender_name || ' regarding "' || task_title || '".',
        NEW.task_id::text,
        'message',
        false,
        false
      );
    END LOOP;
  -- If sender is not the task owner, notify the task owner
  ELSIF NEW.sender_id <> task_owner_id THEN
    -- Create notification for task owner
    INSERT INTO public.notifications (
      user_id,
      type,
      message,
      related_id,
      related_type,
      read,
      email_sent
    ) VALUES (
      task_owner_id,
      'message',
      'You have received a new message from ' || sender_name || ' regarding "' || task_title || '".',
      NEW.task_id::text,
      'message',
      false,
      false
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function for offer status update notifications
CREATE OR REPLACE FUNCTION create_offer_update_notification()
RETURNS TRIGGER AS $$
DECLARE
  task_title TEXT;
  owner_name TEXT;
BEGIN
  -- Only proceed if status has changed
  IF OLD.status = NEW.status THEN
    RETURN NEW;
  END IF;
  
  -- Get task title
  SELECT title INTO task_title
  FROM public.tasks
  WHERE id = NEW.task_id;
  
  -- Get task owner name
  SELECT 
    CASE 
      WHEN first_name IS NOT NULL AND last_name IS NOT NULL THEN
        first_name || ' ' || last_name
      ELSE
        'The task owner'
    END INTO owner_name
  FROM public.profiles p
  JOIN public.tasks t ON p.id = t.user_id
  WHERE t.id = NEW.task_id;
  
  -- Create appropriate notification based on new status
  IF NEW.status = 'accepted' THEN
    -- Notify supplier that their offer was accepted
    INSERT INTO public.notifications (
      user_id,
      type,
      message,
      related_id,
      related_type,
      read,
      email_sent
    ) VALUES (
      NEW.user_id,
      'offer',
      'Your offer of £' || NEW.amount::text || ' on "' || task_title || '" has been accepted!',
      NEW.task_id::text,
      'offer',
      false,
      false
    );
  ELSIF NEW.status = 'rejected' THEN
    -- Notify supplier that their offer was rejected
    INSERT INTO public.notifications (
      user_id,
      type,
      message,
      related_id,
      related_type,
      read,
      email_sent
    ) VALUES (
      NEW.user_id,
      'offer',
      'Your offer of £' || NEW.amount::text || ' on "' || task_title || '" has been declined.',
      NEW.task_id::text,
      'offer',
      false,
      false
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for offer status updates
DROP TRIGGER IF EXISTS offer_update_notification_trigger ON public.offers;
CREATE TRIGGER offer_update_notification_trigger
AFTER UPDATE ON public.offers
FOR EACH ROW
EXECUTE FUNCTION create_offer_update_notification();
