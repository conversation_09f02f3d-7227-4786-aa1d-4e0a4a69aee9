-- Update the supplier user to have a valid email and organization ID
-- This script uses the real email from the Auth system

-- First, get the email from the Auth system
WITH auth_email AS (
  SELECT email FROM auth.users WHERE id = '18625693-2496-45a4-a1d8-675a9bf2683b'
)

-- Then update the profile with the real email and organization ID
UPDATE profiles
SET
  email = ARRAY[(SELECT email FROM auth_email)],
  organization_id = (SELECT organization_id FROM profiles WHERE id = '4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd')
WHERE
  id = '18625693-2496-45a4-a1d8-675a9bf2683b'
  AND (email IS NULL OR organization_id IS NULL);
