-- Update the tasks table to support the new pending_payment status and payment_status field

-- First, check if payment_status column exists, if not add it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'tasks' 
        AND column_name = 'payment_status'
    ) THEN
        ALTER TABLE public.tasks
        ADD COLUMN payment_status TEXT DEFAULT 'unpaid';
    END IF;
END $$;

-- Create a comment on the payment_status column
COMMENT ON COLUMN public.tasks.payment_status IS 'Payment status for the task: unpaid, pending, processing, paid';

-- Update the RLS policy for tasks to allow task owners to update payment status
CREATE OR REPLACE FUNCTION public.check_task_owner_or_assigned()
RETURNS boolean AS $$
BEGIN
  -- Check if the user is the task owner or the assigned user
  RETURN (
    auth.uid() = user_id OR 
    auth.uid() = assigned_to OR
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Task owners can update their tasks" ON public.tasks;

-- Create new policy
CREATE POLICY "Task owners can update their tasks"
ON public.tasks
FOR UPDATE
USING (check_task_owner_or_assigned())
WITH CHECK (check_task_owner_or_assigned());

-- Ensure the tasks table has RLS enabled
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
