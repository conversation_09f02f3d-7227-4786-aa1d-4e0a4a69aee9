-- Update task workflow schema
-- This script adds new fields to the tasks table and updates RLS policies

-- Add new fields to tasks table
ALTER TABLE public.tasks 
ADD COLUMN IF NOT EXISTS assigned_to UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS visibility TEXT NOT NULL DEFAULT 'admin';

-- Update existing tasks to have public visibility (for backward compatibility)
UPDATE public.tasks SET visibility = 'public' WHERE visibility = 'admin';

-- Drop existing task policies
DROP POLICY IF EXISTS "Organization members can view their organization's tasks" ON public.tasks;

-- Create new task visibility policies
CREATE POLICY "Ad<PERSON> can view all organization tasks" 
ON public.tasks
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
    AND organization_id = (
      SELECT organization_id FROM public.profiles 
      WHERE id = (SELECT user_id FROM public.tasks WHERE id = public.tasks.id)
    )
  )
);

CREATE POLICY "Teachers can view their own tasks" 
ON public.tasks
FOR SELECT
USING (
  user_id = auth.uid()
);

CREATE POLICY "Maintenance and support can view assigned tasks" 
ON public.tasks
FOR SELECT
USING (
  (visibility = 'internal' OR visibility = 'public')
  AND assigned_to = auth.uid()
);

CREATE POLICY "Suppliers can view public tasks" 
ON public.tasks
FOR SELECT
USING (
  visibility = 'public'
  AND EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND account_type = 'supplier'
  )
);

-- Create policies for task creation
CREATE POLICY "Teachers and admins can create tasks" 
ON public.tasks
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND (role = 'teacher' OR role = 'admin')
  )
);

-- Create policies for task updates
CREATE POLICY "Admins can update any task" 
ON public.tasks
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
    AND organization_id = (
      SELECT organization_id FROM public.profiles 
      WHERE id = (SELECT user_id FROM public.tasks WHERE id = public.tasks.id)
    )
  )
);

CREATE POLICY "Task owners can update their own tasks" 
ON public.tasks
FOR UPDATE
USING (
  user_id = auth.uid()
);

-- Create policies for task deletion
CREATE POLICY "Admins can delete any task" 
ON public.tasks
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'admin'
    AND organization_id = (
      SELECT organization_id FROM public.profiles 
      WHERE id = (SELECT user_id FROM public.tasks WHERE id = public.tasks.id)
    )
  )
);

CREATE POLICY "Task owners can delete their own tasks" 
ON public.tasks
FOR DELETE
USING (
  user_id = auth.uid()
);
