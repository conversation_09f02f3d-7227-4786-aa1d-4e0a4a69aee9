-- Update tasks table schema for task type system
-- This script adds the type and assigned_role columns to the tasks table
-- and populates them based on existing data

-- Add type column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'tasks' 
                   AND column_name = 'type') THEN
        ALTER TABLE public.tasks ADD COLUMN type TEXT;
        RAISE NOTICE 'Added type column to tasks table';
    ELSE
        RAISE NOTICE 'type column already exists in tasks table';
    END IF;
END $$;

-- Add assigned_role column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'tasks' 
                   AND column_name = 'assigned_role') THEN
        ALTER TABLE public.tasks ADD COLUMN assigned_role TEXT;
        RAISE NOTICE 'Added assigned_role column to tasks table';
    ELSE
        RAISE NOTICE 'assigned_role column already exists in tasks table';
    END IF;
END $$;

-- Update type column based on visibility
UPDATE public.tasks
SET type = CASE 
    WHEN visibility = 'internal' THEN 'internal'
    ELSE 'external'
END
WHERE type IS NULL;

-- Update assigned_role column for internal tasks
-- This joins with the profiles table to get the role of the assigned user
UPDATE public.tasks t
SET assigned_role = p.role
FROM public.profiles p
WHERE t.assigned_to = p.id
AND t.visibility = 'internal'
AND t.assigned_role IS NULL
AND t.assigned_to IS NOT NULL;

-- Create a function to determine task type from visibility
CREATE OR REPLACE FUNCTION determine_task_type()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.visibility = 'internal' THEN
        NEW.type := 'internal';
    ELSE
        NEW.type := 'external';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically set the type when a task is created
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'set_task_type') THEN
        CREATE TRIGGER set_task_type
        BEFORE INSERT ON public.tasks
        FOR EACH ROW
        EXECUTE FUNCTION determine_task_type();
        RAISE NOTICE 'Created set_task_type trigger';
    ELSE
        RAISE NOTICE 'set_task_type trigger already exists';
    END IF;
END $$;

-- Create a function to update assigned_role when assigned_to changes
CREATE OR REPLACE FUNCTION update_assigned_role()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update assigned_role for internal tasks
    IF NEW.visibility = 'internal' AND NEW.assigned_to IS NOT NULL THEN
        -- Get the role from the profiles table
        SELECT role INTO NEW.assigned_role
        FROM public.profiles
        WHERE id = NEW.assigned_to;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update assigned_role when assigned_to changes
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_assigned_role') THEN
        CREATE TRIGGER update_assigned_role
        BEFORE INSERT OR UPDATE OF assigned_to ON public.tasks
        FOR EACH ROW
        EXECUTE FUNCTION update_assigned_role();
        RAISE NOTICE 'Created update_assigned_role trigger';
    ELSE
        RAISE NOTICE 'update_assigned_role trigger already exists';
    END IF;
END $$;

-- Add a comment to the type column
COMMENT ON COLUMN public.tasks.type IS 'Task type: internal (for school staff) or external (for marketplace)';

-- Add a comment to the assigned_role column
COMMENT ON COLUMN public.tasks.assigned_role IS 'Role of the assigned staff member for internal tasks';

-- Verify the changes
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default, 
    pg_catalog.col_description(format('%I.%I', table_schema, table_name)::regclass::oid, ordinal_position) as column_comment
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'tasks'
    AND column_name IN ('type', 'assigned_role')
ORDER BY 
    ordinal_position;
