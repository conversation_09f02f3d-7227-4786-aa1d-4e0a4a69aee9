-- Update a user's profile with their email from the Auth system
-- This is a function that takes a user_id and email parameters

CREATE OR REPLACE FUNCTION update_user_email_from_auth(user_id UUID, auth_email TEXT)
RETURNS VOID AS $$
BEGIN
  -- Update the profile with the provided email
  UPDATE profiles
  SET
    email = ARRAY[auth_email]
  WHERE
    id = user_id
    AND (email IS NULL OR email = '{}' OR email = '{null}');

  RAISE NOTICE 'Updated email for user % to %', user_id, auth_email;
END;
$$ LANGUAGE plpgsql;

-- Example usage:
-- SELECT update_user_email_from_auth('18625693-2496-45a4-a1d8-675a9bf2683b', '<EMAIL>');

-- Note: This function requires the auth email to be provided as a parameter.
-- You can get the auth email using the Supabase Auth API or admin functions.

-- JavaScript example:
/*
const { data: authUser } = await supabase.auth.admin.getUserById(userId);
if (authUser && authUser.user) {
  const { data, error } = await supabase.rpc(
    'update_user_email_from_auth',
    { user_id: userId, auth_email: authUser.user.email }
  );
}
*/
