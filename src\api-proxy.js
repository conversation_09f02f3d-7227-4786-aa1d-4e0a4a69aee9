/**
 * SECURITY NOTICE: This file has been disabled for security reasons
 *
 * Service role keys should NEVER be exposed to client-side code.
 * This functionality has been moved to secure server-side endpoints.
 *
 * For admin operations, use:
 * - Supabase Edge Functions with service role access
 * - Server-side API routes with proper authentication
 * - RLS policies for data access control
 */

console.error('[SECURITY] API Proxy disabled - Service role keys cannot be used in client-side code');
console.error('[SECURITY] Use Supabase Edge Functions or server-side API routes instead');

// All functions disabled for security
export async function getAdminTasks(userId) {
  throw new Error('SECURITY: Admin operations moved to server-side for security. Use Supabase Edge Functions instead.');
}
