/**
 * Health Check API for Monitoring System Status
 * Provides real-time status of all critical services
 */

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: {
    database: ServiceHealth;
    email: ServiceHealth;
    payments: ServiceHealth;
    chat: ServiceHealth;
    storage: ServiceHealth;
  };
  version: string;
  uptime: number;
}

interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  lastChecked: string;
  error?: string;
}

class HealthChecker {
  private startTime: number;

  constructor() {
    this.startTime = Date.now();
  }

  async checkHealth(): Promise<HealthStatus> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkEmail(),
      this.checkPayments(),
      this.checkChat(),
      this.checkStorage()
    ]);

    const [database, email, payments, chat, storage] = checks.map(result => 
      result.status === 'fulfilled' ? result.value : this.createErrorHealth(result.reason)
    );

    const overallStatus = this.determineOverallStatus([database, email, payments, chat, storage]);

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      services: { database, email, payments, chat, storage },
      version: process.env.VITE_APP_VERSION || '1.0.0',
      uptime: Date.now() - this.startTime
    };
  }

  private async checkDatabase(): Promise<ServiceHealth> {
    const start = Date.now();
    
    try {
      const response = await fetch('/api/health/database', {
        method: 'GET',
        timeout: 5000
      });
      
      const responseTime = Date.now() - start;
      
      if (!response.ok) {
        throw new Error(`Database check failed: ${response.status}`);
      }
      
      return {
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        responseTime,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - start,
        lastChecked: new Date().toISOString(),
        error: error.message
      };
    }
  }

  private async checkEmail(): Promise<ServiceHealth> {
    const start = Date.now();
    
    try {
      // Test Resend API connectivity
      const response = await fetch('https://api.resend.com/domains', {
        headers: {
          'Authorization': `Bearer ${process.env.RESEND_API_KEY}`
        },
        timeout: 5000
      });
      
      const responseTime = Date.now() - start;
      
      return {
        status: response.ok ? 'healthy' : 'degraded',
        responseTime,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - start,
        lastChecked: new Date().toISOString(),
        error: error.message
      };
    }
  }

  private async checkPayments(): Promise<ServiceHealth> {
    const start = Date.now();
    
    try {
      // Test Stripe API connectivity
      const response = await fetch('https://api.stripe.com/v1/account', {
        headers: {
          'Authorization': `Bearer ${process.env.STRIPE_SECRET_KEY}`
        },
        timeout: 5000
      });
      
      const responseTime = Date.now() - start;
      
      return {
        status: response.ok ? 'healthy' : 'degraded',
        responseTime,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - start,
        lastChecked: new Date().toISOString(),
        error: error.message
      };
    }
  }

  private async checkChat(): Promise<ServiceHealth> {
    const start = Date.now();
    
    try {
      // Test GetStream connectivity
      const response = await fetch('https://chat.stream-io-api.com/health', {
        timeout: 5000
      });
      
      const responseTime = Date.now() - start;
      
      return {
        status: response.ok ? 'healthy' : 'degraded',
        responseTime,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - start,
        lastChecked: new Date().toISOString(),
        error: error.message
      };
    }
  }

  private async checkStorage(): Promise<ServiceHealth> {
    const start = Date.now();
    
    try {
      // Test Supabase storage
      const response = await fetch(`${process.env.VITE_SUPABASE_URL}/storage/v1/bucket`, {
        headers: {
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        timeout: 5000
      });
      
      const responseTime = Date.now() - start;
      
      return {
        status: response.ok ? 'healthy' : 'degraded',
        responseTime,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - start,
        lastChecked: new Date().toISOString(),
        error: error.message
      };
    }
  }

  private createErrorHealth(error: any): ServiceHealth {
    return {
      status: 'unhealthy',
      responseTime: 0,
      lastChecked: new Date().toISOString(),
      error: error.message || 'Unknown error'
    };
  }

  private determineOverallStatus(services: ServiceHealth[]): 'healthy' | 'degraded' | 'unhealthy' {
    const unhealthyCount = services.filter(s => s.status === 'unhealthy').length;
    const degradedCount = services.filter(s => s.status === 'degraded').length;
    
    if (unhealthyCount > 0) return 'unhealthy';
    if (degradedCount > 0) return 'degraded';
    return 'healthy';
  }
}

// API endpoint
export async function GET() {
  const healthChecker = new HealthChecker();
  const health = await healthChecker.checkHealth();
  
  const statusCode = health.status === 'healthy' ? 200 : 
                    health.status === 'degraded' ? 200 : 503;
  
  return new Response(JSON.stringify(health), {
    status: statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache'
    }
  });
}

export { HealthChecker };
