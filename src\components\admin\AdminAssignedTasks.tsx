import { useState } from 'react';
import { useAdminTasks } from '@/hooks/use-admin-tasks';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, MapPin, PoundSterling, Tag, User, Clock } from 'lucide-react';
import { format } from 'date-fns';
import { Task } from '@/services/taskService';
import { Skeleton } from '@/components/ui/skeleton';
import { Link } from 'react-router-dom';

export function AdminAssignedTasks() {
  const { 
    adminAssignedTasks, 
    isLoadingAdminAssignedTasks, 
    refetchAdminAssignedTasks 
  } = useAdminTasks();

  // Function to get badge color based on visibility
  const getVisibilityBadge = (visibility: string) => {
    switch (visibility) {
      case 'internal':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">Internal</Badge>;
      case 'public':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">Public</Badge>;
      default:
        return <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">{visibility}</Badge>;
    }
  };

  // Function to get badge color based on status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200">Open</Badge>;
      case 'assigned':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">Assigned</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">Completed</Badge>;
      default:
        return <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">{status}</Badge>;
    }
  };

  if (isLoadingAdminAssignedTasks) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader className="pb-2">
              <Skeleton className="h-6 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-4" />
              <div className="grid grid-cols-2 gap-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            </CardContent>
            <CardFooter className="pt-2 border-t">
              <Skeleton className="h-10 w-full" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  if (!adminAssignedTasks || adminAssignedTasks.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Assigned Tasks</CardTitle>
          <CardDescription>
            There are currently no tasks that have been assigned or made public.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-500">
            When you assign tasks to staff members or make them available to suppliers, they will appear here.
          </p>
          <div className="flex space-x-4 mt-4">
            <Button
              variant="outline"
              onClick={() => {
                refetchAdminAssignedTasks();
              }}
            >
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Assigned Tasks ({adminAssignedTasks.length})</h2>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => {
              console.log('Refreshing assigned tasks...');
              refetchAdminAssignedTasks();
            }}
          >
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {adminAssignedTasks.map((task) => (
          <Card key={task.id} className="overflow-hidden h-full flex flex-col">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg">{task.title}</CardTitle>
                <div className="flex flex-col gap-1 items-end">
                  {getStatusBadge(task.status)}
                  {getVisibilityBadge(task.visibility)}
                </div>
              </div>
              <div className="flex items-center text-gray-600 mt-1">
                <MapPin size={16} className="mr-1" /> {task.location}
              </div>
              {task.assigned_to && (
                <div className="flex items-center text-gray-600 mt-1 text-sm">
                  <User size={14} className="mr-1" />
                  <span>Assigned to: {task.assigned_to}</span>
                </div>
              )}
            </CardHeader>
            <CardContent className="pb-2 flex-grow">
              <p className="text-sm text-gray-600 line-clamp-3 mb-4">{task.description}</p>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center">
                  <PoundSterling size={16} className="mr-1 text-gray-500" />
                  <span>£{task.budget.toFixed(2)}</span>
                </div>
                <div className="flex items-center">
                  <Tag size={16} className="mr-1 text-gray-500" />
                  <span>{task.category}</span>
                </div>
                <div className="flex items-center">
                  <Calendar size={16} className="mr-1 text-gray-500" />
                  <span>{format(new Date(task.due_date), 'MMM d, yyyy')}</span>
                </div>
                <div className="flex items-center">
                  <Clock size={16} className="mr-1 text-gray-500" />
                  <span>{format(new Date(task.created_at), 'MMM d, yyyy')}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter className="pt-2 border-t">
              <Link to={`/tasks/${task.id}`} className="w-full">
                <Button className="w-full" variant="outline">
                  View Details
                </Button>
              </Link>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
