import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { 
  Users, 
  ClipboardList, 
  Building, 
  MessageSquare, 
  TrendingUp, 
  Calendar 
} from 'lucide-react';

// Mock data for statistics
const stats = {
  totalUsers: 120,
  activeUsers: 87,
  totalTasks: 245,
  completedTasks: 183,
  organizations: 12,
  messages: 1432,
  growthRate: '+12%',
  registrationsThisMonth: 24
};

const StatCard = ({ 
  title, 
  value, 
  description, 
  icon: Icon, 
  trend 
}: { 
  title: string; 
  value: string | number; 
  description?: string; 
  icon: React.ElementType; 
  trend?: string;
}) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between pb-2">
      <CardTitle className="text-sm font-medium text-muted-foreground">
        {title}
      </CardTitle>
      <Icon className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      {description && (
        <p className="text-xs text-muted-foreground mt-1">
          {description}
        </p>
      )}
      {trend && (
        <div className="flex items-center mt-1">
          <TrendingUp className={`h-3 w-3 ${trend.startsWith('+') ? 'text-green-500' : 'text-red-500'} mr-1`} />
          <span className={`text-xs ${trend.startsWith('+') ? 'text-green-500' : 'text-red-500'}`}>
            {trend} from last month
          </span>
        </div>
      )}
    </CardContent>
  </Card>
);

const AdminStats = () => {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatCard
        title="Total Users"
        value={stats.totalUsers}
        description={`${stats.activeUsers} active users`}
        icon={Users}
        trend="+8%"
      />
      
      <StatCard
        title="Total Tasks"
        value={stats.totalTasks}
        description={`${stats.completedTasks} completed`}
        icon={ClipboardList}
        trend="+15%"
      />
      
      <StatCard
        title="Organizations"
        value={stats.organizations}
        icon={Building}
        trend="+5%"
      />
      
      <StatCard
        title="Messages"
        value={stats.messages}
        icon={MessageSquare}
        trend="+24%"
      />
      
      <StatCard
        title="Growth Rate"
        value={stats.growthRate}
        description="User growth rate"
        icon={TrendingUp}
      />
      
      <StatCard
        title="New Registrations"
        value={stats.registrationsThisMonth}
        description="This month"
        icon={Calendar}
      />
    </div>
  );
};

export default AdminStats;