import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAdminTasks } from '@/hooks/use-admin-tasks';
import { useOrganizationUsers } from '@/hooks/use-organization-users';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { AlertCircle, Calendar, MapPin, PoundSterling, Tag, User, Clock, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { Task } from '@/services/taskService';
import { Skeleton } from '@/components/ui/skeleton';
import { UserRole } from '@/types/organization';

export function AdminTaskReview() {
  const {
    adminReviewTasks,
    isLoadingAdminReviewTasks,
    assignTask,
    isAssigningTask,
    refetchAdminReviewTasks,
    adminAssignedTasks,
    refetchAdminAssignedTasks
  } = useAdminTasks();
  const { toast } = useToast();
  const { organizationUsers, isLoading: isLoadingUsers } = useOrganizationUsers();
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [assignmentType, setAssignmentType] = useState<'internal' | 'public'>('internal');
  const [taskBudget, setTaskBudget] = useState<string>('');
  const [directTasks, setDirectTasks] = useState<Task[]>([]);
  const [isLoadingDirectTasks, setIsLoadingDirectTasks] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { user } = useAuth();
  const navigate = useNavigate();

  // Fetch tasks using the API proxy
  useEffect(() => {
    async function fetchAdminTasks() {
      if (!user) return;

      setIsLoadingDirectTasks(true);
      try {
        console.log('Fetching tasks using server-side API...');

        // Use server-side API endpoint
        const response = await fetch(`/api/admin-tasks?user_id=${user.id}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch admin tasks');
        }

        const data = await response.json();
        console.log('Server API tasks:', data.tasks);
        setDirectTasks(data.tasks || []);
      } catch (err) {
        console.error('Error fetching admin tasks via server API:', err);

        // Fallback to direct Supabase query
        try {
          console.log('Falling back to direct Supabase query...');
          const { data, error } = await supabase
            .from('tasks')
            .select('*')
            .order('created_at', { ascending: false });

          if (error) {
            console.error('Error with fallback query:', error);
          } else {
            console.log('Fallback direct tasks:', data);
            setDirectTasks(data || []);
          }
        } catch (fallbackErr) {
          console.error('Error with fallback query:', fallbackErr);
        }
      } finally {
        setIsLoadingDirectTasks(false);
      }
    }

    fetchAdminTasks();
  }, [user]);

  if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG AdminTaskReview - adminReviewTasks:', adminReviewTasks);
    console.log('DEBUG AdminTaskReview - isLoadingAdminReviewTasks:', isLoadingAdminReviewTasks);
    console.log('DEBUG AdminTaskReview - directTasks:', directTasks);
    console.log('DEBUG AdminTaskReview - organizationUsers:', organizationUsers);
    console.log('DEBUG AdminTaskReview - isLoadingUsers:', isLoadingUsers);
  }

  // Use direct tasks if adminReviewTasks is empty
  // Filter out any tasks that are not in 'admin' visibility state
  const filteredDirectTasks = directTasks.filter(task => task.visibility === 'admin' && task.status === 'open');
  const rawTasksToDisplay = adminReviewTasks?.length > 0 ? adminReviewTasks : filteredDirectTasks;

  // Sort tasks by priority: overdue first, then due soon, then by creation date
  const tasksToDisplay = rawTasksToDisplay.sort((a, b) => {
    const now = new Date();
    const aDueDate = new Date(a.due_date);
    const bDueDate = new Date(b.due_date);
    const aCreatedDate = new Date(a.created_at);
    const bCreatedDate = new Date(b.created_at);

    // Calculate days until due
    const aDaysUntilDue = Math.ceil((aDueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    const bDaysUntilDue = Math.ceil((bDueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    // Calculate days since created
    const aDaysSinceCreated = Math.ceil((now.getTime() - aCreatedDate.getTime()) / (1000 * 60 * 60 * 24));
    const bDaysSinceCreated = Math.ceil((now.getTime() - bCreatedDate.getTime()) / (1000 * 60 * 60 * 24));

    // Priority scoring: lower score = higher priority
    const getPriorityScore = (daysUntilDue: number, daysSinceCreated: number) => {
      if (daysUntilDue < 0) return 0; // Overdue - highest priority
      if (daysUntilDue <= 1) return 1; // Due today/tomorrow
      if (daysSinceCreated > 3) return 2; // Pending for more than 3 days
      if (daysUntilDue <= 3) return 3; // Due within 3 days
      if (daysSinceCreated > 1) return 4; // Pending for more than 1 day
      return 5; // Normal priority
    };

    const aScore = getPriorityScore(aDaysUntilDue, aDaysSinceCreated);
    const bScore = getPriorityScore(bDaysUntilDue, bDaysSinceCreated);

    // Sort by priority score first
    if (aScore !== bScore) {
      return aScore - bScore;
    }

    // If same priority, sort by creation date (oldest first)
    return aCreatedDate.getTime() - bCreatedDate.getTime();
  });

  console.log('AdminTaskReview - adminReviewTasks:', adminReviewTasks?.length || 0);
  console.log('AdminTaskReview - filteredDirectTasks:', filteredDirectTasks.length);
  console.log('AdminTaskReview - tasksToDisplay:', tasksToDisplay.length);

  // Filter users by role
  const maintenanceUsers = organizationUsers?.filter(user => user.role === 'maintenance') || [];
  const supportUsers = organizationUsers?.filter(user => user.role === 'support') || [];
  const adminUsers = organizationUsers?.filter(user => user.role === 'admin') || [];

  // Include admin users in the internal users list to allow self-assignment
  // Explicitly exclude teachers from the assignable users list
  const internalUsers = [...maintenanceUsers, ...supportUsers, ...adminUsers];

  // Log the filtered users for debugging (development only)
  if (process.env.NODE_ENV === 'development') {
    console.log('[AdminTaskReview] Filtered users:', {
      maintenance: maintenanceUsers.length,
      support: supportUsers.length,
      admin: adminUsers.length,
      total: internalUsers.length
    });

    console.log('DEBUG AdminTaskReview - maintenanceUsers:', maintenanceUsers);
    console.log('DEBUG AdminTaskReview - supportUsers:', supportUsers);
    console.log('DEBUG AdminTaskReview - internalUsers:', internalUsers);

    // Debug: Check raw organization users data
    console.log('DEBUG AdminTaskReview - Raw organizationUsers data:', organizationUsers);
    if (organizationUsers) {
      console.log('DEBUG AdminTaskReview - User roles in organizationUsers:',
        organizationUsers.map(user => ({ id: user.user_id, email: user.email, role: user.role })));
    }
  }

  // State for staff role filter
  const [staffRoleFilter, setStaffRoleFilter] = useState<'all' | 'maintenance' | 'support' | 'admin'>('all');

  // Filter internal users based on selected role filter
  const filteredInternalUsers = staffRoleFilter === 'all'
    ? internalUsers
    : staffRoleFilter === 'maintenance'
      ? maintenanceUsers
      : staffRoleFilter === 'support'
        ? supportUsers
        : adminUsers;

  // Check if there are no maintenance or support staff
  const hasMaintenanceOrSupportStaff = maintenanceUsers.length > 0 || supportUsers.length > 0;

  // Auto-select admin if there are no maintenance or support staff and we're in internal assignment mode
  // Only run this when the dialog opens or assignment type changes, not on every render
  useEffect(() => {
    if (assignmentType === 'internal' && !hasMaintenanceOrSupportStaff && adminUsers.length > 0 && !selectedUserId) {
      // Find the current admin user
      const currentAdmin = adminUsers.find(admin => admin.user_id === user?.id);

      // If current admin exists, select them
      if (currentAdmin) {
        setSelectedUserId(currentAdmin.user_id);
        console.log('Auto-selected admin user:', currentAdmin.user_id);
      } else if (adminUsers.length > 0) {
        // Otherwise select the first admin
        setSelectedUserId(adminUsers[0].user_id);
        console.log('Auto-selected first admin user:', adminUsers[0].user_id);
      }
    }
  }, [assignmentType, hasMaintenanceOrSupportStaff, adminUsers.length, user?.id]); // Removed adminUsers from deps

  const handleAssignTask = () => {
    console.log('handleAssignTask called');
    console.log('selectedTask:', selectedTask);
    console.log('selectedUserId:', selectedUserId);
    console.log('assignmentType:', assignmentType);

    // For public assignments, we don't need a selectedUserId
    if (!selectedTask) {
      console.error('No task selected');
      return;
    }

    if (assignmentType === 'internal' && !selectedUserId) {
      console.error('No user selected for internal assignment');
      return;
    }

    console.log(`Assigning task ${selectedTask.id} to ${selectedUserId || 'public'} with visibility ${assignmentType}`);

    // For public assignments, include the budget
    const budget = assignmentType === 'public' ? parseFloat(taskBudget) : undefined;
    console.log(`Task budget: ${budget !== undefined ? budget : 'unchanged'}`);

    // Set refreshing state to show loading indicator
    setIsRefreshing(true);

    assignTask({
      taskId: selectedTask.id,
      userId: assignmentType === 'public' ? '' : selectedUserId, // Empty string for public assignments
      visibility: assignmentType,
      budget: budget // Include budget for public tasks
    }, {
      onSuccess: async () => {
        console.log('Task assigned successfully');

        try {
          // Explicitly refetch both task lists to ensure UI is updated correctly
          await Promise.all([
            refetchAdminReviewTasks(),
            refetchAdminAssignedTasks()
          ]);

          console.log('Task lists refreshed successfully');

          // Show a more detailed success message
          if (assignmentType === 'public') {
            toast({
              title: "Task Made Public",
              description: "The task is now visible to suppliers and has been moved to the Assigned Tasks tab.",
            });
          } else {
            toast({
              title: "Task Assigned",
              description: `The task has been assigned to the selected staff member and moved to the Assigned Tasks tab.`,
            });
          }
        } catch (error) {
          console.error('Error refreshing task lists:', error);
          toast({
            variant: "warning",
            title: "Task Assigned",
            description: "The task was assigned successfully, but you may need to refresh to see the changes.",
          });
        } finally {
          // Close dialog and reset state
          setAssignDialogOpen(false);
          setSelectedTask(null);
          setSelectedUserId('');
          setIsRefreshing(false);
        }
      },
      onError: (error) => {
        console.error('Error assigning task:', error);
        toast({
          variant: "destructive",
          title: "Error Assigning Task",
          description: "There was a problem assigning the task. Please try again.",
        });
        setIsRefreshing(false);
      }
    });
  };

  const openAssignDialog = (task: Task) => {
    setSelectedTask(task);
    setSelectedUserId('');
    setAssignmentType('internal');
    setStaffRoleFilter('all'); // Reset staff role filter
    setTaskBudget(task.budget.toString()); // Initialize budget with current task budget
    setAssignDialogOpen(true);
  };



  if (isLoadingAdminReviewTasks || isLoadingDirectTasks) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-64" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-64 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if ((!adminReviewTasks || adminReviewTasks.length === 0) && (!directTasks || directTasks.length === 0)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Tasks Pending Review</CardTitle>
          <CardDescription>
            There are currently no tasks waiting for admin review.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-500">
            When teachers create new tasks, they will appear here for you to review and assign.
          </p>
          <div className="flex space-x-4 mt-4">
            <Button
              variant="outline"
              onClick={() => {
                refetchAdminReviewTasks();
              }}
            >
              Refresh
            </Button>
            <Button
              variant="outline"
              onClick={async () => {
                if (!user) return;

                setIsLoadingDirectTasks(true);
                try {
                  console.log('Manually fetching tasks using server API (empty state)...');

                  // Use server-side API endpoint
                  const response = await fetch(`/api/admin-tasks?user_id=${user.id}`);

                  if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || 'Failed to fetch admin tasks');
                  }

                  const data = await response.json();
                  console.log('Server API tasks (empty state):', data.tasks);
                  setDirectTasks(data.tasks || []);
                } catch (err) {
                  console.error('Error fetching admin tasks via server API (empty state):', err);

                  // Fallback to direct Supabase query
                  try {
                    console.log('Falling back to direct Supabase query (empty state)...');
                    const { data, error } = await supabase
                      .from('tasks')
                      .select('*')
                      .order('created_at', { ascending: false });

                    if (error) {
                      console.error('Error with fallback query (empty state):', error);
                    } else {
                      console.log('Fallback direct tasks (empty state):', data);
                      setDirectTasks(data || []);
                    }
                  } catch (fallbackErr) {
                    console.error('Error with fallback query (empty state):', fallbackErr);
                  }
                } finally {
                  setIsLoadingDirectTasks(false);
                }
              }}
            >
              Fetch Directly
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Tasks Pending Review ({tasksToDisplay.length})</h2>
          <p className="text-sm text-gray-600 mt-1">Sorted by priority: overdue first, then due soon, then by creation date</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => {
              console.log('Refreshing tasks...');
              refetchAdminReviewTasks();
            }}
          >
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={async () => {
              if (!user) return;

              setIsLoadingDirectTasks(true);
              try {
                console.log('Manually fetching tasks using server API...');

                // Use server-side API endpoint
                const response = await fetch(`/api/admin-tasks?user_id=${user.id}`);

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || 'Failed to fetch admin tasks');
                }

                const data = await response.json();
                console.log('Server API tasks (manual):', data.tasks);
                setDirectTasks(data.tasks || []);
              } catch (err) {
                console.error('Error fetching admin tasks via server API (manual):', err);

                // Fallback to direct Supabase query
                try {
                  console.log('Falling back to direct Supabase query (manual)...');
                  const { data, error } = await supabase
                    .from('tasks')
                    .select('*')
                    .order('created_at', { ascending: false });

                  if (error) {
                    console.error('Error with fallback query (manual):', error);
                  } else {
                    console.log('Fallback direct tasks (manual):', data);
                    setDirectTasks(data || []);
                  }
                } catch (fallbackErr) {
                  console.error('Error with fallback query (manual):', fallbackErr);
                }
              } finally {
                setIsLoadingDirectTasks(false);
              }
            }}
          >
            Fetch Directly
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {tasksToDisplay.map((task) => (
          <Card
            key={task.id}
            className="overflow-hidden h-full flex flex-col"
          >
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg">{task.title}</CardTitle>
                <Badge variant="outline" className={
                  `${task.status === 'open' ? 'bg-amber-100 text-amber-800 border-amber-200' :
                    task.status === 'assigned' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                    task.status === 'in_progress' ? 'bg-indigo-100 text-indigo-800 border-indigo-200' :
                    task.status === 'completed' ? 'bg-purple-100 text-purple-800 border-purple-200' :
                    task.status === 'closed' ? 'bg-teal-100 text-teal-800 border-teal-200' :
                    'bg-gray-100 text-gray-800 border-gray-200'}`
                }>
                  {task.status === 'open' ? 'Pending Review' :
                   task.status === 'in_progress' ? 'In Progress' :
                   task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                </Badge>
              </div>
              <div className="flex items-center text-gray-600 mt-1">
                <MapPin size={16} className="mr-1" /> {task.location}
              </div>
              {task.assigned_to && (
                <div className="flex items-center text-gray-600 mt-1 text-sm">
                  <User size={14} className="mr-1" />
                  <span>Assigned to: </span>
                  <Badge variant="outline" className="ml-1 text-xs">
                    {(() => {
                      const assignedUser = organizationUsers?.find(u => u.user_id === task.assigned_to);
                      return assignedUser
                        ? `${assignedUser.first_name || 'Unknown'} ${assignedUser.last_name || ''} (${assignedUser.role})`
                        : 'Unknown User';
                    })()}
                  </Badge>
                </div>
              )}
            </CardHeader>
            <CardContent className="pb-2 flex-grow">
              <p className="text-sm text-gray-600 line-clamp-3 mb-4">{task.description}</p>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center">
                  <PoundSterling size={16} className="mr-1 text-gray-500" />
                  <span>£{task.budget.toFixed(2)}</span>
                </div>
                <div className="flex items-center">
                  <Tag size={16} className="mr-1 text-gray-500" />
                  <span>{task.category}</span>
                </div>
                <div className="flex items-center">
                  <Calendar size={16} className="mr-1 text-gray-500" />
                  <span>{format(new Date(task.due_date), 'MMM d, yyyy')}</span>
                </div>
                <div className="flex items-center">
                  <Clock size={16} className="mr-1 text-gray-500" />
                  <span>{format(new Date(task.created_at), 'MMM d, yyyy')}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter className="pt-2 border-t">
              <Button
                className="w-full"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent card click
                  openAssignDialog(task);
                }}
                variant={task.status === 'assigned' ? 'outline' : 'default'}
              >
                {task.status === 'assigned' ? 'Reassign Task' : 'Assign Task'}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Dialog open={assignDialogOpen} onOpenChange={setAssignDialogOpen}>
        <DialogContent className="sm:max-w-2xl max-h-[85vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Assign Task</DialogTitle>
            <DialogDescription>
              Assign "{selectedTask?.title}" to a staff member or make it available to suppliers.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <RadioGroup
              value={assignmentType}
              onValueChange={(value) => setAssignmentType(value as 'internal' | 'public')}
              className="space-y-3"
            >
              <div className="flex items-start space-x-3 space-y-0">
                <RadioGroupItem value="internal" id="internal" />
                <div className="grid gap-1.5">
                  <Label htmlFor="internal" className="font-medium">
                    Assign Internally
                  </Label>
                  <p className="text-sm text-gray-500">
                    Assign to admin, maintenance, or support staff within your organization
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3 space-y-0">
                <RadioGroupItem value="public" id="public" />
                <div className="grid gap-1.5">
                  <Label htmlFor="public" className="font-medium">
                    Make Public for Suppliers
                  </Label>
                  <p className="text-sm text-gray-500">
                    Make this task visible to all suppliers who can then make offers
                  </p>
                </div>
              </div>
            </RadioGroup>

            {assignmentType === 'public' && (
              <div className="space-y-4 mt-4">
                <div className="space-y-2">
                  <Label htmlFor="budget">Budget (£)</Label>
                  <div className="relative">
                    <PoundSterling className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="budget"
                      type="number"
                      placeholder="Enter budget amount"
                      className="pl-9"
                      value={taskBudget}
                      onChange={(e) => setTaskBudget(e.target.value)}
                      min="1"
                      step="0.01"
                      required
                    />
                  </div>
                  <p className="text-xs text-gray-500">
                    Set a budget for suppliers to bid on this task
                  </p>
                </div>
              </div>
            )}

            {assignmentType === 'internal' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="role-filter">Filter by Role</Label>
                  <div className="grid grid-cols-4 gap-2">
                    <Button
                      variant={staffRoleFilter === 'all' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setStaffRoleFilter('all')}
                      className="justify-between"
                    >
                      <span>All Staff</span>
                      {internalUsers.length > 0 && (
                        <Badge variant="secondary" className="ml-2">{internalUsers.length}</Badge>
                      )}
                    </Button>
                    <Button
                      variant={staffRoleFilter === 'maintenance' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setStaffRoleFilter('maintenance')}
                      className="justify-between"
                    >
                      <span>Maintenance</span>
                      {maintenanceUsers.length > 0 && (
                        <Badge variant="secondary" className="ml-2">{maintenanceUsers.length}</Badge>
                      )}
                    </Button>
                    <Button
                      variant={staffRoleFilter === 'support' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setStaffRoleFilter('support')}
                      className="justify-between"
                    >
                      <span>Support</span>
                      {supportUsers.length > 0 && (
                        <Badge variant="secondary" className="ml-2">{supportUsers.length}</Badge>
                      )}
                    </Button>
                    <Button
                      variant={staffRoleFilter === 'admin' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setStaffRoleFilter('admin')}
                      className="justify-between"
                    >
                      <span>Admin</span>
                      {adminUsers.length > 0 && (
                        <Badge variant="secondary" className="ml-2">{adminUsers.length}</Badge>
                      )}
                    </Button>
                  </div>
                </div>

                {/* Show message if no maintenance or support staff */}
                {!hasMaintenanceOrSupportStaff && (
                  <div className="p-3 bg-amber-50 border border-amber-200 rounded-md mb-4">
                    <p className="text-sm text-amber-700 flex items-start">
                      <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                      <span>
                        <strong>Note:</strong> No maintenance or support staff found. You can assign this task to yourself or another admin.
                      </span>
                    </p>
                  </div>
                )}

                <div className="space-y-2">
                  <Label>Select Staff Member</Label>
                  {isLoadingUsers ? (
                    <div className="p-4 text-center">
                      <span className="text-sm text-gray-500">Loading staff members...</span>
                    </div>
                  ) : filteredInternalUsers.length === 0 ? (
                    <div className="p-4 text-center border rounded-md">
                      <span className="text-sm text-gray-500">
                        {staffRoleFilter === 'all'
                          ? 'No staff available for assignment'
                          : `No ${staffRoleFilter} staff available`}
                      </span>
                    </div>
                  ) : (
                    <div className="space-y-2 max-h-48 overflow-y-auto border rounded-md p-3">
                      {filteredInternalUsers.map((user) => (
                        <label
                          key={user.user_id}
                          className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md cursor-pointer"
                        >
                          <input
                            type="radio"
                            name="staff-selection"
                            value={user.user_id}
                            checked={selectedUserId === user.user_id}
                            onChange={() => setSelectedUserId(user.user_id)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{user.email}</span>
                              <Badge variant="outline" className={
                                user.role === 'maintenance' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                                user.role === 'support' ? 'bg-purple-50 text-purple-700 border-purple-200' :
                                user.role === 'admin' ? 'bg-amber-50 text-amber-700 border-amber-200' :
                                'bg-gray-50'
                              }>
                                {user.role}
                              </Badge>
                            </div>
                            {user.first_name && (
                              <div className="text-sm text-gray-500">
                                {user.first_name} {user.last_name || ''}
                              </div>
                            )}
                          </div>
                        </label>
                      ))}
                    </div>
                  )}

                  {selectedUserId && (
                    <div className="mt-2 p-2 border rounded-md bg-gray-50">
                      <p className="text-sm font-medium">Selected Staff Member:</p>
                      {(() => {
                        const selectedUser = filteredInternalUsers.find(u => u.user_id === selectedUserId);
                        return selectedUser ? (
                          <div className="mt-1">
                            <div className="flex items-center">
                              <User className="mr-2 h-4 w-4 text-gray-500" />
                              <span className="font-medium">{selectedUser.email}</span>
                            </div>
                            <div className="mt-1 text-sm text-gray-500">
                              <span>Role: </span>
                              <span className="font-medium">{selectedUser.role}</span>
                            </div>
                            {selectedUser.first_name && (
                              <div className="mt-1 text-sm text-gray-500">
                                <span>Name: </span>
                                <span className="font-medium">
                                  {selectedUser.first_name} {selectedUser.last_name || ''}
                                </span>
                              </div>
                            )}
                          </div>
                        ) : null;
                      })()}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setAssignDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAssignTask}
              disabled={
                isAssigningTask || isRefreshing ||
                (assignmentType === 'internal' && !selectedUserId) ||
                (assignmentType === 'public' && (!taskBudget || parseFloat(taskBudget) <= 0))
              }
            >
              {isAssigningTask || isRefreshing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isAssigningTask ? 'Assigning...' : 'Refreshing...'}
                </>
              ) : (
                assignmentType === 'public' ? 'Make Public' : 'Assign Task'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
