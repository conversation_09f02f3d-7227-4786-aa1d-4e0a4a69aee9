import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useRolePermissions, Permission } from '@/hooks/useRolePermissions';

interface RoleProtectedRouteProps {
  /**
   * Content to render if the user has the required role/permission
   */
  children: React.ReactNode;

  /**
   * Required permission to access the route
   */
  requiredPermission?: Permission;

  /**
   * Whether site admin privileges are required
   */
  requireSiteAdmin?: boolean;

  /**
   * Required role to access the route
   */
  requiredRole?: string;
}

/**
 * Component that protects routes based on user roles and permissions
 * Redirects to login or access denied page if the user doesn't have the required role/permission
 */
const RoleProtectedRoute: React.FC<RoleProtectedRouteProps> = ({
  children,
  requiredPermission,
  requireSiteAdmin = false,
  requiredRole
}) => {
  const { user, isLoading } = useAuth();
  const { isSiteAdmin, hasPermission } = useRolePermissions();
  const location = useLocation();

  // Show loading state while checking authentication
  if (isLoading) {
    return <div>Loading...</div>;
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check site admin requirement
  if (requireSiteAdmin && !isSiteAdmin) {
    return <Navigate to="/access-denied" state={{ from: location }} replace />;
  }

  // Check role requirement using the useAuth hook which gets role from profiles table
  const { userRole } = useAuth();
  if (requiredRole && requiredRole !== userRole) {
    return <Navigate to="/access-denied" state={{ from: location }} replace />;
  }

  // Check permission requirement
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return <Navigate to="/access-denied" state={{ from: location }} replace />;
  }

  // User has the required role/permission, render the protected content
  return <>{children}</>;
};

export default RoleProtectedRoute;
