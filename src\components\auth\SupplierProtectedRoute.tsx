import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import { Briefcase } from 'lucide-react';

interface SupplierProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

/**
 * A route component that only allows suppliers to access the content
 * Non-suppliers will see an access denied message
 */
const SupplierProtectedRoute: React.FC<SupplierProtectedRouteProps> = ({
  children,
  redirectTo = '/access-denied'
}) => {
  const { user, isSupplier, isLoading } = useAuth();

  // If auth is still loading, show nothing
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // If user is not logged in, redirect to login
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // If user is not a supplier, show enhanced desktop marketplace information
  if (!isSupplier) {
    return (
      <MainLayout>
        <div className="container mx-auto py-12 px-4">
          <div className="max-w-4xl mx-auto">
            {/* Header Section */}
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-50 rounded-full mb-6">
                <Briefcase className="h-10 w-10 text-blue-500" />
              </div>
              <h1 className="text-4xl font-bold text-gray-900 mb-4">Marketplace Information</h1>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                The marketplace is where professional suppliers can view and bid on tasks that need external support.
              </p>
            </div>

            {/* How It Works Section */}
            <div className="grid md:grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-green-50 rounded-full mb-4">
                  <span className="text-2xl font-bold text-green-600">1</span>
                </div>
                <h3 className="text-lg font-semibold mb-2">Tasks Posted</h3>
                <p className="text-gray-600">
                  When your school posts tasks that need external help, they appear in the marketplace.
                </p>
              </div>

              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-50 rounded-full mb-4">
                  <span className="text-2xl font-bold text-blue-600">2</span>
                </div>
                <h3 className="text-lg font-semibold mb-2">Suppliers Respond</h3>
                <p className="text-gray-600">
                  Qualified professionals can view task details and submit competitive offers.
                </p>
              </div>

              <div className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-purple-50 rounded-full mb-4">
                  <span className="text-2xl font-bold text-purple-600">3</span>
                </div>
                <h3 className="text-lg font-semibold mb-2">Best Match Selected</h3>
                <p className="text-gray-600">
                  Schools can review offers and select the best supplier for their needs.
                </p>
              </div>
            </div>

            {/* Information Cards */}
            <div className="grid md:grid-cols-2 gap-8 mb-12">
              <div className="bg-white border rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-3 text-gray-900">For Schools</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    Access to qualified professional suppliers
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    Competitive pricing through multiple offers
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    Streamlined communication and project management
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    Secure payment processing and invoicing
                  </li>
                </ul>
              </div>

              <div className="bg-white border rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-3 text-gray-900">For Suppliers</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">✓</span>
                    Direct access to school maintenance projects
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">✓</span>
                    Clear project specifications and requirements
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">✓</span>
                    Integrated chat for project communication
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">✓</span>
                    Reliable payment terms and processing
                  </li>
                </ul>
              </div>
            </div>

            {/* Call to Action */}
            <div className="text-center bg-gray-50 rounded-lg p-8">
              <h3 className="text-xl font-semibold mb-4">Ready to manage your tasks?</h3>
              <p className="text-gray-600 mb-6">
                While you can't view the marketplace directly, any tasks from your school that need external support will be posted here for suppliers to find and respond to.
              </p>
              <div className="flex gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-classtasker-blue hover:bg-classtasker-blue/90"
                  onClick={() => window.location.href = '/dashboard'}
                >
                  Go to My Tasks
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => window.location.href = '/register'}
                >
                  Learn About Supplier Accounts
                </Button>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  // User is a supplier, allow access
  return <>{children}</>;
};

export default SupplierProtectedRoute;
