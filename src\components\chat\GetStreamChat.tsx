/**
 * GetStream Chat Component
 *
 * A component that uses GetStream for chat functionality
 */

import React, { useEffect } from 'react';
import {
  Cha<PERSON>,
  Channel,
  ChannelHeader,
  MessageInput,
  MessageList,
  Thread,
  Window,
} from 'stream-chat-react';
import { useGetStreamChat } from '@/hooks/use-getstream-chat';
import { useAuth } from '@/contexts/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';

// We'll use our own CSS instead of the Stream Chat CSS
// The Stream Chat CSS import is causing issues

interface GetStreamChatProps {
  taskId: string;
  threadId?: string;
  taskTitle?: string;
  className?: string;
}

const GetStreamChat: React.FC<GetStreamChatProps> = ({
  taskId,
  threadId,
  taskTitle = 'Task Chat',
  className = '',
}) => {
  const { user } = useAuth();
  const {
    client,
    channel,
    isLoading,
    error,
  } = useGetStreamChat({ taskId, threadId });

  // Log component mounting for debugging
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('GetStreamChat mounted with: completed');

      }
    return () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('GetStreamChat unmounting');
        }
    };
  }, [taskId, threadId, user]);

  if (!user) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-md">
        <p className="text-gray-500">Please sign in to use chat</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64 bg-red-50 rounded-md">
        <p className="text-red-500">Error loading chat: {error.message}</p>
      </div>
    );
  }

  if (isLoading || !client || !channel) {
    return (
      <div className="space-y-4 p-4 bg-gray-50 rounded-md">
        <Skeleton className="h-8 w-3/4" />
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <div key={i} className={`flex ${i % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
              <div className={`max-w-[80%] rounded-lg p-3 ${i % 2 === 0 ? 'bg-white' : 'bg-blue-100'}`}>
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-20 mt-2" />
              </div>
            </div>
          ))}
        </div>
        <Skeleton className="h-10 w-full mt-4" />
      </div>
    );
  }

  return (
    <div className={`getstream-chat-container h-full ${className}`}>
      <Chat client={client} theme="messaging light">
        <Channel channel={channel}>
          <Window>
            <ChannelHeader title={taskTitle} />
            <MessageList />
            <MessageInput />
          </Window>
          <Thread />
        </Channel>
      </Chat>
    </div>
  );
};

export default GetStreamChat;
