import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRolePermissions, Permission } from '@/hooks/useRolePermissions';

interface RoleBasedUIProps {
  /**
   * Required role(s) to render the children
   */
  requiredRole?: string | string[];
  
  /**
   * Required permission(s) to render the children
   */
  requiredPermission?: Permission | Permission[];
  
  /**
   * Whether site admin privileges are required
   */
  requireSiteAdmin?: boolean;
  
  /**
   * Content to render if the user doesn't have the required role/permission
   */
  fallback?: React.ReactNode;
  
  /**
   * Content to render if the user has the required role/permission
   */
  children: React.ReactNode;
}

/**
 * Component that conditionally renders content based on user roles and permissions
 */
const RoleBasedUI: React.FC<RoleBasedUIProps> = ({
  requiredRole,
  requiredPermission,
  requireSiteAdmin = false,
  fallback = null,
  children
}) => {
  const { userRole } = useAuth();
  const { isSiteAdmin, hasPermission } = useRolePermissions();
  
  // Check site admin requirement
  if (requireSiteAdmin && !isSiteAdmin) {
    return <>{fallback}</>;
  }
  
  // Check role requirement
  if (requiredRole) {
    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    if (!roles.includes(userRole)) {
      return <>{fallback}</>;
    }
  }
  
  // Check permission requirement
  if (requiredPermission) {
    const permissions = Array.isArray(requiredPermission) 
      ? requiredPermission 
      : [requiredPermission];
      
    const hasAllPermissions = permissions.every(permission => 
      hasPermission(permission)
    );
    
    if (!hasAllPermissions) {
      return <>{fallback}</>;
    }
  }
  
  // All checks passed, render the children
  return <>{children}</>;
};

export default RoleBasedUI;
