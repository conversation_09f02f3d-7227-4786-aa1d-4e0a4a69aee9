import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { <PERSON>ert<PERSON>ircle, Clock, CheckCircle, ArrowRight } from 'lucide-react';
import { Task } from '@/services/taskService';
import { Badge } from '@/components/ui/badge';

interface ActionItemsProps {
  tasks: Task[] | undefined;
  isLoading: boolean;
  isTeacher: boolean;
  isAdmin: boolean;
  isMaintenance: boolean;
  isSupport?: boolean;
  isSupplier: boolean;
}

const ActionItems: React.FC<ActionItemsProps> = ({
  tasks,
  isLoading,
  isTeacher,
  isAdmin,
  isMaintenance,
  isSupport,
  isSupplier
}) => {
  if (!tasks || tasks.length === 0) {
    return null;
  }

  // Filter tasks based on user role
  let actionItems: Task[] = [];
  let title = 'Action Items';
  let emptyMessage = 'No action items at this time.';

  if (isTeacher) {
    // For teachers: Tasks pending admin review
    actionItems = tasks.filter(task =>
      task.visibility === 'admin' && task.status === 'open'
    );

    // Log for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log('Teacher action items:', {
      totalTasks: tasks.length,
      filteredTasks: actionItems.length,
      actionItems: actionItems.map(t => ({ id: t.id, title: t.title, visibility: t.visibility, status: t.status }))
    });

      }
    title = 'Tasks Pending Review';
    emptyMessage = 'No tasks pending admin review.';
  } else if (isAdmin) {
    // For admins: Tasks that need review
    actionItems = tasks.filter(task =>
      task.visibility === 'admin' && task.status === 'open'
    );
    title = 'Tasks Needing Review';
    emptyMessage = 'No tasks need your review at this time.';
  } else if (isMaintenance) {
    // For maintenance: Assigned tasks not yet started
    actionItems = tasks.filter(task =>
      task.status === 'assigned'
    );
    title = 'Tasks Assigned to You';
    emptyMessage = 'No tasks are currently assigned to you.';
  } else if (isSupport) {
    // For support: Assigned tasks not yet started
    actionItems = tasks.filter(task =>
      task.status === 'assigned'
    );
    title = 'Tasks Assigned to You';
    emptyMessage = 'No tasks are currently assigned to you.';
  } else if (isSupplier) {
    // For suppliers: Tasks with accepted offers
    actionItems = tasks.filter(task =>
      task.status === 'assigned' && task.assigned_to === 'current_user_id' // This would need to be the actual user ID
    );
    title = 'Your Accepted Jobs';
    emptyMessage = 'No jobs have been accepted yet.';
  }

  // Limit to 3 items
  const displayItems = actionItems.slice(0, 3);

  return (
    <Card className="mb-8">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold flex items-center">
          <AlertCircle className="h-5 w-5 text-amber-500 mr-2" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {isLoading ? (
            Array(2).fill(0).map((_, index) => (
              <div key={index} className="border rounded-lg p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <div className="flex justify-between items-center">
                  <Skeleton className="h-4 w-40" />
                  <Skeleton className="h-8 w-16" />
                </div>
              </div>
            ))
          ) : displayItems.length > 0 ? (
            displayItems.map((task) => (
              <div key={task.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <Link to={`/tasks/enhanced/${task.id}`} className="font-medium hover:text-classtasker-blue">
                    {task.title}
                  </Link>
                  <Badge
                    variant="outline"
                    className={
                      task.visibility === 'admin' && task.status === 'open'
                        ? 'bg-purple-100 text-purple-800 border-purple-200'
                        : task.status === 'assigned'
                        ? 'bg-blue-100 text-blue-800 border-blue-200'
                        : 'bg-amber-100 text-amber-800 border-amber-200'
                    }
                  >
                    {task.visibility === 'admin' && task.status === 'open'
                      ? 'Pending Review'
                      : task.status.charAt(0).toUpperCase() + task.status.slice(1).replace('_', ' ')}
                  </Badge>
                </div>
                <div className="text-sm text-gray-500 flex items-center mb-3">
                  <Clock className="h-3.5 w-3.5 mr-1" />
                  Due: {new Date(task.due_date).toLocaleDateString()}
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">£{task.budget}</span>
                  <Button variant="outline" size="sm" asChild>
                    <Link to={`/tasks/enhanced/${task.id}`}>View Details</Link>
                  </Button>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-6">
              <p className="text-gray-500">{emptyMessage}</p>
            </div>
          )}

          {displayItems.length > 0 && (
            <div className="pt-2 text-right">
              <Button variant="ghost" size="sm" className="text-classtasker-blue" asChild>
                <Link to={isTeacher || isAdmin || isSupport ? "/dashboard?tab=my-tasks" : "/dashboard?tab=my-jobs"}>
                  View All <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ActionItems;
