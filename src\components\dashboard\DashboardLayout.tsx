
import { ReactNode, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { shouldBypassAuthRedirect } from '@/utils/routeUtils';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  ListTodo,
  CheckCircle,
  PoundSterling,
  MessageSquare,
  Star,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface DashboardLayoutProps {
  children: ReactNode;
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const DashboardLayout = ({ children, activeTab, setActiveTab }: DashboardLayoutProps) => {
  const { user, profile, isSchool, isSupplier } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Log for debugging
  if (process.env.NODE_ENV === 'development') {

    console.log("DashboardLayout - Profile:", profile);

    }
  if (process.env.NODE_ENV === 'development') {

    console.log("DashboardLayout - isSchool:", isSchool);

    }
  if (process.env.NODE_ENV === 'development') {

    console.log("DashboardLayout - isSupplier:", isSupplier);


    }
  // Only redirect to login if not authenticated and trying to access the dashboard
  useEffect(() => {
    if (!user && location.pathname === '/dashboard') {
      if (process.env.NODE_ENV === 'development') {

        console.log("DashboardLayout - Redirecting to login from dashboard");

        }
      navigate('/login', { replace: true });
    }
  }, [user, navigate, location.pathname]);

  // If authentication is still loading or user is not authenticated, don't render dashboard
  if (!user) {
    return null; // Don't render anything while checking auth state
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col lg:flex-row justify-between items-start gap-6">
        {/* Sidebar */}
        <div className="w-full lg:w-64 space-y-6">
          <Card>
            <CardContent className="p-6">
              <h2 className="font-semibold text-lg mb-4">Dashboard</h2>

              <nav className="space-y-1">
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${activeTab === 'overview' ? 'bg-blue-50 text-classtasker-blue' : ''}`}
                  onClick={() => setActiveTab('overview')}
                >
                  <ListTodo className="mr-2 h-4 w-4" />
                  Overview
                </Button>

                {isSchool ? (
                  <>
                    <Button
                      variant="ghost"
                      className={`w-full justify-start ${activeTab === 'my-tasks' ? 'bg-blue-50 text-classtasker-blue' : ''}`}
                      onClick={() => setActiveTab('my-tasks')}
                    >
                      <ListTodo className="mr-2 h-4 w-4" />
                      My Posted Tasks
                    </Button>
                    <Button
                      variant="ghost"
                      className={`w-full justify-start ${activeTab === 'offers' ? 'bg-blue-50 text-classtasker-blue' : ''}`}
                      onClick={() => setActiveTab('offers')}
                    >
                      <PoundSterling className="mr-2 h-4 w-4" />
                      Task Offers
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      variant="ghost"
                      className={`w-full justify-start ${activeTab === 'my-jobs' ? 'bg-blue-50 text-classtasker-blue' : ''}`}
                      onClick={() => setActiveTab('my-jobs')}
                    >
                      <CheckCircle className="mr-2 h-4 w-4" />
                      My Assigned Jobs
                    </Button>
                    <Button
                      variant="ghost"
                      className={`w-full justify-start ${activeTab === 'my-offers' ? 'bg-blue-50 text-classtasker-blue' : ''}`}
                      onClick={() => setActiveTab('my-offers')}
                    >
                      <PoundSterling className="mr-2 h-4 w-4" />
                      My Submitted Offers
                    </Button>
                  </>
                )}

                <Button
                  variant="ghost"
                  className={`w-full justify-start ${activeTab === 'messages' ? 'bg-blue-50 text-classtasker-blue' : ''}`}
                  onClick={() => setActiveTab('messages')}
                >
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Messages
                </Button>
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${activeTab === 'reviews' ? 'bg-blue-50 text-classtasker-blue' : ''}`}
                  onClick={() => setActiveTab('reviews')}
                >
                  <Star className="mr-2 h-4 w-4" />
                  Reviews
                </Button>
              </nav>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <h3 className="font-semibold mb-4">Quick Actions</h3>
              {isSchool ? (
                <Button className="w-full bg-classtasker-blue hover:bg-blue-600" asChild>
                  <Link to="/post-task">Create New Task</Link>
                </Button>
              ) : (
                <Button className="w-full bg-classtasker-blue hover:bg-blue-600" asChild>
                  <Link to="/tasks">Find Available Tasks</Link>
                </Button>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {children}
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
