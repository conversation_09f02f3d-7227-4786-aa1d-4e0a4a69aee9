
import { Card, CardContent } from '@/components/ui/card';
import {
  ListTodo,
  PoundSterling,
  CheckCircle,
  AlertCircle,
  Clock,
  User,
  PlayCircle
} from 'lucide-react';

interface DashboardStatsProps {
  activeTasks: number | string;
  offers: number | string;
  completedTasks: number | string;
  pendingReviewTasks: number | string;
  assignedTasks: number | string;
  inProgressTasks: number | string;
  urgentTasks: number | string;
  acceptedJobsCount: number | string;
  isTeacher: boolean;
  isAdmin: boolean;
  isMaintenance: boolean;
  isSupport?: boolean;
  isSupplier: boolean;
  isLoading: boolean;
}

// Interface for stat items
interface StatItem {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
  tooltip?: string;
}

const DashboardStats = ({
  activeTasks,
  offers,
  completedTasks,
  pendingReviewTasks,
  assignedTasks,
  inProgressTasks,
  urgentTasks,
  acceptedJobsCount,
  isTeacher,
  isAdmin,
  isMaintenance,
  isSupport,
  isSupplier,
  isLoading
}: DashboardStatsProps) => {
  // Define which stats to show based on user role
  let statsToShow = [];

  if (isTeacher) {
    statsToShow = [
      {
        title: 'Pending Review',
        value: pendingReviewTasks,
        icon: <AlertCircle className="h-5 w-5 text-purple-500" />,
        color: 'text-purple-600'
      },
      {
        title: 'Active Tasks',
        value: activeTasks,
        icon: <ListTodo className="h-5 w-5 text-classtasker-blue" />,
        color: 'text-classtasker-blue',
        tooltip: 'Tasks that are new, pending review, assigned, in progress, or completed but not closed'
      },
      {
        title: 'Completed',
        value: completedTasks,
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
        color: 'text-green-600'
      },
      {
        title: 'Due Soon',
        value: urgentTasks,
        icon: <Clock className="h-5 w-5 text-amber-500" />,
        color: 'text-amber-600'
      }
    ];
  } else if (isAdmin) {
    statsToShow = [
      {
        title: 'Needs Review',
        value: pendingReviewTasks,
        icon: <AlertCircle className="h-5 w-5 text-purple-500" />,
        color: 'text-purple-600'
      },
      {
        title: 'Active Tasks',
        value: activeTasks,
        icon: <ListTodo className="h-5 w-5 text-classtasker-blue" />,
        color: 'text-classtasker-blue',
        tooltip: 'Tasks that are new, pending review, assigned, in progress, or completed but not closed'
      },
      {
        title: 'Assigned',
        value: assignedTasks,
        icon: <User className="h-5 w-5 text-blue-500" />,
        color: 'text-blue-600'
      },
      {
        title: 'Completed',
        value: completedTasks,
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
        color: 'text-green-600'
      }
    ];
  } else if (isMaintenance) {
    statsToShow = [
      {
        title: 'Assigned to You',
        value: assignedTasks,
        icon: <User className="h-5 w-5 text-blue-500" />,
        color: 'text-blue-600'
      },
      {
        title: 'In Progress',
        value: inProgressTasks,
        icon: <PlayCircle className="h-5 w-5 text-indigo-500" />,
        color: 'text-indigo-600'
      },
      {
        title: 'Completed',
        value: completedTasks,
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
        color: 'text-green-600'
      },
      {
        title: 'Due Soon',
        value: urgentTasks,
        icon: <Clock className="h-5 w-5 text-amber-500" />,
        color: 'text-amber-600'
      }
    ];
  } else if (isSupport) {
    statsToShow = [
      {
        title: 'Assigned to You',
        value: assignedTasks,
        icon: <User className="h-5 w-5 text-blue-500" />,
        color: 'text-blue-600'
      },
      {
        title: 'In Progress',
        value: inProgressTasks,
        icon: <PlayCircle className="h-5 w-5 text-indigo-500" />,
        color: 'text-indigo-600'
      },
      {
        title: 'Completed',
        value: completedTasks,
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
        color: 'text-green-600'
      },
      {
        title: 'Due Soon',
        value: urgentTasks,
        icon: <Clock className="h-5 w-5 text-amber-500" />,
        color: 'text-amber-600'
      }
    ];
  } else if (isSupplier) {
    statsToShow = [
      {
        title: 'Active Jobs',
        value: acceptedJobsCount,
        icon: <User className="h-5 w-5 text-blue-500" />,
        color: 'text-blue-600'
      },
      {
        title: 'Pending Offers',
        value: offers,
        icon: <PoundSterling className="h-5 w-5 text-classtasker-green" />,
        color: 'text-classtasker-green'
      },
      {
        title: 'Completed Jobs',
        value: completedTasks,
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
        color: 'text-green-600'
      },
      {
        title: 'Due Soon',
        value: urgentTasks,
        icon: <Clock className="h-5 w-5 text-amber-500" />,
        color: 'text-amber-600'
      }
    ];
  }

  // Limit to 4 stats
  const displayStats = statsToShow.slice(0, 4);

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      {displayStats.map((stat, index) => (
        <Card key={index} title={stat.tooltip}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-500">
                {stat.title}
                {stat.tooltip && (
                  <span className="ml-1 inline-block w-4 h-4 text-xs text-gray-400 border border-gray-300 rounded-full text-center cursor-help" title={stat.tooltip}>
                    ?
                  </span>
                )}
              </h3>
              {stat.icon}
            </div>
            <p className={`text-2xl font-bold mt-2 ${stat.color}`}>
              {isLoading ? '-' : stat.value}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default DashboardStats;
