import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageSquare, Loader2, RefreshCw } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/AuthContext';
import { Link } from 'react-router-dom';
import { Skeleton } from '@/components/ui/skeleton';
import { supabase } from '@/integrations/supabase/client';
import { StreamChat } from 'stream-chat';
import {
  getStreamClient,
  connectUser,
  disconnectUser,
  incrementConnectionCount,
  decrementConnectionCount,
  syncUserChannelsWithTasks,
  getAllUserTaskChannels
} from '@/integrations/getstream/client';
import { isPWA } from '@/utils/pwa-utils';
import { Chat, Channel, ChannelHeader, MessageInput, MessageList, Window } from 'stream-chat-react';

// Import GetStream CSS
import 'stream-chat-react/dist/css/v2/index.css';
import '@stream-io/stream-chat-css/dist/v2/css/index.css';

// Import custom CSS for GetStream chat
import '@/styles/getstream-chat.css';

interface ChatChannel {
  id: string;
  name: string;
  taskId?: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
  isActive?: boolean;
  taskStatus?: string;
  priority?: 'high' | 'medium' | 'low';
}

const GetStreamMessagesSection = () => {
  const [channels, setChannels] = useState<ChatChannel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [selectedChannelId, setSelectedChannelId] = useState<string | null>(null);
  const [streamClient, setStreamClient] = useState<StreamChat | null>(null);
  const [activeChannel, setActiveChannel] = useState<any>(null);
  const [hasMoreChannels, setHasMoreChannels] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [showArchived, setShowArchived] = useState(false);
  const { user, profile } = useAuth();

  // Smart channel loading with priority-based filtering
  const loadChannels = async (page = 0, append = false) => {
    if (!streamClient || !user) return;

    try {
      const pageSize = 20; // Smaller pages for better performance
      const offset = page * pageSize;

      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[GetStreamMessagesSection] Loading channels page ${page}, offset ${offset}`);
          }
      }

      // Smart filtering: Recent and active chats first
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Priority filter: Recent activity first, then older
      const filter = {
        type: 'messaging',
        members: { $in: [user.id] },
        // Prioritize channels with recent activity
        ...(page === 0 && !showArchived ? {
          last_message_at: { $gte: thirtyDaysAgo.toISOString() }
        } : {})
      };

      const sort = { last_message_at: -1 };

      const streamChannels = await streamClient.queryChannels(filter, sort, {
        limit: pageSize,
        offset: offset,
        state: true,
        watch: false,
        message_limit: 1,
      });

      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[GetStreamMessagesSection] Page ${page}: Found ${streamChannels.length} channels`);
          }
      }

      // Check if there are more channels
      const hasMore = streamChannels.length === pageSize;
      setHasMoreChannels(hasMore);

      // Get task details for priority classification
      const taskChannels = streamChannels.filter(channel => channel.data?.task_id);
      let taskDetails = new Map();

      if (taskChannels.length > 0) {
        const taskIds = taskChannels.map(channel => channel.data.task_id);
        const { data: tasks } = await supabase
          .from('tasks')
          .select('id, title, status, due_date, assigned_to, user_id, organization_id, visibility')
          .in('id', taskIds);

        if (tasks) {
          taskDetails = new Map(tasks.map(task => [task.id, task]));
        }
      }

      // Format channels with smart priority classification
      const formattedChannels = streamChannels.map(channel => {
        const taskId = channel.id.startsWith('task-') ? channel.id.substring(5) : channel.data?.task_id;
        const task = taskDetails.get(taskId);

        // Determine priority based on task status and activity
        let priority: 'high' | 'medium' | 'low' = 'medium';
        let isActive = true;

        if (task) {
          // High priority: Overdue or urgent tasks
          if (task.due_date && new Date(task.due_date) < now) {
            priority = 'high';
          }
          // High priority: Recently assigned tasks
          else if (task.status === 'assigned' || task.status === 'in_progress') {
            priority = 'high';
          }
          // Low priority: Completed or closed tasks
          else if (task.status === 'completed' || task.status === 'closed') {
            priority = 'low';
            isActive = false;
          }
        }

        // Check if channel has recent activity
        const lastActivity = channel.data?.last_message_at;
        if (lastActivity && new Date(lastActivity) < thirtyDaysAgo) {
          isActive = false;
          if (priority === 'medium') priority = 'low';
        }

        return {
          id: channel.id,
          name: task?.title || channel.data?.name || 'Chat',
          taskId: taskId,
          lastMessage: channel.state?.messages?.[0]?.text || 'No messages yet',
          lastMessageTime: channel.data?.last_message_at,
          unreadCount: 0,
          isActive,
          taskStatus: task?.status,
          priority
        };
      });

      // Sort by priority and activity
      formattedChannels.sort((a, b) => {
        // First by priority
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority!] - priorityOrder[a.priority!];
        if (priorityDiff !== 0) return priorityDiff;

        // Then by activity status
        if (a.isActive !== b.isActive) return a.isActive ? -1 : 1;

        // Finally by last message time
        if (!a.lastMessageTime) return 1;
        if (!b.lastMessageTime) return -1;
        return new Date(b.lastMessageTime).getTime() - new Date(a.lastMessageTime).getTime();
      });

      if (append) {
        setChannels(prev => [...prev, ...formattedChannels]);
      } else {
        setChannels(formattedChannels);
      }

      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[GetStreamMessagesSection] Loaded ${formattedChannels.length} formatted channels for page ${page}`);
          }
      }

    } catch (error) {
      console.error(`[GetStreamMessagesSection] Error loading channels page ${page}:`, error);
    }
  };

  // Load more channels function
  const loadMoreChannels = async () => {
    if (isLoadingMore || !hasMoreChannels) return;

    setIsLoadingMore(true);
    const nextPage = currentPage + 1;
    await loadChannels(nextPage, true);
    setCurrentPage(nextPage);
    setIsLoadingMore(false);
  };

  // Toggle archived channels
  const toggleArchivedChannels = async () => {
    setShowArchived(!showArchived);
    setCurrentPage(0);
    setIsLoading(true);
    await loadChannels(0, false);
    setIsLoading(false);
  };

  // Initialize GetStream client
  useEffect(() => {
    if (!user) return;

    const initializeStreamClient = async () => {
      try {
        setIsLoading(true);

        // Increment connection count
        incrementConnectionCount();

        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        // Check if we're in PWA mode
        const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
            console.log('[GetStreamMessagesSection] Initializing Stream client, PWA mode:', isPWA);
            }
        }

        // Connect to GetStream with retry logic
        let client;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            if (process.env.NODE_ENV === 'development') {
              if (process.env.NODE_ENV === 'development') {
                console.log(`[GetStreamMessagesSection] Connecting to Stream (attempt ${retryCount + 1}/${maxRetries})`);
                }
            }
            client = await connectUser(
              user.id,
              userName,
              user.id, // This is just a placeholder, the actual token is generated server-side
              profile?.avatar_url || undefined
            );

            // If we get here, connection was successful
            if (process.env.NODE_ENV === 'development') {
              if (process.env.NODE_ENV === 'development') {
                console.log('[GetStreamMessagesSection] Successfully connected to Stream');
                }
            }
            break;
          } catch (connectError) {
            retryCount++;
            console.error(`[GetStreamMessagesSection] Error connecting to Stream (attempt ${retryCount}/${maxRetries}):`, connectError);

            if (retryCount >= maxRetries) {
              // Decrement connection count on final failure
              decrementConnectionCount(false);
              throw connectError; // Re-throw if we've exhausted retries
            }

            // Wait before retrying (exponential backoff)
            const delay = Math.min(1000 * Math.pow(2, retryCount), 8000);
            if (process.env.NODE_ENV === 'development') {
              if (process.env.NODE_ENV === 'development') {
                console.log(`[GetStreamMessagesSection] Retrying in ${delay}ms...`);
                }
            }
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (!client) {
          throw new Error('Failed to connect to Stream after multiple attempts');
        }

        setStreamClient(client);

        // Verify client is connected before querying channels
        if (!client.isConnected()) {
          console.warn('[GetStreamMessagesSection] Client reports as not connected, attempting to reconnect');

          // Try to reconnect
          await client.connectUser(
            {
              id: user.id,
              name: userName,
              image: profile?.avatar_url || undefined
            },
            await client.tokenProvider.getToken()
          );
        }

        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
            console.log('[GetStreamMessagesSection] Using smart channel loading with priority filtering');
            }
        }

        // Use the new smart loading function
        await loadChannels(0, false);
        setIsLoading(false);
      } catch (error) {
        console.error('[GetStreamMessagesSection] Error initializing Stream client:', error);
        setIsLoading(false);

        // Decrement connection count on error
        decrementConnectionCount(false);
      }
    };

    initializeStreamClient();

    return () => {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.log('[GetStreamMessagesSection] Component unmounting');
          }
      }

      // Decrement connection count and let the manager handle disconnection
      try {
        decrementConnectionCount(true);
      } catch (error) {
        console.warn('[GetStreamMessagesSection] Error in cleanup function:', error);
      }
    };
  }, [user, profile]);

  // Handle channel selection with ON-DEMAND LOADING
  const handleSelectChannel = async (channelId: string) => {
    if (!streamClient) {
      console.error('[GetStreamMessagesSection] Cannot select channel: Stream client is not initialized');
      return;
    }

    try {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[GetStreamMessagesSection] 🔄 Loading full chat for channel: ${channelId}`);
          }
      }
      setSelectedChannelId(channelId);

      // Check if client is connected
      if (!streamClient.isConnected()) {
        console.warn('[GetStreamMessagesSection] Client not connected, attempting to reconnect');

        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        try {
          // Try to reconnect
          await streamClient.connectUser(
            {
              id: user.id,
              name: userName,
              image: profile?.avatar_url || undefined
            },
            await streamClient.tokenProvider.getToken()
          );
          if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {
              console.log('[GetStreamMessagesSection] Successfully reconnected client');
              }
          }
        } catch (reconnectError) {
          console.error('[GetStreamMessagesSection] Failed to reconnect client:', reconnectError);

          // Create a new client as a last resort
          if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {
              console.log('[GetStreamMessagesSection] Creating new client as fallback');
              }
          }
          const newClient = await connectUser(
            user.id,
            userName,
            user.id,
            profile?.avatar_url || undefined
          );

          setStreamClient(newClient);
        }
      }

      // NOW we load the full channel state (on-demand)
      const channel = streamClient.channel('messaging', channelId);

      // Watch the channel to get full state and real-time updates
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[GetStreamMessagesSection] 📡 Watching channel for full state: ${channelId}`);
          }
      }
      await channel.watch();

      // Update channel name with task title if needed (on-demand)
      const taskId = channel.data?.task_id ||
        (channel.id.startsWith('task-') ? channel.id.replace('task-', '') : null);

      if (taskId && (!channel.data?.name || channel.data.name === 'Chat' || channel.data.name === 'Task Chat')) {
        try {
          const { data: taskData } = await supabase
            .from('tasks')
            .select('title')
            .eq('id', taskId)
            .single();

          if (taskData?.title) {
            await channel.update({
              name: taskData.title,
              task_id: taskId
            });

            // Update the channel in our list
            setChannels(prev => prev.map(ch =>
              ch.id === channelId ? { ...ch, name: taskData.title } : ch
            ));
          }
        } catch (error) {
          console.error('[GetStreamMessagesSection] Error updating channel name:', error);
        }
      }

      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[GetStreamMessagesSection] ✅ Successfully loaded full chat: ${channelId}`);
          }
      }
      setActiveChannel(channel);
    } catch (error) {
      console.error('[GetStreamMessagesSection] ❌ Error loading chat:', error);
    }
  };

  // Get initials for avatar
  const getInitials = (name: string): string => {
    if (!name) return 'C';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Format date
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4">Messages</h2>
          <div className="flex justify-center py-4">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle refresh using smart loading
  const handleRefresh = async () => {
    if (!user || !streamClient || isRefreshing) return;

    setIsRefreshing(true);
    setCurrentPage(0);

    try {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.log('[GetStreamMessagesSection] Refreshing channels with smart loading');
          }
      }
      await loadChannels(0, false);
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.log('[GetStreamMessagesSection] Refresh completed successfully');
          }
      }
    } catch (error) {
      console.error('[GetStreamMessagesSection] Error refreshing channels:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-semibold">Messages</h2>
            <p className="text-sm text-gray-500">
              {showArchived ? 'All conversations' : 'Recent & active conversations'}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleArchivedChannels}
              disabled={isLoading}
            >
              {showArchived ? 'Show Recent' : 'Show All'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing || isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Channel List */}
          <div className="md:col-span-1 border rounded-md overflow-hidden">
            <div className="bg-gray-50 p-3 border-b">
              <h3 className="font-medium text-sm">Recent Conversations</h3>
            </div>
            <div className="divide-y overflow-y-auto" style={{ maxHeight: '400px' }}>
              {channels.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No conversations yet</p>
                </div>
              ) : (
                <>
                  {channels.map((channel) => (
                    <div
                      key={channel.id}
                      className={`p-3 cursor-pointer hover:bg-gray-50 ${
                        selectedChannelId === channel.id ? 'bg-blue-50' : ''
                      } ${!channel.isActive ? 'opacity-75' : ''}`}
                      onClick={() => handleSelectChannel(channel.id)}
                    >
                      <div className="flex items-start">
                        <Avatar className="h-8 w-8 mr-3">
                          <AvatarFallback>{getInitials(channel.name)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start">
                            <div className="flex items-center gap-1">
                              <h4 className="font-medium text-sm truncate">{channel.name}</h4>
                              {/* Priority indicator */}
                              {channel.priority === 'high' && (
                                <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0" title="High priority" />
                              )}
                              {channel.priority === 'medium' && channel.isActive && (
                                <div className="w-2 h-2 bg-yellow-500 rounded-full flex-shrink-0" title="Active" />
                              )}
                            </div>
                            <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                              {formatDate(channel.lastMessageTime)}
                            </span>
                          </div>
                          <div className="flex items-center gap-1 mt-1">
                            <p className="text-xs text-gray-500 truncate flex-1">
                              {channel.lastMessage}
                            </p>
                            {channel.taskStatus && (
                              <span className={`text-xs px-1 py-0.5 rounded text-white flex-shrink-0 ${
                                channel.taskStatus === 'assigned' || channel.taskStatus === 'in_progress'
                                  ? 'bg-green-500'
                                  : channel.taskStatus === 'completed'
                                  ? 'bg-gray-500'
                                  : 'bg-blue-500'
                              }`}>
                                {channel.taskStatus}
                              </span>
                            )}
                          </div>
                          {channel.taskId && (
                            <Link
                              to={`/tasks/${channel.taskId}`}
                              className="text-xs text-classtasker-blue hover:underline mt-1 inline-block"
                              onClick={(e) => e.stopPropagation()}
                            >
                              View Task
                            </Link>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Load More Button */}
                  {hasMoreChannels && (
                    <div className="p-3 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={loadMoreChannels}
                        disabled={isLoadingMore}
                        className="w-full"
                      >
                        {isLoadingMore ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Loading...
                          </>
                        ) : (
                          'Load More Conversations'
                        )}
                      </Button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>

          {/* Chat Window */}
          <div className="md:col-span-2">
            {selectedChannelId && streamClient && activeChannel ? (
              <div className="border rounded-md overflow-hidden h-[400px] flex flex-col">
                <Chat client={streamClient} theme="messaging light">
                  <Channel channel={activeChannel}>
                    <Window>
                      <ChannelHeader />
                      <MessageList />
                      <MessageInput />
                    </Window>
                  </Channel>
                </Chat>
              </div>
            ) : (
              <div className="border rounded-md flex items-center justify-center p-8 h-[400px]">
                <p className="text-gray-500">
                  {channels.length === 0 ?
                    'You have no conversations yet' :
                    'Select a conversation to start messaging'}
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default GetStreamMessagesSection;
