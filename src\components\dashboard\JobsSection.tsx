
import { Card, CardContent } from '@/components/ui/card';
import { useOffers } from '@/hooks/use-offers';
import { useTasks } from '@/hooks/use-tasks';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useEffect, useState } from 'react';
import { Task } from '@/services/taskService';
import { useQueryClient } from '@tanstack/react-query';

const JobsSection = () => {
  const { getUserOffers } = useOffers();
  const { tasks, isLoadingTasks } = useTasks();
  const { user, isSupplier } = useAuth();
  const [activeJobs, setActiveJobs] = useState<Task[]>([]);
  const queryClient = useQueryClient();

  // Use direct getUserOffers call with forced periodic refetch
  const { 
    data: userOffers, 
    isLoading: isLoadingUserOffers,
    refetch: refetchOffers 
  } = getUserOffers();

  // Add periodic refetch to ensure data is fresh
  useEffect(() => {
    const intervalId = setInterval(() => {
      refetchOffers();
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    }, 2000);
    
    return () => clearInterval(intervalId);
  }, [refetchOffers, queryClient]);

  useEffect(() => {
    // For suppliers: Find tasks where they have an accepted offer
    if (isSupplier && userOffers && tasks) {
      if (process.env.NODE_ENV === 'development') {

        console.log('JobsSection - All user offers for supplier: completed');

        }
      if (process.env.NODE_ENV === 'development') {
    console.log('JobsSection - All tasks: completed');
  }
      // First, find all accepted offers by this supplier
      const acceptedOffers = userOffers.filter(offer => offer.status === 'accepted');
      if (process.env.NODE_ENV === 'development') {
    console.log('JobsSection - Accepted offers for supplier:', acceptedOffers);
  }
      // Then find tasks that match these accepted offers
      const assignedUserTasks = tasks.filter(task => 
        acceptedOffers.some(offer => offer.task_id === task.id && task.status === 'assigned')
      );
      if (process.env.NODE_ENV === 'development') {

        console.log('JobsSection - Assigned tasks for supplier: completed');
      

        }
      // These are the active jobs
      setActiveJobs(assignedUserTasks);
    } 
    // For schools: Find their tasks that are assigned (have accepted offers)
    else if (!isSupplier && tasks) {
      const assignedTasks = tasks.filter(task => task.status === 'assigned');
      if (process.env.NODE_ENV === 'development') {
    console.log('JobsSection - School active jobs:', assignedTasks);
  }
      setActiveJobs(assignedTasks);
    }
  }, [userOffers, tasks, isSupplier, user?.id]);

  if (isLoadingUserOffers || isLoadingTasks) {
    return (
      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4">My Jobs</h2>
          <div className="space-y-4">
            {Array(3).fill(0).map((_, i) => (
              <div key={i} className="border rounded-lg p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-1/3" />
                  <Skeleton className="h-4 w-1/5" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-6">
        <h2 className="text-xl font-semibold mb-6">My Jobs</h2>
        
        {activeJobs.length > 0 ? (
          <div className="space-y-4">
            {activeJobs.map(job => (
              <div key={job.id} className="border rounded-lg p-4">
                <Link to={`/tasks/${job.id}`} className="font-medium hover:text-classtasker-blue block mb-1">
                  {job.title}
                </Link>
                <p className="text-sm text-gray-600 mb-2 line-clamp-2">{job.description}</p>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600">
                    Due: {new Date(job.due_date).toLocaleDateString()}
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <Link to={`/tasks/${job.id}`}>View Details</Link>
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6">
            <p className="text-gray-500">You don't have any active jobs.</p>
            <Button className="mt-4 bg-classtasker-blue hover:bg-blue-600" asChild>
              <Link to="/tasks">Find Tasks</Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default JobsSection;
