
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { PoundSterling, MessageSquare, CheckCircle, Bell, Trash2 } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { useNotifications } from '@/contexts/NotificationContext';
import { useAuth } from '@/contexts/AuthContext';
import { Link } from 'react-router-dom';

interface NotificationsListProps {
  limit?: number;
  showMarkAllButton?: boolean;
  showEmptyState?: boolean;
}

const NotificationsList = ({
  limit = 5,
  showMarkAllButton = true,
  showEmptyState = true
}: NotificationsListProps) => {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    isLoading
  } = useNotifications();

  const { user } = useAuth();

  // Log user ID for debugging
  console.log('NotificationsList - Current user ID:', user?.id);

  // Limit the number of notifications to display
  const displayedNotifications = notifications.slice(0, limit);

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'offer':
        return <PoundSterling className="h-4 w-4" />;
      case 'message':
        return <MessageSquare className="h-4 w-4" />;
      case 'task_update':
      case 'system':
      default:
        return <CheckCircle className="h-4 w-4" />;
    }
  };

  // Get notification link based on type and relatedId
  const getNotificationLink = (notification: any) => {
    if (!notification.relatedId) return null;

    switch (notification.relatedType) {
      case 'task':
        return `/tasks/${notification.relatedId}`;
      case 'message':
        return `/dashboard?tab=messages`;
      case 'offer':
        return `/tasks/${notification.relatedId}`;
      default:
        return null;
    }
  };

  // Handle notification click
  const handleNotificationClick = async (id: string) => {
    await markAsRead(id);
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">
            Recent Notifications
            {unreadCount > 0 && (
              <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                {unreadCount} new
              </span>
            )}
          </h3>
          {showMarkAllButton && unreadCount > 0 && (
            <Button
              variant="ghost"
              className="text-classtasker-blue"
              onClick={() => markAllAsRead()}
            >
              Mark All as Read
            </Button>
          )}
        </div>

        {isLoading ? (
          <div className="space-y-4">
            {Array(3).fill(0).map((_, i) => (
              <div key={i} className="border rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-3 w-1/4" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : displayedNotifications.length > 0 ? (
          <div className="space-y-4">
            {displayedNotifications.map((notification) => {
              const link = getNotificationLink(notification);
              const NotificationContent = (
                <div
                  key={notification.id}
                  className={`border rounded-lg p-4 transition-colors ${
                    !notification.read ? 'bg-blue-50 border-blue-100' : ''
                  }`}
                  onClick={() => handleNotificationClick(notification.id)}
                >
                  <div className="flex items-start gap-3">
                    <div className={`rounded-full p-2 ${
                      notification.type === 'offer' ? 'bg-green-100 text-green-600' :
                      notification.type === 'message' ? 'bg-blue-100 text-blue-600' :
                      notification.type === 'task_update' ? 'bg-orange-100 text-orange-600' :
                      'bg-gray-100 text-gray-600'
                    }`}>
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="flex-1">
                      <p className={`${!notification.read ? 'font-medium' : ''}`}>
                        {notification.message}
                      </p>
                      <div className="flex justify-between items-center mt-1">
                        <p className="text-sm text-gray-500">{notification.time}</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-gray-400 hover:text-red-500"
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            deleteNotification(notification.id);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              );

              return link ? (
                <Link key={notification.id} to={link}>
                  {NotificationContent}
                </Link>
              ) : (
                NotificationContent
              );
            })}
          </div>
        ) : showEmptyState ? (
          <div className="text-center py-8">
            <Bell className="h-12 w-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500">You don't have any notifications yet.</p>
          </div>
        ) : null}

        {notifications.length > limit && (
          <div className="mt-4 text-center">
            <Button variant="outline" asChild>
              <Link to="/notifications">View All Notifications</Link>
            </Button>
          </div>
        )}


      </CardContent>
    </Card>
  );
};

export default NotificationsList;
