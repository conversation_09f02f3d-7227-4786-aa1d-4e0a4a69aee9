
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowRight } from 'lucide-react';
import { Task } from '@/services/taskService';

interface RecentTasksProps {
  tasks: Task[] | undefined;
  isLoading: boolean;
  isSchool: boolean;
}

const RecentTasks = ({ tasks, isLoading, isSchool }: RecentTasksProps) => {
  return (
    <Card className="mb-8">
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">
            {isSchool ? 'Your Recent Tasks' : 'Your Recent Jobs'}
          </h3>
          <Button variant="ghost" asChild className="text-classtasker-blue">
            <Link to={isSchool ? "/my-tasks" : "/my-jobs"}>
              View All <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        </div>

        <div className="space-y-4">
          {isLoading ? (
            Array(3).fill(0).map((_, index) => (
              <div key={index} className="border rounded-lg p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <div className="flex justify-between items-center">
                  <Skeleton className="h-4 w-40" />
                  <Skeleton className="h-8 w-16" />
                </div>
              </div>
            ))
          ) : tasks && tasks.length > 0 ? (
            tasks.slice(0, 3).map((task) => (
              <div key={task.id} className="border rounded-lg p-4 flex justify-between items-center">
                <div>
                  <Link to={`/tasks/${task.id}`} className="font-medium hover:text-classtasker-blue">
                    {task.title}
                  </Link>
                  <div className="text-sm text-gray-500 mt-1">
                    Due: {new Date(task.due_date).toLocaleDateString()} • £{task.budget}
                  </div>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link to={`/tasks/${task.id}`}>View</Link>
                </Button>
              </div>
            ))
          ) : (
            <div className="text-center py-6">
              <p className="text-gray-500">You haven't posted any tasks yet.</p>
              {isSchool && (
                <Button className="mt-4 bg-classtasker-blue hover:bg-blue-600" asChild>
                  <Link to="/post-task">Post Your First Task</Link>
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentTasks;
