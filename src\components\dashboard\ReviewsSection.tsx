
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

const ReviewsSection = () => {
  return (
    <Card>
      <CardContent className="p-6">
        <h2 className="text-xl font-semibold mb-4">Reviews</h2>
        
        <div className="flex items-center mb-6">
          <div className="mr-6">
            <span className="text-5xl font-bold">4.8</span>
            <div className="flex text-yellow-400 mt-2">
              ★★★★★
            </div>
            <p className="text-sm text-gray-500 mt-1">12 reviews</p>
          </div>
          
          <div className="flex-1 space-y-2">
            <div className="flex items-center">
              <span className="w-14 text-sm">5 stars</span>
              <Progress value={83} className="h-2 flex-1 mx-3" />
              <span className="w-8 text-sm text-right">10</span>
            </div>
            <div className="flex items-center">
              <span className="w-14 text-sm">4 stars</span>
              <Progress value={17} className="h-2 flex-1 mx-3" />
              <span className="w-8 text-sm text-right">2</span>
            </div>
            <div className="flex items-center">
              <span className="w-14 text-sm">3 stars</span>
              <Progress value={0} className="h-2 flex-1 mx-3" />
              <span className="w-8 text-sm text-right">0</span>
            </div>
            <div className="flex items-center">
              <span className="w-14 text-sm">2 stars</span>
              <Progress value={0} className="h-2 flex-1 mx-3" />
              <span className="w-8 text-sm text-right">0</span>
            </div>
            <div className="flex items-center">
              <span className="w-14 text-sm">1 star</span>
              <Progress value={0} className="h-2 flex-1 mx-3" />
              <span className="w-8 text-sm text-right">0</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReviewsSection;
