
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import TaskCard from '@/components/tasks/TaskCard';
import { Task } from '@/services/taskService';
import { useAuth } from '@/contexts/AuthContext';
import { useEffect } from 'react';

interface TasksListProps {
  tasks: Task[] | undefined;
  isLoading: boolean;
}

const TasksList = ({ tasks, isLoading }: TasksListProps) => {
  const { userRole, user } = useAuth();
  const isTeacher = userRole === 'teacher';

  // Log tasks for debugging (development only)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && isTeacher && tasks) {
      if (process.env.NODE_ENV === 'development') {

        console.log('TasksList - Teacher tasks:', {
        totalTasks: tasks.length,
        taskDetails: tasks.map(t => ({
          id: t.id,
          title: t.title,
          visibility: t.visibility,
          status: t.status
        }))
      });

        }
    }
  }, [isTeacher, tasks]);

  return (
    <Card>
      <CardContent className="p-6">
        <h2 className="text-xl font-semibold mb-6">
          My Tasks
        </h2>
        <p className="text-gray-600 mb-4 text-sm">
          Tasks you've created and tasks assigned to you
        </p>
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array(4).fill(0).map((_, i) => (
              <div key={i} className="border rounded-lg p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-5/6 mb-4" />
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-4 w-1/4" />
                </div>
              </div>
            ))}
          </div>
        ) : tasks && tasks.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {tasks.map(task => {
              const isCreatedByUser = task.user_id === user?.id;
              const isAssignedToUser = task.assigned_to === user?.id;

              return (
                <Link
                  key={task.id}
                  to={`/tasks/${task.id}?messages=true`}
                  className="block"
                >
                  <TaskCard
                    id={task.id}
                    title={task.title}
                    description={task.description}
                    location={task.location}
                    dueDate={task.due_date}
                    budget={task.budget}
                    category={task.category}
                    status={task.status}
                    offers={task.offers_count}
                    showCreatedByMe={isCreatedByUser}
                    showAssignedToMe={isAssignedToUser}
                  />
                </Link>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">You don't have any tasks yet.</p>
            <p className="text-gray-400 mb-4 text-sm">Tasks you create or are assigned to will appear here.</p>
            <Button className="bg-classtasker-blue hover:bg-blue-600" asChild>
              <Link to="/tasks/create">Create Your First Task</Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TasksList;
