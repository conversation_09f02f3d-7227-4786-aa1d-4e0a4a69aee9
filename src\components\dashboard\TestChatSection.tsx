import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageSquare, Loader2, RefreshCw, Clock } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { StreamChat } from 'stream-chat';
import {
  getStreamClient,
  connectUser,
  disconnectUser,
  incrementConnectionCount,
  decrementConnectionCount
} from '@/integrations/getstream/client';
import { Chat, Channel, ChannelHeader, MessageInput, MessageList, Window } from 'stream-chat-react';

// Import GetStream CSS
import 'stream-chat-react/dist/css/v2/index.css';
import '@stream-io/stream-chat-css/dist/v2/css/index.css';
import '@/styles/getstream-chat.css';

interface LightweightChannel {
  id: string;
  name: string;
  taskId?: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
  memberCount?: number;
}

interface PerformanceMetrics {
  listLoadTime: number;
  chatLoadTime: number;
  totalChannels: number;
  secureChannels: number;
}

const TestChatSection = () => {
  const [channels, setChannels] = useState<LightweightChannel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedChannelId, setSelectedChannelId] = useState<string | null>(null);
  const [streamClient, setStreamClient] = useState<StreamChat | null>(null);
  const [activeChannel, setActiveChannel] = useState<any>(null);
  const [chatLoading, setChatLoading] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const { user, profile } = useAuth();

  // Initialize GetStream client and load lightweight channel list
  useEffect(() => {
    if (!user) return;

    const initializeTestChat = async () => {
      const startTime = performance.now();

      try {
        setIsLoading(true);
        if (process.env.NODE_ENV === 'development') {
          console.log('[TestChat] 🚀 Starting lightweight chat initialization...');

          }
        // Increment connection count
        incrementConnectionCount();

        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        // Connect to GetStream
        if (process.env.NODE_ENV === 'development') {
          console.log('[TestChat] 📡 Connecting to GetStream...');
          }
        const client = await connectUser(
          user.id,
          userName,
          user.id,
          profile?.avatar_url || undefined
        );

        setStreamClient(client);

        // LIGHTWEIGHT APPROACH: Get channel list without full state
        if (process.env.NODE_ENV === 'development') {
          console.log('[TestChat] 📋 Fetching lightweight channel list...');
          }
        const filter = { type: 'messaging', members: { $in: [user.id] } };
        const sort = { last_message_at: -1 };

        const lightweightChannels = await client.queryChannels(filter, sort, {
          limit: 20,
          state: true,         // ✅ Need state for member filtering to work
          watch: false,        // ✅ No real-time subscriptions
          message_limit: 1,    // ✅ Only last message for preview
        });

        if (process.env.NODE_ENV === 'development') {

          console.log(`[TestChat] 📊 Found ${lightweightChannels.length} channels (lightweight)`);


          }
        // DEBUG: Check for specific problematic channel
        const targetChannelId = 'task-71416246-5184-478f-b9cb-eaa14ce5e8bb';
        const targetChannel = lightweightChannels.find(ch => ch.id === targetChannelId);
        if (targetChannel) {
          if (process.env.NODE_ENV === 'development') {
            console.log(`[TestChat] ✅ FOUND target channel ${targetChannelId}:`, {
            id: targetChannel.id,
            taskId: targetChannel.data?.task_id,
            name: targetChannel.data?.name,
            members: Object.keys(targetChannel.state?.members || {}),
            memberDetails: targetChannel.state?.members,
            userIsMember: Object.keys(targetChannel.state?.members || {}).includes(user.id)
          });
            }
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.log(`[TestChat] ❌ TARGET CHANNEL NOT FOUND: ${targetChannelId}`);
            }
          if (process.env.NODE_ENV === 'development') {
            console.log('[TestChat] Available channel IDs:', lightweightChannels.map(ch => ch.id));
            }
        }

        // SECURITY: Filter channels based on organization membership
        const { data: userProfile } = await supabase
          .from('profiles')
          .select('organization_id, account_type')
          .eq('id', user.id)
          .single();

        const secureChannels = [];

        // Separate task and non-task channels
        const taskChannels = lightweightChannels.filter(channel =>
          channel.data?.task_id || channel.id.startsWith('task-')
        );
        const nonTaskChannels = lightweightChannels.filter(channel =>
          !channel.data?.task_id && !channel.id.startsWith('task-')
        );

        // Add non-task channels immediately
        secureChannels.push(...nonTaskChannels);

        if (taskChannels.length > 0) {
          // Extract task IDs for batch query
          const taskIds = taskChannels.map(channel =>
            channel.data?.task_id || channel.id.replace('task-', '')
          );

          // Batch query all tasks at once
          const { data: tasks, error: tasksError } = await supabase
            .from('tasks')
            .select('id, organization_id, visibility, title')
            .in('id', taskIds);

          if (tasksError) {
            console.error('[TestChat] ❌ Error fetching tasks for security filtering:', tasksError);
          } else if (tasks) {
            const taskMap = new Map(tasks.map(task => [task.id, task]));

            for (const channel of taskChannels) {
              const taskId = channel.data?.task_id || channel.id.replace('task-', '');
              const task = taskMap.get(taskId);

              if (!task) {
                console.warn('[TestChat] ⚠️ Task not found for channel:', channel.id);
                continue;
              }

              const hasAccess = (
                userProfile?.organization_id === task.organization_id ||
                (userProfile?.account_type === 'supplier' && task.visibility === 'public')
              );

              if (hasAccess) {
                secureChannels.push(channel);
              } else {
                console.warn('[TestChat] 🚫 Filtering out unauthorized channel:', {
                  channelId: channel.id,
                  taskId,
                  userOrg: userProfile?.organization_id,
                  taskOrg: task.organization_id
                });
              }
            }
          }
        }

        if (process.env.NODE_ENV === 'development') {
    console.log(`[TestChat] 🛡️ Secure channels after filtering: ${secureChannels.length}`);
  }
        // Format channels for lightweight display
        const formattedChannels: LightweightChannel[] = secureChannels.map(channel => {
          const taskId = channel.data?.task_id ||
            (channel.id.startsWith('task-') ? channel.id.replace('task-', '') : undefined);

          // Get channel name (will be enhanced when chat is opened)
          let channelName = channel.data?.name || 'Chat';
          if (taskId && (!channelName || channelName === 'Chat')) {
            channelName = 'Task Chat'; // Will be updated when opened
          }

          // Get last message preview
          const lastMessage = channel.state?.messages?.[0]?.text ||
                             channel.data?.last_message_text ||
                             'No messages yet';

          return {
            id: channel.id,
            name: channelName,
            taskId,
            lastMessage,
            lastMessageTime: channel.data?.last_message_at,
            unreadCount: 0, // Will be calculated when opened
            memberCount: Object.keys(channel.state?.members || {}).length
          };
        });

        // Sort by last message time
        formattedChannels.sort((a, b) => {
          if (!a.lastMessageTime) return 1;
          if (!b.lastMessageTime) return -1;
          return new Date(b.lastMessageTime).getTime() - new Date(a.lastMessageTime).getTime();
        });

        setChannels(formattedChannels);

        const endTime = performance.now();
        const loadTime = endTime - startTime;

        setMetrics({
          listLoadTime: loadTime,
          chatLoadTime: 0,
          totalChannels: lightweightChannels.length,
          secureChannels: secureChannels.length
        });

        if (process.env.NODE_ENV === 'development') {

          console.log(`[TestChat] ✅ Lightweight initialization complete in ${loadTime.toFixed(2)}ms`);

          }
        setIsLoading(false);

      } catch (error) {
        console.error('[TestChat] ❌ Error initializing test chat:', error);
        setIsLoading(false);
        decrementConnectionCount(false);
      }
    };

    initializeTestChat();

    return () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('[TestChat] 🧹 Component unmounting');
        }
      try {
        decrementConnectionCount(true);
      } catch (error) {
        console.warn('[TestChat] ⚠️ Error in cleanup:', error);
      }
    };
  }, [user, profile]);

  // Handle channel selection with on-demand loading
  const handleSelectChannel = async (channelId: string) => {
    if (!streamClient) {
      console.error('[TestChat] ❌ Cannot select channel: Stream client not initialized');
      return;
    }

    const startTime = performance.now();
    setChatLoading(true);
    setSelectedChannelId(channelId);

    try {
      if (process.env.NODE_ENV === 'development') {
        console.log(`[TestChat] 🔄 Loading full chat for channel: ${channelId}`);

        }
      // NOW we load the full channel state (on-demand)
      const channel = streamClient.channel('messaging', channelId);

      // Watch the channel to get full state and real-time updates
      await channel.watch();

      // Update channel name with task title if needed
      const taskId = channel.data?.task_id ||
        (channel.id.startsWith('task-') ? channel.id.replace('task-', '') : null);

      if (taskId && (!channel.data?.name || channel.data.name === 'Chat' || channel.data.name === 'Task Chat')) {
        try {
          const { data: taskData } = await supabase
            .from('tasks')
            .select('title')
            .eq('id', taskId)
            .single();

          if (taskData?.title) {
            await channel.update({
              name: taskData.title,
              task_id: taskId
            });

            // Update the channel in our list
            setChannels(prev => prev.map(ch =>
              ch.id === channelId ? { ...ch, name: taskData.title } : ch
            ));
          }
        } catch (error) {
          console.error('[TestChat] ⚠️ Error updating channel name:', error);
        }
      }

      setActiveChannel(channel);

      const endTime = performance.now();
      const chatLoadTime = endTime - startTime;

      setMetrics(prev => prev ? { ...prev, chatLoadTime } : null);

      if (process.env.NODE_ENV === 'development') {

        console.log(`[TestChat] ✅ Chat loaded in ${chatLoadTime.toFixed(2)}ms`);


        }
    } catch (error) {
      console.error('[TestChat] ❌ Error loading chat:', error);
    } finally {
      setChatLoading(false);
    }
  };

  // Get initials for avatar
  const getInitials = (name: string): string => {
    if (!name) return 'C';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Format date
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4">🧪 Test Chat (Lightweight Loading)</h2>
          <div className="flex justify-center py-4">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            <span className="ml-2">Loading lightweight chat list...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-semibold">🧪 Test Chat (Lightweight Loading)</h2>
            {metrics && (
              <div className="text-sm text-gray-600 mt-1">
                📊 List: {metrics.listLoadTime.toFixed(0)}ms |
                Chat: {metrics.chatLoadTime.toFixed(0)}ms |
                Channels: {metrics.secureChannels}/{metrics.totalChannels}
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                try {
                  if (process.env.NODE_ENV === 'development') {
                    console.log('[TestChat] 🔧 Attempting to fix channel membership...');

                    }
                  const response = await fetch('/api/getstream/fix-channel', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      channelId: 'task-71416246-5184-478f-b9cb-eaa14ce5e8bb',
                      userId: user.id
                    }),
                  });

                  if (response.ok) {
                    const result = await response.json();
                    if (process.env.NODE_ENV === 'development') {
                      console.log('[TestChat] ✅ Fix successful:', result);
                      }
                    alert('✅ Channel fixed successfully! Refresh the page to see the channel.');
                  } else {
                    const errorText = await response.text();
                    console.error('[TestChat] ❌ Fix failed:', response.status, errorText);
                    alert(`❌ Fix failed: ${response.status} ${response.statusText}`);
                  }
                } catch (error) {
                  console.error('[TestChat] ❌ Fix error:', error);
                  alert('❌ Fix error. Check console for details.');
                }
              }}
            >
              🔧 Fix Channel
            </Button>
            <Button variant="outline" size="sm" disabled>
              <RefreshCw className="h-4 w-4 mr-2" />
              Test Mode
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Lightweight Channel List */}
          <div className="md:col-span-1 border rounded-md overflow-hidden">
            <div className="bg-gray-50 p-3 border-b">
              <h3 className="font-medium text-sm">💨 Lightweight Channels</h3>
            </div>
            <div className="divide-y overflow-y-auto" style={{ maxHeight: '400px' }}>
              {channels.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No conversations yet</p>
                </div>
              ) : (
                channels.map((channel) => (
                  <div
                    key={channel.id}
                    className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors ${
                      selectedChannelId === channel.id ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                    }`}
                    onClick={() => handleSelectChannel(channel.id)}
                  >
                    <div className="flex items-start">
                      <Avatar className="h-8 w-8 mr-3">
                        <AvatarFallback>{getInitials(channel.name)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-start">
                          <h4 className="font-medium text-sm truncate">{channel.name}</h4>
                          <div className="flex items-center text-xs text-gray-400 ml-2">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatDate(channel.lastMessageTime)}
                          </div>
                        </div>
                        <p className="text-xs text-gray-500 truncate mt-1">{channel.lastMessage}</p>
                        {channel.memberCount && (
                          <span className="text-xs text-blue-500">
                            {channel.memberCount} members
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* On-Demand Chat Window */}
          <div className="md:col-span-2">
            {chatLoading ? (
              <div className="border rounded-md flex items-center justify-center p-8 h-[400px]">
                <Loader2 className="h-8 w-8 animate-spin text-gray-500 mr-3" />
                <p className="text-gray-500">Loading full chat...</p>
              </div>
            ) : selectedChannelId && streamClient && activeChannel ? (
              <div className="border rounded-md overflow-hidden h-[400px] flex flex-col">
                <div className="bg-green-50 p-2 text-center text-sm text-green-700">
                  ✅ Chat loaded on-demand (efficient!)
                </div>
                <Chat client={streamClient} theme="messaging light">
                  <Channel channel={activeChannel}>
                    <Window>
                      <ChannelHeader />
                      <MessageList />
                      <MessageInput />
                    </Window>
                  </Channel>
                </Chat>
              </div>
            ) : (
              <div className="border rounded-md flex items-center justify-center p-8 h-[400px]">
                <div className="text-center">
                  <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">
                    {channels.length === 0 ?
                      'No conversations available' :
                      'Select a conversation to load it on-demand'}
                  </p>
                  <p className="text-xs text-gray-400 mt-2">
                    💡 This approach only loads full chat data when you click
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TestChatSection;
