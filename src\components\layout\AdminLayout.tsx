import React, { useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import {
  Users,
  BarChart3,
  Settings,
  Building,
  Shield,
  LogOut,
  Home,
  Menu,
  X,
  ClipboardList
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const { user, isAdmin, signOut } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Check if user is super admin using the profiles table as the source of truth
  const { userRole } = useAuth();
  useEffect(() => {
    if (user) {
      const isSuperAdmin = userRole === 'admin';
      if (!isSuperAdmin) {
        toast({
          title: 'Access Denied',
          description: 'You do not have permission to access the admin area.',
          variant: 'destructive',
        });
        navigate('/dashboard');
      }
    } else {
      // No user is logged in
      navigate('/login');
    }
  }, [user, userRole, navigate]);

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (!user || !isAdmin) {
    return null; // Don't render anything if not admin
  }

  const navItems = [
    {
      title: 'Task Review',
      icon: <ClipboardList className="h-5 w-5" />,
      href: '/admin/tasks',
      active: location.pathname === '/admin/tasks',
    },
    {
      title: 'Users',
      icon: <Users className="h-5 w-5" />,
      href: '/admin/users',
      active: location.pathname === '/admin/users',
    },
    {
      title: 'Organizations',
      icon: <Building className="h-5 w-5" />,
      href: '/admin/organizations',
      active: location.pathname === '/admin/organizations',
    },
    {
      title: 'Settings',
      icon: <Settings className="h-5 w-5" />,
      href: '/admin/settings',
      active: location.pathname === '/admin/settings',
    },
  ];

  const NavLink = ({ item }: { item: typeof navItems[0] }) => (
    <Link
      to={item.href}
      className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all ${
        item.active
          ? 'bg-primary text-primary-foreground'
          : 'text-muted-foreground hover:bg-muted hover:text-foreground'
      }`}
    >
      {item.icon}
      {item.title}
    </Link>
  );

  const Sidebar = () => (
    <div className="flex h-full flex-col gap-2">
      <div className="flex h-14 items-center border-b px-4">
        <Link to="/organization/dashboard" className="flex items-center gap-2 font-semibold">
          <Shield className="h-6 w-6 text-amber-500" />
          <span className="text-lg">Admin Panel</span>
        </Link>
      </div>
      <ScrollArea className="flex-1 px-3">
        <div className="flex flex-col gap-2 py-2">
          {navItems.map((item) => (
            <NavLink key={item.href} item={item} />
          ))}
        </div>
      </ScrollArea>
      <div className="mt-auto border-t p-4">
        <div className="flex items-center gap-2 py-2">
          <Avatar className="h-9 w-9">
            <AvatarFallback className="bg-primary text-primary-foreground">
              {user.email?.charAt(0).toUpperCase() || 'A'}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <span className="text-sm font-medium leading-none">
              {user.email}
            </span>
            <span className="text-xs text-muted-foreground">Administrator</span>
          </div>
        </div>
        <Separator className="my-2" />
        <div className="flex flex-col gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="justify-start"
            onClick={() => navigate('/dashboard')}
          >
            <Home className="mr-2 h-4 w-4" />
            Back to App
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="justify-start text-red-500 hover:text-red-500 hover:bg-red-50"
            onClick={handleSignOut}
          >
            <LogOut className="mr-2 h-4 w-4" />
            Sign Out
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex min-h-screen">
      {/* Desktop Sidebar */}
      <aside className="hidden border-r bg-background lg:block lg:w-64">
        <Sidebar />
      </aside>

      {/* Mobile Sidebar */}
      <Sheet>
        <SheetTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            className="lg:hidden absolute left-4 top-4 z-50"
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">Toggle Menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0 w-64">
          <Sidebar />
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <main className="flex-1 overflow-auto">
        <div className="lg:pl-0 pt-16 lg:pt-0">{children}</div>
      </main>
    </div>
  );
};

export default AdminLayout;
