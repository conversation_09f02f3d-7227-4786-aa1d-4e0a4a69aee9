
import { Link } from "react-router-dom";
import { Facebook, Twitter, Instagram, Mail } from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-50 pt-12 pb-8 mt-16">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-9 h-9 rounded-full bg-classtasker-blue text-white flex items-center justify-center font-bold text-xl">C</div>
              <span className="text-xl font-bold text-classtasker-dark">Classtasker</span>
            </Link>
            <p className="mt-4 text-gray-600">
              Connecting schools with qualified maintenance professionals for all your facility needs.
            </p>
            <div className="flex space-x-4 mt-6">
              <a href="#" className="text-gray-500 hover:text-classtasker-blue transition-colors">
                <Facebook size={20} />
              </a>
              <a href="#" className="text-gray-500 hover:text-classtasker-blue transition-colors">
                <Twitter size={20} />
              </a>
              <a href="#" className="text-gray-500 hover:text-classtasker-blue transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-gray-500 hover:text-classtasker-blue transition-colors">
                <Mail size={20} />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">For Schools</h3>
            <ul className="space-y-2">
              <li><Link to="/post-task" className="text-gray-600 hover:text-classtasker-blue transition-colors">Post a Task</Link></li>
              <li><Link to="/how-it-works" className="text-gray-600 hover:text-classtasker-blue transition-colors">How It Works</Link></li>
              <li><Link to="/plans" className="text-gray-600 hover:text-classtasker-blue transition-colors">Pricing</Link></li>
              <li><Link to="/school-resources" className="text-gray-600 hover:text-classtasker-blue transition-colors">School Resources</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">For Suppliers</h3>
            <ul className="space-y-2">
              <li><Link to="/tasks" className="text-gray-600 hover:text-classtasker-blue transition-colors">Find Jobs</Link></li>
              <li><Link to="/supplier-signup" className="text-gray-600 hover:text-classtasker-blue transition-colors">Become a Supplier</Link></li>
              <li><Link to="/supplier-resources" className="text-gray-600 hover:text-classtasker-blue transition-colors">Supplier Resources</Link></li>
              <li><Link to="/supplier-faq" className="text-gray-600 hover:text-classtasker-blue transition-colors">Supplier FAQ</Link></li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Support</h3>
            <ul className="space-y-2">
              <li><Link to="/help" className="text-gray-600 hover:text-classtasker-blue transition-colors">Help Center</Link></li>
              <li><Link to="/contact" className="text-gray-600 hover:text-classtasker-blue transition-colors">Contact Us</Link></li>
              <li><Link to="/terms" className="text-gray-600 hover:text-classtasker-blue transition-colors">Terms of Service</Link></li>
              <li><Link to="/privacy" className="text-gray-600 hover:text-classtasker-blue transition-colors">Privacy Policy</Link></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-200 mt-12 pt-6 text-center text-gray-500 text-sm">
          <p>&copy; {currentYear} Classtasker. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
