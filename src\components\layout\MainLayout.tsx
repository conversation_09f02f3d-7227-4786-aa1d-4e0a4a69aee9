
import React from 'react';
import Navbar from './Navbar';
import Footer from './Footer';
import PWAQuickDebug from '@/components/pwa/PWAQuickDebug';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow">
        {children}
      </main>
      <Footer />
      <PWAQuickDebug />
    </div>
  );
};

export default MainLayout;
