/**
 * Global Mobile Navigation Bar Component
 *
 * A fixed bottom navigation bar for mobile devices that is present throughout the entire app.
 * This component is rendered directly in the App component, not in a nested route.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Home, PlusCircle, MessageSquare, LayoutDashboard, ClipboardList } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import { isInRoleGroup, ROLE_GROUPS } from '@/constants/roles';

interface NavItemProps {
  icon: React.ReactNode;
  label: string;
  path: string;
  isActive: boolean;
  onClick: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ icon, label, isActive, onClick }) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "flex flex-col items-center justify-center flex-1 py-2 px-1 transition-colors",
        isActive
          ? "text-blue-600"
          : "text-gray-500 hover:text-blue-500"
      )}
    >
      <div className="relative">
        {icon}
      </div>
      <span className="text-xs mt-1 font-medium">{label}</span>
    </button>
  );
};

const GlobalMobileNavBar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { profile, userRole, user } = useAuth();
  const [isVisible, setIsVisible] = useState(false);

  // Debug log
  useEffect(() => {
    console.log('GlobalMobileNavBar mounted', {
      location: location.pathname,
      user: user?.id,
      userRole
    });

    // Check if we're on mobile
    const checkMobile = () => {
      const isMobile = window.innerWidth < 768;
      setIsVisible(isMobile);

      console.log('GlobalMobileNavBar visibility check', { isMobile, isVisible: isMobile });
    };

    // Check initially
    checkMobile();

    // Add resize listener
    window.addEventListener('resize', checkMobile);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, [location.pathname, user, userRole]);

  // Don't render if not visible
  if (!isVisible || !user) {
    return null;
  }

  // Determine if the user is an admin or can create tasks
  const isAdmin = userRole === 'admin';
  const canCreateTasks = isInRoleGroup(userRole, ROLE_GROUPS.TASK_CREATORS);
  const isSupplier = profile?.account_type === 'supplier';

  // Check if the current path matches a nav item
  const isActive = (path: string) => {
    if (path === '/dashboard' && location.pathname === '/dashboard') {
      return true;
    }
    if (path === '/tasks/my-tasks' &&
        (location.pathname === '/tasks/my-tasks' || location.pathname === '/dashboard?tab=tasks')) {
      return true;
    }
    if (path === '/post-task' && location.pathname === '/post-task') {
      return true;
    }
    if (path === '/mobile/chats' &&
        (location.pathname === '/mobile/chats' || location.pathname.startsWith('/mobile/stream-chat/') ||
         location.pathname === '/messages' || location.pathname === '/dashboard?tab=messages')) {
      return true;
    }
    if (path === '/organization/dashboard' && location.pathname === '/organization/dashboard') {
      return true;
    }
    return false;
  };

  // Navigate to a path
  const navigateTo = (path: string) => {
    navigate(path);
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
      <div className="flex items-center justify-around">
        <NavItem
          icon={<Home size={20} />}
          label="Home"
          path="/dashboard"
          isActive={isActive('/dashboard')}
          onClick={() => navigateTo('/dashboard')}
        />

        <NavItem
          icon={<ClipboardList size={20} />}
          label="My Tasks"
          path="/tasks/my-tasks"
          isActive={isActive('/tasks/my-tasks')}
          onClick={() => navigateTo('/tasks/my-tasks')}
        />

        {/* Only show Create Task for users who can create tasks */}
        {(canCreateTasks || isSupplier) && (
          <NavItem
            icon={<PlusCircle size={20} />}
            label="New Task"
            path="/post-task"
            isActive={isActive('/post-task')}
            onClick={() => navigateTo('/post-task')}
          />
        )}

        <NavItem
          icon={<MessageSquare size={20} />}
          label="Messages"
          path="/mobile/chats"
          isActive={isActive('/mobile/chats')}
          onClick={() => navigateTo('/mobile/chats')}
        />

        {/* Only show Organization Dashboard for admins */}
        {isAdmin && (
          <NavItem
            icon={<LayoutDashboard size={20} />}
            label="Org"
            path="/organization/dashboard"
            isActive={isActive('/organization/dashboard')}
            onClick={() => navigateTo('/organization/dashboard')}
          />
        )}
      </div>

      {/* Add a spacer at the bottom to prevent content from being hidden behind the nav bar */}
      <div className="h-safe-area-bottom bg-white" />
    </div>
  );
};

export default GlobalMobileNavBar;
