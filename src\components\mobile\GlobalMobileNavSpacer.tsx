/**
 * Global Mobile Navigation Spacer Component
 *
 * This component adds padding at the bottom of the page content to prevent
 * it from being hidden behind the mobile navigation bar.
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

const GlobalMobileNavSpacer: React.FC = () => {
  const { user } = useAuth();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if we're on mobile
    const checkMobile = () => {
      const isMobile = window.innerWidth < 768;
      setIsVisible(isMobile);
    };

    // Check initially
    checkMobile();

    // Add resize listener
    window.addEventListener('resize', checkMobile);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Only add the spacer if the user is logged in and we're on mobile
  if (!user || !isVisible) {
    return null;
  }

  return (
    <div className="h-16 pb-safe-area-bottom" />
  );
};

export default GlobalMobileNavSpacer;
