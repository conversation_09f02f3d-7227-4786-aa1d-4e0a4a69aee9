/**
 * Mobile Layout Component
 *
 * A layout component for mobile views that includes the navigation bar
 * and proper spacing.
 */

import React from 'react';
import { Outlet } from 'react-router-dom';
import SimpleMobileNavBar from './SimpleMobileNavBar';

const MobileLayout: React.FC = () => {
  // Debug log
  console.log('MobileLayout rendered', {
    path: window.location.pathname
  });

  // Get the current version/build time for debugging
  const buildTime = new Date().toISOString();

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Debug indicator */}
      <div className="fixed top-0 right-0 bg-green-500 text-white text-xs px-2 py-1 z-50">
        Mobile Layout Active - {buildTime.substring(0, 16)}
      </div>

      {/* Main content */}
      <main className="flex-1 pb-16">
        <Outlet />
      </main>

      {/* Mobile navigation */}
      <SimpleMobileNavBar />
    </div>
  );
};

export default MobileLayout;
