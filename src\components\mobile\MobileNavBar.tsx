/**
 * Mobile Navigation Bar Component
 *
 * A fixed bottom navigation bar for mobile devices that provides quick access
 * to the most important features of the application.
 */

import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Home, PlusCircle, MessageSquare, LayoutDashboard, ClipboardList, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import { isInRoleGroup, ROLE_GROUPS } from '@/constants/roles';

interface NavItemProps {
  icon: React.ReactNode;
  label: string;
  path: string;
  isActive: boolean;
  onClick: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ icon, label, isActive, onClick }) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "flex flex-col items-center justify-center flex-1 py-2 px-1 transition-colors",
        isActive
          ? "text-blue-600"
          : "text-gray-500 hover:text-blue-500"
      )}
    >
      <div className="relative">
        {icon}
      </div>
      <span className="text-xs mt-1 font-medium">{label}</span>
    </button>
  );
};

const MobileNavBar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, profile, userRole, isLoading } = useAuth();
  const [navVisible, setNavVisible] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // Effect to handle authentication state changes
  useEffect(() => {
    console.log('MobileNavBar: Auth state changed', { user, profile, userRole, isLoading });

    // If we have user and profile, show the nav bar
    if (user && profile) {
      setNavVisible(true);
      return;
    }

    // If auth is still loading, wait
    if (isLoading) {
      return;
    }

    // If auth has loaded but we don't have user/profile, try to recover
    if (!isLoading && (!user || !profile) && retryCount < 3) {
      console.log('MobileNavBar: Attempting to recover auth state, retry:', retryCount + 1);

      // Set a timeout to retry
      const timer = setTimeout(() => {
        setRetryCount(prev => prev + 1);
      }, 1000);

      return () => clearTimeout(timer);
    }

    // If we've retried enough times and still don't have user/profile, hide the nav
    if (retryCount >= 3 && (!user || !profile)) {
      setNavVisible(false);
    }
  }, [user, profile, userRole, isLoading, retryCount]);

  // Show loading state while auth is loading
  if (isLoading) {
    return (
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg md:hidden z-50 py-4">
        <div className="flex justify-center items-center">
          <Loader2 className="h-5 w-5 animate-spin text-blue-500 mr-2" />
          <span className="text-sm text-gray-500">Loading navigation...</span>
        </div>
      </div>
    );
  }

  // Don't show the nav bar if it's not visible
  if (!navVisible) {
    return null;
  }

  // Determine if the user is an admin or can create tasks
  const isAdmin = userRole === 'admin';
  const canCreateTasks = isInRoleGroup(userRole, ROLE_GROUPS.TASK_CREATORS);
  const isSupplier = profile?.account_type === 'supplier';

  // Check if the current path matches a nav item
  const isActive = (path: string) => {
    if (path === '/dashboard' && location.pathname === '/dashboard') {
      return true;
    }
    if (path === '/tasks/my-tasks' &&
        (location.pathname === '/tasks/my-tasks' || location.pathname === '/dashboard?tab=tasks')) {
      return true;
    }
    if (path === '/post-task' && location.pathname === '/post-task') {
      return true;
    }
    if (path === '/mobile/chats' &&
        (location.pathname === '/mobile/chats' || location.pathname.startsWith('/mobile/chat/') ||
         location.pathname === '/messages' || location.pathname === '/dashboard?tab=messages')) {
      return true;
    }
    if (path === '/organization/dashboard' && location.pathname === '/organization/dashboard') {
      return true;
    }
    return false;
  };

  // Navigate to a path
  const navigateTo = (path: string) => {
    navigate(path);
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg md:hidden z-50">
      <div className="flex items-center justify-around">
        <NavItem
          icon={<Home size={20} />}
          label="Home"
          path="/dashboard"
          isActive={isActive('/dashboard')}
          onClick={() => navigateTo('/dashboard')}
        />

        <NavItem
          icon={<ClipboardList size={20} />}
          label="My Tasks"
          path="/tasks/my-tasks"
          isActive={isActive('/tasks/my-tasks')}
          onClick={() => navigateTo('/tasks/my-tasks')}
        />

        {/* Only show Create Task for users who can create tasks */}
        {(canCreateTasks || isSupplier) && (
          <NavItem
            icon={<PlusCircle size={20} />}
            label="New Task"
            path="/post-task"
            isActive={isActive('/post-task')}
            onClick={() => navigateTo('/post-task')}
          />
        )}

        <NavItem
          icon={<MessageSquare size={20} />}
          label="Messages"
          path="/mobile/chats"
          isActive={isActive('/mobile/chats') || location.pathname.startsWith('/mobile/chat/')}
          onClick={() => navigateTo('/mobile/chats')}
        />

        {/* Only show Organisation Dashboard for admins */}
        {isAdmin && (
          <NavItem
            icon={<LayoutDashboard size={20} />}
            label="Org"
            path="/organisation/dashboard"
            isActive={isActive('/organisation/dashboard')}
            onClick={() => navigateTo('/organisation/dashboard')}
          />
        )}
      </div>

      {/* Add a spacer at the bottom to prevent content from being hidden behind the nav bar */}
      <div className="h-safe-area-bottom bg-white" />
    </div>
  );
};

export default MobileNavBar;
