/**
 * Mobile Navigation Spacer Component
 * 
 * This component adds padding at the bottom of the page content to prevent
 * it from being hidden behind the mobile navigation bar.
 */

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';

const MobileNavSpacer: React.FC = () => {
  const { user } = useAuth();
  
  // Only add the spacer if the user is logged in
  if (!user) {
    return null;
  }
  
  return (
    <div className="h-16 md:h-0 pb-safe-area-bottom" />
  );
};

export default MobileNavSpacer;
