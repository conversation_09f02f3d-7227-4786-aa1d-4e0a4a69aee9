import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';
import {
  Search,
  Plus,
  Calendar,
  CheckCircle,
  AlertCircle,
  Clock,
  Upload,
  FileText,
  Trash2,
  RefreshC<PERSON>,
  <PERSON>lter,
  <PERSON><PERSON><PERSON>,
  ListFilter,
  CalendarDays,
  CheckSquare,
  Eye,
  EyeOff
} from 'lucide-react';
import { Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  format,
  addDays,
  addWeeks,
  addMonths,
  addYears,
  parseISO,
  isAfter,
  isBefore,
  startOfDay,
  startOfWeek,
  startOfMonth,
  startOfYear,
  endOfDay,
  endOfWeek,
  endOfMonth,
  endOfYear,
  isWithinInterval,
  isSameDay,
  isSameWeek,
  isSameMonth,
  isSameYear
} from 'date-fns';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';

// Define types
interface ComplianceTask {
  id: string;
  title: string;
  description: string;
  cycle: 'daily' | 'weekly' | 'monthly' | 'annually';
  last_completed_at: string | null;
  next_due_date: string;
  created_at: string;
  created_by: string;
  has_proof?: boolean; // Flag to indicate if proof has been uploaded
  parent_task_id?: string | null; // Reference to the original task if this is a recurring instance
  completion_id?: string | null; // Reference to the completion record
  is_completed?: boolean; // Flag to indicate if the task is completed
}

interface ComplianceAttachment {
  id: string;
  compliance_task_id: string;
  file_name: string;
  file_type: string;
  file_path: string;
  uploaded_at: string;
  uploaded_by: string;
}

interface ComplianceCompletion {
  id: string;
  compliance_task_id: string;
  completed_by: string;
  completed_at: string;
  notes?: string;
}

const ComplianceManagement = () => {
  const { organizationId, user } = useAuth();
  const [complianceTasks, setComplianceTasks] = useState<ComplianceTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<ComplianceTask | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [attachments, setAttachments] = useState<ComplianceAttachment[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'calendar' | 'summary'>('list');
  const [activeTab, setActiveTab] = useState<'all' | 'today' | 'week' | 'month' | 'completed' | 'overdue'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'completed' | 'overdue' | 'due-soon' | 'upcoming'>('all');
  const [cycleFilter, setCycleFilter] = useState<'all' | 'daily' | 'weekly' | 'monthly' | 'annually'>('all');
  const [attentionFilter, setAttentionFilter] = useState<'all' | 'requires-attention' | 'up-to-date'>('all');
  const [showCompletedTasks, setShowCompletedTasks] = useState(true);
  const [statsVisible, setStatsVisible] = useState(true);
  const [historyFilter, setHistoryFilter] = useState<'all' | 'daily' | 'weekly' | 'monthly' | 'annually'>('all');

  // Form states
  const [taskTitle, setTaskTitle] = useState('');
  const [taskDescription, setTaskDescription] = useState('');
  const [taskCycle, setTaskCycle] = useState<'daily' | 'weekly' | 'monthly' | 'annually'>('monthly');
  const [taskDueDate, setTaskDueDate] = useState('');
  const [taskCompleted, setTaskCompleted] = useState(false);

  // Fetch compliance tasks from the database
  const fetchComplianceTasks = async () => {
    if (!organizationId) return;

    try {
      setLoading(true);

      // Fetch all compliance tasks
      const { data, error } = await supabase
        .from('compliance_tasks')
        .select('*')
        .eq('organization_id', organizationId)
        .order('next_due_date', { ascending: true });

      if (error) throw error;

      if (data) {
        // Create a map to store tasks with their proof status
        const tasksWithProof: ComplianceTask[] = [];

        // Process each task
        for (const task of data) {
          // Check if this task has any attachments (proof)
          const { data: attachments, error: attachmentsError } = await supabase
            .from('compliance_attachments')
            .select('id')
            .eq('compliance_task_id', task.id)
            .limit(1);

          if (attachmentsError) {
            console.error('Error checking attachments:', attachmentsError);
          }

          // Check if this task has a completion record
          const { data: completions, error: completionsError } = await supabase
            .from('compliance_completions')
            .select('id')
            .eq('compliance_task_id', task.id)
            .limit(1);

          if (completionsError) {
            console.error('Error checking completions:', completionsError);
          }

          // Add the task with proof and completion status
          tasksWithProof.push({
            ...task,
            has_proof: attachments && attachments.length > 0,
            // Keep the is_completed flag from the database if it exists, otherwise calculate it
            is_completed: task.is_completed === true ? true :
              (task.last_completed_at ? isAfter(parseISO(task.last_completed_at), parseISO(task.next_due_date)) : false),
            completion_id: completions && completions.length > 0 ? completions[0].id : null
          });
        }

        setComplianceTasks(tasksWithProof);
      }
    } catch (error: any) {
      console.error('Error fetching compliance tasks:', error.message);
      toast({
        title: 'Error fetching compliance tasks',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch attachments for a specific task
  const fetchAttachments = async (taskId: string) => {
    try {
      const { data, error } = await supabase
        .from('compliance_attachments')
        .select('*')
        .eq('compliance_task_id', taskId)
        .order('uploaded_at', { ascending: false });

      if (error) throw error;

      if (data) {
        setAttachments(data as ComplianceAttachment[]);
      }
    } catch (error: any) {
      console.error('Error fetching attachments:', error.message);
      toast({
        title: 'Error fetching attachments',
        description: error.message,
        variant: 'destructive',
      });
    }
  };

  useEffect(() => {
    if (organizationId) {
      fetchComplianceTasks();
    }
  }, [organizationId]);

  // Calculate next due date based on cycle
  const calculateNextDueDate = (cycle: string, currentDate: Date = new Date()): Date => {
    switch (cycle) {
      case 'daily':
        return addDays(currentDate, 1);
      case 'weekly':
        return addWeeks(currentDate, 1);
      case 'monthly':
        return addMonths(currentDate, 1);
      case 'annually':
        return addYears(currentDate, 1);
      default:
        return addMonths(currentDate, 1); // Default to monthly
    }
  };

  // Handle adding a new compliance task
  const handleAddTask = async () => {
    if (!taskTitle.trim()) {
      toast({
        title: 'Error',
        description: 'Task title is required',
        variant: 'destructive',
      });
      return;
    }

    if (!organizationId || !user) return;

    try {
      setIsProcessing(true);

      // Calculate next due date based on cycle
      const nextDueDate = taskDueDate
        ? new Date(taskDueDate).toISOString()
        : calculateNextDueDate(taskCycle).toISOString();

      const newTask = {
        organization_id: organizationId,
        title: taskTitle,
        description: taskDescription,
        cycle: taskCycle,
        next_due_date: nextDueDate,
        created_by: user.id,
      };

      const { data, error } = await supabase
        .from('compliance_tasks')
        .insert(newTask)
        .select()
        .single();

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Compliance task added successfully',
      });

      // Reset form fields
      setTaskTitle('');
      setTaskDescription('');
      setTaskCycle('monthly');
      setTaskDueDate('');

      // Close dialog and refresh tasks
      setAddDialogOpen(false);
      fetchComplianceTasks();
    } catch (error: any) {
      console.error('Error adding compliance task:', error.message);
      toast({
        title: 'Error adding compliance task',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle updating a compliance task
  const handleUpdateTask = async () => {
    if (!selectedTask || !taskTitle.trim()) return;

    try {
      setIsProcessing(true);

      // Calculate next due date based on cycle if not explicitly set
      const nextDueDate = taskDueDate
        ? new Date(taskDueDate).toISOString()
        : calculateNextDueDate(taskCycle).toISOString();

      const updates = {
        title: taskTitle,
        description: taskDescription,
        cycle: taskCycle,
        next_due_date: nextDueDate,
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('compliance_tasks')
        .update(updates)
        .eq('id', selectedTask.id);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Compliance task updated successfully',
      });

      // Close dialog and refresh tasks
      setEditDialogOpen(false);
      fetchComplianceTasks();
    } catch (error: any) {
      console.error('Error updating compliance task:', error.message);
      toast({
        title: 'Error updating compliance task',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle marking a task as completed
  const handleMarkCompleted = async (task: ComplianceTask) => {
    try {
      setIsProcessing(true);

      const now = new Date().toISOString();

      // Calculate the next due date based on the cycle
      const nextDueDate = calculateNextDueDate(task.cycle).toISOString();

      // First, add a record to compliance_completions
      const { data: completionData, error: completionError } = await supabase
        .from('compliance_completions')
        .insert({
          compliance_task_id: task.id,
          completed_by: user?.id,
          completed_at: now,
        })
        .select()
        .single();

      if (completionError) throw completionError;

      // Update the current task to mark it as completed
      // We set is_completed to true explicitly and update the next_due_date to be before the completion date
      // This ensures the task is properly marked as completed
      const { error: updateError } = await supabase
        .from('compliance_tasks')
        .update({
          last_completed_at: now,
          is_completed: true, // Explicitly mark as completed
          updated_at: now,
        })
        .eq('id', task.id);

      if (updateError) throw updateError;

      // Create a new task entry for the next cycle
      const { data: newTaskData, error: newTaskError } = await supabase
        .from('compliance_tasks')
        .insert({
          organization_id: task.organization_id,
          title: task.title,
          description: task.description,
          cycle: task.cycle,
          next_due_date: nextDueDate,
          created_by: user?.id,
          parent_task_id: task.id, // Reference to the original task
          created_at: now,
          updated_at: now,
        })
        .select()
        .single();

      if (newTaskError) throw newTaskError;

      toast({
        title: 'Success',
        description: 'Task marked as completed and new task created for next cycle',
      });

      // Refresh tasks
      fetchComplianceTasks();
    } catch (error: any) {
      console.error('Error marking task as completed:', error.message);
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle deleting a compliance task
  const handleDeleteTask = async (taskId: string) => {
    if (!confirm('Are you sure you want to delete this compliance task? This action cannot be undone.')) {
      return;
    }

    try {
      setIsProcessing(true);

      const { error } = await supabase
        .from('compliance_tasks')
        .delete()
        .eq('id', taskId);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Compliance task deleted successfully',
      });

      // Refresh tasks
      fetchComplianceTasks();
    } catch (error: any) {
      console.error('Error deleting compliance task:', error.message);
      toast({
        title: 'Error deleting compliance task',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle file upload
  const handleFileUpload = async () => {
    if (!selectedTask || !selectedFile || !user) return;

    try {
      setIsProcessing(true);

      // Upload file to storage
      const fileExt = selectedFile.name.split('.').pop();
      const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `compliance/${selectedTask.id}/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('attachments')
        .upload(filePath, selectedFile);

      if (uploadError) throw uploadError;

      // Add record to compliance_attachments table
      const { error: dbError } = await supabase
        .from('compliance_attachments')
        .insert({
          compliance_task_id: selectedTask.id,
          file_name: selectedFile.name,
          file_type: selectedFile.type,
          file_path: filePath,
          uploaded_by: user.id,
        });

      if (dbError) throw dbError;

      toast({
        title: 'Success',
        description: 'File uploaded successfully',
      });

      // Reset and close dialog
      setSelectedFile(null);
      setUploadDialogOpen(false);

      // Refresh attachments
      fetchAttachments(selectedTask.id);
    } catch (error: any) {
      console.error('Error uploading file:', error.message);
      toast({
        title: 'Error uploading file',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Get task status
  const getTaskStatus = (task: ComplianceTask) => {
    // If the task is explicitly marked as completed, return completed status
    if (task.is_completed) {
      return 'completed';
    }

    const dueDate = parseISO(task.next_due_date);
    const now = new Date();

    if (task.last_completed_at) {
      const completedDate = parseISO(task.last_completed_at);
      if (isAfter(completedDate, dueDate)) {
        return 'completed';
      }
    }

    if (isBefore(dueDate, now)) {
      return 'overdue';
    }

    // Due within 7 days
    const sevenDaysFromNow = addDays(now, 7);
    if (isBefore(dueDate, sevenDaysFromNow)) {
      return 'due-soon';
    }

    return 'upcoming';
  };

  // Helper functions for time-based grouping
  const isTaskDueToday = (task: ComplianceTask) => {
    const dueDate = parseISO(task.next_due_date);
    return isSameDay(dueDate, new Date());
  };

  const isTaskDueThisWeek = (task: ComplianceTask) => {
    const dueDate = parseISO(task.next_due_date);
    return isSameWeek(dueDate, new Date(), { weekStartsOn: 1 }); // Week starts on Monday
  };

  const isTaskDueThisMonth = (task: ComplianceTask) => {
    const dueDate = parseISO(task.next_due_date);
    return isSameMonth(dueDate, new Date());
  };

  const isTaskCompleted = (task: ComplianceTask) => {
    // If the task has the is_completed flag set, use that
    if (task.is_completed) return true;

    // Otherwise, fall back to the date-based check
    if (!task.last_completed_at) return false;

    const completedDate = parseISO(task.last_completed_at);
    const dueDate = parseISO(task.next_due_date);

    // Task is considered completed if it was completed after its due date
    return isAfter(completedDate, dueDate);
  };

  const isTaskOverdue = (task: ComplianceTask) => {
    const dueDate = parseISO(task.next_due_date);
    return isBefore(dueDate, new Date()) && !isTaskCompleted(task);
  };

  // Get tasks for the current time period based on active tab
  const getTasksForActiveTab = () => {
    switch (activeTab) {
      case 'today':
        return complianceTasks.filter(task => isTaskDueToday(task));
      case 'week':
        return complianceTasks.filter(task => isTaskDueThisWeek(task));
      case 'month':
        return complianceTasks.filter(task => isTaskDueThisMonth(task));
      case 'completed':
        return complianceTasks.filter(task => isTaskCompleted(task));
      case 'overdue':
        return complianceTasks.filter(task => isTaskOverdue(task));
      case 'all':
      default:
        return complianceTasks;
    }
  };

  // Apply filters to active tasks (non-completed tasks)
  const applyFiltersToActiveTasks = (tasks: ComplianceTask[]) => {
    return tasks.filter(task => {
      // Only include non-completed tasks
      const taskStatus = getTaskStatus(task);
      const isActive = taskStatus !== 'completed';

      if (!isActive) return false;

      // Apply search filter
      const matchesSearch =
        task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchTerm.toLowerCase());

      // Apply status filter
      const matchesStatus = statusFilter === 'all' || taskStatus === statusFilter;

      // Apply cycle filter
      const matchesCycle = cycleFilter === 'all' || task.cycle === cycleFilter;

      // Apply attention filter
      const requiresAttention = taskStatus === 'overdue';
      const matchesAttention =
        attentionFilter === 'all' ||
        (attentionFilter === 'requires-attention' && requiresAttention) ||
        (attentionFilter === 'up-to-date' && !requiresAttention);

      return matchesSearch && matchesStatus && matchesCycle && matchesAttention;
    });
  };

  // Apply filters to history tasks (completed tasks)
  const applyFiltersToHistoryTasks = (tasks: ComplianceTask[]) => {
    return tasks.filter(task => {
      // Only include completed tasks
      const taskStatus = getTaskStatus(task);
      const isCompleted = taskStatus === 'completed';

      if (!isCompleted) return false;

      // Apply search filter
      const matchesSearch =
        task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchTerm.toLowerCase());

      // Apply cycle filter for history
      const matchesCycle = historyFilter === 'all' || task.cycle === historyFilter;

      return matchesSearch && matchesCycle;
    });
  };

  // Get filtered active tasks
  const filteredActiveTasks = applyFiltersToActiveTasks(getTasksForActiveTab());

  // Get filtered history tasks
  const filteredHistoryTasks = applyFiltersToHistoryTasks(complianceTasks);

  // Get detailed task statistics by cycle
  const getTaskStatistics = () => {
    const allTasks = complianceTasks;

    // Define cycles
    const cycles = ['daily', 'weekly', 'monthly', 'annually'] as const;

    // Create statistics object
    const statistics = {
      overall: {
        total: allTasks.length,
        completed: allTasks.filter(task => getTaskStatus(task) === 'completed').length,
        outstanding: allTasks.filter(task => getTaskStatus(task) !== 'completed').length,
        overdue: allTasks.filter(task => getTaskStatus(task) === 'overdue').length,
        dueSoon: allTasks.filter(task => getTaskStatus(task) === 'due-soon').length,
        dueToday: allTasks.filter(task => isTaskDueToday(task)).length,
        upcoming: allTasks.filter(task => getTaskStatus(task) === 'upcoming').length,
        dueThisWeek: allTasks.filter(task => isTaskDueThisWeek(task)).length,
        dueThisMonth: allTasks.filter(task => isTaskDueThisMonth(task)).length,
      },
      byCycle: {} as Record<string, {
        total: number;
        completed: number;
        outstanding: number;
        overdue: number;
        dueSoon: number;
        dueToday: number;
        upcoming: number;
      }>
    };

    // Calculate statistics for each cycle
    cycles.forEach(cycle => {
      const tasksInCycle = allTasks.filter(task => task.cycle === cycle);
      const completedCount = tasksInCycle.filter(task => getTaskStatus(task) === 'completed').length;

      statistics.byCycle[cycle] = {
        total: tasksInCycle.length,
        completed: completedCount,
        outstanding: tasksInCycle.length - completedCount,
        overdue: tasksInCycle.filter(task => getTaskStatus(task) === 'overdue').length,
        dueSoon: tasksInCycle.filter(task => getTaskStatus(task) === 'due-soon').length,
        dueToday: tasksInCycle.filter(task => isTaskDueToday(task)).length,
        upcoming: tasksInCycle.filter(task => getTaskStatus(task) === 'upcoming').length,
      };
    });

    return statistics;
  };

  const taskStats = getTaskStatistics();
  const taskCounts = taskStats.overall;

  return (
    <div className="space-y-4">
      {/* Statistics Dashboard */}
      {statsVisible && (
        <Card className="border-gray-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <BarChart className="h-5 w-5 mr-2 text-blue-600" />
              Compliance Tasks Overview
            </CardTitle>
            <CardDescription>
              Summary of all compliance tasks by frequency and status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-50 border-b border-gray-200">
                    <th className="px-4 py-2 text-left font-medium text-gray-600">Frequency</th>
                    <th className="px-4 py-2 text-center font-medium text-gray-600">Total</th>
                    <th className="px-4 py-2 text-center font-medium text-green-600">Completed</th>
                    <th className="px-4 py-2 text-center font-medium text-orange-600">Outstanding</th>
                    <th className="px-4 py-2 text-center font-medium text-blue-600">Due Today</th>
                    <th className="px-4 py-2 text-center font-medium text-yellow-600">Due Soon</th>
                    <th className="px-4 py-2 text-center font-medium text-red-600">Overdue</th>
                    <th className="px-4 py-2 text-center font-medium text-gray-600">Upcoming</th>
                  </tr>
                </thead>
                <tbody>
                  {/* Daily Tasks */}
                  <tr className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-4 py-3 font-medium">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-gray-500" />
                        Daily
                      </div>
                    </td>
                    <td className="px-4 py-3 text-center font-semibold">
                      {taskStats.byCycle.daily.total}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.daily.completed > 0 ? (
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.daily.completed}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.daily.outstanding > 0 ? (
                        <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.daily.outstanding}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.daily.dueToday > 0 ? (
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                          <Calendar className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.daily.dueToday}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.daily.dueSoon > 0 ? (
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                          <Clock className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.daily.dueSoon}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.daily.overdue > 0 ? (
                        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.daily.overdue}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.daily.upcoming > 0 ? (
                        <span className="text-gray-600">{taskStats.byCycle.daily.upcoming}</span>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                  </tr>

                  {/* Weekly Tasks */}
                  <tr className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-4 py-3 font-medium">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                        Weekly
                      </div>
                    </td>
                    <td className="px-4 py-3 text-center font-semibold">
                      {taskStats.byCycle.weekly.total}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.weekly.completed > 0 ? (
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.weekly.completed}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.weekly.outstanding > 0 ? (
                        <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.weekly.outstanding}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.weekly.dueToday > 0 ? (
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                          <Calendar className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.weekly.dueToday}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.weekly.dueSoon > 0 ? (
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                          <Clock className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.weekly.dueSoon}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.weekly.overdue > 0 ? (
                        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.weekly.overdue}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.weekly.upcoming > 0 ? (
                        <span className="text-gray-600">{taskStats.byCycle.weekly.upcoming}</span>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                  </tr>

                  {/* Monthly Tasks */}
                  <tr className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-4 py-3 font-medium">
                      <div className="flex items-center">
                        <CalendarDays className="h-4 w-4 mr-2 text-gray-500" />
                        Monthly
                      </div>
                    </td>
                    <td className="px-4 py-3 text-center font-semibold">
                      {taskStats.byCycle.monthly.total}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.monthly.completed > 0 ? (
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.monthly.completed}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.monthly.outstanding > 0 ? (
                        <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.monthly.outstanding}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.monthly.dueToday > 0 ? (
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                          <Calendar className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.monthly.dueToday}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.monthly.dueSoon > 0 ? (
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                          <Clock className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.monthly.dueSoon}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.monthly.overdue > 0 ? (
                        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.monthly.overdue}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.monthly.upcoming > 0 ? (
                        <span className="text-gray-600">{taskStats.byCycle.monthly.upcoming}</span>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                  </tr>

                  {/* Annual Tasks */}
                  <tr className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-4 py-3 font-medium">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                        Annual
                      </div>
                    </td>
                    <td className="px-4 py-3 text-center font-semibold">
                      {taskStats.byCycle.annually.total}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.annually.completed > 0 ? (
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.annually.completed}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.annually.outstanding > 0 ? (
                        <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.annually.outstanding}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.annually.dueToday > 0 ? (
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                          <Calendar className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.annually.dueToday}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.annually.dueSoon > 0 ? (
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                          <Clock className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.annually.dueSoon}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.annually.overdue > 0 ? (
                        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          {taskStats.byCycle.annually.overdue}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskStats.byCycle.annually.upcoming > 0 ? (
                        <span className="text-gray-600">{taskStats.byCycle.annually.upcoming}</span>
                      ) : (
                        <span className="text-gray-400">0</span>
                      )}
                    </td>
                  </tr>

                  {/* Totals Row */}
                  <tr className="bg-gray-50 font-medium">
                    <td className="px-4 py-3">
                      <div className="flex items-center">
                        <CheckSquare className="h-4 w-4 mr-2 text-blue-600" />
                        All Tasks
                      </div>
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskCounts.total}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskCounts.completed > 0 ? (
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                          {taskCounts.completed}
                        </Badge>
                      ) : (
                        <span className="text-gray-500">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskCounts.outstanding > 0 ? (
                        <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-200">
                          {taskCounts.outstanding}
                        </Badge>
                      ) : (
                        <span className="text-gray-500">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskCounts.dueToday > 0 ? (
                        <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                          {taskCounts.dueToday}
                        </Badge>
                      ) : (
                        <span className="text-gray-500">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskCounts.dueSoon > 0 ? (
                        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
                          {taskCounts.dueSoon}
                        </Badge>
                      ) : (
                        <span className="text-gray-500">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskCounts.overdue > 0 ? (
                        <Badge className="bg-red-100 text-red-800 hover:bg-red-200">
                          {taskCounts.overdue}
                        </Badge>
                      ) : (
                        <span className="text-gray-500">0</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-center">
                      {taskCounts.upcoming > 0 ? (
                        <span className="font-semibold text-gray-700">{taskCounts.upcoming}</span>
                      ) : (
                        <span className="text-gray-500">0</span>
                      )}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search compliance tasks..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => setStatsVisible(!statsVisible)}
              title={statsVisible ? "Hide statistics" : "Show statistics"}
            >
              <BarChart className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="icon"
              onClick={() => setShowCompletedTasks(!showCompletedTasks)}
              title={showCompletedTasks ? "Hide completed tasks" : "Show completed tasks"}
            >
              {showCompletedTasks ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>

            <Select value={cycleFilter} onValueChange={(value: any) => setCycleFilter(value)}>
              <SelectTrigger className="w-[130px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by cycle" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Cycles</SelectItem>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="annually">Annually</SelectItem>
              </SelectContent>
            </Select>

            <Select value={attentionFilter} onValueChange={(value: any) => setAttentionFilter(value)}>
              <SelectTrigger className="w-[170px]">
                <AlertCircle className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by attention" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Tasks</SelectItem>
                <SelectItem value="requires-attention">Requires Attention</SelectItem>
                <SelectItem value="up-to-date">Up to Date</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Button onClick={() => {
          setTaskTitle('');
          setTaskDescription('');
          setTaskCycle('monthly');
          setTaskDueDate('');
          setAddDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Add Compliance Task
        </Button>
      </div>

      {/* Time Period Tabs */}
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
        <TabsList className="grid grid-cols-6 w-full">
          <TabsTrigger value="all">All Tasks</TabsTrigger>
          <TabsTrigger value="today">
            Today
            {taskCounts.dueToday > 0 && (
              <Badge variant="secondary" className="ml-2">{taskCounts.dueToday}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="week">
            This Week
            {taskCounts.dueThisWeek > 0 && (
              <Badge variant="secondary" className="ml-2">{taskCounts.dueThisWeek}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="month">
            This Month
            {taskCounts.dueThisMonth > 0 && (
              <Badge variant="secondary" className="ml-2">{taskCounts.dueThisMonth}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="completed">
            Completed
            {taskCounts.completed > 0 && (
              <Badge variant="secondary" className="ml-2">{taskCounts.completed}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="overdue">
            Overdue
            {taskCounts.overdue > 0 && (
              <Badge variant="destructive" className="ml-2">{taskCounts.overdue}</Badge>
            )}
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {loading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <>
          {/* Active Tasks List */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <h3 className="text-lg font-semibold flex items-center">
                <CheckSquare className="h-5 w-5 mr-2 text-blue-600" />
                Active Compliance Tasks
              </h3>
              <p className="text-sm text-gray-500 ml-4">
                These are your current active compliance tasks that need to be completed
              </p>
            </div>

            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[30px]"></TableHead>
                    <TableHead>Task</TableHead>
                    <TableHead>Cycle</TableHead>
                    <TableHead>Next Due Date</TableHead>
                    <TableHead>Last Completed</TableHead>
                    <TableHead>Status & Attention</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredActiveTasks.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-4">
                        {searchTerm ? (
                          <p>No active compliance tasks found matching your search</p>
                        ) : activeTab !== 'all' ? (
                          <p>No active compliance tasks found for this time period</p>
                        ) : (
                          <p>No active compliance tasks found. Click "Add Compliance Task" to create your first task.</p>
                        )}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredActiveTasks.map((task) => {
                      const status = getTaskStatus(task);
                      const isCompleted = false; // Active tasks are never completed
                      const hasProof = task.has_proof;

                      return (
                        <TableRow
                          key={task.id}
                          className={
                            status === 'overdue' ? 'bg-red-50 border-l-4 border-l-red-500' :
                            status === 'due-soon' ? 'bg-yellow-50 border-l-4 border-l-yellow-500' :
                            'border-l-4 border-l-transparent'
                          }
                        >
                          <TableCell>
                            <div className="relative">
                              <Checkbox
                                checked={isCompleted}
                                onCheckedChange={() => !isCompleted && handleMarkCompleted(task)}
                                disabled={isProcessing || isCompleted}
                              />
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            <div className="flex items-start">
                              <div>
                                <div className="flex items-center">
                                  <span>
                                    {task.title}
                                  </span>
                                  {hasProof && (
                                    <Badge variant="outline" className="ml-2 bg-blue-50 text-blue-700 border-blue-200">
                                      <FileText className="h-3 w-3 mr-1" />
                                      Proof
                                    </Badge>
                                  )}
                                  {task.parent_task_id && (
                                    <Badge variant="outline" className="ml-2 bg-purple-50 text-purple-700 border-purple-200">
                                      <RefreshCw className="h-3 w-3 mr-1" />
                                      Recurring
                                    </Badge>
                                  )}
                                </div>
                                {task.description && (
                                  <p className="text-sm text-gray-500 truncate max-w-[200px]">
                                    {task.description}
                                  </p>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="capitalize">
                              {task.cycle}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {format(parseISO(task.next_due_date), 'dd MMM yyyy')}
                          </TableCell>
                          <TableCell>
                            {task.last_completed_at ? (
                              format(parseISO(task.last_completed_at), 'dd MMM yyyy')
                            ) : (
                              'Never'
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col gap-1">
                              {/* Primary status indicator */}
                              {status === 'overdue' && (
                                <Badge variant="destructive" className="bg-red-100 text-red-800">
                                  <AlertCircle className="h-3 w-3 mr-1" />
                                  Overdue
                                </Badge>
                              )}
                              {status === 'due-soon' && (
                                <Badge variant="warning" className="bg-yellow-100 text-yellow-800">
                                  <Clock className="h-3 w-3 mr-1" />
                                  Due Soon
                                </Badge>
                              )}
                              {status === 'upcoming' && (
                                <Badge variant="outline" className="bg-blue-50 text-blue-800">
                                  <Calendar className="h-3 w-3 mr-1" />
                                  Upcoming
                                </Badge>
                              )}

                              {/* Secondary attention indicator */}
                              {status === 'overdue' ? (
                                <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                                  <AlertCircle className="h-3 w-3 mr-1" />
                                  Requires Attention
                                </Badge>
                              ) : (
                                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Up to Date
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant={hasProof ? "default" : "outline"}
                                size="sm"
                                onClick={() => {
                                  setSelectedTask(task);
                                  fetchAttachments(task.id);
                                  setUploadDialogOpen(true);
                                }}
                                className={hasProof ? "bg-blue-500 hover:bg-blue-600" : ""}
                              >
                                <Upload className="h-4 w-4" />
                                <span className="sr-only">Upload</span>
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedTask(task);
                                  setTaskTitle(task.title);
                                  setTaskDescription(task.description);
                                  setTaskCycle(task.cycle);
                                  setTaskDueDate(task.next_due_date.split('T')[0]);
                                  setEditDialogOpen(true);
                                }}
                              >
                                <FileText className="h-4 w-4" />
                                <span className="sr-only">Edit</span>
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteTask(task.id)}
                                disabled={isProcessing}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                                <span className="sr-only">Delete</span>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Completed Tasks History */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <h3 className="text-lg font-semibold flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                  Completed Tasks History
                </h3>
                <p className="text-sm text-gray-500 ml-4">
                  History of all completed compliance tasks
                </p>
              </div>

              <Select value={historyFilter} onValueChange={(value: any) => setHistoryFilter(value)}>
                <SelectTrigger className="w-[150px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter history" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Cycles</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="annually">Annual</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="border rounded-md bg-gray-50">
              <Table>
                <TableHeader>
                  <TableRow className="bg-green-50">
                    <TableHead className="w-[30px]"></TableHead>
                    <TableHead>Task</TableHead>
                    <TableHead>Cycle</TableHead>
                    <TableHead>Completed Date</TableHead>
                    <TableHead>Proof</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredHistoryTasks.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        {searchTerm ? (
                          <p>No completed tasks found matching your search</p>
                        ) : historyFilter !== 'all' ? (
                          <p>No completed {historyFilter} tasks found</p>
                        ) : (
                          <p>No completed tasks found. Mark tasks as completed to see them here.</p>
                        )}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredHistoryTasks.map((task) => {
                      const hasProof = task.has_proof;

                      return (
                        <TableRow
                          key={task.id}
                          className="bg-green-50 border-l-4 border-l-green-500"
                        >
                          <TableCell>
                            <div className="relative">
                              <Checkbox
                                checked={true}
                                disabled={true}
                                className="border-green-500 bg-green-100"
                              />
                              <div className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-green-500 border-2 border-white" />
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            <div className="flex items-start">
                              <div className="text-gray-500">
                                <div className="flex items-center">
                                  <span className="line-through decoration-2 decoration-green-500">
                                    {task.title}
                                  </span>
                                  {task.parent_task_id && (
                                    <Badge variant="outline" className="ml-2 bg-purple-50 text-purple-700 border-purple-200">
                                      <RefreshCw className="h-3 w-3 mr-1" />
                                      Recurring
                                    </Badge>
                                  )}
                                </div>
                                {task.description && (
                                  <p className="text-sm text-gray-500 truncate max-w-[200px]">
                                    {task.description}
                                  </p>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="capitalize bg-gray-100 text-gray-600">
                              {task.cycle}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {task.last_completed_at && (
                              <div className="flex items-center">
                                <span className="text-green-600">
                                  {format(parseISO(task.last_completed_at), 'dd MMM yyyy')}
                                </span>
                                <CheckCircle className="h-4 w-4 ml-1 text-green-500" />
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            {hasProof ? (
                              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                <FileText className="h-3 w-3 mr-1" />
                                Proof Available
                              </Badge>
                            ) : (
                              <span className="text-gray-400">No proof</span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant={hasProof ? "default" : "outline"}
                                size="sm"
                                onClick={() => {
                                  setSelectedTask(task);
                                  fetchAttachments(task.id);
                                  setUploadDialogOpen(true);
                                }}
                                className={hasProof ? "bg-blue-500 hover:bg-blue-600" : ""}
                              >
                                <Upload className="h-4 w-4" />
                                <span className="sr-only">View Proof</span>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </>
      )}

      {/* Add Task Dialog */}
      <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add Compliance Task</DialogTitle>
            <DialogDescription>
              Create a new recurring compliance task for your organization.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="title" className="text-sm font-medium">
                Task Title*
              </label>
              <Input
                id="title"
                placeholder="e.g., Fire Alarm Testing"
                value={taskTitle}
                onChange={(e) => setTaskTitle(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium">
                Description
              </label>
              <Textarea
                id="description"
                placeholder="Provide details about the compliance task..."
                value={taskDescription}
                onChange={(e) => setTaskDescription(e.target.value)}
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="cycle" className="text-sm font-medium">
                  Recurrence Cycle*
                </label>
                <Select
                  value={taskCycle}
                  onValueChange={(value: any) => setTaskCycle(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select cycle" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="annually">Annually</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label htmlFor="dueDate" className="text-sm font-medium">
                  First Due Date
                </label>
                <Input
                  id="dueDate"
                  type="date"
                  value={taskDueDate}
                  onChange={(e) => setTaskDueDate(e.target.value)}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddTask} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding...
                </>
              ) : (
                'Add Task'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Task Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Compliance Task</DialogTitle>
            <DialogDescription>
              Update the details of this compliance task.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="edit-title" className="text-sm font-medium">
                Task Title*
              </label>
              <Input
                id="edit-title"
                placeholder="e.g., Fire Alarm Testing"
                value={taskTitle}
                onChange={(e) => setTaskTitle(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="edit-description" className="text-sm font-medium">
                Description
              </label>
              <Textarea
                id="edit-description"
                placeholder="Provide details about the compliance task..."
                value={taskDescription}
                onChange={(e) => setTaskDescription(e.target.value)}
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="edit-cycle" className="text-sm font-medium">
                  Recurrence Cycle*
                </label>
                <Select
                  value={taskCycle}
                  onValueChange={(value: any) => setTaskCycle(value)}
                >
                  <SelectTrigger id="edit-cycle">
                    <SelectValue placeholder="Select cycle" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="annually">Annually</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label htmlFor="edit-dueDate" className="text-sm font-medium">
                  Next Due Date
                </label>
                <Input
                  id="edit-dueDate"
                  type="date"
                  value={taskDueDate}
                  onChange={(e) => setTaskDueDate(e.target.value)}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateTask} disabled={isProcessing}>
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Task'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Upload Attachment Dialog */}
      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Upload Proof of Completion</DialogTitle>
            <DialogDescription>
              Upload documents or photos as proof that this compliance task was completed.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            {selectedTask && (
              <div className="bg-blue-50 p-3 rounded-md">
                <p className="font-medium">{selectedTask.title}</p>
                <p className="text-sm text-gray-600">
                  Due: {format(parseISO(selectedTask.next_due_date), 'dd MMM yyyy')}
                </p>
              </div>
            )}

            <div className="space-y-2">
              <label htmlFor="file-upload" className="text-sm font-medium">
                Upload File
              </label>
              <Input
                id="file-upload"
                type="file"
                onChange={(e) => {
                  if (e.target.files && e.target.files[0]) {
                    setSelectedFile(e.target.files[0]);
                  }
                }}
              />
              <p className="text-xs text-gray-500">
                Accepted file types: PDF, DOC, DOCX, JPG, PNG
              </p>
            </div>

            {/* Show existing attachments */}
            {attachments.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Existing Attachments</h4>
                <div className="border rounded-md divide-y">
                  {attachments.map((attachment) => (
                    <div key={attachment.id} className="p-2 flex justify-between items-center">
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-blue-500" />
                        <span className="text-sm">{attachment.file_name}</span>
                      </div>
                      <div className="text-xs text-gray-500">
                        {format(parseISO(attachment.uploaded_at), 'dd MMM yyyy')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setUploadDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleFileUpload}
              disabled={isProcessing || !selectedFile}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                'Upload'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ComplianceManagement;
