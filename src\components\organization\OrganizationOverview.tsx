import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Link } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  BarChart,
  CheckSquare,
  ClipboardList,
  Clock,
  FileText,
  AlertCircle,
  Users,
  Globe,
  PoundSterling,
  ArrowRight,
  CheckCircle,
  Calendar,
  TrendingUp,
  TrendingDown,
  Building,
  PieChart,
  Activity,
} from 'lucide-react';
import { Loader2 } from 'lucide-react';

// Define types
interface Task {
  id: string;
  title: string;
  description: string;
  status: 'open' | 'assigned' | 'in_progress' | 'pending_payment' | 'completed' | 'closed';
  created_by?: string;
  user_id: string;
  assigned_to?: string;
  created_at: string;
  due_date?: string;
  organization_id?: string;
  category?: string;
  location?: string;
  budget?: number;
  visibility: 'admin' | 'internal' | 'public';
  payment_status?: 'unpaid' | 'pending' | 'processing' | 'paid' | 'not_required';
  offers_count?: number;
}

interface ComplianceTask {
  id: string;
  title: string;
  description: string;
  cycle: 'daily' | 'weekly' | 'monthly' | 'annually';
  last_completed_at: string | null;
  next_due_date: string;
  created_at: string;
  created_by: string;
  is_completed?: boolean;
}

interface Profile {
  id: string;
  email: string;
  full_name: string;
  role: string;
  avatar_url?: string;
  organization_id: string;
  account_type?: string;
  phone?: string;
  job_title?: string;
}

interface Invoice {
  id: string;
  amount: number;
  status: 'paid' | 'pending' | 'overdue';
  due_date: string;
  created_at: string;
  supplier_id?: string;
  supplier_name?: string;
}

const OrganizationOverview = () => {
  const { organizationId, user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [complianceTasks, setComplianceTasks] = useState<ComplianceTask[]>([]);
  const [profiles, setProfiles] = useState<Profile[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [organizationName, setOrganizationName] = useState('');

  // Stats
  const [taskStats, setTaskStats] = useState({
    total: 0,
    open: 0,
    inProgress: 0,
    completed: 0,
    internal: 0,
    external: 0,
    totalBudget: 0,
    pendingBudget: 0,
  });

  const [complianceStats, setComplianceStats] = useState({
    total: 0,
    completed: 0,
    overdue: 0,
    dueToday: 0,
    dueThisWeek: 0,
    completionRate: 0,
    daily: { total: 0, completed: 0 },
    weekly: { total: 0, completed: 0 },
    monthly: { total: 0, completed: 0 },
    annually: { total: 0, completed: 0 },
  });

  const [invoiceStats, setInvoiceStats] = useState({
    total: 0,
    paid: 0,
    pending: 0,
    overdue: 0,
    totalAmount: 0,
    pendingAmount: 0,
  });

  const [userStats, setUserStats] = useState({
    total: 0,
    admins: 0,
    teachers: 0,
    maintenance: 0,
    suppliers: 0,
  });

  useEffect(() => {
    if (organizationId) {
      fetchAllData();
    }
  }, [organizationId]);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchOrganizationInfo(),
        fetchTasks(),
        fetchComplianceTasks(),
        fetchProfiles(),
        fetchInvoices(),
      ]);
    } catch (error) {
      console.error('Error fetching overview data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load overview data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchOrganizationInfo = async () => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('name')
        .eq('id', organizationId)
        .single();

      if (error) throw error;
      if (data) setOrganizationName(data.name);
    } catch (error) {
      console.error('Error fetching organization info:', error);
    }
  };

  const fetchTasks = async () => {
    try {
      // Get profiles in this organization
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id')
        .eq('organization_id', organizationId);

      if (profilesError) throw profilesError;
      const orgUserIds = profilesData.map(profile => profile.id);

      // Fetch tasks created by organization members
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .in('user_id', orgUserIds)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const tasksData = data || [];
      setTasks(tasksData);

      // Calculate task statistics
      const stats = {
        total: tasksData.length,
        open: tasksData.filter(t => t.status === 'open').length,
        inProgress: tasksData.filter(t => t.status === 'in_progress').length,
        completed: tasksData.filter(t => t.status === 'completed' || t.status === 'closed').length,
        internal: tasksData.filter(t => t.visibility === 'internal' || t.visibility === 'admin').length,
        external: tasksData.filter(t => t.visibility === 'public').length,
        totalBudget: tasksData.reduce((sum, task) => sum + (task.budget || 0), 0),
        pendingBudget: tasksData.filter(t =>
          t.visibility === 'public' &&
          t.status !== 'completed' &&
          t.status !== 'closed'
        ).reduce((sum, task) => sum + (task.budget || 0), 0),
      };

      setTaskStats(stats);
    } catch (error) {
      console.error('Error fetching tasks:', error);
    }
  };

  const fetchComplianceTasks = async () => {
    try {
      const { data, error } = await supabase
        .from('compliance_tasks')
        .select('*')
        .eq('organization_id', organizationId);

      if (error) throw error;

      const tasksData = data || [];
      setComplianceTasks(tasksData);

      // Calculate compliance statistics
      const today = new Date();
      const oneWeekFromNow = new Date(today);
      oneWeekFromNow.setDate(today.getDate() + 7);

      const stats = {
        total: tasksData.length,
        completed: tasksData.filter(t => t.is_completed).length,
        overdue: tasksData.filter(t => !t.is_completed && new Date(t.next_due_date) < today).length,
        dueToday: tasksData.filter(t => {
          const dueDate = new Date(t.next_due_date);
          return !t.is_completed &&
            dueDate.getDate() === today.getDate() &&
            dueDate.getMonth() === today.getMonth() &&
            dueDate.getFullYear() === today.getFullYear();
        }).length,
        dueThisWeek: tasksData.filter(t => {
          const dueDate = new Date(t.next_due_date);
          return !t.is_completed && dueDate > today && dueDate <= oneWeekFromNow;
        }).length,
        completionRate: tasksData.length > 0 ? Math.round((tasksData.filter(t => t.is_completed).length / tasksData.length) * 100) : 0,
        daily: {
          total: tasksData.filter(t => t.cycle === 'daily').length,
          completed: tasksData.filter(t => t.cycle === 'daily' && t.is_completed).length
        },
        weekly: {
          total: tasksData.filter(t => t.cycle === 'weekly').length,
          completed: tasksData.filter(t => t.cycle === 'weekly' && t.is_completed).length
        },
        monthly: {
          total: tasksData.filter(t => t.cycle === 'monthly').length,
          completed: tasksData.filter(t => t.cycle === 'monthly' && t.is_completed).length
        },
        annually: {
          total: tasksData.filter(t => t.cycle === 'annually').length,
          completed: tasksData.filter(t => t.cycle === 'annually' && t.is_completed).length
        }
      };

      setComplianceStats(stats);
    } catch (error) {
      console.error('Error fetching compliance tasks:', error);
    }
  };

  const fetchProfiles = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('organization_id', organizationId);

      if (error) throw error;

      const profilesData = data || [];
      setProfiles(profilesData);

      // Calculate user statistics
      const stats = {
        total: profilesData.length,
        admins: profilesData.filter(p => p.role === 'admin').length,
        teachers: profilesData.filter(p => p.role === 'teacher').length,
        maintenance: profilesData.filter(p => p.role === 'maintenance').length,
        suppliers: profilesData.filter(p => p.account_type === 'supplier').length,
      };

      setUserStats(stats);
    } catch (error) {
      console.error('Error fetching profiles:', error);
    }
  };

  const fetchInvoices = async () => {
    try {
      // This is a placeholder - you'll need to implement the actual invoice fetching
      // based on your database schema
      const mockInvoices: Invoice[] = [];
      setInvoices(mockInvoices);

      // Calculate invoice statistics
      const stats = {
        total: mockInvoices.length,
        paid: mockInvoices.filter(i => i.status === 'paid').length,
        pending: mockInvoices.filter(i => i.status === 'pending').length,
        overdue: mockInvoices.filter(i => i.status === 'overdue').length,
        totalAmount: mockInvoices.reduce((sum, invoice) => sum + invoice.amount, 0),
        pendingAmount: mockInvoices.filter(i => i.status !== 'paid').reduce((sum, invoice) => sum + invoice.amount, 0),
      };

      setInvoiceStats(stats);
    } catch (error) {
      console.error('Error fetching invoices:', error);
    }
  };

  // Calculate additional high-level metrics

  // Task distribution percentages
  const taskDistribution = {
    internal: taskStats.total > 0 ? Math.round((taskStats.internal / taskStats.total) * 100) : 0,
    external: taskStats.total > 0 ? Math.round((taskStats.external / taskStats.total) * 100) : 0
  };

  // Compliance status by cycle
  const complianceByType = {
    daily: {
      total: complianceTasks.filter(t => t.cycle === 'daily').length,
      completed: complianceTasks.filter(t => t.cycle === 'daily' && t.is_completed).length,
      percentage: complianceTasks.filter(t => t.cycle === 'daily').length > 0
        ? Math.round((complianceTasks.filter(t => t.cycle === 'daily' && t.is_completed).length /
                      complianceTasks.filter(t => t.cycle === 'daily').length) * 100)
        : 0
    },
    weekly: {
      total: complianceTasks.filter(t => t.cycle === 'weekly').length,
      completed: complianceTasks.filter(t => t.cycle === 'weekly' && t.is_completed).length,
      percentage: complianceTasks.filter(t => t.cycle === 'weekly').length > 0
        ? Math.round((complianceTasks.filter(t => t.cycle === 'weekly' && t.is_completed).length /
                      complianceTasks.filter(t => t.cycle === 'weekly').length) * 100)
        : 0
    },
    monthly: {
      total: complianceTasks.filter(t => t.cycle === 'monthly').length,
      completed: complianceTasks.filter(t => t.cycle === 'monthly' && t.is_completed).length,
      percentage: complianceTasks.filter(t => t.cycle === 'monthly').length > 0
        ? Math.round((complianceTasks.filter(t => t.cycle === 'monthly' && t.is_completed).length /
                      complianceTasks.filter(t => t.cycle === 'monthly').length) * 100)
        : 0
    },
    annually: {
      total: complianceTasks.filter(t => t.cycle === 'annually').length,
      completed: complianceTasks.filter(t => t.cycle === 'annually' && t.is_completed).length,
      percentage: complianceTasks.filter(t => t.cycle === 'annually').length > 0
        ? Math.round((complianceTasks.filter(t => t.cycle === 'annually' && t.is_completed).length /
                      complianceTasks.filter(t => t.cycle === 'annually').length) * 100)
        : 0
    }
  };

  // Task status distribution
  const taskStatusDistribution = {
    open: taskStats.total > 0 ? Math.round((taskStats.open / taskStats.total) * 100) : 0,
    inProgress: taskStats.total > 0 ? Math.round((taskStats.inProgress / taskStats.total) * 100) : 0,
    completed: taskStats.total > 0 ? Math.round((taskStats.completed / taskStats.total) * 100) : 0
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Organization Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">{organizationName} Overview</h2>
          <p className="text-muted-foreground">
            Dashboard last updated: {new Date().toLocaleString()}
          </p>
        </div>
        <Button onClick={fetchAllData}>
          <Clock className="mr-2 h-4 w-4" />
          Refresh Data
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Building className="h-4 w-4 mr-2 text-purple-500" />
              Internal Tasks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{taskStats.internal}</div>
            <div className="flex justify-between mt-2 text-sm">
              <span className="text-muted-foreground">Completed:</span>
              <span className="font-medium">
                {tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') &&
                  (t.status === 'completed' || t.status === 'closed')).length} / {taskStats.internal}
              </span>
            </div>
            <Progress
              value={taskStats.internal > 0 ?
                (tasks.filter(t => (t.visibility === 'internal' || t.visibility === 'admin') &&
                  (t.status === 'completed' || t.status === 'closed')).length / taskStats.internal) * 100 : 0}
              className="h-2 mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Globe className="h-4 w-4 mr-2 text-orange-500" />
              External Tasks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{taskStats.external}</div>
            <div className="flex justify-between mt-2 text-sm">
              <span className="text-muted-foreground">Completed:</span>
              <span className="font-medium">
                {tasks.filter(t => t.visibility === 'public' &&
                  (t.status === 'completed' || t.status === 'closed')).length} / {taskStats.external}
              </span>
            </div>
            <Progress
              value={taskStats.external > 0 ?
                (tasks.filter(t => t.visibility === 'public' &&
                  (t.status === 'completed' || t.status === 'closed')).length / taskStats.external) * 100 : 0}
              className="h-2 mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <CheckSquare className="h-4 w-4 mr-2 text-green-500" />
              Compliance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{complianceStats.total}</div>
            <div className="flex justify-between mt-2 text-sm">
              <span className="text-muted-foreground">Completion Rate:</span>
              <span className="font-medium">{complianceStats.completionRate}%</span>
            </div>
            <Progress
              value={complianceStats.completionRate}
              className="h-2 mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <PoundSterling className="h-4 w-4 mr-2 text-emerald-500" />
              Budget
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">£{taskStats.totalBudget.toFixed(2)}</div>
            <div className="flex justify-between mt-2 text-sm">
              <span className="text-muted-foreground">Pending:</span>
              <span className="font-medium">£{taskStats.pendingBudget.toFixed(2)}</span>
            </div>
            <Progress
              value={taskStats.totalBudget > 0 ? ((taskStats.totalBudget - taskStats.pendingBudget) / taskStats.totalBudget) * 100 : 0}
              className="h-2 mt-2"
            />
          </CardContent>
        </Card>
      </div>

      {/* Task Distribution */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Task Type Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <PieChart className="mr-2 h-5 w-5 text-blue-500" />
                Task Distribution
              </div>
              <Link to="/organization/dashboard?tab=tasks" className="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                View Details <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </CardTitle>
            <CardDescription>Breakdown of internal vs external tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium flex items-center">
                    <Building className="h-4 w-4 mr-2 text-purple-500" />
                    Internal Tasks
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {taskStats.internal} ({taskDistribution.internal}%)
                  </span>
                </div>
                <Progress
                  value={taskDistribution.internal}
                  className="h-2 bg-gray-200"
                />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium flex items-center">
                    <Globe className="h-4 w-4 mr-2 text-orange-500" />
                    External Tasks
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {taskStats.external} ({taskDistribution.external}%)
                  </span>
                </div>
                <Progress
                  value={taskDistribution.external}
                  className="h-2 bg-gray-200"
                />
              </div>
            </div>

            <div className="mt-6">
              <h4 className="text-sm font-medium mb-2">Task Status Breakdown</h4>
              <div className="grid grid-cols-3 gap-2 text-center">
                <div className="bg-red-50 p-2 rounded-md">
                  <div className="text-red-600 font-medium">{taskStats.open}</div>
                  <div className="text-xs text-gray-500">Open</div>
                </div>
                <div className="bg-yellow-50 p-2 rounded-md">
                  <div className="text-yellow-600 font-medium">{taskStats.inProgress}</div>
                  <div className="text-xs text-gray-500">In Progress</div>
                </div>
                <div className="bg-green-50 p-2 rounded-md">
                  <div className="text-green-600 font-medium">{taskStats.completed}</div>
                  <div className="text-xs text-gray-500">Completed</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Task Budget Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <PoundSterling className="mr-2 h-5 w-5 text-emerald-500" />
                Budget Overview
              </div>
              <Link to="/organization/dashboard?tab=invoices" className="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                View Details <ArrowRight className="ml-1 h-4 w-4" />
              </Link>
            </CardTitle>
            <CardDescription>Financial summary of tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-md">
                  <div className="text-sm text-gray-500">Total Budget</div>
                  <div className="text-xl font-bold text-blue-600">£{taskStats.totalBudget.toFixed(2)}</div>
                </div>
                <div className="bg-amber-50 p-4 rounded-md">
                  <div className="text-sm text-gray-500">Pending</div>
                  <div className="text-xl font-bold text-amber-600">£{taskStats.pendingBudget.toFixed(2)}</div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Budget Allocation</h4>
                <div className="flex h-4 rounded-full overflow-hidden">
                  <div
                    className="bg-green-500"
                    style={{ width: `${taskStats.totalBudget > 0 ? ((taskStats.totalBudget - taskStats.pendingBudget) / taskStats.totalBudget) * 100 : 0}%` }}
                  ></div>
                  <div
                    className="bg-amber-500"
                    style={{ width: `${taskStats.totalBudget > 0 ? (taskStats.pendingBudget / taskStats.totalBudget) * 100 : 0}%` }}
                  ></div>
                </div>
                <div className="flex justify-between mt-1 text-xs">
                  <span>Paid/Completed</span>
                  <span>Pending</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Compliance Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <CheckSquare className="mr-2 h-5 w-5 text-green-500" />
              Compliance Overview
            </div>
            <Link to="/organization/dashboard?tab=compliance" className="text-sm text-blue-600 hover:text-blue-800 flex items-center">
              View Details <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </CardTitle>
          <CardDescription>Compliance task completion by frequency</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Compliance Status */}
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">Daily Tasks</span>
                  <span className="text-sm text-muted-foreground">
                    {complianceByType.daily.completed}/{complianceByType.daily.total} ({complianceByType.daily.percentage}%)
                  </span>
                </div>
                <Progress
                  value={complianceByType.daily.percentage}
                  className="h-2"
                />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">Weekly Tasks</span>
                  <span className="text-sm text-muted-foreground">
                    {complianceByType.weekly.completed}/{complianceByType.weekly.total} ({complianceByType.weekly.percentage}%)
                  </span>
                </div>
                <Progress
                  value={complianceByType.weekly.percentage}
                  className="h-2"
                />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">Monthly Tasks</span>
                  <span className="text-sm text-muted-foreground">
                    {complianceByType.monthly.completed}/{complianceByType.monthly.total} ({complianceByType.monthly.percentage}%)
                  </span>
                </div>
                <Progress
                  value={complianceByType.monthly.percentage}
                  className="h-2"
                />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium">Annual Tasks</span>
                  <span className="text-sm text-muted-foreground">
                    {complianceByType.annually.completed}/{complianceByType.annually.total} ({complianceByType.annually.percentage}%)
                  </span>
                </div>
                <Progress
                  value={complianceByType.annually.percentage}
                  className="h-2"
                />
              </div>
            </div>

            {/* Compliance Summary */}
            <div>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="bg-green-50 p-4 rounded-md text-center">
                  <div className="text-3xl font-bold text-green-600">{complianceStats.completionRate}%</div>
                  <div className="text-sm text-gray-500">Overall Completion</div>
                </div>
                <div className="bg-red-50 p-4 rounded-md text-center">
                  <div className="text-3xl font-bold text-red-600">{complianceStats.overdue}</div>
                  <div className="text-sm text-gray-500">Overdue Tasks</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-md text-center">
                  <div className="text-3xl font-bold text-blue-600">{complianceStats.dueToday}</div>
                  <div className="text-sm text-gray-500">Due Today</div>
                </div>
                <div className="bg-yellow-50 p-4 rounded-md text-center">
                  <div className="text-3xl font-bold text-yellow-600">{complianceStats.dueThisWeek}</div>
                  <div className="text-sm text-gray-500">Due This Week</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Team Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Users className="mr-2 h-5 w-5 text-purple-500" />
              Team Overview
            </div>
            <Link to="/organization/dashboard?tab=users" className="text-sm text-blue-600 hover:text-blue-800 flex items-center">
              View Details <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </CardTitle>
          <CardDescription>Summary of team members by role</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-purple-50 p-4 rounded-md text-center">
              <div className="text-2xl font-bold text-purple-600">{userStats.admins}</div>
              <div className="text-sm text-gray-500">Admins</div>
            </div>
            <div className="bg-blue-50 p-4 rounded-md text-center">
              <div className="text-2xl font-bold text-blue-600">{userStats.teachers}</div>
              <div className="text-sm text-gray-500">Teachers</div>
            </div>
            <div className="bg-green-50 p-4 rounded-md text-center">
              <div className="text-2xl font-bold text-green-600">{userStats.maintenance}</div>
              <div className="text-sm text-gray-500">Maintenance</div>
            </div>
            <div className="bg-orange-50 p-4 rounded-md text-center">
              <div className="text-2xl font-bold text-orange-600">{userStats.suppliers}</div>
              <div className="text-sm text-gray-500">Suppliers</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OrganizationOverview;
