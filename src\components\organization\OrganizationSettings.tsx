import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useLocation, useNavigate } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';
import { Loader2, MapPin } from 'lucide-react';
import LocationSearch from '@/components/ui/location-search';
import { getCoordinates } from '@/utils/location-utils';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

// Define the form schema
const organizationSchema = z.object({
  name: z.string().min(2, { message: 'Organization name must be at least 2 characters' }),
  organization_type: z.enum(['school', 'trust']),
  parent_organization_id: z.union([z.string().uuid(), z.literal('none'), z.null()]).optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().url({ message: 'Please enter a valid URL' }).optional().or(z.literal('')),
  // New location fields
  location_formatted: z.string().optional(),
  location_lat: z.number().optional(),
  location_lng: z.number().optional(),
  location_place_id: z.string().optional(),
});

type OrganizationFormValues = z.infer<typeof organizationSchema>;

export const OrganizationSettings = () => {
  // This component is for updating the current organization's profile and settings
  // It allows editing organization details like name, type, address, etc.
  const { organizationId, isAdmin, profile, user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // Check if this is a new organization setup
  const searchParams = new URLSearchParams(location.search);
  const isNewOrganization = searchParams.get('new') === 'true';

  console.log('OrganizationSettings: isAdmin =', isAdmin);
  console.log('OrganizationSettings: profile =', profile);
  console.log('OrganizationSettings: isNewOrganization =', isNewOrganization);

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [locationInput, setLocationInput] = useState('');
  const [isGeocodingLocation, setIsGeocodingLocation] = useState(false);

  // Initialize form
  const form = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: '',
      organization_type: 'school',
      parent_organization_id: null,
      address: '',
      city: '',
      state: '',
      zip: '',
      phone: '',
      website: '',
      // Initialize location fields
      location_formatted: '',
      location_lat: undefined,
      location_lng: undefined,
      location_place_id: '',
    },
  });

  // State for available parent organizations (trusts)
  const [availableTrusts, setAvailableTrusts] = useState<Array<{ id: string; name: string }>>([]);

  // State for available schools (for admins to select)
  const [availableSchools, setAvailableSchools] = useState<Array<{ id: string; name: string }>>([]);

  // State for selected organization (current org by default)
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<string | null>(organizationId);

  // State to track the current organization name being edited
  const [currentOrgName, setCurrentOrgName] = useState<string>('');

  // Debug: Log when availableSchools changes
  useEffect(() => {
    console.log('availableSchools state updated:', availableSchools);

    // Add a debug message to show if the organization is a trust
    const checkOrgType = async () => {
      if (!organizationId) return;

      try {
        const { data, error } = await supabase
          .from('organizations')
          .select('organization_type')
          .eq('id', organizationId)
          .single();

        if (error) throw error;

        console.log('DEBUG: Organization type check:', {
          id: organizationId,
          type: data?.organization_type,
          isTrust: String(data?.organization_type || '').toLowerCase() === 'trust',
          isAdmin,
          profile
        });
      } catch (err) {
        console.error('Error checking organization type:', err);
      }
    };

    checkOrgType();
  }, [availableSchools, organizationId, isAdmin, profile]);

  // Initialize location input from formatted address or address field
  useEffect(() => {
    if (form.getValues('location_formatted')) {
      setLocationInput(form.getValues('location_formatted'));
    } else if (form.getValues('address')) {
      // Combine address fields to create a full address string
      const addressParts = [
        form.getValues('address'),
        form.getValues('city'),
        form.getValues('state'),
        form.getValues('zip')
      ].filter(Boolean);

      setLocationInput(addressParts.join(', '));
    }
  }, [form]);

  // Handle location selection
  const handleLocationChange = async (location: string) => {
    setLocationInput(location);

    if (location) {
      setIsGeocodingLocation(true);
      try {
        // Get coordinates for the selected location
        const coordinates = await getCoordinates(location);

        if (coordinates) {
          // Update form values with location data
          form.setValue('location_formatted', location);
          form.setValue('location_lat', coordinates.lat);
          form.setValue('location_lng', coordinates.lng);

          // Also update the address field for backward compatibility
          form.setValue('address', location);
        }
      } catch (error) {
        console.error('Error geocoding location:', error);
      } finally {
        setIsGeocodingLocation(false);
      }
    }
  };

  useEffect(() => {
    console.log('OrganizationSettings: organizationId =', organizationId);

    // If this is a new organization setup, set loading to false and initialize empty form
    if (isNewOrganization) {
      setLoading(false);
      // Reset form with default values for a new organization
      form.reset({
        name: '',
        organization_type: 'school',
        parent_organization_id: 'none',
        address: '',
        city: '',
        state: '',
        zip: '',
        phone: '',
        website: '',
        location_formatted: '',
        location_lat: undefined,
        location_lng: undefined,
        location_place_id: '',
      });
    }
    // Otherwise, fetch existing organization details
    else if (organizationId) {
      fetchOrganizationDetails(organizationId);
      fetchAvailableTrusts();
      fetchAvailableSchools();
    }
  }, [organizationId, isNewOrganization]); // eslint-disable-line react-hooks/exhaustive-deps

  // Update form when selected organization changes
  useEffect(() => {
    if (selectedOrganizationId) {
      console.log('Selected organization changed to:', selectedOrganizationId);
      fetchOrganizationDetails(selectedOrganizationId);
    }
  }, [selectedOrganizationId]); // eslint-disable-line react-hooks/exhaustive-deps

  // Fetch available schools for admin selection
  const fetchAvailableSchools = async () => {
    console.log('Fetching available schools for organization:', organizationId);

    // Only proceed if user is an admin
    if (!isAdmin) {
      console.log('User is not an admin, skipping school fetch');
      return;
    }

    // Check if the user's organization is a trust
    if (profile?.organization_id !== organizationId) {
      console.log('User is not part of this organization, skipping school fetch');
      return;
    }
    try {
      // Only fetch schools if we're a trust admin
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('organization_type')
        .eq('id', organizationId)
        .single();

      if (orgError) throw orgError;

      console.log('Organization type:', orgData?.organization_type);
      // Only fetch schools if we're a trust (case-insensitive check)
      const orgType = String(orgData?.organization_type || '').toLowerCase();
      if (orgType === 'trust') {
        const { data, error } = await supabase
          .from('organizations')
          .select('id, name')
          .eq('organization_type', 'school')
          .eq('parent_organization_id', organizationId);

        if (error) throw error;

        if (data) {
          console.log('Available schools:', data);
          setAvailableSchools(data);
        }
      }
    } catch (error: any) {
      console.error('Error fetching available schools:', error.message);
    }
  };

  // Fetch available trusts for parent organization selection
  const fetchAvailableTrusts = async () => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('id, name')
        .eq('organization_type', 'trust');

      if (error) throw error;

      if (data) {
        setAvailableTrusts(data);
      }
    } catch (error: any) {
      console.error('Error fetching available trusts:', error.message);
    }
  };

  const fetchOrganizationDetails = async (orgId: string) => {
    try {
      console.log('Fetching organization details for ID:', orgId);
      setLoading(true);

      // Define the organization type for TypeScript
      interface OrganizationData {
        id: string;
        name: string;
        organization_type: 'school' | 'trust';
        parent_organization_id: string | null;
        address?: string;
        city?: string;
        state?: string;
        zip?: string;
        phone?: string;
        website?: string;
        created_at: string;
        updated_at: string;
        // New location fields
        location_formatted?: string;
        location_lat?: number;
        location_lng?: number;
        location_place_id?: string;
      }

      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', orgId)
        .single();

      if (error) {
        console.error('Error fetching organization:', error);
        throw error;
      }

      console.log('Organization data received:', data);

      if (data) {
        const orgData = data as unknown as OrganizationData;
        console.log('Setting form values with:', orgData);
        // Update the current organization name
        setCurrentOrgName(orgData.name || '');
        // Reset form with organization data
        form.reset({
          name: orgData.name || '',
          organization_type: orgData.organization_type || 'school',
          parent_organization_id: orgData.parent_organization_id || 'none',
          address: orgData.address || '',
          city: orgData.city || '',
          state: orgData.state || '',
          zip: orgData.zip || '',
          phone: orgData.phone || '',
          website: orgData.website || '',
          // Set location fields
          location_formatted: orgData.location_formatted || '',
          location_lat: orgData.location_lat,
          location_lng: orgData.location_lng,
          location_place_id: orgData.location_place_id || '',
        });

        // Update location input field
        if (orgData.location_formatted) {
          setLocationInput(orgData.location_formatted);
        } else if (orgData.address) {
          // Combine address fields to create a full address string
          const addressParts = [
            orgData.address,
            orgData.city,
            orgData.state,
            orgData.zip
          ].filter(Boolean);

          setLocationInput(addressParts.join(', '));
        }
      } else {
        console.log('No organization data found');
        setCurrentOrgName('');
      }

      // Check if we need to re-fetch available schools
      const orgType = String((data as any)?.organization_type || '').toLowerCase();
      console.log('Organization type in fetchOrganizationDetails:', orgType);
      if (orgType === 'trust') {
        console.log('This is a trust, fetching available schools again');
        fetchAvailableSchools();
      } else {
        console.log('This is not a trust, no need to fetch schools');
      }
    } catch (error: any) {
      console.error('Error fetching organization details:', error.message);
      toast({
        title: 'Error',
        description: 'Failed to load organization details',
        variant: 'destructive',
      });
    } finally {
      console.log('Finished loading organization details');
      setLoading(false);
    }
  };

  const onSubmit = async (values: OrganizationFormValues) => {
    console.log('Submitting form with values:', values);
    try {
      setSaving(true);

      // Process values before submitting
      const processedValues = {
        ...values,
        // Handle 'none' value for parent_organization_id
        parent_organization_id: values.parent_organization_id === 'none' ? null : values.parent_organization_id
      };

      console.log('Processed values for submission:', processedValues);

      // If this is a new organization, create it
      if (isNewOrganization) {
        if (!user) {
          toast({
            variant: 'destructive',
            title: 'Error',
            description: 'You must be logged in to create an organization',
          });
          return;
        }

        // Create a new organization
        const { data, error } = await supabase
          .from('organizations')
          .insert({
            ...processedValues,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select();

        if (error) throw error;

        if (data && data.length > 0) {
          const newOrgId = data[0].id;

          // Update the user's profile with the new organization ID
          const { error: profileError } = await supabase
            .from('profiles')
            .update({
              organization_id: newOrgId,
              role: 'admin' // Set the user as an admin of the new organization
            })
            .eq('id', user.id);

          if (profileError) throw profileError;

          // Get the organization name for the success message
          const orgName = form.getValues('name');
          const orgType = values.organization_type === 'trust' ? 'Multi-Academy Trust' : 'School';

          toast({
            title: `${orgType} created`,
            description: `Your ${orgType.toLowerCase()} has been created successfully`,
          });

          // Redirect to the dashboard
          navigate('/organization/dashboard');
        }
      }
      // Otherwise, update the existing organization
      else {
        // Use the selected organization ID if available, otherwise use the current organization ID
        const targetOrgId = selectedOrganizationId || organizationId;
        if (!targetOrgId) return;

        const { error } = await supabase
          .from('organizations')
          .update({
            ...processedValues,
            updated_at: new Date().toISOString()
          })
          .eq('id', targetOrgId);

        if (error) throw error;

        // Get the organization name for the success message
        const orgName = form.getValues('name');
        toast({
          title: 'Success',
          description: `${orgName} settings updated successfully`,
        });
      }
    } catch (error: any) {
      console.error('Error updating/creating organization:', error.message);
      toast({
        title: 'Error',
        description: isNewOrganization ? 'Failed to create organization' : 'Failed to update organization settings',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="space-y-4">
          {/* School selection dropdown for trust admins */}

          {availableSchools.length > 0 && (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium">Select School to Edit</label>
                {loading && <Loader2 className="h-4 w-4 animate-spin text-gray-500" />}
              </div>
              <Select
                onValueChange={(value) => {
                  setLoading(true); // Show loading state when changing organization
                  setSelectedOrganizationId(value);
                }}
                defaultValue={selectedOrganizationId || undefined}
                disabled={loading} // Disable while loading
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a school" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={organizationId}>Current Organization (Trust)</SelectItem>
                  {availableSchools.map((school) => (
                    <SelectItem key={school.id} value={school.id}>
                      {school.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-500 mt-1">
                Choose a school to edit its details, or select the trust to edit trust details.
              </p>
            </div>
          )}
          {/* Show which organization is being edited */}
          {selectedOrganizationId && availableSchools.length > 0 && (
            <div className="bg-blue-50 p-3 rounded-md mb-4 border border-blue-200">
              <p className="text-sm text-blue-700">
                You are currently editing: <strong>{currentOrgName}</strong>
              </p>
              <p className="text-xs text-blue-500 mt-1">
                Organization type: {form.getValues('organization_type')}
              </p>
            </div>
          )}

          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Organization Name*</FormLabel>
                <FormControl>
                  <Input placeholder="Enter organization name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="organization_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Organization Type*</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select organization type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="school">School</SelectItem>
                    <SelectItem value="trust">Multi-Academy Trust</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Only show parent organization field if type is school */}
          {form.watch('organization_type') === 'school' && (
            <FormField
              control={form.control}
              name="parent_organization_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Parent Trust (Optional)</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(value)}
                    defaultValue={field.value === null ? 'none' : field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select parent trust" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {availableTrusts.map((trust) => (
                        <SelectItem key={trust.id} value={trust.id}>
                          {trust.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          <FormField
            control={form.control}
            name="location_formatted"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Location</FormLabel>
                <FormControl>
                  <div className="relative">
                    <LocationSearch
                      value={locationInput}
                      onChange={handleLocationChange}
                      placeholder="Enter organization address"
                      label=""
                      className=""
                    />
                    {isGeocodingLocation && (
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                        <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-opacity-50 border-t-transparent rounded-full"></div>
                      </div>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
                <p className="text-sm text-gray-500 mt-1">
                  Enter your organization's full address to enable location-based features
                </p>
                {form.getValues('location_lat') && form.getValues('location_lng') && (
                  <p className="text-xs text-green-600 mt-1 flex items-center">
                    <MapPin className="h-3 w-3 mr-1" />
                    Location coordinates saved: {form.getValues('location_lat')?.toFixed(6)}, {form.getValues('location_lng')?.toFixed(6)}
                  </p>
                )}
              </FormItem>
            )}
          />

          {/* Hidden fields to store location data */}
          <input type="hidden" {...form.register('location_lat')} />
          <input type="hidden" {...form.register('location_lng')} />
          <input type="hidden" {...form.register('location_place_id')} />

          {/* Keep the address fields for backward compatibility but hide them */}
          <input type="hidden" {...form.register('address')} />
          <input type="hidden" {...form.register('city')} />
          <input type="hidden" {...form.register('state')} />
          <input type="hidden" {...form.register('zip')} />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input placeholder="Phone number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="website"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Website</FormLabel>
                <FormControl>
                  <Input placeholder="https://www.example.com" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" type="button" onClick={() => form.reset()} disabled={saving}>
              Reset
            </Button>
            <Button type="submit" disabled={saving}>
              {saving ? 'Saving...' : isNewOrganization ? 'Create Organisation' : 'Save Changes'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
};

