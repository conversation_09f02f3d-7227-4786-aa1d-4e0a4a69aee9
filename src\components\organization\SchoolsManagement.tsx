import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from '@/hooks/use-toast';
import { Search, School, Plus, Users, Edit, Trash2 } from 'lucide-react';
import { Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

// School type definition
type School = {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  organization_type: 'school';
  parent_organization_id: string;
  user_count: number;
  created_at: string;
};

const SchoolsManagement = () => {
  // This component is for Multi-Academy Trusts to manage their member schools
  // It allows adding new schools, editing school details, and removing schools
  const { organizationId } = useAuth();
  const [schools, setSchools] = useState<School[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [newSchoolName, setNewSchoolName] = useState('');
  const [newSchoolAddress, setNewSchoolAddress] = useState('');
  const [newSchoolCity, setNewSchoolCity] = useState('');
  const [newSchoolState, setNewSchoolState] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Fetch schools from the database
  const fetchSchools = async () => {
    try {
      setLoading(true);

      // Fetch schools from the database where parent_organization_id matches the current organization
      const { data, error } = await supabase
        .from('organizations')
        .select(`
          id,
          name,
          organization_type,
          parent_organization_id,
          created_at
        `)
        .eq('organization_type', 'school')
        .eq('parent_organization_id', organizationId);

      if (error) throw error;

      if (data) {
        // Transform the data to match our School type
        const schoolsData: School[] = data.map((school: any) => ({
          id: school.id,
          name: school.name,
          address: '',
          city: '',
          state: '',
          zip: '',
          organization_type: school.organization_type,
          parent_organization_id: school.parent_organization_id,
          user_count: 0, // We'll implement user count later
          created_at: school.created_at
        }));

        setSchools(schoolsData);
      }
    } catch (error: any) {
      console.error('Error fetching schools:', error.message);
      toast({
        title: 'Error fetching schools',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (organizationId) {
      fetchSchools();
    }
  }, [organizationId]);

  const handleAddSchool = async () => {
    if (!newSchoolName.trim()) {
      toast({
        title: 'Error',
        description: 'School name is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsProcessing(true);

      // Create a new school in the database with the parent_organization_id set to the current organization (trust) ID
      const { data, error } = await supabase
        .from('organizations')
        .insert({
          name: newSchoolName,
          organization_type: 'school',
          parent_organization_id: organizationId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      if (data) {
        // Add the new school to the list
        const newSchool: School = {
          id: data.id,
          name: data.name,
          address: '',
          city: '',
          state: '',
          zip: '',
          organization_type: 'school',
          parent_organization_id: organizationId,
          user_count: 0,
          created_at: data.created_at
        };

        setSchools([...schools, newSchool]);

        // Reset form
        setNewSchoolName('');
        setNewSchoolAddress('');
        setNewSchoolCity('');
        setNewSchoolState('');
        setAddDialogOpen(false);

        toast({
          title: 'Success',
          description: 'School added successfully',
        });
      }

      setIsProcessing(false);
    } catch (error: any) {
      console.error('Error adding school:', error.message);
      toast({
        title: 'Error adding school',
        description: error.message,
        variant: 'destructive',
      });
      setIsProcessing(false);
    }
  };

  const handleDeleteSchool = async (schoolId: string) => {
    if (!confirm('Are you sure you want to delete this school? This action cannot be undone.')) {
      return;
    }

    try {
      setIsProcessing(true);

      // Delete the school from the database
      const { error } = await supabase
        .from('organizations')
        .delete()
        .eq('id', schoolId);

      if (error) throw error;

      // Update the UI
      setSchools(schools.filter(school => school.id !== schoolId));

      toast({
        title: 'Success',
        description: 'School deleted successfully',
      });
    } catch (error: any) {
      console.error('Error deleting school:', error.message);
      toast({
        title: 'Error deleting school',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const filteredSchools = schools.filter(school =>
    school.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    school.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    school.state?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search schools..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add School
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add a New School</DialogTitle>
              <DialogDescription>
                Add a new school to your Multi-Academy Trust
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <label htmlFor="name">School Name*</label>
                <Input
                  id="name"
                  placeholder="Enter school name"
                  value={newSchoolName}
                  onChange={(e) => setNewSchoolName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="address">Address</label>
                <Input
                  id="address"
                  placeholder="Enter address"
                  value={newSchoolAddress}
                  onChange={(e) => setNewSchoolAddress(e.target.value)}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="city">City</label>
                  <Input
                    id="city"
                    placeholder="City"
                    value={newSchoolCity}
                    onChange={(e) => setNewSchoolCity(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="state">State</label>
                  <Input
                    id="state"
                    placeholder="State"
                    value={newSchoolState}
                    onChange={(e) => setNewSchoolState(e.target.value)}
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddSchool} disabled={isProcessing}>
                {isProcessing ? 'Adding...' : 'Add School'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Members</TableHead>
                <TableHead>Added</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSchools.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-4">
                    No schools found
                  </TableCell>
                </TableRow>
              ) : (
                filteredSchools.map((school) => (
                  <TableRow key={school.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <School className="h-4 w-4 mr-2 text-muted-foreground" />
                        {school.name}
                      </div>
                    </TableCell>
                    <TableCell>{`${school.city || ''}, ${school.state || ''}`}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className="flex items-center w-fit gap-1">
                        <Users className="h-3 w-3" />
                        {school.user_count}
                      </Badge>
                    </TableCell>
                    <TableCell>{new Date(school.created_at).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteSchool(school.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};

export default SchoolsManagement;