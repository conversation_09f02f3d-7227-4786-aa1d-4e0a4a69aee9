import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { organizationService } from '@/services/organizationService';
import { supabase } from '@/integrations/supabase/client';
import { supabaseAdmin } from '@/services/supabaseAdmin';
import { OrganizationUser, UserInvitation, UserRole } from '@/types/organization';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/hooks/use-toast';
import { Loader2, UserPlus, Upload, X, Edit2, UserX } from 'lucide-react';

export function UserManagementForm() {
  const { organizationId, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [users, setUsers] = useState<OrganizationUser[]>([]);
  const [invitations, setInvitations] = useState<UserInvitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<OrganizationUser | null>(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<UserRole>('teacher');
  const [editRole, setEditRole] = useState<UserRole>('teacher');
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (organizationId) {
      // Clean up all metadata invitations first
      organizationService.cleanupAllMetadataInvitations()
        .then(() => {
          if (process.env.NODE_ENV === 'development') {
    console.log('Successfully cleaned up all metadata invitations');
  }
          // Then fetch organization data
          fetchOrganizationData();
        })
        .catch(error => {
          console.error('Error cleaning up metadata invitations:', error);
          // Still fetch organization data even if cleanup fails
          fetchOrganizationData();
        });

      // Set up an interval to refresh data every 5 seconds
      const intervalId = setInterval(() => {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
    console.log('Auto-refreshing organization data...');
  }
        }
        fetchOrganizationData(false); // Don't show loading state for auto-refresh
      }, 5000);

      // Clean up interval on unmount
      return () => clearInterval(intervalId);
    } else {
      setIsLoading(false);
    }
  }, [organizationId]);

  const fetchOrganizationData = async (showLoading = true) => {
    if (!organizationId) return;

    if (process.env.NODE_ENV === 'development') {


      console.log('DEBUG UserManagement: Fetching organization data for: completed');


      }
    if (showLoading) {
      setIsLoading(true);
    }
    try {
      // Fetch users
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG UserManagement: Fetching organization users...');

          }
      }
      const usersData = await organizationService.getOrganizationUsers(organizationId);
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG UserManagement: Received users data: completed');

          }
      }

      // Fetch invitations
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG UserManagement: Fetching organization invitations...');

          }
      }
      const invitationsData = await organizationService.getOrganizationInvitations(organizationId);
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG UserManagement: Received invitations data count: completed');

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG UserManagement: Invitations details:', JSON.stringify(invitationsData, null, 2.replace(/user.*/, 'hasUser: ' + !!user)));

          }
      }

      // Update state
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG UserManagement: Setting users state with: completed');

          }
      }
      setUsers(usersData);
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG UserManagement: Setting invitations state with: completed');

          }
      }
      setInvitations(invitationsData);
    } catch (error) {
      console.error('Error fetching organization data:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load organization data',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInviteUser = async () => {
    if (!organizationId) return;

    setIsProcessing(true);
    try {
      await organizationService.inviteUser(organizationId, {
        email: inviteEmail,
        role: inviteRole
      });

      toast({
        title: 'Invitation sent',
        description: `An invitation has been sent to ${inviteEmail}`,
      });

      // Refresh invitations
      const invitationsData = await organizationService.getOrganizationInvitations(organizationId);
      setInvitations(invitationsData);

      // Reset form
      setInviteEmail('');
      setInviteRole('teacher');
      setInviteDialogOpen(false);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to send invitation',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    if (!organizationId) {
      console.error('No organizationId found');
      return;
    }

    try {
      // Show a confirmation dialog
      if (!confirm('Are you sure you want to cancel this invitation?')) {
        if (process.env.NODE_ENV === 'development') {

          console.log('User cancelled the confirmation dialog');

          }
        return;
      }

      // SECURITY: Don't log tokens in production
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('Cancelling invitation with ID/token:', invitationId);
  }
      }
      const invitationToCancel = invitations.find(inv => inv.id === invitationId || inv.token === invitationId);
      if (process.env.NODE_ENV === 'development') {
    console.log('Invitation object:', invitationToCancel);
  }
      if (!invitationToCancel) {
        console.error('Could not find invitation with ID/token:', invitationId);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Could not find invitation to cancel',
        });
        return;
      }

      // Set a loading state
      setIsProcessing(true);

      // Optimistically update the UI by removing the invitation from the list
      if (process.env.NODE_ENV === 'development') {
    console.log('Optimistically updating UI by removing invitation');
  }
      setInvitations(prevInvitations => {
        if (process.env.NODE_ENV === 'development') {
    console.log('Previous invitations:', prevInvitations);
  }
        const filtered = prevInvitations.filter(inv => inv.id !== invitationToCancel.id);
        if (process.env.NODE_ENV === 'development') {
    console.log('Filtered invitations:', filtered);
  }
        return filtered;
      });

      // Call the service to cancel the invitation
      try {
        if (process.env.NODE_ENV === 'development') {
    console.log('Calling organizationService.cancelInvitation with:', invitationId);
  }
        // SECURITY: Don't log tokens in production
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
    console.log('Using token from invitation:', invitationToCancel.token);
  }
        }

        // DIRECT APPROACH: Try to delete directly from Supabase using admin client
        const { data: userData } = await supabase.auth.getUser();
        if (process.env.NODE_ENV === 'development') {

          console.log('Current user: completed');


          }
        if (process.env.NODE_ENV === 'development') {
    console.log('Directly deleting invitation by token using Supabase ADMIN client');
  }
        const { data: deleteData, error: deleteError } = await supabaseAdmin
          .from('user_invitations')
          .delete()
          .eq('token', invitationToCancel.token)
          .select();

        if (deleteError) {
          console.error('Error directly deleting invitation:', deleteError);
          throw new Error(`Failed to delete invitation: ${deleteError.message}`);
        }

        // SECURITY: Don't log delete results that might contain tokens in production
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
    console.log('Delete result:', deleteData);
  }
        } else {
          if (process.env.NODE_ENV === 'development') {
    console.log('Delete result count:', deleteData?.length || 0);
  }
        }

        if (deleteData && deleteData.length > 0) {
          if (process.env.NODE_ENV === 'development') {
    console.log('Successfully deleted invitation directly');
  }
        } else {
          if (process.env.NODE_ENV === 'development') {
    console.log('No data returned from delete operation, trying organizationService');
  }
          await organizationService.cancelInvitation(invitationToCancel.token);
        }

        if (process.env.NODE_ENV === 'development') {
    console.log('Successfully called cancelInvitation');
  }
      } catch (cancelError) {
        console.error('Error in cancelInvitation call:', cancelError);
        // Revert the optimistic update if there was an error
        if (process.env.NODE_ENV === 'development') {
    console.log('Reverting optimistic update due to error');
  }
        setInvitations(prevInvitations => [...prevInvitations, invitationToCancel]);
        throw cancelError;
      }

      toast({
        title: 'Invitation cancelled',
        description: 'The invitation has been cancelled',
      });

      // Refresh data immediately
      try {
        if (process.env.NODE_ENV === 'development') {
    console.log('Refreshing data after cancellation');
  }
        await fetchOrganizationData(false);
      } catch (refreshError) {
        console.error('Error refreshing data:', refreshError);
        // Force a page reload as a last resort
        window.location.reload();
      }
    } catch (error: any) {
      console.error('Error cancelling invitation:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to cancel invitation',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleUpdateUserRole = async () => {
    if (!organizationId || !selectedUser) return;

    setIsProcessing(true);
    try {
      await organizationService.updateUserRole(selectedUser.user_id, editRole);

      toast({
        title: 'Role updated',
        description: `User role has been updated to ${editRole}`,
      });

      // Refresh users
      const usersData = await organizationService.getOrganizationUsers(organizationId);
      setUsers(usersData);

      // Reset form
      setSelectedUser(null);
      setEditDialogOpen(false);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to update user role',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRemoveUser = async (userId: string) => {
    if (!organizationId) return;

    if (!confirm('Are you sure you want to remove this user from the organization?')) {
      return;
    }

    try {
      await organizationService.removeUserFromOrganization(userId);

      toast({
        title: 'User removed',
        description: 'The user has been removed from the organization',
      });

      // Refresh users
      const usersData = await organizationService.getOrganizationUsers(organizationId);
      setUsers(usersData);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to remove user',
      });
    }
  };

  const handleUploadCSV = async () => {
    if (!organizationId || !csvFile) return;

    setIsProcessing(true);
    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const text = e.target?.result as string;
        const rows = text.split('\n');

        // Skip header row
        const emailsToInvite = rows.slice(1)
          .map(row => row.split(',')[0]?.trim())
          .filter(email => email && email.includes('@'));

        if (emailsToInvite.length === 0) {
          toast({
            variant: 'destructive',
            title: 'Error',
            description: 'No valid email addresses found in the CSV file',
          });
          setIsProcessing(false);
          return;
        }

        // Invite each user
        const invitePromises = emailsToInvite.map(email =>
          organizationService.inviteUser(organizationId!, {
            email,
            role: 'teacher' // Default role for CSV imports
          })
        );

        await Promise.all(invitePromises);

        toast({
          title: 'Invitations sent',
          description: `${emailsToInvite.length} invitations have been sent`,
        });

        // Refresh invitations
        const invitationsData = await organizationService.getOrganizationInvitations(organizationId);
        setInvitations(invitationsData);

        // Reset form
        setCsvFile(null);
      };

      reader.readAsText(csvFile);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to process CSV file',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (!organizationId) {
    return (
      <div className="text-center py-4">
        You need to create or join an organization first.
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="text-center py-4">
        You don't have permission to manage users.
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-6">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <div>
      <Tabs defaultValue="users" onValueChange={(value) => {
        if (value === 'invitations' && organizationId) {
          // Refresh invitations when tab is selected
          if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {
    console.log('Refreshing invitations on tab change');
  }
          }
          organizationService.getOrganizationInvitations(organizationId)
            .then(data => {
              if (process.env.NODE_ENV === 'development') {
                if (process.env.NODE_ENV === 'development') {
    console.log('Refreshed invitations: completed');
  }
              }
              setInvitations(data);
            })
            .catch(error => console.error('Error refreshing invitations:', error));
        }
      }}>
        <TabsList className="mb-4">
          <TabsTrigger value="users">Users ({users.length})</TabsTrigger>
          <TabsTrigger value="invitations">Invitations ({invitations.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <div className="mb-4 flex flex-wrap gap-2">
            <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Invite User
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Invite a User</DialogTitle>
                  <DialogDescription>
                    Send an invitation to join your organization
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <label htmlFor="email">Email Address</label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={inviteEmail}
                      onChange={(e) => setInviteEmail(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="role">Role</label>
                    <Select value={inviteRole} onValueChange={(value) => setInviteRole(value as UserRole)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="teacher">Teacher</SelectItem>
                        <SelectItem value="support">Support Staff</SelectItem>
                        <SelectItem value="maintenance">Maintenance Worker</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setInviteDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleInviteUser} disabled={isProcessing}>
                    {isProcessing ? 'Sending...' : 'Send Invitation'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload CSV
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Upload User List</DialogTitle>
                  <DialogDescription>
                    Upload a CSV file with email addresses to invite multiple users
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <label htmlFor="csv">CSV File (with email column)</label>
                    <Input
                      id="csv"
                      type="file"
                      accept=".csv"
                      onChange={(e) => setCsvFile(e.target.files?.[0] || null)}
                    />
                    <p className="text-sm text-gray-500">
                      The CSV should have a header row and at least one column with email addresses
                    </p>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setCsvFile(null)}>
                    Cancel
                  </Button>
                  <Button onClick={handleUploadCSV} disabled={!csvFile || isProcessing}>
                    {isProcessing ? 'Processing...' : 'Upload and Invite'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Joined</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-4">
                    No users found for organization ID: {organizationId}
                    <div className="mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fetchOrganizationData()}
                      >
                        Refresh Users
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                users.map((user) => (
                  <TableRow key={`${user.user_id}-${user.email}`}>
                    <TableCell>
                      {user.first_name} {user.last_name}
                    </TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <span className="capitalize">{user.role}</span>
                    </TableCell>
                    <TableCell>
                      {new Date(user.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right">
                      <Dialog open={editDialogOpen && selectedUser?.user_id === user.user_id}
                              onOpenChange={(open) => {
                                setEditDialogOpen(open);
                                if (!open) setSelectedUser(null);
                              }}>
                        <DialogTrigger asChild>
                          <Button variant="ghost" size="sm" onClick={() => {
                            setSelectedUser(user);
                            setEditRole(user.role as UserRole);
                          }}>
                            <Edit2 className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Edit User Role</DialogTitle>
                            <DialogDescription>
                              Change the role for {user.first_name} {user.last_name}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4 py-4">
                            <div className="space-y-2">
                              <label htmlFor="edit-role">Role</label>
                              <Select value={editRole} onValueChange={(value) => setEditRole(value as UserRole)}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a role" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="admin">Admin</SelectItem>
                                  <SelectItem value="teacher">Teacher</SelectItem>
                                  <SelectItem value="support">Support Staff</SelectItem>
                                  <SelectItem value="maintenance">Maintenance Worker</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          <DialogFooter>
                            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
                              Cancel
                            </Button>
                            <Button onClick={handleUpdateUserRole} disabled={isProcessing}>
                              {isProcessing ? 'Updating...' : 'Update Role'}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>

                      <Button variant="ghost" size="sm" onClick={() => handleRemoveUser(user.user_id)}>
                        <UserX className="h-4 w-4 text-red-500" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TabsContent>

        <TabsContent value="invitations">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Sent</TableHead>
                <TableHead>Expires</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invitations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-4">
                    No invitations found
                  </TableCell>
                </TableRow>
              ) : (
                invitations.map((invitation) => (
                  <TableRow key={`${invitation.id}-${invitation.email}`}>
                    <TableCell>{invitation.email}</TableCell>
                    <TableCell>
                      <span className="capitalize">{invitation.role}</span>
                    </TableCell>
                    <TableCell>
                      <span className="capitalize">{invitation.status}</span>
                    </TableCell>
                    <TableCell>
                      {new Date(invitation.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      {new Date(invitation.expires_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right">
                      {invitation.status === 'pending' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            // SECURITY: Don't log invitation objects with tokens in production
                            if (process.env.NODE_ENV === 'development') {
                              if (process.env.NODE_ENV === 'development') {
    console.log('Cancel button clicked for invitation:', invitation);
  }
                              if (process.env.NODE_ENV === 'development') {
    console.log('Using token:', invitation.token);
  }
                            }
                            if (process.env.NODE_ENV === 'development') {
    console.log('Using id:', invitation.id);
  }
                            handleCancelInvitation(invitation.token || invitation.id);
                          }}
                        >
                          <X className="h-4 w-4 text-red-500" />
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default UserManagementForm;