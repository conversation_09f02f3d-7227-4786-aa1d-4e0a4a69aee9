import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Loader2, CheckCircle, School, Wrench, User, BookOpen, Briefcase } from 'lucide-react';

interface OrganizationDetails {
  name: string;
  organization_type: string;
}

const WelcomeToOrganization = () => {
  const { user, profile } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [organization, setOrganization] = useState<OrganizationDetails | null>(null);

  useEffect(() => {
    const fetchOrganizationDetails = async () => {
      if (!profile?.organization_id) {
        setIsLoading(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('organizations')
          .select('name, organization_type')
          .eq('id', profile.organization_id)
          .single();

        if (error) {
          console.error('Error fetching organization details:', error);
        } else {
          setOrganization(data);
        }
      } catch (error) {
        console.error('Error fetching organization details:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganizationDetails();
  }, [profile]);

  if (isLoading) {
    return (
      <Card className="max-w-3xl mx-auto">
        <CardContent className="p-6 flex justify-center items-center min-h-[300px]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </CardContent>
      </Card>
    );
  }

  if (!organization) {
    return (
      <Card className="max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>Organization Not Found</CardTitle>
          <CardDescription>
            We couldn't find your organization details. Please contact support.
          </CardDescription>
        </CardHeader>
        <CardFooter className="flex justify-center">
          <Button onClick={() => navigate('/dashboard')}>Go to Dashboard</Button>
        </CardFooter>
      </Card>
    );
  }

  // Get role-specific content
  const getRoleContent = () => {
    const role = profile?.role || 'unknown';

    switch (role) {
      case 'admin':
        return {
          icon: <School className="h-16 w-16 text-blue-500 mx-auto mb-4" />,
          title: 'Welcome, Administrator!',
          description: `As an administrator at ${organization.name}, you can manage users, create and assign tasks, and oversee all operations.`,
          actions: [
            { label: 'Manage Organization', path: '/organization/dashboard' },
            { label: 'Create a Task', path: '/tasks/create' }
          ]
        };
      case 'teacher':
        return {
          icon: <BookOpen className="h-16 w-16 text-green-500 mx-auto mb-4" />,
          title: 'Welcome, Teacher!',
          description: `As a teacher at ${organization.name}, you can create tasks for maintenance and support, and track their progress.`,
          actions: [
            { label: 'Create a Task', path: '/tasks/create' },
            { label: 'View My Tasks', path: '/dashboard' }
          ]
        };
      case 'maintenance':
        return {
          icon: <Wrench className="h-16 w-16 text-orange-500 mx-auto mb-4" />,
          title: 'Welcome, Maintenance Staff!',
          description: `As a maintenance staff member at ${organization.name}, you can view and manage tasks assigned to you.`,
          actions: [
            { label: 'View Assigned Tasks', path: '/tasks/assigned' },
            { label: 'Go to Dashboard', path: '/dashboard' }
          ]
        };
      case 'support':
        return {
          icon: <User className="h-16 w-16 text-purple-500 mx-auto mb-4" />,
          title: 'Welcome, Support Staff!',
          description: `As a support staff member at ${organization.name}, you can view and manage support tasks assigned to you.`,
          actions: [
            { label: 'View Assigned Tasks', path: '/tasks/assigned' },
            { label: 'Go to Dashboard', path: '/dashboard' }
          ]
        };
      case 'supplier':
        return {
          icon: <Briefcase className="h-16 w-16 text-yellow-500 mx-auto mb-4" />,
          title: 'Welcome, Supplier!',
          description: `As a supplier partnered with ${organization.name}, you can view available tasks, submit offers, and manage your assigned work.`,
          actions: [
            { label: 'Browse Available Tasks', path: '/tasks' },
            { label: 'View My Tasks', path: '/dashboard' }
          ]
        };
      default:
        return {
          icon: <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />,
          title: 'Welcome to Classtasker!',
          description: `You have successfully joined ${organization.name}. You can now start using Classtasker to manage tasks and services.`,
          actions: [
            { label: 'Go to Dashboard', path: '/dashboard' }
          ]
        };
    }
  };

  const roleContent = getRoleContent();
  const userRole = profile?.role || '';

  return (
    <Card className="max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="text-center">{roleContent.title}</CardTitle>
        <CardDescription className="text-center">
          You've successfully joined {organization.name}
        </CardDescription>
      </CardHeader>
      <CardContent className="p-6 text-center">
        {roleContent.icon}
        <p className="text-gray-600 mb-6">
          {roleContent.description}
        </p>
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="font-medium text-gray-800 mb-2">What you can do:</h3>
          <ul className="text-left list-disc list-inside space-y-2">
            {userRole === 'admin' && (
              <>
                <li>Manage users and invite new members</li>
                <li>Create and assign tasks to staff members</li>
                <li>Post tasks to the marketplace for suppliers</li>
                <li>Review and approve completed tasks</li>
                <li>Manage organization settings</li>
              </>
            )}
            {userRole === 'teacher' && (
              <>
                <li>Create maintenance and support requests</li>
                <li>Track the status of your requests</li>
                <li>Communicate with assigned staff</li>
                <li>Mark tasks as completed when resolved</li>
              </>
            )}
            {userRole === 'maintenance' && (
              <>
                <li>View tasks assigned to you</li>
                <li>Update task status as you work</li>
                <li>Communicate with task creators</li>
                <li>Mark tasks as completed when finished</li>
              </>
            )}
            {userRole === 'support' && (
              <>
                <li>View support requests assigned to you</li>
                <li>Respond to and resolve support issues</li>
                <li>Communicate with staff members</li>
                <li>Track your support performance</li>
              </>
            )}
            {userRole === 'supplier' && (
              <>
                <li>Browse available tasks in the marketplace</li>
                <li>Submit offers for tasks you can complete</li>
                <li>Communicate with organization admins</li>
                <li>Track your assigned tasks and payments</li>
              </>
            )}
            {!['admin', 'teacher', 'maintenance', 'support', 'supplier'].includes(userRole) && (
              <>
                <li>View your dashboard</li>
                <li>Communicate with team members</li>
                <li>Track tasks relevant to your role</li>
              </>
            )}
          </ul>
        </div>
      </CardContent>
      <CardFooter className="flex justify-center space-x-4">
        {roleContent.actions.map((action, index) => (
          <Button
            key={index}
            onClick={() => navigate(action.path)}
            variant={index === 0 ? "default" : "outline"}
          >
            {action.label}
          </Button>
        ))}
      </CardFooter>
    </Card>
  );
};

export default WelcomeToOrganization;
