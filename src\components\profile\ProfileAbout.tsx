
import { Card, CardContent } from '@/components/ui/card';
import { ProfileData } from '@/hooks/use-profile';
import { Briefcase, User, Clock, Calendar } from 'lucide-react';

interface ProfileAboutProps {
  profile: ProfileData;
  userTasks?: any[];
}

const ProfileAbout = ({ profile, userTasks }: ProfileAboutProps) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">About</h3>
            <p className="text-gray-700">{profile.bio || 'No bio available.'}</p>
          </div>

          {profile.job_title && (
            <div>
              <h3 className="text-lg font-semibold mb-2">Job Title</h3>
              <div className="flex items-center">
                <Briefcase size={18} className="text-gray-500 mr-2" />
                <span>{profile.job_title}</span>
              </div>
            </div>
          )}

          <div>
            <h3 className="text-lg font-semibold mb-2">Member Information</h3>
            <div className="space-y-2">
              <div className="flex items-center">
                <User size={18} className="text-gray-500 mr-2" />
                <span>Member since {new Date(profile.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'long' })}</span>
              </div>
              <div className="flex items-center">
                <Clock size={18} className="text-gray-500 mr-2" />
                <span>Typical response time: 2 hours</span>
              </div>
              <div className="flex items-center">
                <Calendar size={18} className="text-gray-500 mr-2" />
                <span>Posted {userTasks?.length || 0} tasks</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileAbout;
