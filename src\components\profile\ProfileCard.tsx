
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { MapPin, MessageSquare, Pencil, Mail } from 'lucide-react';
import { ProfileData } from '@/hooks/use-profile';
import { useAuth } from '@/contexts/AuthContext';

interface ProfileCardProps {
  profile: ProfileData;
  isOwnProfile: boolean;
  userTasks?: any[];
  userFullName: string;
  onEditProfile: () => void;
}

const ProfileCard = ({
  profile,
  isOwnProfile,
  userTasks,
  userFullName,
  onEditProfile
}: ProfileCardProps) => {
  const { user } = useAuth();

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col items-center text-center">
          <Avatar className="h-24 w-24 mb-4">
            {profile.avatar_url ? (
              <AvatarImage src={profile.avatar_url} alt={userFullName} />
            ) : (
              <AvatarFallback className="text-2xl bg-classtasker-blue text-white">
                {userFullName.charAt(0)}
              </AvatarFallback>
            )}
          </Avatar>
          <h2 className="text-xl font-bold">{userFullName}</h2>
          <p className="text-gray-600">{profile.job_title || (profile.account_type === 'school' ? 'School Staff' : 'Supplier')}</p>

          {/* Show email for own profile */}
          {isOwnProfile && user?.email && (
            <div className="flex items-center mt-2 text-sm text-gray-600">
              <Mail size={14} className="mr-1" />
              {user.email}
            </div>
          )}

          {profile.location && (
            <div className="flex items-center mt-2 text-sm text-gray-600">
              <MapPin size={14} className="mr-1" />
              {profile.location}
            </div>
          )}

          {isOwnProfile ? (
            <Button
              className="mt-4 w-full bg-classtasker-blue hover:bg-blue-600"
              size="sm"
              onClick={onEditProfile}
            >
              <Pencil size={16} className="mr-2" /> Edit Profile
            </Button>
          ) : (
            <Button
              className="mt-4 w-full bg-classtasker-blue hover:bg-blue-600"
              size="sm"
            >
              <MessageSquare size={16} className="mr-2" /> Contact
            </Button>
          )}
        </div>

        <div className="border-t border-gray-100 mt-6 pt-6">
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Member since</span>
              <span className="font-medium">{new Date(profile.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'long' })}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Tasks posted</span>
              <span className="font-medium">{userTasks?.length || 0}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Avg. response time</span>
              <span className="font-medium">2 hours</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileCard;
