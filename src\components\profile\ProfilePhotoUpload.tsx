import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Upload, X, Camera } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

interface ProfilePhotoUploadProps {
  currentPhotoUrl: string | null;
  userFullName: string;
  onPhotoUpdated: (url: string) => void;
}

const ProfilePhotoUpload: React.FC<ProfilePhotoUploadProps> = ({
  currentPhotoUrl,
  userFullName,
  onPhotoUpdated
}) => {
  const { user } = useAuth();
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentPhotoUrl);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !user) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        variant: 'destructive',
        title: 'Invalid file type',
        description: 'Please upload an image file (JPEG, PNG, etc.)'
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        variant: 'destructive',
        title: 'File too large',
        description: 'Please upload an image smaller than 5MB'
      });
      return;
    }

    setIsUploading(true);

    try {
      // Create a local preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Generate a unique file name that includes the user ID in the path
      // This is important for storage policies to work correctly
      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `avatars/${user.id}/${fileName}`;

      // Use a simpler approach - just try to upload directly
      // If the bucket doesn't exist, we'll handle that in the error handling

      // Upload to Supabase Storage
      const { error: uploadError, data } = await supabase.storage
        .from('profile-photos')
        .upload(filePath, file, {
          upsert: true,
          contentType: file.type
        });

      if (uploadError) {
        console.error('Upload error details:', uploadError);
        throw uploadError;
      }

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('profile-photos')
        .getPublicUrl(filePath);

      // Update the user's profile with the new avatar URL
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: publicUrl })
        .eq('id', user.id);

      if (updateError) throw updateError;

      // Call the callback with the new URL
      onPhotoUpdated(publicUrl);

      toast({
        title: 'Photo uploaded',
        description: 'Your profile photo has been updated successfully.'
      });
    } catch (error: any) {
      console.error('Error uploading photo:', error);

      // Provide more specific error messages based on the error type
      let errorMessage = 'Failed to upload profile photo';

      if (error.message) {
        errorMessage = error.message;
      }

      // Handle specific error cases
      if (error.statusCode === 400) {
        errorMessage = 'Storage error: The bucket may not exist or you may not have permission to upload.';
      } else if (error.statusCode === 413) {
        errorMessage = 'The file is too large. Please upload a smaller image.';
      } else if (error.statusCode === 401 || error.statusCode === 403) {
        errorMessage = 'You do not have permission to upload files.';
      }

      toast({
        variant: 'destructive',
        title: 'Upload failed',
        description: errorMessage
      });

      // Reset preview to original photo
      setPreviewUrl(currentPhotoUrl);
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemovePhoto = async () => {
    if (!user) return;

    try {
      // Update the user's profile to remove the avatar URL
      const { error } = await supabase
        .from('profiles')
        .update({ avatar_url: null })
        .eq('id', user.id);

      if (error) throw error;

      // Reset the preview
      setPreviewUrl(null);

      // Call the callback with empty string
      onPhotoUpdated('');

      toast({
        title: 'Photo removed',
        description: 'Your profile photo has been removed.'
      });
    } catch (error: any) {
      console.error('Error removing photo:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to remove profile photo'
      });
    }
  };

  return (
    <div className="flex flex-col items-center">
      <div className="relative group">
        <Avatar className="h-24 w-24 mb-2">
          {previewUrl ? (
            <AvatarImage src={previewUrl} alt={userFullName} />
          ) : (
            <AvatarFallback className="text-2xl bg-classtasker-blue text-white">
              {userFullName.charAt(0)}
            </AvatarFallback>
          )}

          <div
            className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
            onClick={() => fileInputRef.current?.click()}
          >
            <Camera className="h-6 w-6 text-white" />
          </div>
        </Avatar>

        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          accept="image/*"
          onChange={handleFileChange}
        />
      </div>

      <div className="flex space-x-2 mt-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
        >
          <Upload className="h-4 w-4 mr-1" />
          {isUploading ? 'Uploading...' : 'Upload'}
        </Button>

        {previewUrl && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleRemovePhoto}
            disabled={isUploading}
          >
            <X className="h-4 w-4 mr-1" />
            Remove
          </Button>
        )}
      </div>
    </div>
  );
};

export default ProfilePhotoUpload;
