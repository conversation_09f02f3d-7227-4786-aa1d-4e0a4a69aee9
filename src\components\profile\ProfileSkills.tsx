
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { ProfileData } from '@/hooks/use-profile';

interface ProfileSkillsProps {
  profile: ProfileData;
}

const ProfileSkills = ({ profile }: ProfileSkillsProps) => {
  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="font-semibold mb-3">Skills & Expertise</h3>
        <div className="flex flex-wrap gap-2">
          {profile.account_type === 'school' 
            ? ['Facilities Management', 'Project Management', 'Procurement'].map((skill) => (
                <Badge key={skill} variant="secondary">{skill}</Badge>
              ))
            : ['Painting', 'Electrical', 'Plumbing'].map((skill) => (
                <Badge key={skill} variant="secondary">{skill}</Badge>
              ))
          }
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileSkills;
