
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import TaskCard from '@/components/tasks/TaskCard';
import { ProfileData } from '@/hooks/use-profile';
import { Link } from 'react-router-dom';

interface ProfileTasksProps {
  profile: ProfileData;
  userTasks?: any[];
  isLoadingUserTasks: boolean;
  userFullName: string;
}

const ProfileTasks = ({ profile, userTasks, isLoadingUserTasks, userFullName }: ProfileTasksProps) => {
  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold mb-4">Tasks Posted by {userFullName}</h3>
        
        {isLoadingUserTasks ? (
          <div className="grid grid-cols-1 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="border rounded-lg p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-4" />
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-4 w-1/4" />
                </div>
              </div>
            ))}
          </div>
        ) : userTasks && userTasks.length > 0 ? (
          <div className="grid grid-cols-1 gap-6">
            {userTasks.map(task => (
              <Link to={`/tasks/${task.id}`} key={task.id} className="block">
                <TaskCard 
                  id={task.id}
                  title={task.title}
                  description={task.description}
                  location={task.location}
                  dueDate={task.due_date}
                  budget={task.budget}
                  category={task.category}
                  status={task.status}
                  offers={task.offers_count}
                />
              </Link>
            ))}
          </div>
        ) : (
          <p className="text-center text-gray-500 py-8">
            No tasks have been posted by this user yet.
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default ProfileTasks;
