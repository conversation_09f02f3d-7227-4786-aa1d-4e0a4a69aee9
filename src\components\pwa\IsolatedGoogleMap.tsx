import React, { useEffect, useRef, useState } from 'react';
import { loadGoogleMapsApi, isGoogleMapsLoaded } from '@/utils/pwa-google-maps';
import { Loader2 } from 'lucide-react';

// Define the Task interface
interface Task {
  id: string;
  title: string;
  description: string;
  location: string;
  budget: number | string;
  location_lat?: number;
  location_lng?: number;
  [key: string]: any; // Allow other properties
}

interface IsolatedGoogleMapProps {
  tasks: Task[];
  visible: boolean;
  onTaskSelect: (taskId: string) => void;
}

/**
 * A completely isolated Google Maps component that prevents <PERSON>act from
 * directly managing the DOM elements used by Google Maps
 */
const IsolatedGoogleMap: React.FC<IsolatedGoogleMapProps> = ({
  tasks,
  visible,
  onTaskSelect
}) => {
  // Create refs to hold DOM elements and map objects
  const containerRef = useRef<HTMLDivElement>(null);
  const mapContainerRef = useRef<HTMLDivElement | null>(null);
  const mapInstanceRef = useRef<any>(null);
  const markersRef = useRef<any[]>([]);
  const infoWindowRef = useRef<any>(null);
  const boundsRef = useRef<any>(null);
  
  // State for loading and errors
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mapInitialized, setMapInitialized] = useState(false);
  
  // Create the map container on mount
  useEffect(() => {
    console.log('[IsolatedGoogleMap] Component mounted');
    
    // Create a div that will hold the map
    // This div will be managed manually, not by React
    if (!mapContainerRef.current && containerRef.current) {
      const mapDiv = document.createElement('div');
      mapDiv.style.width = '100%';
      mapDiv.style.height = '100%';
      mapDiv.id = `google-map-container-${Date.now()}`;
      
      // Append to the container ref
      containerRef.current.appendChild(mapDiv);
      mapContainerRef.current = mapDiv;
      
      console.log('[IsolatedGoogleMap] Created map container div');
    }
    
    // Cleanup function
    return () => {
      console.log('[IsolatedGoogleMap] Component unmounting');
      
      // Clean up markers
      if (markersRef.current.length > 0) {
        console.log('[IsolatedGoogleMap] Cleaning up markers');
        markersRef.current.forEach(marker => {
          try {
            if (marker && typeof marker.setMap === 'function') {
              // Remove event listeners
              if (window.google && window.google.maps) {
                window.google.maps.event.clearInstanceListeners(marker);
              }
              marker.setMap(null);
            }
          } catch (error) {
            console.error('[IsolatedGoogleMap] Error cleaning up marker:', error);
          }
        });
        markersRef.current = [];
      }
      
      // Close info window
      try {
        if (infoWindowRef.current && typeof infoWindowRef.current.close === 'function') {
          infoWindowRef.current.close();
          infoWindowRef.current = null;
        }
      } catch (error) {
        console.error('[IsolatedGoogleMap] Error closing info window:', error);
      }
      
      // Clear map instance
      mapInstanceRef.current = null;
      boundsRef.current = null;
      
      // Remove the map container div
      // This is important to prevent memory leaks
      if (mapContainerRef.current && containerRef.current) {
        try {
          // Check if the element is still a child before removing
          if (containerRef.current.contains(mapContainerRef.current)) {
            containerRef.current.removeChild(mapContainerRef.current);
          }
          mapContainerRef.current = null;
        } catch (error) {
          console.error('[IsolatedGoogleMap] Error removing map container:', error);
        }
      }
    };
  }, []);
  
  // Initialize the map when visible changes
  useEffect(() => {
    // Only initialize if component is visible and container exists
    if (!visible || !mapContainerRef.current) {
      return;
    }
    
    // If map is already initialized, just trigger resize
    if (mapInitialized && mapInstanceRef.current) {
      console.log('[IsolatedGoogleMap] Map already initialized, triggering resize');
      try {
        if (window.google && window.google.maps) {
          window.google.maps.event.trigger(mapInstanceRef.current, 'resize');
        }
      } catch (error) {
        console.error('[IsolatedGoogleMap] Error triggering resize:', error);
      }
      return;
    }
    
    console.log('[IsolatedGoogleMap] Initializing map');
    setLoading(true);
    setError(null);
    
    // Function to initialize the map
    const initializeMap = () => {
      if (!mapContainerRef.current || !isGoogleMapsLoaded()) {
        console.error('[IsolatedGoogleMap] Cannot initialize map - container or API not ready');
        setError('Failed to initialize map');
        setLoading(false);
        return;
      }
      
      try {
        console.log('[IsolatedGoogleMap] Creating map instance');
        
        // Default center (UK)
        const defaultCenter = { lat: 54.093409, lng: -2.89479 };
        
        // Create map instance
        const mapInstance = new window.google.maps.Map(mapContainerRef.current, {
          center: defaultCenter,
          zoom: 6,
          mapTypeControl: false,
          streetViewControl: false,
          fullscreenControl: false,
        });
        
        // Create info window
        const infoWindow = new window.google.maps.InfoWindow();
        
        // Create bounds object
        const bounds = new window.google.maps.LatLngBounds();
        
        // Store references
        mapInstanceRef.current = mapInstance;
        infoWindowRef.current = infoWindow;
        boundsRef.current = bounds;
        
        console.log('[IsolatedGoogleMap] Map created successfully');
        setMapInitialized(true);
        setLoading(false);
        
        // Add markers
        updateMarkers();
      } catch (error) {
        console.error('[IsolatedGoogleMap] Error initializing map:', error);
        setError('Failed to initialize map');
        setLoading(false);
      }
    };
    
    // Check if the API is already loaded
    if (isGoogleMapsLoaded()) {
      initializeMap();
    } else {
      loadGoogleMapsApi(initializeMap);
    }
  }, [visible, mapInitialized, tasks]);
  
  // Update markers when tasks change
  const updateMarkers = () => {
    if (!mapInstanceRef.current || !infoWindowRef.current || !boundsRef.current || !isGoogleMapsLoaded()) {
      console.log('[IsolatedGoogleMap] Map not ready for markers');
      return;
    }
    
    console.log('[IsolatedGoogleMap] Updating markers for', tasks.length, 'tasks');
    
    try {
      // Clear existing markers
      markersRef.current.forEach(marker => {
        try {
          if (marker && typeof marker.setMap === 'function') {
            if (window.google && window.google.maps) {
              window.google.maps.event.clearInstanceListeners(marker);
            }
            marker.setMap(null);
          }
        } catch (error) {
          console.error('[IsolatedGoogleMap] Error clearing marker:', error);
        }
      });
      markersRef.current = [];
      
      // Reset bounds
      boundsRef.current = new window.google.maps.LatLngBounds();
      
      // Create new markers
      let hasValidLocations = false;
      
      tasks.forEach(task => {
        // Skip tasks without location data
        if (!task.location_lat || !task.location_lng) {
          console.log(`[IsolatedGoogleMap] Task ${task.id} has no location data, skipping`);
          return;
        }
        
        const position = {
          lat: task.location_lat,
          lng: task.location_lng
        };
        
        console.log(`[IsolatedGoogleMap] Adding marker for task ${task.id} at ${position.lat}, ${position.lng}`);
        
        try {
          // Create marker
          const marker = new window.google.maps.Marker({
            position,
            map: mapInstanceRef.current,
            title: task.title,
            animation: window.google.maps.Animation.DROP
          });
          
          // Add click listener
          marker.addListener('click', () => {
            // Format budget for display
            const budget = typeof task.budget === 'number'
              ? task.budget.toFixed(2)
              : parseFloat(task.budget as string).toFixed(2);
            
            // Set info window content
            infoWindowRef.current.setContent(`
              <div style="max-width: 200px;">
                <h3 style="font-weight: bold; margin-bottom: 5px;">${task.title}</h3>
                <p style="margin-bottom: 5px;">${task.location}</p>
                <p style="font-weight: bold; color: green;">£${budget}</p>
                <button id="view-task-${task.id}" style="background-color: #3b82f6; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">View Details</button>
              </div>
            `);
            
            // Open info window
            infoWindowRef.current.open(mapInstanceRef.current, marker);
            
            // Add event listener to the button after the info window is opened
            setTimeout(() => {
              const button = document.getElementById(`view-task-${task.id}`);
              if (button) {
                button.addEventListener('click', () => {
                  onTaskSelect(task.id);
                });
              }
            }, 10);
          });
          
          // Add to markers array
          markersRef.current.push(marker);
          
          // Extend bounds
          boundsRef.current.extend(position);
          hasValidLocations = true;
        } catch (error) {
          console.error(`[IsolatedGoogleMap] Error creating marker for task ${task.id}:`, error);
        }
      });
      
      // Fit bounds if we have valid locations
      if (hasValidLocations && markersRef.current.length > 0 && mapInstanceRef.current) {
        console.log(`[IsolatedGoogleMap] Fitting bounds to ${markersRef.current.length} markers`);
        mapInstanceRef.current.fitBounds(boundsRef.current);
        
        // Don't zoom in too far
        const listener = window.google.maps.event.addListener(mapInstanceRef.current, 'idle', () => {
          if (mapInstanceRef.current && mapInstanceRef.current.getZoom() > 16) {
            mapInstanceRef.current.setZoom(16);
          }
          window.google.maps.event.removeListener(listener);
        });
      }
    } catch (error) {
      console.error('[IsolatedGoogleMap] Error updating markers:', error);
    }
  };
  
  return (
    <div 
      ref={containerRef}
      className="w-full h-[400px] rounded-md border relative"
      style={{ minHeight: '400px' }}
    >
      {loading && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-100 bg-opacity-80 rounded-md z-10">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400 mb-2" />
          <p className="text-sm text-gray-500">Loading Google Maps...</p>
        </div>
      )}
      
      {error && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-100 bg-opacity-80 rounded-md z-10">
          <p className="text-red-500 mb-2">{error}</p>
          <button 
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => window.location.reload()}
          >
            Reload
          </button>
        </div>
      )}
    </div>
  );
};

export default IsolatedGoogleMap;
