import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle, ArrowRight } from 'lucide-react';

interface PWAAdminReviewStatusProps {
  className?: string;
}

/**
 * A component to display when a task is pending admin review in the PWA interface
 */
const PWAAdminReviewStatus: React.FC<PWAAdminReviewStatusProps> = ({ className }) => {
  return (
    <Card className={`border-purple-200 bg-purple-50 ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-purple-600 mr-3 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-medium text-purple-800 mb-1">Awaiting Review</h3>
            <p className="text-sm text-purple-700 mb-2">
              This task has been submitted and is awaiting review by a school administrator.
            </p>
            <div className="text-xs text-purple-600 flex items-center">
              <span className="mr-1">Next:</span>
              <ArrowRight className="h-3 w-3 mx-1" />
              <span>Admin will review and decide how to proceed</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PWAAdminReviewStatus;
