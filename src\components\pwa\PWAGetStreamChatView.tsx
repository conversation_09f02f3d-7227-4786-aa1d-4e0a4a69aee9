import React, { useState, useEffect, Component, ErrorInfo, ReactNode } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  ArrowLeft,
  Info,
  WifiOff,
  RefreshCw,
  AlertCircle,
} from 'lucide-react';
import { useGetStreamChat } from '@/hooks/use-getstream-chat';
import {
  isOnline,
  registerConnectivityListeners
} from '@/utils/pwa-utils';
import PWAMobileLayout from './PWAMobileLayout';
import { supabase } from '@/integrations/supabase/client';
import PWASimplifiedChat from './PWASimplifiedChat';

// Import only the essential GetStream CSS for better performance in PWA
import 'stream-chat-react/dist/css/v2/index.css';

// Import simplified custom CSS for mobile
import '@/styles/mobile-chat.css';

// Error boundary component to prevent the entire app from freezing
interface ChatErrorBoundaryProps {
  children: ReactNode;
  onRefresh?: () => void;
}

interface ChatErrorBoundaryState {
  hasError: boolean;
}

class ChatErrorBoundary extends Component<ChatErrorBoundaryProps, ChatErrorBoundaryState> {
  constructor(props: ChatErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): ChatErrorBoundaryState {
    return { hasError: true };
  }

  componentDidCatch(error: Error, info: ErrorInfo): void {
    console.error('[ChatErrorBoundary] Error in chat component:', error, info);
  }

  render(): ReactNode {
    if (this.state.hasError) {
      return (
        <div className="p-4 text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
          <p className="text-red-500">Chat failed to load</p>
          <Button
            variant="outline"
            className="mt-3"
            onClick={() => {
              this.setState({ hasError: false });
              // Use custom refresh handler if available, otherwise navigate back
              if (this.props.onRefresh) {
                this.props.onRefresh();
              } else {
                window.history.go(-1);
              }
            }}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reload
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}

// The CustomInput component is now defined inside the main component function
// to have access to component state and props

const PWAGetStreamChatView: React.FC = () => {
  // Get URL parameters
  const { channelId } = useParams<{ channelId: string }>();
  const [searchParams] = useSearchParams();
  const taskId = searchParams.get('task');
  const navigate = useNavigate();
  const { user } = useAuth();

  // Local state
  const [offlineMode, setOfflineMode] = useState(!isOnline());
  const [taskTitle, setTaskTitle] = useState<string>('Chat');
  const [taskStatus, setTaskStatus] = useState<string>('');
  const [error, setError] = useState<Error | string | null>(null);
  const [isStreamSlow, setIsStreamSlow] = useState(false);

  // Use the GetStream chat hook
  if (process.env.NODE_ENV === 'development') {

    console.log('[PWAGetStreamChatView] Initializing with params:', { taskId, channelId });


    }
  const {
    client,
    channel,
    messages,
    isLoading,
    isSending,
    sendMessage,
    error: streamError
  } = useGetStreamChat({
    taskId: taskId || '',
    threadId: channelId
  });

  // Enhanced debug logging (development only)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log('[PWAGetStreamChatView] Chat state:', {
        client: !!client,
        channel: !!channel,
        channelId: channel?.id,
        channelType: channel?.type,
        channelData: channel?.data,
        isLoading,
        hasError: !!streamError,
        streamErrorMessage: streamError?.message,
        isPWA: typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches,
        isOnline: navigator.onLine,
        messages: messages?.length || 0,
        userAgent: navigator.userAgent,
        url: window.location.href,
        apiKey: import.meta.env.VITE_GETSTREAM_API_KEY,
        apiBaseUrl: typeof window !== 'undefined' ? window.location.origin : '',
        taskId,
        channelIdParam: channelId
      });

        }
    }

    // Log more details about the channel if available (development only)
    if (process.env.NODE_ENV === 'development') {
      if (channel) {
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] Channel details:', {
          id: channel.id,
          type: channel.type,
          cid: channel.cid,
          data: channel.data,
          state: {
            members: Object.keys(channel.state?.members || {}),
            messages: channel.state?.messages?.length || 0,
            watchers: Object.keys(channel.state?.watchers || {})
          }
        });

          }
      } else if (!isLoading) {
        console.error('[PWAGetStreamChatView] Channel is null but not loading');
      }

      // Log client details if available
      if (client) {
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] Client details:', {
          userID: client.userID,
          connectionID: client.connectionID,
          isConnected: typeof client.isConnected === 'function' ? client.isConnected(.replace(/user.*/, 'hasUser: ' + !!user)) : !!client.wsConnection,
          activeChannels: client.activeChannels ? Object.keys(client.activeChannels).length : 0,
          apiKey: client.key,
          baseURL: client.baseURL
        });

          }
      } else if (!isLoading) {
        console.error('[PWAGetStreamChatView] Client is null but not loading');
      }

      // Log messages if available
      if (messages && messages.length > 0) {
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] First message:', {
          id: messages[0].id,
          text: messages[0].text?.substring(0, 50),
          user: messages[0].user?.id
        });

          }
      }

      // Log environment variables
      if (process.env.NODE_ENV === 'development') {

        console.log('[PWAGetStreamChatView] Environment variables:', {
        VITE_GETSTREAM_API_KEY: import.meta.env.VITE_GETSTREAM_API_KEY ? 'present' : 'missing'
      });

        }
    }
  }, [client, channel, isLoading, streamError, messages, taskId, channelId]);

  // Enhanced error handling for stream errors
  useEffect(() => {
    if (streamError) {
      console.error('[PWAGetStreamChatView] Stream error:', streamError);

      // For errors, provide more specific error messages based on the error type
      if (streamError.message?.includes('token')) {
        console.error('[PWAGetStreamChatView] Token error detected');
        setError('Authentication error. Please try refreshing the page.');

        // Try to reconnect automatically after a token error
        const reconnectTimer = setTimeout(() => {
          if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {

              console.log('[PWAGetStreamChatView] Attempting to reconnect after token error');

              }
          }
          handleRefresh();
        }, 3000);

        return () => clearTimeout(reconnectTimer);
      } else if (streamError.message?.includes('network') || streamError.message?.includes('connection')) {
        setError('Network error. Please check your internet connection.');
      } else if (streamError.message?.includes('channel')) {
        setError('Error loading chat channel. The chat may not exist or you may not have access.');
      } else if (streamError.message?.includes('timeout')) {
        setError('Connection timeout. Please try again.');
      } else {
        setError(streamError.message || 'Failed to load chat');
      }
    } else {
      setError(null);
    }
  }, [streamError]);

  // Effect to handle online/offline status
  useEffect(() => {
    const cleanup = registerConnectivityListeners(
      // Online callback
      () => {
        setOfflineMode(false);
      },
      // Offline callback
      () => {
        setOfflineMode(true);
      }
    );

    return cleanup;
  }, []);

  // Removed slow loading timeout - chats load fast enough without it
  useEffect(() => {
    // Reset the slow state when loading completes
    if (!isLoading) {
      setIsStreamSlow(false);
    }
  }, [isLoading]);

  // Fetch task details or channel info
  useEffect(() => {
    const fetchDetails = async () => {
      // If we have a taskId, prioritize getting task details from the database
      if (taskId) {
        try {
          const { data, error } = await supabase
            .from('tasks')
            .select('title, status')
            .eq('id', taskId)
            .single();

          if (error) {
            console.error('[PWAGetStreamChatView] Error fetching task details:', error);
          } else if (data) {
            // Always use the task title from the database if available
            setTaskTitle(data.title || 'Chat');
            setTaskStatus(data.status || '');
            if (process.env.NODE_ENV === 'development') {
              if (process.env.NODE_ENV === 'development') {

                console.log('[PWAGetStreamChatView] Using task details:', { title: data.title, status: data.status });

                }
            }

            // If we have a channel, update its name to match the task title
            if (channel && data.title) {
              try {
                // Always update the channel name to match the task title
                // This ensures consistency across all views
                await channel.update({
                  name: data.title,
                  task_title: data.title // Add an explicit task_title field
                });
                if (process.env.NODE_ENV === 'development') {
                  if (process.env.NODE_ENV === 'development') {

                    console.log('[PWAGetStreamChatView] Updated channel name to match task title:', data.title);

                    }
                }

                // Also update the channel data locally to ensure immediate UI update
                channel.data.name = data.title;
                channel.data.task_title = data.title;
              } catch (updateError) {
                console.error('[PWAGetStreamChatView] Error updating channel name:', updateError);
              }
            }

            return; // Exit early since we have the task details
          }
        } catch (error) {
          console.error('[PWAGetStreamChatView] Error in fetchDetails:', error);
        }
      }

      // Fallback: If we couldn't get task details or don't have a taskId, use channel name
      if (channel) {
        setTaskTitle(channel.data?.name || 'Chat');
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {

            console.log('[PWAGetStreamChatView] Using channel name:', channel.data?.name);

            }
        }
      }
    };

    fetchDetails();
  }, [taskId, channel]);

  // Handle back button - improved for PWA
  const handleBack = () => {
    // Try to go back in history first
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      // Fallback to messages list if no history
      navigate('/messages');
    }
  };

  // Handle hardware back button for Android PWA
  useEffect(() => {
    const handlePopState = (event: PopStateEvent) => {
      // This handles the hardware back button on Android
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] Hardware back button pressed');

          }
      }
      handleBack();
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  // Debug: Add event listeners to catch any refresh triggers (development only)
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] ===== BEFORE UNLOAD EVENT =====');

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] Page is about to unload/refresh');

          }
      }
    };

    const handleVisibilityChange = () => {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] ===== VISIBILITY CHANGE =====');

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] Document visibility:', document.visibilityState);

          }
      }
    };

    const handleFocus = () => {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] ===== WINDOW FOCUS =====');

          }
      }
    };

    const handleBlur = () => {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] ===== WINDOW BLUR =====');

          }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, []);

  // Prevent pull-to-refresh with precise targeting (preserves smooth scrolling)
  useEffect(() => {
    let startY = 0;
    let startTime = 0;
    let isScrolling = false;

    const handleTouchStart = (e: TouchEvent) => {
      startY = e.touches[0].clientY;
      startTime = Date.now();
      isScrolling = false;
    };

    const handleTouchMove = (e: TouchEvent) => {
      const currentY = e.touches[0].clientY;
      const deltaY = currentY - startY;
      const deltaTime = Date.now() - startTime;

      // Detect if user is actively scrolling (fast movement)
      if (Math.abs(deltaY) > 5 && deltaTime < 100) {
        isScrolling = true;
      }

      // Only prevent if:
      // 1. We're at the very top of the page
      // 2. User is pulling down (positive deltaY)
      // 3. It's a significant pull (> 30px)
      // 4. User is NOT actively scrolling
      if (window.scrollY === 0 && deltaY > 30 && !isScrolling) {
        e.preventDefault();
      }
    };

    // Use passive: true for touchstart to not interfere with scrolling
    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchmove', handleTouchMove, { passive: false });

    return () => {
      document.removeEventListener('touchstart', handleTouchStart);
      document.removeEventListener('touchmove', handleTouchMove);
    };
  }, []);

  // Handle refresh - refresh GetStream data without navigation
  const handleRefresh = async () => {
    try {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] ===== REFRESH TRIGGERED =====');

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] Current URL:', window.location.href);

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('[PWAGetStreamChatView] Refreshing chat data without navigation');

          }
      }

      // Reset error state
      setError(null);
      setIsLoading(true);

      if (client && channel) {
        try {
          // Refresh the channel data from GetStream
          if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {

              console.log('[PWAGetStreamChatView] Refreshing channel data');

              }
          }
          await channel.query();

          // Refresh messages
          const messageResponse = await channel.query({
            messages: { limit: 50 }
          });

          if (messageResponse.messages) {
            setMessages(messageResponse.messages);
          }

          if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {

              console.log('[PWAGetStreamChatView] Channel refresh completed');

              }
          }
        } catch (streamError) {
          console.error('[PWAGetStreamChatView] Error refreshing GetStream data:', streamError);
          setError('Failed to refresh chat. Please try again.');
        }
      } else {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {

            console.log('[PWAGetStreamChatView] No client or channel available for refresh');

            }
        }
        setError('Chat connection lost. Please go back and try again.');
      }

    } catch (error) {
      console.error('[PWAGetStreamChatView] Error during refresh:', error);
      setError('Failed to refresh chat. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // We're now using the PWASimplifiedChat component instead of a custom input

  return (
    <PWAMobileLayout hideHeader={true}>
      {/* Full screen chat container */}
      <div className="flex flex-col h-screen bg-white" data-chat-container>
        {/* Header */}
        <div className="flex items-center p-3 border-b bg-white sticky top-0 z-10">
          <Button variant="ghost" size="icon" onClick={handleBack} className="mr-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex-1 min-w-0">
            <h1 className="text-lg font-semibold truncate">{taskTitle}</h1>
            {taskStatus && (
              <div className="text-xs text-gray-500">
                Status: <span className="capitalize">{taskStatus}</span>
              </div>
            )}
          </div>
          {taskId && (
            <Button variant="ghost" size="icon" onClick={() => navigate(`/tasks/${taskId}`)}>
              <Info className="h-5 w-5" />
            </Button>
          )}
        </div>

        {/* Offline warning */}
        {offlineMode && (
          <div className="bg-yellow-50 p-2 text-center text-sm text-yellow-700 flex items-center justify-center">
            <WifiOff className="h-4 w-4 mr-2" />
            You're offline. Messages will be sent when you're back online.
          </div>
        )}

        {/* Error message - only show persistent errors, not loading errors */}
        {error && !isLoading && (
          <div className="bg-red-50 border-red-200 border-b px-4 py-2 flex items-center justify-center">
            <AlertCircle className="h-4 w-4 mr-2 text-red-500" />
            <span className="text-xs text-red-700">
              {error instanceof Error ? error.message : error}
            </span>
            <Button variant="ghost" size="sm" onClick={handleRefresh} className="ml-2">
              <RefreshCw className="h-3 w-3 mr-1" />
              Retry
            </Button>
          </div>
        )}

        {/* Chat Content - takes up all available space */}
        <div className="flex-1 overflow-hidden">
          {isLoading ? (
            // Loading state
            <div className="flex flex-col h-full justify-center items-center p-4">
              <Skeleton className="h-8 w-8 rounded-full mb-2" />
              <Skeleton className="h-4 w-32" />

              {/* Slow loading fallback */}
              {isStreamSlow && (
                <div className="mt-8 p-4 border rounded-md bg-gray-50 w-full max-w-md">
                  <p className="text-center text-gray-500 mb-4">
                    Chat is taking longer than expected to load.
                  </p>
                  {taskId && (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => navigate(`/tasks/${taskId}`)}
                    >
                      View Task Details Instead
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    className="w-full mt-2"
                    onClick={() => navigate('/messages')}
                  >
                    Back to Messages
                  </Button>
                </div>
              )}
            </div>
          ) : !client || !channel ? (
            // Minimal loading state - no error messages during refresh
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MessageSquare className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">Loading chat...</p>
              </div>
            </div>
          ) : (
            // Use the simplified chat component for PWA
            <ChatErrorBoundary onRefresh={handleRefresh}>
              <PWASimplifiedChat
                client={client}
                channel={channel}
                messages={messages}
                onSendMessage={sendMessage}
                isSending={isSending}
                onRefresh={handleRefresh}
              />
            </ChatErrorBoundary>
          )}
        </div>
      </div>
    </PWAMobileLayout>
  );
};

export default PWAGetStreamChatView;
