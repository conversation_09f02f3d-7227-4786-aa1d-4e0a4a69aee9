import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Image, X, Upload, Loader2, Send } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { v4 as uuidv4 } from 'uuid';
import { useAuth } from '@/contexts/AuthContext';
import { Channel } from 'stream-chat';
import { createFileUploadMessage } from '@/utils/streamSystemMessages';

interface PWAImageUploaderProps {
  taskId: string;
  channel: Channel | null;
  onClose: () => void;
}

const PWAImageUploader = ({ taskId, channel, onClose }: PWAImageUploaderProps) => {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        variant: "destructive",
        title: "Invalid file type",
        description: "Please select an image file (JPEG, PNG, etc.)",
      });
      return;
    }

    // Check file size (limit to 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        variant: "destructive",
        title: "File too large",
        description: "Image must be less than 5MB",
      });
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = () => {
      setPreviewUrl(reader.result as string);
    };
    reader.readAsDataURL(file);

    setSelectedFile(file);
  };

  const handleUpload = async () => {
    if (!selectedFile || !user) return;

    setIsUploading(true);

    try {
      // Generate a unique filename
      const fileExt = selectedFile.name.split('.').pop();
      const fileName = `${taskId}/${uuidv4()}.${fileExt}`;
      const filePath = `task-images/${fileName}`;

      console.log('PWA: Uploading image to path:', filePath);

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from('attachments')
        .upload(filePath, selectedFile, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('PWA: Supabase storage upload error:', error);

        // Check for specific error types
        if (error.message.includes('row-level security')) {
          throw new Error('Permission denied: You do not have permission to upload images. This may be a configuration issue.');
        } else if (error.statusCode === '413') {
          throw new Error('File too large: The image exceeds the maximum allowed size.');
        } else if (error.statusCode === '400') {
          throw new Error('Invalid request: The file could not be processed. Please try a different image.');
        }

        throw error;
      }

      console.log('PWA: Image uploaded successfully');

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('attachments')
        .getPublicUrl(filePath);

      const imageUrl = urlData.publicUrl;

      // Send the image as a message through the channel
      if (channel) {
        await channel.sendMessage({
          text: '',
          attachments: [
            {
              type: 'image',
              image_url: imageUrl,
              fallback: 'Image attachment'
            }
          ]
        });

        // Send a system message about the image upload
        await createFileUploadMessage(channel, taskId, user.id, 'image');
      }

      // Clear the preview and input
      setPreviewUrl(null);
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      toast({
        title: "Image uploaded",
        description: "Your image has been uploaded successfully",
      });

      // Close the uploader
      onClose();
    } catch (error: any) {
      console.error('Error uploading image:', error);

      // Provide a more user-friendly error message
      let errorMessage = "Failed to upload image. Please try again.";

      if (error.message) {
        errorMessage = error.message;

        // Log detailed error information for debugging
        console.error('PWA: Upload error details:', {
          message: error.message,
          statusCode: error.statusCode,
          error: error
        });
      }

      toast({
        variant: "destructive",
        title: "Upload failed",
        description: errorMessage,
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleCancel = () => {
    setPreviewUrl(null);
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onClose();
  };

  return (
    <div className="bg-white border-t border-gray-200">
      {!previewUrl ? (
        <div className="p-4 space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900">Upload Image</h3>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept="image/*"
            className="hidden"
            disabled={isUploading}
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="flex items-center text-gray-600 w-full"
          >
            <Image className="h-4 w-4 mr-2" />
            Select Image
          </Button>
        </div>
      ) : (
        <div className="p-3 space-y-3">
          {/* Image preview in input area */}
          <div className="relative inline-block">
            <img
              src={previewUrl}
              alt="Preview"
              className="max-h-32 max-w-full rounded-lg border border-gray-200"
            />
            <button
              onClick={handleCancel}
              className="absolute -top-2 -right-2 bg-gray-800 bg-opacity-80 rounded-full p-1 text-white hover:bg-opacity-100 transition-opacity"
              disabled={isUploading}
            >
              <X className="h-3 w-3" />
            </button>
          </div>

          {/* Action buttons */}
          <div className="flex space-x-2">
            <Button
              type="button"
              size="sm"
              onClick={handleUpload}
              disabled={isUploading}
              className="bg-blue-600 hover:bg-blue-700 flex-1"
            >
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Image
                </>
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleCancel}
              disabled={isUploading}
              className="px-4"
            >
              Cancel
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PWAImageUploader;
