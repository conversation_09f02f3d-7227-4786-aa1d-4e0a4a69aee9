import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Download, Check, AlertCircle } from 'lucide-react';
import { isPWA } from '@/utils/pwa-utils';

// Create a global variable to store the install prompt event
declare global {
  interface Window {
    deferredPrompt: BeforeInstallPromptEvent | null;
  }
}

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

const PWAInstallButton: React.FC = () => {
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstalled, setIsInstalled] = useState<boolean>(false);
  const [isInstalling, setIsInstalling] = useState<boolean>(false);

  // Use a ref to track if we've already set up the event listeners
  const eventListenersSetUp = useRef(false);

  useEffect(() => {
    // Initialize the global deferredPrompt if it doesn't exist
    if (window.deferredPrompt === undefined) {
      window.deferredPrompt = null;
    }

    // Log PWA installation debugging information (development only)
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWAInstallButton] PWA installation debugging info:', {
        userAgent: navigator.userAgent,
        isPWA: isPWA(.replace(/user.*/, 'hasUser: ' + !!user)),
        isStandalone: window.matchMedia('(display-mode: standalone)').matches,
        isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream,
        isAndroid: /Android/.test(navigator.userAgent),
        supportsBeforeInstallPrompt: 'BeforeInstallPromptEvent' in window || 'onbeforeinstallprompt' in window,
        displayMode: window.matchMedia('(display-mode: standalone)').matches ? 'standalone' : 'browser',
        url: window.location.href,
        hasExistingPrompt: !!window.deferredPrompt
      });
        }
    }

    // Check if already installed as PWA
    if (isPWA()) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWAInstallButton] App is already installed as PWA');
        }
      setIsInstalled(true);
      return;
    }

    // Check if we already have a stored prompt
    if (window.deferredPrompt) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWAInstallButton] Using previously stored installation prompt');
        }
      setInstallPrompt(window.deferredPrompt);
    }

    // Only set up event listeners once
    if (!eventListenersSetUp.current) {
      // Listen for the beforeinstallprompt event
      const handleBeforeInstallPrompt = (e: Event) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWAInstallButton] beforeinstallprompt event fired', e);

          }
        // Prevent Chrome 76+ from automatically showing the prompt
        e.preventDefault();

        // Store the event globally so it persists across component remounts
        window.deferredPrompt = e as BeforeInstallPromptEvent;

        // Also store it in component state for immediate use
        setInstallPrompt(e as BeforeInstallPromptEvent);
      };

      // Listen for app installed event
      const handleAppInstalled = () => {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWAInstallButton] appinstalled event fired');
          }
        setIsInstalled(true);
        setInstallPrompt(null);
        window.deferredPrompt = null;
      };

      // Add the event listeners
      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.addEventListener('appinstalled', handleAppInstalled);

      // Mark that we've set up the event listeners
      eventListenersSetUp.current = true;

      // Clean up function
      return () => {
        window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
        window.removeEventListener('appinstalled', handleAppInstalled);
        eventListenersSetUp.current = false;
      };
    }

    // Log if the app meets the installability criteria
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration()
        .then(registration => {
          if (process.env.NODE_ENV === 'development') {
            console.log('[PWAInstallButton] Service worker registration status:', {
            registered: !!registration,
            scope: registration?.scope
          });
            }
        })
        .catch(error => {
          console.error('[PWAInstallButton] Error checking service worker registration:', error);
        });
    }
  }, []);

  const handleInstallClick = async () => {
    // Check if we have a prompt in component state
    let promptToUse = installPrompt;

    // If not, check if we have one stored globally
    if (!promptToUse && window.deferredPrompt) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWAInstallButton] Using globally stored prompt');
        }
      promptToUse = window.deferredPrompt;
      setInstallPrompt(promptToUse);
    }

    if (!promptToUse) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWAInstallButton] No installation prompt available');

        }
      // Check if this is iOS
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;

      if (isIOS) {
        // iOS-specific instructions
        alert('To install this app on your iOS device:\n\n1. Tap the Share button (rectangle with arrow)\n2. Scroll down and tap "Add to Home Screen"\n3. Tap "Add" in the top right corner\n\nThis will allow you to use ClassTasker offline and with enhanced features.');
      } else {
        // Generic instructions for other browsers
        alert('To install this app on your device:\n\n1. Tap the browser menu (⋮)\n2. Select "Install App" or "Add to Home Screen"\n\nThis will allow you to use ClassTasker offline and with enhanced features.');
      }
      return;
    }

    setIsInstalling(true);

    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWAInstallButton] Showing installation prompt');

        }
      // Show the install prompt
      await promptToUse.prompt();

      // Wait for the user to respond to the prompt
      const choiceResult = await promptToUse.userChoice;

      if (choiceResult.outcome === 'accepted') {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWAInstallButton] User accepted the install prompt');
          }
        setIsInstalled(true);
        // Clear both the component state and global storage
        setInstallPrompt(null);
        window.deferredPrompt = null;
      } else {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWAInstallButton] User dismissed the install prompt');
          }
        // We should clear the prompt as it can't be used again
        setInstallPrompt(null);
        window.deferredPrompt = null;
      }
    } catch (error) {
      console.error('[PWAInstallButton] Error during PWA installation:', error);
      alert('There was an error installing the app. Please try again or use your browser menu to add to home screen.');

      // Clear the prompts on error as well
      setInstallPrompt(null);
      window.deferredPrompt = null;
    } finally {
      setIsInstalling(false);
    }
  };

  // Show installed status if already installed
  if (isInstalled) {
    return (
      <Button variant="outline" size="sm" disabled className="text-green-600">
        <Check className="mr-2 h-4 w-4" />
        Installed
      </Button>
    );
  }

  // Check if we have a prompt either in component state or globally
  const hasPrompt = installPrompt || window.deferredPrompt;

  // If we don't have a prompt, show a button that gives manual instructions
  if (!hasPrompt) {
    // Check if this is iOS
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;

    return (
      <Button
        variant="outline"
        size="sm"
        onClick={handleInstallClick}
        className="animate-pulse"
      >
        <Download className="mr-2 h-4 w-4" />
        {isIOS ? 'Add to Home Screen' : 'Install App'}
      </Button>
    );
  }

  // If we have a prompt, show a button that will trigger it
  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleInstallClick}
      disabled={isInstalling}
      className="animate-pulse bg-blue-50"
    >
      <Download className="mr-2 h-4 w-4" />
      {isInstalling ? 'Installing...' : 'Install App'}
    </Button>
  );
};

export default PWAInstallButton;
