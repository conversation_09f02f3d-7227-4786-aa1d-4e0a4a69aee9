import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Download, Smartphone, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

declare global {
  interface Window {
    deferredPrompt: BeforeInstallPromptEvent | null;
  }
}

const PWAInstallDebug: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstalling, setIsInstalling] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(Date.now());

  const updateDebugInfo = () => {
    const info = {
      // Browser Support
      hasServiceWorker: 'serviceWorker' in navigator,
      hasBeforeInstallPrompt: 'BeforeInstallPromptEvent' in window,
      hasOnBeforeInstallPrompt: 'onbeforeinstallprompt' in window,
      
      // PWA State
      isStandalone: window.matchMedia('(display-mode: standalone)').matches,
      isPWA: window.matchMedia('(display-mode: standalone)').matches || (window.navigator as any).standalone === true,
      
      // Install Prompt
      hasGlobalPrompt: !!window.deferredPrompt,
      hasLocalPrompt: !!installPrompt,
      
      // Device Info
      userAgent: navigator.userAgent,
      isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream,
      isAndroid: /Android/.test(navigator.userAgent),
      isMobile: /Mobi|Android/i.test(navigator.userAgent),
      
      // Network
      isOnline: navigator.onLine,
      
      // URL Info
      currentURL: window.location.href,
      isHTTPS: window.location.protocol === 'https:',
      
      // Timestamps
      lastUpdate: new Date().toLocaleTimeString()
    };
    
    setDebugInfo(info);
    setLastUpdate(Date.now());
  };

  useEffect(() => {
    updateDebugInfo();
    
    // Check for existing prompt
    if (window.deferredPrompt) {
      setInstallPrompt(window.deferredPrompt);
    }
    
    // Listen for beforeinstallprompt
    const handleBeforeInstallPrompt = (e: Event) => {
      console.log('[PWAInstallDebug] beforeinstallprompt event fired');
      e.preventDefault();
      const promptEvent = e as BeforeInstallPromptEvent;
      window.deferredPrompt = promptEvent;
      setInstallPrompt(promptEvent);
      updateDebugInfo();
    };
    
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    
    // Listen for app installed
    const handleAppInstalled = () => {
      console.log('[PWAInstallDebug] App was installed');
      window.deferredPrompt = null;
      setInstallPrompt(null);
      updateDebugInfo();
    };
    
    window.addEventListener('appinstalled', handleAppInstalled);
    
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const forceInstallPrompt = async () => {
    const promptToUse = installPrompt || window.deferredPrompt;
    
    if (!promptToUse) {
      alert('No install prompt available. This could be because:\n\n1. App is already installed\n2. Browser doesn\'t support PWA install\n3. Site doesn\'t meet PWA criteria\n4. Need more user engagement\n\nTry visiting the site multiple times or interacting more with the page.');
      return;
    }
    
    try {
      setIsInstalling(true);
      console.log('[PWAInstallDebug] Showing install prompt');
      
      await promptToUse.prompt();
      const choiceResult = await promptToUse.userChoice;
      
      console.log('[PWAInstallDebug] User choice:', choiceResult.outcome);
      
      if (choiceResult.outcome === 'accepted') {
        console.log('[PWAInstallDebug] User accepted the install prompt');
      } else {
        console.log('[PWAInstallDebug] User dismissed the install prompt');
      }
      
      // Clear the prompt
      setInstallPrompt(null);
      window.deferredPrompt = null;
      updateDebugInfo();
      
    } catch (error) {
      console.error('[PWAInstallDebug] Error during install:', error);
      alert('Error during installation: ' + (error as Error).message);
    } finally {
      setIsInstalling(false);
    }
  };

  const checkServiceWorker = async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registrations = await navigator.serviceWorker.getRegistrations();
        console.log('[PWAInstallDebug] Service Worker registrations:', registrations);
        
        if (registrations.length === 0) {
          alert('No service worker registered. PWA install requires a service worker.');
        } else {
          alert(`Found ${registrations.length} service worker(s). Check console for details.`);
        }
      } catch (error) {
        console.error('[PWAInstallDebug] Error checking service worker:', error);
        alert('Error checking service worker: ' + (error as Error).message);
      }
    } else {
      alert('Service Worker not supported in this browser.');
    }
  };

  const simulateEngagement = () => {
    // Simulate user engagement by dispatching events
    const events = ['click', 'scroll', 'keydown', 'touchstart'];
    events.forEach(eventType => {
      document.dispatchEvent(new Event(eventType, { bubbles: true }));
    });
    
    // Wait a bit then check for prompt
    setTimeout(() => {
      updateDebugInfo();
      if (window.deferredPrompt) {
        alert('Install prompt is now available!');
      } else {
        alert('Still no install prompt. May need more engagement or multiple visits.');
      }
    }, 1000);
  };

  if (process.env.NODE_ENV !== 'development') {
    return null; // Only show in development
  }

  return (
    <Card className="mb-4 border-orange-200 bg-orange-50">
      <CardHeader>
        <CardTitle className="text-orange-800 flex items-center">
          <Smartphone className="mr-2 h-5 w-5" />
          PWA Install Debug Panel
          <Button
            variant="outline"
            size="sm"
            onClick={updateDebugInfo}
            className="ml-auto"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Install Actions */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={forceInstallPrompt}
            disabled={isInstalling || (!installPrompt && !window.deferredPrompt)}
            className="bg-orange-600 hover:bg-orange-700"
          >
            <Download className="mr-2 h-4 w-4" />
            {isInstalling ? 'Installing...' : 'Force Install'}
          </Button>
          
          <Button variant="outline" onClick={checkServiceWorker}>
            Check Service Worker
          </Button>
          
          <Button variant="outline" onClick={simulateEngagement}>
            Simulate Engagement
          </Button>
        </div>

        {/* Status Indicators */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          <Badge variant={debugInfo.hasServiceWorker ? "default" : "destructive"}>
            {debugInfo.hasServiceWorker ? <CheckCircle className="mr-1 h-3 w-3" /> : <AlertCircle className="mr-1 h-3 w-3" />}
            Service Worker
          </Badge>
          
          <Badge variant={debugInfo.hasGlobalPrompt ? "default" : "secondary"}>
            {debugInfo.hasGlobalPrompt ? <CheckCircle className="mr-1 h-3 w-3" /> : <AlertCircle className="mr-1 h-3 w-3" />}
            Install Prompt
          </Badge>
          
          <Badge variant={debugInfo.isHTTPS ? "default" : "destructive"}>
            {debugInfo.isHTTPS ? <CheckCircle className="mr-1 h-3 w-3" /> : <AlertCircle className="mr-1 h-3 w-3" />}
            HTTPS
          </Badge>
          
          <Badge variant={debugInfo.isPWA ? "default" : "secondary"}>
            {debugInfo.isPWA ? <CheckCircle className="mr-1 h-3 w-3" /> : <AlertCircle className="mr-1 h-3 w-3" />}
            Installed
          </Badge>
        </div>

        {/* Debug Info */}
        <div className="text-xs bg-white p-3 rounded border">
          <div className="font-mono">
            <div><strong>Device:</strong> {debugInfo.isIOS ? 'iOS' : debugInfo.isAndroid ? 'Android' : 'Desktop'}</div>
            <div><strong>Browser Support:</strong> {debugInfo.hasBeforeInstallPrompt ? 'Yes' : 'No'}</div>
            <div><strong>Standalone Mode:</strong> {debugInfo.isStandalone ? 'Yes' : 'No'}</div>
            <div><strong>Online:</strong> {debugInfo.isOnline ? 'Yes' : 'No'}</div>
            <div><strong>Last Update:</strong> {debugInfo.lastUpdate}</div>
          </div>
        </div>

        {debugInfo.isIOS && (
          <div className="bg-blue-50 border border-blue-200 p-3 rounded">
            <p className="text-sm text-blue-800">
              <strong>iOS Device Detected:</strong> iOS doesn't support the beforeinstallprompt event. 
              Users must manually add to home screen via Safari's share menu.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PWAInstallDebug;
