import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download, Smartphone, Bug } from 'lucide-react';

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

declare global {
  interface Window {
    deferredPrompt: BeforeInstallPromptEvent | null;
    forcePWAInstall: () => void;
    checkPWAStatus: () => void;
  }
}

const PWAQuickDebug: React.FC = () => {
  const [isInstalling, setIsInstalling] = useState(false);

  const quickInstall = async () => {
    setIsInstalling(true);
    
    try {
      // Try to use the global function first
      if (window.forcePWAInstall) {
        window.forcePWAInstall();
        return;
      }

      // Fallback to direct prompt
      const promptToUse = window.deferredPrompt;
      
      if (promptToUse) {
        console.log('[PWAQuickDebug] Showing install prompt');
        await promptToUse.prompt();
        const choiceResult = await promptToUse.userChoice;
        console.log('[PWAQuickDebug] User choice:', choiceResult.outcome);
        
        if (choiceResult.outcome === 'accepted') {
          alert('App installation started!');
        } else {
          alert('Installation cancelled by user.');
        }
        
        window.deferredPrompt = null;
      } else {
        // Show detailed debug info
        const debugInfo = {
          hasServiceWorker: 'serviceWorker' in navigator,
          hasBeforeInstallPrompt: 'BeforeInstallPromptEvent' in window,
          isStandalone: window.matchMedia('(display-mode: standalone)').matches,
          userAgent: navigator.userAgent,
          isHTTPS: location.protocol === 'https:',
          currentURL: location.href,
          hasGlobalPrompt: !!window.deferredPrompt
        };
        
        console.log('[PWAQuickDebug] No install prompt available. Debug info:', debugInfo);
        
        let message = 'No install prompt available.\n\nPossible reasons:\n';
        if (!debugInfo.hasServiceWorker) message += '• Service Worker not supported\n';
        if (!debugInfo.hasBeforeInstallPrompt) message += '• Browser doesn\'t support PWA install\n';
        if (debugInfo.isStandalone) message += '• App is already installed\n';
        if (!debugInfo.isHTTPS) message += '• Site is not HTTPS\n';
        message += '• Need more user engagement\n';
        message += '• Multiple visits required\n\n';
        message += 'Check console for detailed debug info.';
        
        alert(message);
      }
    } catch (error) {
      console.error('[PWAQuickDebug] Error during install:', error);
      alert('Error during installation: ' + (error as Error).message);
    } finally {
      setIsInstalling(false);
    }
  };

  const checkStatus = () => {
    if (window.checkPWAStatus) {
      window.checkPWAStatus();
    } else {
      const status = {
        hasServiceWorker: 'serviceWorker' in navigator,
        hasInstallPrompt: !!window.deferredPrompt,
        isInstalled: window.matchMedia('(display-mode: standalone)').matches,
        isHTTPS: location.protocol === 'https:',
        userAgent: navigator.userAgent,
        currentURL: location.href
      };
      
      console.log('[PWAQuickDebug] PWA Status:', status);
      alert('PWA Status logged to console. Check DevTools.');
    }
  };

  // Always show this component for testing
  return (
    <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2">
      <Button
        onClick={quickInstall}
        disabled={isInstalling}
        className="bg-orange-600 hover:bg-orange-700 text-white shadow-lg"
        size="sm"
      >
        <Download className="mr-2 h-4 w-4" />
        {isInstalling ? 'Installing...' : 'PWA Install'}
      </Button>
      
      <Button
        onClick={checkStatus}
        variant="outline"
        className="bg-white shadow-lg"
        size="sm"
      >
        <Bug className="mr-2 h-4 w-4" />
        Debug
      </Button>
    </div>
  );
};

export default PWAQuickDebug;
