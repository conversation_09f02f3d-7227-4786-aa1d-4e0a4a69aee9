import React, { useState, useEffect, useRef } from 'react';
import { Channel, StreamChat, Message as StreamMessage } from 'stream-chat';
import { Button } from '@/components/ui/button';
import { Send, RefreshCw, Image as ImageIcon, Smile, MoreVertical, Trash2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import PWAImageUploader from './PWAImageUploader';
import { useAuth } from '@/contexts/AuthContext';

interface PWASimplifiedChatProps {
  client: StreamChat;
  channel: Channel;
  messages: StreamMessage[];
  onSendMessage: (text: string) => Promise<{ success: boolean; reason?: string }>;
  isSending: boolean;
  onRefresh: () => void;
}

/**
 * A simplified chat component for PWA that doesn't use the heavy GetStream components
 * This helps prevent freezing and blank screens in PWA mode
 */
const PWASimplifiedChat: React.FC<PWASimplifiedChatProps> = ({
  client,
  channel,
  messages,
  onSendMessage,
  isSending,
  onRefresh
}) => {
  const { user } = useAuth();
  const [text, setText] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [showImageUploader, setShowImageUploader] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle message submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!text.trim()) return;

    try {
      const result = await onSendMessage(text);
      if (result.success) {
        setText('');
      } else {
        setError(result.reason || 'Failed to send message');
      }
    } catch (err) {
      setError('Error sending message');
      console.error('[PWASimplifiedChat] Error sending message:', err);
    }
  };

  // Format message timestamp
  const formatMessageTime = (date: string | Date) => {
    try {
      return formatDistanceToNow(new Date(date), { addSuffix: true });
    } catch (err) {
      return '';
    }
  };

  // Get user display name - with null checks
  const getUserName = (userId: string) => {
    if (!client || !channel) {
      return 'User';
    }

    if (userId === client.userID) {
      return 'You';
    }

    const member = channel.state?.members?.[userId];
    return member?.user?.name || 'User';
  };

  // Check if message is from current user - with null check
  const isOwnMessage = (userId: string) => {
    if (!client) {
      return false;
    }
    return userId === client.userID;
  };

  // Common emojis for quick access
  const commonEmojis = ['😀', '😂', '😍', '👍', '👎', '❤️', '🎉', '🔥', '💯', '👏', '🙏', '✅'];

  // Handle emoji selection
  const handleEmojiSelect = (emoji: string) => {
    setText(prev => prev + emoji);
    setShowEmojiPicker(false);
  };

  // Handle message deletion (works for both text and image messages)
  const handleDeleteMessage = async (messageId: string) => {
    try {
      console.log('[PWASimplifiedChat] Attempting to delete message:', messageId);

      // Use the client to delete the message
      const response = await client.deleteMessage(messageId);
      console.log('[PWASimplifiedChat] Delete response:', response);

      setSelectedMessage(null);
      setError(null); // Clear any previous errors

      // Optionally refresh the channel to update the message list
      await channel.query();
    } catch (error: any) {
      console.error('[PWASimplifiedChat] Error deleting message:', error);
      setError(`Failed to delete message: ${error.message || 'Unknown error'}`);

      // Auto-clear error after 3 seconds
      setTimeout(() => setError(null), 3000);
    }
  };

  // Enhanced logging for debugging (development only)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('[PWASimplifiedChat] Component state:', {
        hasClient: !!client,
        hasChannel: !!channel,
        messagesCount: messages?.length || 0,
        isSending,
        clientConnected: typeof client?.isConnected === 'function' ? client.isConnected() : !!client?.wsConnection,
        hasClientUser: !!client?.userID,
        hasChannelId: !!channel?.id,
        channelType: channel?.type,
        hasChannelCid: !!channel?.cid,
        error
      });
    }

    if (client && process.env.NODE_ENV === 'development') {
      console.log('[PWASimplifiedChat] Client details:', {
        userID: client.userID,
        connectionID: client.connectionID,
        isConnected: typeof client.isConnected === 'function' ? client.isConnected() : !!client.wsConnection,
        wsConnection: client.wsConnection ? 'active' : 'inactive',
        activeChannels: client.activeChannels ? Object.keys(client.activeChannels).length : 0
      });
    }

    if (channel && process.env.NODE_ENV === 'development') {
      console.log('[PWASimplifiedChat] Channel details:', {
        id: channel.id,
        type: channel.type,
        cid: channel.cid,
        initialized: channel.initialized,
        data: channel.data,
        state: {
          members: Object.keys(channel.state?.members || {}),
          messages: channel.state?.messages?.length || 0,
          watchers: Object.keys(channel.state?.watchers || {})
        }
      });
    }
  }, [client, channel, messages, isSending, error]);

  // Safety check for required props
  if (!client || !channel) {
    if (process.env.NODE_ENV === 'development') {
      console.error('[PWASimplifiedChat] Missing required props:', {
        hasClient: !!client,
        hasChannel: !!channel,
        clientDetails: client ? {
          userID: client.userID,
          connectionID: client.connectionID,
          isConnected: typeof client.isConnected === 'function' ? client.isConnected() : !!client.wsConnection
        } : 'null',
        channelDetails: channel ? {
          id: channel.id,
          type: channel.type,
          cid: channel.cid
        } : 'null'
      });
    } else {
      console.error('[PWASimplifiedChat] Missing required props - client or channel not available');
    }
    return (
      <div className="flex flex-col h-full items-center justify-center p-4">
        <div className="text-red-500 mb-4">
          Chat connection error
        </div>
        <div className="text-xs text-gray-500 mb-4 text-center">
          {!client ? "Client connection failed" : "Channel connection failed"}
        </div>
        <Button variant="outline" onClick={onRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Messages container with padding at bottom for input area (no navigation) */}
      <div
        className="flex-1 overflow-y-auto pb-20 bg-white"
        style={{ WebkitOverflowScrolling: 'touch' }}
      >
        {!messages || messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 bg-white">
            <p>No messages yet</p>
            <Button variant="ghost" size="sm" onClick={onRefresh} className="mt-2">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        ) : (
          <div className="px-2 bg-white">
            {messages.map((msg, index) => {
              const isOwn = isOwnMessage(msg.user?.id || '');
              const isSystem = msg.type === 'system';
              const showDateHeader = index === 0 ||
                new Date(msg.created_at || new Date()).toDateString() !==
                new Date(messages[index - 1].created_at || new Date()).toDateString();

              return (
                <React.Fragment key={msg.id}>
                  {/* Date header */}
                  {showDateHeader && (
                    <div className="flex justify-center my-4">
                      <div className="bg-gray-100 text-gray-500 text-xs px-3 py-1 rounded-full">
                        {new Date(msg.created_at || new Date()).toLocaleDateString(undefined, {
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </div>
                    </div>
                  )}

                  {/* System message */}
                  {isSystem ? (
                    <div className="flex justify-center my-2">
                      <span className="text-xs bg-blue-50 text-blue-700 px-3 py-1 rounded-full italic">
                        {msg.text}
                      </span>
                    </div>
                  ) : (
                    <div className={`flex mb-2 ${isOwn ? 'justify-end' : 'justify-start'}`}>
                      <div className={`max-w-[80%] ${isOwn ? 'text-right' : 'text-left'}`}>
                        {/* Message sender - only show for others and if different from previous message */}
                        {!isOwn && (index === 0 || messages[index - 1].user?.id !== msg.user?.id) && (
                          <div className="font-medium text-xs text-gray-700 mb-1 ml-1">
                            {getUserName(msg.user?.id || '')}
                          </div>
                        )}

                        {/* Handle image attachments - show without bubble for cleaner look */}
                        {msg.attachments && msg.attachments.length > 0 && (
                          <div className="mb-2 relative group">
                            {msg.attachments.map((attachment: any, attachIndex: number) => {
                              if (attachment.type === 'image' && attachment.image_url) {
                                return (
                                  <div key={attachIndex} className="relative">
                                    <img
                                      src={attachment.image_url}
                                      alt={attachment.fallback || 'Image'}
                                      className={`max-w-[280px] max-h-64 rounded-lg cursor-pointer shadow-sm border border-gray-200 ${
                                        isOwn ? 'ml-auto' : 'mr-auto'
                                      }`}
                                      style={{ display: 'block' }}
                                      onClick={() => window.open(attachment.image_url, '_blank')}
                                      onError={(e) => {
                                        console.error('Failed to load image:', attachment.image_url);
                                        (e.target as HTMLImageElement).style.display = 'none';
                                      }}
                                    />

                                    {/* Image delete button - only for own messages */}
                                    {isOwn && (
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity bg-black/50 hover:bg-black/70 text-white border-0"
                                        onClick={(e) => {
                                          e.stopPropagation(); // Prevent image click
                                          setSelectedMessage(selectedMessage === msg.id ? null : msg.id);
                                        }}
                                      >
                                        <MoreVertical className="h-3 w-3" />
                                      </Button>
                                    )}

                                    {/* Delete confirmation for image messages */}
                                    {selectedMessage === msg.id && (
                                      <div className="absolute top-8 right-2 bg-white border rounded-lg shadow-lg p-2 z-10">
                                        <Button
                                          variant="destructive"
                                          size="sm"
                                          onClick={() => handleDeleteMessage(msg.id)}
                                          className="text-xs"
                                        >
                                          <Trash2 className="h-3 w-3 mr-1" />
                                          Delete Image
                                        </Button>
                                      </div>
                                    )}
                                  </div>
                                );
                              }
                              return null;
                            })}
                          </div>
                        )}

                        {/* Message bubble - only show if there's text content */}
                        {msg.text && (
                          <div className="relative group">
                            <div
                              className={`inline-block px-3 py-2 rounded-2xl ${
                                isOwn
                                  ? 'bg-blue-500 text-white rounded-tr-none'
                                  : 'bg-gray-100 text-gray-800 rounded-tl-none'
                              }`}
                              onLongPress={() => isOwn && setSelectedMessage(msg.id)}
                            >
                              <div className="text-sm whitespace-pre-wrap break-words">{msg.text}</div>
                            </div>

                            {/* Message actions - only for own messages */}
                            {isOwn && (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="absolute -top-2 -right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity bg-white shadow-sm border"
                                onClick={() => setSelectedMessage(selectedMessage === msg.id ? null : msg.id)}
                              >
                                <MoreVertical className="h-3 w-3" />
                              </Button>
                            )}

                            {/* Delete confirmation */}
                            {selectedMessage === msg.id && (
                              <div className="absolute top-8 right-0 bg-white border rounded-lg shadow-lg p-2 z-10">
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => handleDeleteMessage(msg.id)}
                                  className="text-xs"
                                >
                                  <Trash2 className="h-3 w-3 mr-1" />
                                  Delete
                                </Button>
                              </div>
                            )}
                          </div>
                        )}

                        {/* Message timestamp */}
                        <div className={`text-xs mt-1 ${isOwn ? 'text-gray-500 mr-1' : 'text-gray-500 ml-1'}`}>
                          {formatMessageTime(msg.created_at || new Date())}
                        </div>
                      </div>
                    </div>
                  )}
                </React.Fragment>
              );
            })}
          </div>
        )}
        <div ref={messagesEndRef} className="h-4" />
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border-t border-red-200 px-4 py-2 text-xs text-red-700">
          {error}
        </div>
      )}

      {/* Input form - fixed at the bottom (no navigation in chat screens) */}
      <div className="fixed bottom-0 left-0 right-0 z-20">
        {/* Image uploader - shows inline when active */}
        {showImageUploader ? (
          <PWAImageUploader
            taskId={channel.data?.task_id || channel.id}
            channel={channel}
            onClose={() => setShowImageUploader(false)}
          />
        ) : (
          /* Text input form */
          <div className="border-t bg-white">
            {/* Emoji picker */}
            {showEmojiPicker && (
              <div className="p-3 border-b bg-gray-50">
                <div className="grid grid-cols-6 gap-2">
                  {commonEmojis.map((emoji, index) => (
                    <button
                      key={index}
                      type="button"
                      className="text-2xl p-2 hover:bg-gray-200 rounded-lg transition-colors"
                      onClick={() => handleEmojiSelect(emoji)}
                    >
                      {emoji}
                    </button>
                  ))}
                </div>
              </div>
            )}

            <div className="p-2">
              <form onSubmit={handleSubmit} className="flex items-center px-2">
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="mr-2"
                  onClick={() => setShowImageUploader(true)}
                  disabled={isSending}
                  title="Upload Image"
                >
                  <ImageIcon className="h-5 w-5" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="mr-2"
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  disabled={isSending}
                  title="Add Emoji"
                >
                  <Smile className="h-5 w-5" />
                </Button>
                <input
                  type="text"
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  placeholder="Type a message..."
                  className="flex-1 p-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  disabled={isSending}
                  autoComplete="off"
                />
                <Button
                  type="submit"
                  size="icon"
                  className="ml-2"
                  disabled={!text.trim() || isSending}
                >
                  <Send className="h-5 w-5" />
                </Button>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PWASimplifiedChat;
