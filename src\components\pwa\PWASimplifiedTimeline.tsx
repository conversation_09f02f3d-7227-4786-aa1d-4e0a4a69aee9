import React from 'react';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  MessageSquare,
  PlayCircle,
  ThumbsUp,
  User,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Define the possible task statuses in order
const EXTERNAL_TASK_STATUSES = [
  'open',           // Task is created and open
  'interest',       // Suppliers have expressed interest
  'questions',      // Discussion phase between admin and suppliers
  'offer',          // Suppliers have submitted offers
  'assigned',       // Task has been assigned to someone
  'in_progress',    // Work has started on the task
  'completed',      // Work is completed, awaiting confirmation
  'closed',         // Task is closed by admin
  'pending_payment' // Payment is required to complete the task
];

// Define simplified workflow for internal tasks
const INTERNAL_TASK_STATUSES = [
  'assigned',       // Task has been assigned to internal staff
  'in_progress',    // Work has started on the task
  'completed',      // Work is completed, awaiting confirmation
  'closed'          // Task is closed by admin
];

// Define workflow for admin review tasks
const ADMIN_REVIEW_STATUSES = [
  'open',           // Task is created and awaiting admin review
  'admin_review',   // Admin is reviewing the task
  'assigned'        // Task has been assigned (either internally or to marketplace)
];

// Define status labels for display
const STATUS_LABELS: Record<string, string> = {
  'open': 'Posted',
  'interest': 'Interest',
  'questions': 'Discussion',
  'offer': 'Offers',
  'assigned': 'Assigned',
  'in_progress': 'In Progress',
  'completed': 'Completed',
  'closed': 'Closed',
  'confirmed': 'Closed', // Legacy status - treat as closed
  'approved': 'Closed', // Legacy status - treat as closed
  'pending_payment': 'Payment Due',
  'admin_review': 'Admin Review'
};

// Define icons for each status
const STATUS_ICONS: Record<string, React.ReactNode> = {
  'open': <Clock size={16} />,
  'interest': <MessageSquare size={16} />,
  'questions': <MessageSquare size={16} />,
  'offer': <DollarSign size={16} />,
  'assigned': <User size={16} />,
  'in_progress': <PlayCircle size={16} />,
  'completed': <CheckCircle size={16} />,
  'pending_payment': <DollarSign size={16} />,
  'closed': <ThumbsUp size={16} />,
  'confirmed': <ThumbsUp size={16} />, // Legacy status - treat as closed
  'approved': <ThumbsUp size={16} />, // Legacy status - treat as closed
  'admin_review': <AlertCircle size={16} />
};

interface PWASimplifiedTimelineProps {
  status: string;
  visibility?: 'admin' | 'internal' | 'public';
  className?: string;
}

const PWASimplifiedTimeline: React.FC<PWASimplifiedTimelineProps> = ({
  status,
  visibility = 'public',
  className
}) => {
  // Determine which status array to use based on task visibility and type
  const isInternalTask = visibility === 'internal';
  const isAdminReviewTask = visibility === 'admin' && status === 'open';

  let taskStatuses;
  if (isInternalTask) {
    taskStatuses = INTERNAL_TASK_STATUSES;
  } else if (isAdminReviewTask) {
    taskStatuses = ADMIN_REVIEW_STATUSES;
  } else {
    taskStatuses = EXTERNAL_TASK_STATUSES;
  }

  // Handle legacy statuses by treating them as "closed"
  const normalizedStatus = (status === 'confirmed' || status === 'approved') ? 'closed' : status;

  if (process.env.NODE_ENV === 'development') {
    console.log(`PWASimplifiedTimeline DEBUG: Original status="${status}", Normalized status="${normalizedStatus}", Available statuses:`, taskStatuses);
  }
  // Ensure we have a valid status
  const validStatus = taskStatuses.includes(normalizedStatus) ? normalizedStatus : taskStatuses[0];

  // Find the current status index
  const currentStatusIndex = taskStatuses.indexOf(validStatus);

  // Determine previous, current, and next statuses
  const previousStatus = currentStatusIndex > 0 ? taskStatuses[currentStatusIndex - 1] : null;
  const currentStatus = taskStatuses[currentStatusIndex];
  const nextStatus = currentStatusIndex < taskStatuses.length - 1 ? taskStatuses[currentStatusIndex + 1] : null;

  if (process.env.NODE_ENV === 'development') {
    console.log(`PWASimplifiedTimeline DEBUG: Valid status="${validStatus}"`);
  }
  if (process.env.NODE_ENV === 'development') {
    console.log(`PWASimplifiedTimeline DEBUG: Status label will be="${STATUS_LABELS[validStatus] || STATUS_LABELS[status] || status}"`);
  }
  if (process.env.NODE_ENV === 'development') {
    console.log(`PWASimplifiedTimeline DEBUG: Current status label="${STATUS_LABELS[currentStatus]}"`);
  }
  if (process.env.NODE_ENV === 'development') {
    console.log(`PWASimplifiedTimeline DEBUG: Previous status label="${previousStatus ? STATUS_LABELS[previousStatus] : 'none'}"`);
  }
  if (process.env.NODE_ENV === 'development') {
    console.log(`PWASimplifiedTimeline DEBUG: Next status label="${nextStatus ? STATUS_LABELS[nextStatus] : 'none'}"`);
  }
  if (process.env.NODE_ENV === 'development') {
    console.log(`PWASimplifiedTimeline DEBUG: Current status index=${currentStatusIndex}, Previous=${previousStatus}, Current=${currentStatus}, Next=${nextStatus}`);
  }
  return (
    <div className={cn("w-full py-2", className)}>
      <div className="flex items-center justify-between">
        {/* Previous status */}
        {previousStatus ? (
          <div className="flex flex-col items-center">
            <div className="rounded-full p-1.5 bg-green-100 border border-green-300">
              {STATUS_ICONS[previousStatus]}
            </div>
            <span className="text-xs mt-1 text-green-700 font-medium">
              {STATUS_LABELS[previousStatus]}
            </span>
          </div>
        ) : (
          <div className="w-8" /> // Empty space for alignment
        )}

        {/* Connecting lines */}
        <div className="flex-1 flex items-center justify-center">
          <div className="h-0.5 w-full bg-gray-200 relative">
            {previousStatus && (
              <div className="absolute inset-0 bg-green-500" style={{ width: '100%' }} />
            )}
          </div>
        </div>

        {/* Current status */}
        <div className="flex flex-col items-center z-10">
          <div className="rounded-full p-2 bg-blue-100 border-2 border-blue-400 shadow-sm">
            {STATUS_ICONS[currentStatus]}
          </div>
          <span className="text-xs mt-1 text-blue-700 font-bold">
            {STATUS_LABELS[currentStatus]}
          </span>
        </div>

        {/* Connecting lines */}
        <div className="flex-1 flex items-center justify-center">
          <div className="h-0.5 w-full bg-gray-200" />
        </div>

        {/* Next status */}
        {nextStatus ? (
          <div className="flex flex-col items-center">
            <div className="rounded-full p-1.5 bg-gray-100 border border-gray-300">
              {STATUS_ICONS[nextStatus]}
            </div>
            <span className="text-xs mt-1 text-gray-500">
              {STATUS_LABELS[nextStatus]}
            </span>
          </div>
        ) : (
          <div className="w-8" /> // Empty space for alignment
        )}
      </div>
    </div>
  );
};

export default PWASimplifiedTimeline;
