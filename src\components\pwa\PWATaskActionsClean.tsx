import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ThumbsUp,
  Loader2,
  User,
  DollarSign,
  PlayCircle,
  HandCoins,
  Send,
  PoundSterling
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { isInRoleGroup } from '@/constants/roles';
import { ROLE_GROUPS } from '@/constants/roles';

/**
 * Clean version of PWATaskActions component
 * This component shows task actions based on user role and task status
 */
const PWATaskActionsClean: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user, profile, userRole, isAdmin, isSupplier } = useAuth();
  const [task, setTask] = useState<any>(null);
  const [creatorProfile, setCreatorProfile] = useState<any>(null);
  const [assigneeProfile, setAssigneeProfile] = useState<any>(null);
  const [userOffer, setUserOffer] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Offer form state
  const [showOfferDialog, setShowOfferDialog] = useState(false);
  const [offerAmount, setOfferAmount] = useState<string>('');
  const [offerMessage, setOfferMessage] = useState<string>('');

  // Fetch task data
  const fetchTask = async () => {
    setLoading(true);
    setError(null);

    try {
      if (!id || !user) {
        setError('Task ID is missing or user is not logged in');
        return;
      }

      // Fetch task from API
      const { data: taskData, error: taskError } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', id)
        .single();

      if (taskError) {
        console.error('[PWATaskActions] Error fetching task:', taskError);
        throw taskError;
      }

      if (!taskData) {
        setError('Task not found');
        return;
      }

      if (process.env.NODE_ENV === 'development') {


        console.log('[PWATaskActions] Task data retrieved:', taskData);


        }
      setTask(taskData);

      // Initialize offer amount with task budget if available
      if (taskData.budget) {
        setOfferAmount(taskData.budget.toString());
      }

      // Fetch creator profile
      if (taskData.user_id) {
        const { data: creatorData, error: creatorError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', taskData.user_id)
          .single();

        if (!creatorError && creatorData) {
          setCreatorProfile(creatorData);
        }
      }

      // Fetch assignee profile
      if (taskData.assigned_to) {
        const { data: assigneeData, error: assigneeError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', taskData.assigned_to)
          .single();

        if (!assigneeError && assigneeData) {
          setAssigneeProfile(assigneeData);
        }
      }

      // If user is a supplier, check if they have an offer for this task
      if (isSupplier && taskData.visibility === 'public') {
        const { data: offerData, error: offerError } = await supabase
          .from('offers')
          .select('*')
          .eq('task_id', id)
          .eq('user_id', user.id)
          .maybeSingle();

        if (!offerError && offerData) {
          setUserOffer(offerData);
        }
      }
    } catch (error: any) {
      console.error('[PWATaskActions] Error fetching task:', error);
      setError(error.message || 'Failed to load task');
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {

      console.log('[PWATaskActions] Component mounted with task ID:', id);

      }
    fetchTask();
  }, [id]);

  // Handle back button
  const handleBack = () => {
    navigate(-1);
  };

  // Handle showing the offer dialog
  const handleShowOfferDialog = () => {
    // Initialize with existing offer data if available
    if (userOffer) {
      setOfferAmount(userOffer.amount.toString());
      setOfferMessage(userOffer.message || '');
    } else if (task?.budget) {
      // Otherwise initialize with task budget
      setOfferAmount(task.budget.toString());
      // Default message for new offers
      setOfferMessage(`I can complete this task for £${parseFloat(task.budget).toFixed(2)}.`);
    }

    setShowOfferDialog(true);
  };

  // Handle submitting the offer form
  const handleSubmitOffer = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!offerAmount || !offerMessage) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please provide both an amount and a message for your offer.",
      });
      return;
    }

    // Close dialog and submit the offer
    setShowOfferDialog(false);
    await handleTaskAction('submit_offer', {
      amount: parseFloat(offerAmount),
      message: offerMessage
    });
  };

  // Handle task action
  const handleTaskAction = async (action: string, additionalData?: any) => {
    if (!task || !user) return;

    // For submit_offer action, show the dialog instead of proceeding directly
    if (action === 'submit_offer' && !additionalData) {
      handleShowOfferDialog();
      return;
    }

    setActionLoading(true);

    try {
      let newStatus = '';
      let message = '';
      let updateData: any = {};

      switch (action) {
        // Actions for assigned staff
        case 'start':
          newStatus = 'in_progress';
          message = 'Task started successfully';
          updateData = {
            status: newStatus,
            updated_at: new Date().toISOString()
          };
          break;

        case 'complete':
          newStatus = 'completed';
          message = 'Task marked as completed';
          updateData = {
            status: newStatus,
            updated_at: new Date().toISOString()
          };
          break;

        // Actions for task creators/admins
        case 'assign':
          if (!additionalData?.assignedTo) {
            throw new Error('No assignee specified');
          }
          newStatus = 'assigned';
          message = 'Task assigned successfully';
          updateData = {
            status: newStatus,
            assigned_to: additionalData.assignedTo,
            updated_at: new Date().toISOString()
          };
          break;

        case 'confirm':
          newStatus = 'closed';
          message = 'Task closed successfully';
          updateData = {
            status: newStatus,
            updated_at: new Date().toISOString()
          };
          break;

        case 'request_payment':
          newStatus = 'pending_payment';
          message = 'Payment requested';
          updateData = {
            status: newStatus,
            updated_at: new Date().toISOString()
          };
          break;

        // Actions for suppliers
        case 'express_interest':
          // For suppliers expressing interest, we don't change task status
          // Instead, we create a chat thread or update the existing one
          message = 'Interest expressed successfully';

          // Check if there's already a chat thread for this task and supplier
          const { data: existingThread, error: threadError } = await supabase
            .from('chat_threads')
            .select('*')
            .eq('task_id', task.id)
            .eq('supplier_id', user.id)
            .maybeSingle();

          if (threadError) {
            throw threadError;
          }

          if (!existingThread) {
            // Create a new chat thread
            const { error: createError } = await supabase
              .from('chat_threads')
              .insert({
                task_id: task.id,
                supplier_id: user.id,
                admin_id: task.user_id,
                status: 'interest'
              });

            if (createError) {
              throw createError;
            }
          }

          // Update task status to 'interest' if it's currently 'open'
          if (task.status === 'open') {
            updateData = {
              status: 'interest',
              updated_at: new Date().toISOString()
            };
          }
          break;

        case 'submit_offer':
          if (!additionalData?.amount) {
            throw new Error('No offer amount specified');
          }

          message = 'Offer submitted successfully';

          // Create or update an offer
          if (userOffer) {
            // Update existing offer
            const { error: updateOfferError } = await supabase
              .from('offers')
              .update({
                amount: additionalData.amount,
                message: additionalData.message || '',
                updated_at: new Date().toISOString()
              })
              .eq('id', userOffer.id);

            if (updateOfferError) {
              throw updateOfferError;
            }
          } else {
            // Create new offer
            const { error: createOfferError } = await supabase
              .from('offers')
              .insert({
                task_id: task.id,
                user_id: user.id,
                amount: additionalData.amount,
                message: additionalData.message || '',
                status: 'pending'
              });

            if (createOfferError) {
              throw createOfferError;
            }
          }

          // Update task status to 'offer' if it's currently 'interest' or 'questions'
          if (task.status === 'interest' || task.status === 'questions') {
            updateData = {
              status: 'offer',
              updated_at: new Date().toISOString()
            };
          }
          break;

        // Common actions
        case 'cancel':
          newStatus = 'cancelled';
          message = 'Task cancelled';
          updateData = {
            status: newStatus,
            updated_at: new Date().toISOString()
          };
          break;

        case 'accept':
          newStatus = 'assigned';
          message = 'Offer accepted';

          // For accepting an offer, we need to update the task status and assigned_to
          if (!additionalData?.offerId || !additionalData?.supplierId) {
            throw new Error('Missing offer information');
          }

          updateData = {
            status: newStatus,
            assigned_to: additionalData.supplierId,
            updated_at: new Date().toISOString()
          };

          // Also update the offer status
          const { error: offerError } = await supabase
            .from('offers')
            .update({ status: 'accepted' })
            .eq('id', additionalData.offerId);

          if (offerError) {
            throw offerError;
          }
          break;

        default:
          throw new Error('Invalid action');
      }

      // Update task if there are changes to make
      if (Object.keys(updateData).length > 0) {
        const { error } = await supabase
          .from('tasks')
          .update(updateData)
          .eq('id', task.id);

        if (error) {
          throw error;
        }
      }

      // Show success message
      toast({
        title: 'Success',
        description: message,
      });

      // Refresh task data
      fetchTask();
    } catch (error: any) {
      console.error('[PWATaskActions] Error updating task:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to update task',
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Get available actions based on task status and user role
  const getAvailableActions = () => {
    if (!task || !user || !profile) return [];

    const isCreator = task.user_id === user.id;
    const isAssigned = task.assigned_to === user.id;
    const status = task.status?.toLowerCase() || '';
    const visibility = task.visibility;
    const isInternalTask = visibility === 'internal';
    const isExternalTask = visibility === 'public';
    const isAssignableStaff = isInRoleGroup(userRole, ROLE_GROUPS.TASK_ASSIGNABLE);

    const actions = [];

    // ADMIN ACTIONS
    if (isAdmin) {
      // For open tasks, admins can assign them
      if (status === 'open' && isInternalTask) {
        actions.push({
          label: 'Assign Task',
          action: 'assign',
          icon: <User className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For completed tasks, admins can confirm completion
      if (status === 'completed') {
        actions.push({
          label: 'Confirm Completion',
          action: 'confirm',
          icon: <CheckCircle className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For closed tasks, admins can request payment (external tasks)
      if (status === 'closed' && isExternalTask) {
        actions.push({
          label: 'Request Payment',
          action: 'request_payment',
          icon: <DollarSign className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For tasks with offers, admins can accept an offer
      if (status === 'offer' && isExternalTask) {
        actions.push({
          label: 'Accept Offer',
          action: 'accept',
          icon: <ThumbsUp className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // Admins can cancel tasks that aren't completed or confirmed
      if (!['completed', 'confirmed', 'pending_payment', 'cancelled'].includes(status)) {
        actions.push({
          label: 'Cancel Task',
          action: 'cancel',
          icon: <XCircle className="h-4 w-4 mr-2" />,
          variant: 'destructive' as const
        });
      }
    }

    // TASK CREATOR ACTIONS (if not admin)
    else if (isCreator) {
      // For completed tasks, creators can confirm completion
      if (status === 'completed') {
        actions.push({
          label: 'Confirm Completion',
          action: 'confirm',
          icon: <CheckCircle className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For tasks with offers, creators can accept an offer
      if (status === 'offer' && isExternalTask) {
        actions.push({
          label: 'Accept Offer',
          action: 'accept',
          icon: <ThumbsUp className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // Creators can cancel tasks that aren't completed or confirmed
      if (!['completed', 'confirmed', 'pending_payment', 'cancelled'].includes(status)) {
        actions.push({
          label: 'Cancel Task',
          action: 'cancel',
          icon: <XCircle className="h-4 w-4 mr-2" />,
          variant: 'destructive' as const
        });
      }
    }

    // ASSIGNED STAFF ACTIONS
    if (isAssigned && isAssignableStaff) {
      // For assigned tasks, staff can start them
      if (status === 'assigned') {
        actions.push({
          label: 'Start Task',
          action: 'start',
          icon: <PlayCircle className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For in-progress tasks, staff can mark them as completed
      if (status === 'in_progress') {
        actions.push({
          label: 'Mark as Completed',
          action: 'complete',
          icon: <CheckCircle className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }
    }

    // SUPPLIER ACTIONS
    if (isSupplier && isExternalTask) {
      // For open tasks, suppliers can express interest
      if (status === 'open' || status === 'interest') {
        // Only show if the supplier hasn't already expressed interest
        if (!userOffer) {
          actions.push({
            label: 'Express Interest',
            action: 'express_interest',
            icon: <HandCoins className="h-4 w-4 mr-2" />,
            variant: 'default' as const
          });
        }
      }

      // For tasks in interest or questions phase, suppliers can submit offers
      if (['open', 'interest', 'questions'].includes(status)) {
        actions.push({
          label: userOffer ? 'Update Offer' : 'Submit Offer',
          action: 'submit_offer',
          icon: <Send className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For assigned tasks (to this supplier), they can start work
      if (status === 'assigned' && isAssigned) {
        actions.push({
          label: 'Start Work',
          action: 'start',
          icon: <PlayCircle className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }

      // For in-progress tasks (assigned to this supplier), they can mark as completed
      if (status === 'in_progress' && isAssigned) {
        actions.push({
          label: 'Mark as Completed',
          action: 'complete',
          icon: <CheckCircle className="h-4 w-4 mr-2" />,
          variant: 'default' as const
        });
      }
    }

    return actions;
  };

  if (process.env.NODE_ENV === 'development') {


    console.log('[PWATaskActions] Rendering component with task:', task?.id, 'and status:', task?.status);



    }
  return (
    <div className="container max-w-md mx-auto px-4 py-6">
      {/* Offer Dialog */}
      <Dialog open={showOfferDialog} onOpenChange={setShowOfferDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{userOffer ? 'Update Your Offer' : 'Submit Your Offer'}</DialogTitle>
            <DialogDescription>
              Provide your price and a message explaining your offer.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitOffer}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="offerAmount" className="text-left">
                  Your Price (£)
                </Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <PoundSterling className="h-4 w-4 text-gray-500" />
                  </div>
                  <Input
                    id="offerAmount"
                    type="number"
                    step="0.01"
                    min="1"
                    value={offerAmount}
                    onChange={(e) => setOfferAmount(e.target.value)}
                    className="pl-9"
                    required
                  />
                </div>
                {task?.budget && (
                  <p className="text-xs text-gray-500">
                    Task budget: £{parseFloat(task.budget).toFixed(2)}
                  </p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="offerMessage" className="text-left">
                  Message
                </Label>
                <Textarea
                  id="offerMessage"
                  value={offerMessage}
                  onChange={(e) => setOfferMessage(e.target.value)}
                  placeholder="Explain your price and what you can offer..."
                  rows={4}
                  required
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowOfferDialog(false)}>
                Cancel
              </Button>
              <Button type="submit">
                {userOffer ? 'Update Offer' : 'Submit Offer'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Header with back button */}
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="icon" onClick={handleBack} className="mr-2">
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-xl font-semibold">Task Actions</h1>
      </div>

      {/* Error state */}
      {error && (
        <Card className="mb-6">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p className="text-red-500">{error}</p>
            <Button variant="outline" className="mt-3" onClick={fetchTask}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Loading state */}
      {loading && (
        <div className="space-y-4">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className="h-32 w-full" />
        </div>
      )}

      {/* Task content */}
      {!loading && task && (
        <>
          {/* Task info */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <h2 className="text-lg font-semibold mb-2">{task.title}</h2>
              <div className="flex items-center mb-4">
                <Badge className="text-sm capitalize">
                  {task.status || 'Open'}
                </Badge>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                {task.description || 'No description provided'}
              </p>
            </CardContent>
          </Card>

          {/* Available actions */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium mb-2">Available Actions</h3>

            {getAvailableActions().length === 0 ? (
              <Card>
                <CardContent className="p-4 text-center">
                  <p className="text-gray-500">No actions available for this task</p>
                </CardContent>
              </Card>
            ) : (
              getAvailableActions().map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant}
                  className="w-full justify-start text-left h-auto py-3"
                  onClick={() => handleTaskAction(action.action)}
                  disabled={actionLoading}
                >
                  {actionLoading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    action.icon
                  )}
                  {action.label}
                </Button>
              ))
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default PWATaskActionsClean;