import React from 'react';
import { useParams } from 'react-router-dom';
import PWATaskActionsClean from './PWATaskActionsClean';
import PWAMobileLayout from './PWAMobileLayout';

/**
 * A wrapper component for PWATaskActionsClean to ensure it's properly mounted
 * This helps isolate any issues with the PWATaskActions component
 */
const PWATaskActionsCleanWrapper: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  
  if (process.env.NODE_ENV === 'development') {
    console.log('[PWATaskActionsCleanWrapper] Rendering with task ID: completed');
  }
  return (
    <PWAMobileLayout>
      <PWATaskActionsClean />
    </PWAMobileLayout>
  );
};

export default PWATaskActionsCleanWrapper;
