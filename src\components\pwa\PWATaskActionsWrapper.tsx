import React from 'react';
import { useParams } from 'react-router-dom';
import PWATaskActions from './PWATaskActions';
import PWAMobileLayout from './PWAMobileLayout';

/**
 * A wrapper component for PWATaskActions to ensure it's properly mounted
 * This helps isolate any issues with the PWATaskActions component
 */
const PWATaskActionsWrapper: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  
  if (process.env.NODE_ENV === 'development') {
    console.log('[PWATaskActionsWrapper] Rendering with task ID: completed');
  }
  return (
    <PWAMobileLayout>
      <PWATaskActions />
    </PWAMobileLayout>
  );
};

export default PWATaskActionsWrapper;
