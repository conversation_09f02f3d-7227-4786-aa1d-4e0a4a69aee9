import React, { useState } from 'react';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  Sheet<PERSON><PERSON>le,
  SheetFooter
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { X, Check, SlidersHorizontal } from 'lucide-react';

interface PWATaskFilterProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  categoryFilter: string;
  setCategoryFilter: (category: string) => void;
  minBudget: number | null;
  setMinBudget: (min: number | null) => void;
  maxBudget: number | null;
  setMaxBudget: (max: number | null) => void;
  locationFilter: string;
  setLocationFilter: (location: string) => void;
  onApplyFilters: () => void;
}

const PWATaskFilter: React.FC<PWATaskFilterProps> = ({
  open,
  onOpenChange,
  categoryFilter,
  setCategoryFilter,
  minBudget,
  setMinBudget,
  maxBudget,
  setMaxBudget,
  locationFilter,
  setLocationFilter,
  onApplyFilters
}) => {
  // Local state for the filter form
  const [localCategory, setLocalCategory] = useState(categoryFilter);
  const [localMinBudget, setLocalMinBudget] = useState<number | null>(minBudget);
  const [localMaxBudget, setLocalMaxBudget] = useState<number | null>(maxBudget);
  const [localLocation, setLocalLocation] = useState(locationFilter);

  // Reset filters
  const handleReset = () => {
    setLocalCategory('all');
    setLocalMinBudget(null);
    setLocalMaxBudget(null);
    setLocalLocation('');
  };

  // Apply filters
  const handleApply = () => {
    setCategoryFilter(localCategory);
    setMinBudget(localMinBudget);
    setMaxBudget(localMaxBudget);
    setLocationFilter(localLocation);
    onApplyFilters();
    onOpenChange(false);
  };

  // Handle budget range input
  const handleBudgetMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value === '' ? null : Number(e.target.value);
    setLocalMinBudget(value);
  };

  const handleBudgetMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value === '' ? null : Number(e.target.value);
    setLocalMaxBudget(value);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-full sm:max-w-md p-0 flex flex-col h-full">
        {/* Fixed Header */}
        <div className="p-6 border-b">
          <SheetHeader>
            <SheetTitle className="flex items-center">
              <SlidersHorizontal className="h-5 w-5 mr-2" />
              Filter Tasks
            </SheetTitle>
            <SheetDescription>
              Filter tasks by category, budget, and location
            </SheetDescription>
          </SheetHeader>
        </div>

        {/* Scrollable Content Area */}
        <div className="flex-1 overflow-y-auto p-6 pb-32">
          <div className="space-y-6">
            {/* Category filter */}
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={localCategory}
                onValueChange={setLocalCategory}
              >
                <SelectTrigger id="category">
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="Caretaking">Caretaking</SelectItem>
                  <SelectItem value="Cleaning">Cleaning</SelectItem>
                  <SelectItem value="Plumbing">Plumbing</SelectItem>
                  <SelectItem value="Electrical">Electrical</SelectItem>
                  <SelectItem value="Carpentry">Carpentry</SelectItem>
                  <SelectItem value="Flooring">Flooring</SelectItem>
                  <SelectItem value="Glazing">Glazing</SelectItem>
                  <SelectItem value="Painting">Painting</SelectItem>
                  <SelectItem value="Heating">Heating</SelectItem>
                  <SelectItem value="AC unit">AC unit</SelectItem>
                  <SelectItem value="Intruder Alarm">Intruder Alarm</SelectItem>
                  <SelectItem value="Fire System">Fire System</SelectItem>
                  <SelectItem value="Lift (DDA)">Lift (DDA)</SelectItem>
                  <SelectItem value="Automatic Door">Automatic Door</SelectItem>
                  <SelectItem value="Grounds">Grounds</SelectItem>
                  <SelectItem value="Car Park">Car Park</SelectItem>
                  <SelectItem value="Health & Safety">Health & Safety</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Budget filter */}
            <div className="space-y-2">
              <Label>Budget Range (£)</Label>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  placeholder="Min"
                  value={localMinBudget === null ? '' : localMinBudget}
                  onChange={handleBudgetMinChange}
                  min={0}
                  className="w-1/2"
                />
                <span>to</span>
                <Input
                  type="number"
                  placeholder="Max"
                  value={localMaxBudget === null ? '' : localMaxBudget}
                  onChange={handleBudgetMaxChange}
                  min={0}
                  className="w-1/2"
                />
              </div>
            </div>

            {/* Location filter */}
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                placeholder="Enter location"
                value={localLocation}
                onChange={(e) => setLocalLocation(e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Fixed Footer */}
        <div className="p-6 border-t bg-background absolute bottom-0 left-0 right-0">
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={handleReset}
              className="w-full sm:w-auto"
            >
              <X className="h-4 w-4 mr-2" />
              Reset Filters
            </Button>
            <Button
              onClick={handleApply}
              className="w-full sm:flex-1"
            >
              <Check className="h-4 w-4 mr-2" />
              Apply Filters
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default PWATaskFilter;
