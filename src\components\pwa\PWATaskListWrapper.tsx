import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { SlidersHorizontal } from 'lucide-react';
import PWAMobileLayout from './PWAMobileLayout';
import PWATaskList from './PWATaskList';

/**
 * Wrapper component for PWATaskList that manages the filter button in the header
 * This allows the filter icon to be positioned in the header next to notifications
 * while maintaining the filter state and functionality
 */
const PWATaskListWrapper: React.FC = () => {
  const [showStatusFilter, setShowStatusFilter] = useState(false);
  const [taskTypeFilter, setTaskTypeFilter] = useState<'compliance' | 'internal' | 'external'>('internal');
  const [adminReviewCount, setAdminReviewCount] = useState(0);
  const [isAdminView, setIsAdminView] = useState(false);
  const [complianceFilter, setComplianceFilter] = useState<'all' | 'overdue' | 'pending' | 'completed'>('all');

  // Create filter button for header (now shows for all tabs)
  const filterButton = (
    <div className="relative">
      <Button
        variant="outline"
        size="icon"
        onClick={() => setShowStatusFilter(true)}
        className="rounded-full hover:bg-gray-100 active:bg-gray-200 transition-colors"
        style={{ minWidth: '40px', minHeight: '40px' }}
      >
        <SlidersHorizontal className="h-4 w-4" />
      </Button>
      {/* Show badge for admin review count (internal/external) or compliance filter indicator */}
      {taskTypeFilter !== 'compliance' && isAdminView && adminReviewCount > 0 && (
        <div className="absolute -top-2 -right-2 bg-purple-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
          {adminReviewCount}
        </div>
      )}
      {taskTypeFilter === 'compliance' && complianceFilter !== 'all' && (
        <div className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-3 w-3"></div>
      )}
    </div>
  );

  return (
    <PWAMobileLayout headerAction={filterButton}>
      <PWATaskList
        showStatusFilter={showStatusFilter}
        setShowStatusFilter={setShowStatusFilter}
        taskTypeFilter={taskTypeFilter}
        setTaskTypeFilter={setTaskTypeFilter}
        adminReviewCount={adminReviewCount}
        setAdminReviewCount={setAdminReviewCount}
        isAdminView={isAdminView}
        setIsAdminView={setIsAdminView}
        complianceFilter={complianceFilter}
        setComplianceFilter={setComplianceFilter}
      />
    </PWAMobileLayout>
  );
};

export default PWATaskListWrapper;
