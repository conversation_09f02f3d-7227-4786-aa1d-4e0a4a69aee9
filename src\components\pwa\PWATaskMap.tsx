import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { loadGoogleMapsApi, isGoogleMapsLoaded } from '@/utils/pwa-google-maps';

// Define the Task interface
interface Task {
  id: string;
  title: string;
  description: string;
  status: string;
  budget: number | string; // Handle both number and string formats
  due_date: string;
  created_at: string;
  location: string;
  category: string;
  location_lat?: number;
  location_lng?: number;
  building?: string;
  room?: string;
  visibility: string;
  user_id: string;
  organization_id?: string;
  type?: string;
}

interface PWATaskMapProps {
  tasks: Task[];
  visible?: boolean;
}

const PWATaskMap: React.FC<PWATaskMapProps> = ({ tasks, visible = true }) => {
  const navigate = useNavigate();
  const mapRef = useRef<HTMLDivElement>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [infoWindow, setInfoWindow] = useState<google.maps.InfoWindow | null>(null);

  // Debug environment variables (development only)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWATaskMap] Environment variables check:');
        }
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWATaskMap] VITE_GOOGLE_MAPS_API_KEY:', import.meta.env.VITE_GOOGLE_MAPS_API_KEY || 'not found');
        }
    }
  }, []);

  // Initialize Google Maps
  useEffect(() => {
    // Only initialize if component is visible
    if (!visible) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWATaskMap] Not initializing map - component not visible');
        }
      return;
    }

    if (process.env.NODE_ENV === 'development') {

      console.log('[PWATaskMap] Initializing Google Maps');


      }
    // Function to initialize the map once the API is loaded
    const initializeMap = () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWATaskMap] API loaded, initializing map');

        }
      // Double-check visibility before initializing
      if (!visible) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWATaskMap] Component no longer visible, skipping initialization');
          }
        return;
      }

      if (mapRef.current && !map && isGoogleMapsLoaded()) {
        try {
          if (process.env.NODE_ENV === 'development') {
            console.log('[PWATaskMap] Creating map instance');
            }
          // Default center (UK)
          const defaultCenter = { lat: 54.093409, lng: -2.89479 };

          // Create the map
          const mapInstance = new window.google.maps.Map(mapRef.current, {
            center: defaultCenter,
            zoom: 6,
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false,
          });

          // Create info window
          const infoWindowInstance = new window.google.maps.InfoWindow();

          // Set state
          setMap(mapInstance);
          setInfoWindow(infoWindowInstance);
          setMapLoaded(true);
          if (process.env.NODE_ENV === 'development') {
            console.log('[PWATaskMap] Map initialized successfully');
            }
        } catch (error) {
          console.error('[PWATaskMap] Error initializing map:', error);
        }
      } else if (!isGoogleMapsLoaded()) {
        console.error('[PWATaskMap] Google Maps API still not loaded after callback');
      } else if (!mapRef.current) {
        console.error('[PWATaskMap] Map container ref is not available');
      } else if (map) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWATaskMap] Map already initialized');
          }
      }
    };

    // Check if the API is already loaded
    if (isGoogleMapsLoaded()) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWATaskMap] Google Maps API already loaded');
        }
      initializeMap();
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWATaskMap] Loading Google Maps API');
        }
      // Load the API and initialize the map when it's ready
      loadGoogleMapsApi(initializeMap);
    }

    // Cleanup function
    return () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWATaskMap] Component unmounting, cleaning up');

        }
      try {
        // Clear markers first - safely
        if (markers && markers.length > 0) {
          if (process.env.NODE_ENV === 'development') {
            console.log('[PWATaskMap] Clearing markers');
            }
          markers.forEach(marker => {
            try {
              // Check if marker is valid
              if (marker && typeof marker.setMap === 'function') {
                // Remove all event listeners
                if (window.google && window.google.maps) {
                  window.google.maps.event.clearInstanceListeners(marker);
                }
                // Set map to null to remove from map
                marker.setMap(null);
              }
            } catch (markerError) {
              console.error('[PWATaskMap] Error cleaning up marker:', markerError);
              // Continue with other markers even if one fails
            }
          });
        }

        // Close info window if open
        if (infoWindow && typeof infoWindow.close === 'function') {
          if (process.env.NODE_ENV === 'development') {
            console.log('[PWATaskMap] Closing info window');
            }
          infoWindow.close();
        }
      } catch (cleanupError) {
        console.error('[PWATaskMap] Error during cleanup:', cleanupError);
      } finally {
        // Always clear state, even if cleanup fails
        setMarkers([]);
        setSelectedTask(null);
        setInfoWindow(null);
        setMap(null);
      }
    };
  }, [mapRef, markers, infoWindow, visible]);

  // Handle visibility changes
  useEffect(() => {
    if (visible) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWATaskMap] Component is now visible');

        }
      if (map) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWATaskMap] Map exists, triggering resize');
          }
        // When becoming visible, trigger resize to fix display issues
        setTimeout(() => {
          if (map && window.google && window.google.maps) {
            try {
              window.google.maps.event.trigger(map, 'resize');
              if (process.env.NODE_ENV === 'development') {
                console.log('[PWATaskMap] Resize event triggered successfully');
                }
            } catch (error) {
              console.error('[PWATaskMap] Error triggering resize:', error);
            }
          }
        }, 100);
      } else if (isGoogleMapsLoaded() && mapRef.current) {
        // If map doesn't exist but API is loaded, initialize map
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWATaskMap] Map doesn\'t exist but API is loaded, initializing map');
          }
        try {
          // Default center (UK)
          const defaultCenter = { lat: 54.093409, lng: -2.89479 };

          // Create the map
          const mapInstance = new window.google.maps.Map(mapRef.current, {
            center: defaultCenter,
            zoom: 6,
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false,
          });

          // Create info window
          const infoWindowInstance = new window.google.maps.InfoWindow();

          // Set state
          setMap(mapInstance);
          setInfoWindow(infoWindowInstance);
          setMapLoaded(true);
          if (process.env.NODE_ENV === 'development') {
            console.log('[PWATaskMap] Map initialized successfully on visibility change');
            }
        } catch (error) {
          console.error('[PWATaskMap] Error initializing map on visibility change:', error);
        }
      }
    }
  }, [visible, map, mapRef]);

  // Add markers for tasks with location data
  useEffect(() => {
    // Skip if component is unmounting, not visible, or map isn't ready
    if (!visible || !map || !infoWindow || !isGoogleMapsLoaded() || !mapRef.current) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWATaskMap] Map not ready, not visible, or API not loaded');
        }
      return;
    }

    if (process.env.NODE_ENV === 'development') {

      console.log('[PWATaskMap] Adding markers for tasks');


      }
    try {
      // Clear existing markers safely
      markers.forEach(marker => {
        // Remove all event listeners
        if (window.google && window.google.maps) {
          window.google.maps.event.clearInstanceListeners(marker);
        }
        // Set map to null to remove from map
        marker.setMap(null);
      });

      // Create new markers
      const newMarkers: google.maps.Marker[] = [];
      const bounds = new window.google.maps.LatLngBounds();
      let hasValidLocations = false;

      tasks.forEach(task => {
        // Skip tasks without location data
        if (!task.location_lat || !task.location_lng) {
          if (process.env.NODE_ENV === 'development') {
            console.log(`[PWATaskMap] Task ${task.id} has no location data, skipping`);
            }
          return;
        }

        const position = {
          lat: task.location_lat,
          lng: task.location_lng
        };

        if (process.env.NODE_ENV === 'development') {

          console.log(`[PWATaskMap] Adding marker for task ${task.id} at ${position.lat}, ${position.lng}`);


          }
        // Create marker
        const marker = new window.google.maps.Marker({
          position,
          map,
          title: task.title,
          animation: window.google.maps.Animation.DROP
        });

        // Add click listener
        marker.addListener('click', () => {
          setSelectedTask(task);

          // Set info window content
          const budget = typeof task.budget === 'number'
            ? task.budget.toFixed(2)
            : parseFloat(task.budget as string).toFixed(2);

          infoWindow.setContent(`
            <div style="max-width: 200px;">
              <h3 style="font-weight: bold; margin-bottom: 5px;">${task.title}</h3>
              <p style="margin-bottom: 5px;">${task.location}</p>
              <p style="font-weight: bold; color: green;">£${budget}</p>
            </div>
          `);

          infoWindow.open(map, marker);
        });

        newMarkers.push(marker);
        bounds.extend(position);
        hasValidLocations = true;
      });

      setMarkers(newMarkers);

      // Fit bounds if we have valid locations
      if (hasValidLocations && newMarkers.length > 0) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[PWATaskMap] Fitting bounds to ${newMarkers.length} markers`);
          }
        map.fitBounds(bounds);

        // Don't zoom in too far
        const listener = window.google.maps.event.addListener(map, 'idle', () => {
          if (map.getZoom() > 16) {
            map.setZoom(16);
          }
          window.google.maps.event.removeListener(listener);
        });
      } else {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWATaskMap] No valid locations to show on map');
          }
      }
    } catch (error) {
      console.error('[PWATaskMap] Error adding markers:', error);
    }

    // Return cleanup function for this effect
    return () => {
      try {
        // Only clean up if we still have access to Google Maps API
        if (isGoogleMapsLoaded()) {
          if (process.env.NODE_ENV === 'development') {
            console.log('[PWATaskMap] Cleaning up markers effect');
            }
          markers.forEach(marker => {
            try {
              if (marker && typeof marker.setMap === 'function') {
                if (window.google && window.google.maps) {
                  window.google.maps.event.clearInstanceListeners(marker);
                }
                marker.setMap(null);
              }
            } catch (markerError) {
              console.error('[PWATaskMap] Error cleaning up marker in effect:', markerError);
            }
          });
        }
      } catch (cleanupError) {
        console.error('[PWATaskMap] Error during markers effect cleanup:', cleanupError);
      }
    };
  }, [map, infoWindow, tasks, mapLoaded, visible]);

  // Handle task selection
  const handleTaskSelect = (taskId: string) => {
    navigate(`/tasks/${taskId}`);
  };

  // If no tasks have location data
  const tasksWithLocation = tasks.filter(task => task.location_lat && task.location_lng);

  if (tasksWithLocation.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-gray-500 mb-2">No tasks with location data available</p>
          <p className="text-sm text-gray-400">
            Tasks need latitude and longitude data to appear on the map
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Map container */}
      <div
        ref={mapRef}
        className="w-full h-[400px] rounded-md border"
        style={{ minHeight: '400px' }}
      >
        {!mapLoaded && (
          <div className="w-full h-full flex flex-col items-center justify-center bg-gray-100 rounded-md">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400 mb-2" />
            <p className="text-sm text-gray-500">Loading Google Maps...</p>
          </div>
        )}
      </div>

      {/* Selected task info */}
      {selectedTask && (
        <Card>
          <CardContent className="p-4">
            <h3 className="font-medium mb-1">{selectedTask.title}</h3>
            <p className="text-sm text-gray-500 mb-2">{selectedTask.location}</p>
            <p className="text-sm line-clamp-2 mb-3">{selectedTask.description}</p>
            <div className="flex justify-between items-center">
              <div className="font-medium text-green-700">
                £{typeof selectedTask.budget === 'number'
                  ? selectedTask.budget.toFixed(2)
                  : parseFloat(selectedTask.budget as string).toFixed(2)}
              </div>
              <Button
                size="sm"
                onClick={() => handleTaskSelect(selectedTask.id)}
              >
                View Details
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PWATaskMap;
