import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  ArrowLeft,
  MessageSquare,
  Calendar,
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  FileText,
  WifiOff,
  RefreshCw
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { isOnline, isPWA, getCachedTasks, registerConnectivityListeners } from '@/utils/pwa-utils';
import PWAMobileLayout from './PWAMobileLayout';

const PWATaskView: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [task, setTask] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('details');
  const [offlineMode, setOfflineMode] = useState(!isOnline());

  // Effect to handle online/offline status
  useEffect(() => {
    const cleanup = registerConnectivityListeners(
      // Online callback
      () => {
        setOfflineMode(false);
        fetchTask(); // Refresh data when coming back online
      },
      // Offline callback
      () => {
        setOfflineMode(true);
      }
    );

    return cleanup;
  }, []);

  // Fetch task data
  const fetchTask = async () => {
    setLoading(true);
    setError(null);

    try {
      if (!id) {
        setError('Task ID is missing');
        return;
      }

      console.log('[PWATaskView] Fetching task with ID:', id);

      // If offline and PWA, use cached data
      if (!isOnline() && isPWA()) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWATaskView] Using cached data in offline mode');
        }

        // Get cached tasks
        const cachedTasks = getCachedTasks();
        const cachedTask = cachedTasks.find(t => t.id === id);

        if (cachedTask) {
          setTask(cachedTask);
        } else {
          setError('Task not found in offline cache');
        }
      } else {
        // Fetch task from API - without trying to join profiles directly
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('*, getstream_channel_id, chat_migrated_to_stream')
          .eq('id', id)
          .single();

        if (taskError) {
          console.error('[PWATaskView] Error fetching task:', taskError);
          throw taskError;
        }

        if (!taskData) {
          setError('Task not found');
          return;
        }

        console.log('[PWATaskView] Task data retrieved:', taskData);

        // Now fetch the creator's profile separately
        let creatorProfile = null;
        if (taskData.user_id) {
          const { data: creatorData, error: creatorError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', taskData.user_id)
            .maybeSingle();

          if (creatorError) {
            console.error('[PWATaskView] Error fetching creator profile:', creatorError);
          } else if (creatorData) {
            creatorProfile = creatorData;
          }
        }

        // Fetch the assignee's profile separately
        let assigneeProfile = null;
        if (taskData.assigned_to) {
          const { data: assigneeData, error: assigneeError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', taskData.assigned_to)
            .maybeSingle();

          if (assigneeError) {
            console.error('[PWATaskView] Error fetching assignee profile:', assigneeError);
          } else if (assigneeData) {
            assigneeProfile = assigneeData;
          }
        }

        // Combine the data
        const data = {
          ...taskData,
          created_by_profile: creatorProfile,
          assigned_to_profile: assigneeProfile
        };

        // No need to check for error here as we've already handled errors above

        if (process.env.NODE_ENV === 'development') {
          console.log('[PWATaskView] Task data retrieved:', data);
        }

        if (data) {
          // Format the task data for display
          const formattedTask = {
            ...data,
            created_by_profile: data.created_by_user,
            assigned_to_profile: data.assigned_to_user
          };
          setTask(formattedTask);
        } else {
          setError('Task not found');
        }
      }
    } catch (error: any) {
      console.error('[PWATaskView] Error fetching task:', error);
      setError(error.message || 'Failed to load task');
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchTask();
  }, [id]);

  // Handle back button
  const handleBack = () => {
    navigate(-1);
  };

  // Handle chat button
  const handleChat = () => {
    try {
      console.log('[PWATaskView] Handling chat button click for task:', task?.id);

      // For PWA, we'll use the GetStream channel ID if available
      // If not, we'll use the task ID as the channel ID (format: task-{taskId})
      const channelId = task?.getstream_channel_id || `task-${task?.id}`;

      if (channelId) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWATaskView] Navigating to chat with channelId:', channelId);
        }

        // Make sure we're using the correct channel ID format
        // This ensures consistency between desktop, mobile, and PWA
        navigate(`/messages/${channelId}?task=${task.id}`);

        // Log additional debug info (development only)
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWATaskView] Navigation details:', {
            url: `/messages/${channelId}?task=${task.id}`,
            channelId,
            taskId: task.id,
            hasGetStreamChannelId: !!task?.getstream_channel_id,
            chatMigratedToStream: task?.chat_migrated_to_stream
          });
        }
      } else {
        console.error('[PWATaskView] No channel ID available for chat');
        setError('Cannot open chat - no channel ID available');
      }
    } catch (error) {
      console.error('[PWATaskView] Error in handleChat:', error);
      setError('Failed to open chat');
    }
  };

  // Get status badge variant
  const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'closed':
      case 'finished':
        return 'default';
      case 'in progress':
      case 'accepted':
        return 'secondary';
      case 'pending':
      case 'offered':
        return 'outline';
      case 'rejected':
      case 'cancelled':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Get initials for avatar
  const getInitials = (name: string): string => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Format date
  const formatDate = (dateString: string): string => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <PWAMobileLayout>
      <div className="container max-w-md mx-auto px-4 py-6">
        {/* Header with back button */}
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={handleBack} className="mr-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-semibold">Task Details</h1>
        </div>

        {/* Offline indicator */}
        {offlineMode && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4 flex items-center">
            <WifiOff className="h-5 w-5 text-yellow-500 mr-2" />
            <div className="text-sm text-yellow-700">
              You're offline. Some data may not be up to date.
            </div>
          </div>
        )}

        {/* Error state */}
        {error && (
          <Card className="mb-6">
            <CardContent className="p-6 text-center">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="text-red-500">{error}</p>
              <Button variant="outline" className="mt-3" onClick={fetchTask}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Loading state */}
        {loading && (
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-6 w-1/3" />
            <Skeleton className="h-32 w-full" />
            <div className="flex justify-between">
              <Skeleton className="h-6 w-1/3" />
              <Skeleton className="h-6 w-1/3" />
            </div>
          </div>
        )}

        {/* Task content */}
        {!loading && task && (
          <>
            {/* Task header */}
            <div className="mb-6">
              <h2 className="text-2xl font-bold mb-2">{task.title}</h2>
              <div className="flex items-center justify-between mb-4">
                <Badge variant={getStatusVariant(task.status)} className="text-sm">
                  {task.status}
                </Badge>
                <div className="text-sm text-gray-500">
                  Created: {formatDate(task.created_at)}
                </div>
              </div>
            </div>

            {/* Tabs */}
            <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab} className="mb-6">
              <TabsList className="grid grid-cols-3">
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="people">People</TabsTrigger>
                <TabsTrigger value="timeline">Timeline</TabsTrigger>
              </TabsList>

              {/* Details tab */}
              <TabsContent value="details" className="mt-4 space-y-4">
                <Card>
                  <CardContent className="p-4">
                    <h3 className="font-medium mb-2 flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-gray-500" />
                      Description
                    </h3>
                    <p className="text-gray-700 text-sm whitespace-pre-line">
                      {task.description || 'No description provided'}
                    </p>
                  </CardContent>
                </Card>

                <div className="grid grid-cols-2 gap-3">
                  <Card>
                    <CardContent className="p-4">
                      <h3 className="font-medium mb-2 flex items-center text-sm">
                        <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                        Due Date
                      </h3>
                      <p className="text-gray-700">
                        {formatDate(task.due_date) || 'Not specified'}
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <h3 className="font-medium mb-2 flex items-center text-sm">
                        <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                        Location
                      </h3>
                      <p className="text-gray-700">
                        {task.location || 'Not specified'}
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <h3 className="font-medium mb-2 flex items-center text-sm">
                        <Clock className="h-4 w-4 mr-2 text-gray-500" />
                        Estimated Time
                      </h3>
                      <p className="text-gray-700">
                        {task.estimated_hours ? `${task.estimated_hours} hours` : 'Not specified'}
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <h3 className="font-medium mb-2 flex items-center text-sm">
                        <CheckCircle className="h-4 w-4 mr-2 text-gray-500" />
                        Priority
                      </h3>
                      <p className="text-gray-700">
                        {task.priority || 'Normal'}
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* People tab */}
              <TabsContent value="people" className="mt-4 space-y-4">
                <Card>
                  <CardContent className="p-4">
                    <h3 className="font-medium mb-3">Created By</h3>
                    <div className="flex items-center">
                      <Avatar className="h-10 w-10 mr-3">
                        <AvatarFallback>
                          {getInitials(task.created_by_profile?.first_name + ' ' + task.created_by_profile?.last_name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">
                          {task.created_by_profile?.first_name} {task.created_by_profile?.last_name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {task.created_by_profile?.email?.[0] || ''}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <h3 className="font-medium mb-3">Assigned To</h3>
                    {task.assigned_to_profile ? (
                      <div className="flex items-center">
                        <Avatar className="h-10 w-10 mr-3">
                          <AvatarFallback>
                            {getInitials(task.assigned_to_profile?.first_name + ' ' + task.assigned_to_profile?.last_name)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">
                            {task.assigned_to_profile?.first_name} {task.assigned_to_profile?.last_name}
                          </p>
                          <p className="text-sm text-gray-500">
                            {task.assigned_to_profile?.email?.[0] || ''}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-500">Not assigned</p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Timeline tab */}
              <TabsContent value="timeline" className="mt-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div className="flex">
                        <div className="mr-3 flex flex-col items-center">
                          <div className="h-6 w-6 rounded-full bg-green-100 flex items-center justify-center">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          </div>
                          <div className="w-0.5 h-full bg-gray-200 mt-1"></div>
                        </div>
                        <div>
                          <p className="font-medium">Task Created</p>
                          <p className="text-sm text-gray-500">{formatDate(task.created_at)}</p>
                        </div>
                      </div>

                      {task.status !== 'open' && (
                        <div className="flex">
                          <div className="mr-3 flex flex-col items-center">
                            <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                              <User className="h-4 w-4 text-blue-600" />
                            </div>
                            <div className="w-0.5 h-full bg-gray-200 mt-1"></div>
                          </div>
                          <div>
                            <p className="font-medium">Task Assigned</p>
                            <p className="text-sm text-gray-500">{formatDate(task.assigned_at || task.updated_at)}</p>
                          </div>
                        </div>
                      )}

                      {task.status === 'completed' && (
                        <div className="flex">
                          <div className="mr-3 flex flex-col items-center">
                            <div className="h-6 w-6 rounded-full bg-green-100 flex items-center justify-center">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            </div>
                            <div className="w-0.5 h-full bg-gray-200 mt-1"></div>
                          </div>
                          <div>
                            <p className="font-medium">Task Completed</p>
                            <p className="text-sm text-gray-500">{formatDate(task.completed_at || task.updated_at)}</p>
                          </div>
                        </div>
                      )}

                      {task.status === 'closed' && (
                        <div className="flex">
                          <div className="mr-3 flex flex-col items-center">
                            <div className="h-6 w-6 rounded-full bg-green-100 flex items-center justify-center">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            </div>
                            <div className="w-0.5 h-full bg-gray-200 mt-1"></div>
                          </div>
                          <div>
                            <p className="font-medium">Task Completed</p>
                            <p className="text-sm text-gray-500">{formatDate(task.completed_at || task.updated_at)}</p>
                          </div>
                        </div>
                      )}

                      {task.status === 'closed' && (
                        <div className="flex">
                          <div className="mr-3 flex flex-col items-center">
                            <div className="h-6 w-6 rounded-full bg-teal-100 flex items-center justify-center">
                              <CheckCircle className="h-4 w-4 text-teal-600" />
                            </div>
                          </div>
                          <div>
                            <p className="font-medium">Task Closed</p>
                            <p className="text-sm text-gray-500">{formatDate(task.updated_at)}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Action buttons */}
            <div className="flex gap-3">
              <Button
                variant="outline"
                className="flex-1"
                onClick={handleChat}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Chat
              </Button>

              <Button
                variant="default"
                className="flex-1"
                onClick={() => navigate(`/tasks/${id}/actions`)}
              >
                Actions
              </Button>
            </div>
          </>
        )}
      </div>
    </PWAMobileLayout>
  );
};

export default PWATaskView;
