import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { isPWA } from '@/utils/pwa-utils';

// App version - update this when deploying new versions
const APP_VERSION = '1.5.1';

interface PWAVersionIndicatorProps {
  className?: string;
}

const PWAVersionIndicator: React.FC<PWAVersionIndicatorProps> = ({ className = '' }) => {
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    // Check if app is installed as PWA
    setIsInstalled(isPWA());

    // Listen for service worker updates
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        setUpdateAvailable(true);
      });
    }
  }, []);

  const handleUpdate = () => {
    // Reload the page to apply the update
    window.location.reload();
  };

  if (!isInstalled && !updateAvailable) {
    // Only show version number if not installed as PWA
    return (
      <Badge variant="outline" className={`text-xs ${className}`}>
        v{APP_VERSION}
      </Badge>
    );
  }

  if (updateAvailable) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={handleUpdate}
        className={`text-xs animate-pulse ${className}`}
      >
        <RefreshCw className="h-3 w-3 mr-1" />
        Update Available
      </Button>
    );
  }

  return (
    <Badge variant="outline" className={`text-xs ${className}`}>
      PWA v{APP_VERSION}
    </Badge>
  );
};

export default PWAVersionIndicator;
