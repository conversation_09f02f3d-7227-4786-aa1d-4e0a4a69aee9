import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle, CheckCircle2, ExternalLink } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface ConnectOnboardingProps {
  onComplete?: () => void;
}

export default function ConnectOnboarding({ onComplete }: ConnectOnboardingProps) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Use the correct API URL for Stripe Connect API
  const apiUrl = import.meta.env.VITE_STRIPE_API_URL || 'http://localhost:3001';
  if (process.env.NODE_ENV === 'development') {

    console.log('Using Stripe API URL:', apiUrl);

    }
  const [accountStatus, setAccountStatus] = useState<{
    id?: string;
    account_id?: string;
    account_status?: string;
    charges_enabled?: boolean;
    payouts_enabled?: boolean;
  } | null>(null);

  useEffect(() => {
    if (user) {
      fetchAccountStatus();
    }
  }, [user]);

  const fetchAccountStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if the user already has a Stripe account (excluding deleted accounts)
      const { data, error } = await supabase
        .from('stripe_accounts')
        .select('*')
        .eq('user_id', user?.id)
        .neq('account_status', 'deleted')
        .maybeSingle();

      if (error) {
        throw error;
      }

      if (data) {
        if (process.env.NODE_ENV === 'development') {

          console.log('Found Stripe account in database:', data);

          }
        setAccountStatus(data);

        // If the account exists but is not fully onboarded, refresh the status from API
        if (data.account_id && (!data.charges_enabled || !data.payouts_enabled)) {
          try {
            const response = await fetch(`${apiUrl}/api/stripe-connect/account-status/${data.account_id}`);

            if (response.ok) {
              const status = await response.json();
              if (status) {
                if (process.env.NODE_ENV === 'development') {

                  console.log('Got updated status from API:', status);

                  }
                setAccountStatus(prev => ({ ...prev, ...status }));
              }
            }
          } catch (statusError) {
            console.error('Error fetching account status from API:', statusError);
            // Continue with the existing data even if the status update fails
          }
        }
      } else {
        // No Stripe account found
        if (process.env.NODE_ENV === 'development') {

          console.log('No Stripe account found');

          }
        setAccountStatus(null);
      }
    } catch (err) {
      console.error('Error fetching account status:', err);
      setError('Failed to fetch account status. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAccount = async () => {
    try {
      setLoading(true);
      setError(null);

      if (process.env.NODE_ENV === 'development') {


        console.log('Starting account creation process...');


        }
      if (process.env.NODE_ENV === 'development') {

        console.log('User:', user ? `Authenticated (ID: ${user.id}.replace(/user.*/, 'hasUser: ' + !!user))` : 'Not authenticated');


        }
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Create a Stripe Connect Express account via API
      if (process.env.NODE_ENV === 'development') {

        console.log('Calling API to create Express account...');


        }
      // Use the real Stripe API via our server
      const response = await fetch(`${apiUrl}/api/stripe-connect/create-account`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          email: user.email || '<EMAIL>' // Use the user's email or <NAME_EMAIL>
        }),
      });

      if (!response.ok) {
        // Clone the response before reading it
        const responseClone = response.clone();
        try {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create Stripe account');
        } catch (jsonError) {
          // If the response is not valid JSON, get the text content from the clone
          try {
            const errorText = await responseClone.text();
            console.error('Non-JSON error response:', errorText);
          } catch (textError) {
            console.error('Failed to read response as text:', textError);
          }
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
      }

      const data = await response.json();
      const accountId = data.accountId;

      if (process.env.NODE_ENV === 'development') {


        console.log('Result from API:', data);



        }
      if (!accountId) {
        throw new Error('Failed to create Stripe account');
      }

      // Fetch the account status
      const statusResponse = await fetch(`${apiUrl}/api/stripe-connect/account-status/${accountId}`);
      if (!statusResponse.ok) {
        // Clone the response before reading it
        const responseClone = statusResponse.clone();
        try {
          const errorData = await statusResponse.json();
          throw new Error(errorData.error || 'Failed to get account status');
        } catch (jsonError) {
          // If the response is not valid JSON, get the text content from the clone
          try {
            const errorText = await responseClone.text();
            console.error('Non-JSON error response:', errorText);
          } catch (textError) {
            console.error('Failed to read response as text:', textError);
          }
          throw new Error(`Server error: ${statusResponse.status} ${statusResponse.statusText}`);
        }
      }

      const accountStatus = await statusResponse.json();
      setAccountStatus(accountStatus);
      if (process.env.NODE_ENV === 'development') {

        console.log('Account status set:', accountStatus);


        }
      // Generate an onboarding link via API
      if (process.env.NODE_ENV === 'development') {

        console.log('Generating onboarding link for account:', accountId);

        }
      if (process.env.NODE_ENV === 'development') {

        console.log('Using API URL:', apiUrl);


        }
      const onboardingUrl = `${apiUrl}/api/stripe-connect/onboarding-link`;
      if (process.env.NODE_ENV === 'development') {

        console.log('Full request URL:', onboardingUrl);


        }
      const linkResponse = await fetch(onboardingUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accountId }),
      });

      if (!linkResponse.ok) {
        // Clone the response before reading it
        const responseClone = linkResponse.clone();
        try {
          const errorData = await linkResponse.json();
          throw new Error(errorData.error || 'Failed to generate onboarding link');
        } catch (jsonError) {
          // If the response is not valid JSON, get the text content from the clone
          try {
            const errorText = await responseClone.text();
            console.error('Non-JSON error response:', errorText);
          } catch (textError) {
            console.error('Failed to read response as text:', textError);
          }
          throw new Error(`Server error: ${linkResponse.status} ${linkResponse.statusText}`);
        }
      }

      const linkData = await linkResponse.json();

      if (!linkData.url) {
        throw new Error('Failed to generate onboarding link');
      }

      // Redirect to the onboarding URL
      window.location.href = linkData.url;
    } catch (err) {
      console.error('Error creating Stripe account:', err);

      // Log more detailed error information
      if (err instanceof Error) {
        console.error('Error message:', err.message);
        console.error('Error stack:', err.stack);
      }

      // Set a more descriptive error message
      if (err instanceof Error) {
        setError(`Failed to create Stripe account: ${err.message}`);
      } else {
        setError('Failed to create Stripe account. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDashboardAccess = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!accountStatus?.id) {
        throw new Error('No Stripe account found');
      }

      // Generate a dashboard link via API
      if (process.env.NODE_ENV === 'development') {

        console.log('Generating dashboard link for account:', accountStatus.id);

        }
      if (process.env.NODE_ENV === 'development') {

        console.log('Using API URL:', apiUrl);


        }
      const dashboardUrl = `${apiUrl}/api/stripe-connect/dashboard-link`;
      if (process.env.NODE_ENV === 'development') {

        console.log('Full request URL:', dashboardUrl);


        }
      const response = await fetch(dashboardUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accountId: accountStatus.id }),
      });

      if (!response.ok) {
        // Clone the response before reading it
        const responseClone = response.clone();
        try {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to generate dashboard link');
        } catch (jsonError) {
          // If the response is not valid JSON, get the text content from the clone
          try {
            const errorText = await responseClone.text();
            console.error('Non-JSON error response:', errorText);
          } catch (textError) {
            console.error('Failed to read response as text:', textError);
          }
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
      }

      const data = await response.json();

      if (!data.url) {
        throw new Error('Failed to generate dashboard link');
      }

      // Check if this is an onboarding link (fallback for accounts not fully activated)
      if (data.isOnboarding) {
        if (process.env.NODE_ENV === 'development') {

          console.log('Received onboarding link instead of dashboard link (account not fully activated)');

          }
        // Redirect to the onboarding URL
        window.location.href = data.url;
      } else {
        // Open the dashboard in a new tab
        window.open(data.url, '_blank');
      }
    } catch (err) {
      console.error('Error accessing dashboard:', err);
      setError('Failed to access Stripe dashboard. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleContinueOnboarding = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!accountStatus?.id) {
        throw new Error('No Stripe account found');
      }

      // Generate an onboarding link via API
      const response = await fetch(`${apiUrl}/api/stripe-connect/onboarding-link`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accountId: accountStatus.id }),
      });

      if (!response.ok) {
        // Clone the response before reading it
        const responseClone = response.clone();
        try {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to generate onboarding link');
        } catch (jsonError) {
          // If the response is not valid JSON, get the text content from the clone
          try {
            const errorText = await responseClone.text();
            console.error('Non-JSON error response:', errorText);
          } catch (textError) {
            console.error('Failed to read response as text:', textError);
          }
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
      }

      const data = await response.json();

      if (!data.url) {
        throw new Error('Failed to generate onboarding link');
      }

      // Redirect to the onboarding URL
      window.location.href = data.url;
    } catch (err) {
      console.error('Error continuing onboarding:', err);
      setError('Failed to continue onboarding. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderAccountStatus = () => {
    if (process.env.NODE_ENV === 'development') {

      console.log('Rendering account status:', accountStatus);


      }
    if (!accountStatus) {
      return (
        <div className="text-center py-4">
          <p className="text-gray-500">You haven't connected a Stripe account yet.</p>
        </div>
      );
    }

    const isFullyOnboarded = accountStatus.charges_enabled && accountStatus.payouts_enabled;
    if (process.env.NODE_ENV === 'development') {

      console.log('Is fully onboarded:', isFullyOnboarded);


      }
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <span className="font-medium">Account Status:</span>
          <span className={`px-2 py-1 rounded-full text-xs ${
            isFullyOnboarded
              ? 'bg-green-100 text-green-800'
              : 'bg-yellow-100 text-yellow-800'
          }`}>
            {isFullyOnboarded ? 'Active' : 'Pending'}
          </span>
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <CheckCircle2
              className={isFullyOnboarded ? 'text-green-500' : 'text-gray-300'}
              size={18}
            />
            <span className={isFullyOnboarded ? 'text-green-700' : 'text-gray-500'}>
              Account verification complete
            </span>
          </div>

          <div className="flex items-center gap-2">
            <CheckCircle2
              className={accountStatus.charges_enabled ? 'text-green-500' : 'text-gray-300'}
              size={18}
            />
            <span className={accountStatus.charges_enabled ? 'text-green-700' : 'text-gray-500'}>
              Payment processing enabled
            </span>
          </div>

          <div className="flex items-center gap-2">
            <CheckCircle2
              className={accountStatus.payouts_enabled ? 'text-green-500' : 'text-gray-300'}
              size={18}
            />
            <span className={accountStatus.payouts_enabled ? 'text-green-700' : 'text-gray-500'}>
              Payouts enabled
            </span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Connect with Stripe</CardTitle>
        <CardDescription>
          Set up your Stripe account to receive payments for completed tasks
        </CardDescription>
      </CardHeader>

      <CardContent>
        {loading && (
          <div className="flex justify-center py-4">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        )}

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!loading && renderAccountStatus()}
      </CardContent>

      <CardFooter className="flex flex-col gap-2">
        {/* Debug information */}
        <div className="text-xs text-gray-500 mb-2">
          Account Status: {accountStatus ? JSON.stringify(accountStatus) : 'None'}
        </div>

        {!accountStatus && (
          <Button
            onClick={handleCreateAccount}
            disabled={loading}
            className="w-full"
          >
            {loading ? (
              <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Setting up...</>
            ) : (
              'Connect with Stripe'
            )}
          </Button>
        )}

        {accountStatus && !accountStatus.charges_enabled && (
          <>
            <Button
              onClick={handleContinueOnboarding}
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Loading...</>
              ) : (
                'Continue Onboarding'
              )}
            </Button>

            <Button
              onClick={handleDashboardAccess}
              variant="outline"
              className="w-full mt-2"
              disabled={loading}
            >
              {loading ? (
                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Loading...</>
              ) : (
                <><ExternalLink className="mr-2 h-4 w-4" /> Access Stripe Dashboard</>
              )}
            </Button>
          </>
        )}

        {accountStatus && accountStatus.charges_enabled && (
          <Button
            onClick={handleDashboardAccess}
            variant="outline"
            className="w-full"
            disabled={loading}
          >
            {loading ? (
              <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Loading...</>
            ) : (
              <><ExternalLink className="mr-2 h-4 w-4" /> Manage Stripe Account</>
            )}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}