import { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { stripeService } from '@/services/stripeService';
import { supabase } from '@/integrations/supabase/client';

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

interface PaymentProcessorProps {
  taskId: string;
  offerId: string;
  amount: number;
  onSuccess?: () => void;
  onCancel?: () => void;
}

// The wrapper component that provides the Stripe Elements context
export default function PaymentProcessor(props: PaymentProcessorProps) {
  return (
    <Elements stripe={stripePromise}>
      <PaymentForm {...props} />
    </Elements>
  );
}

// The actual payment form component
function PaymentForm({ taskId, offerId, amount, onSuccess, onCancel }: PaymentProcessorProps) {
  const { user } = useAuth();
  const stripe = useStripe();
  const elements = useElements();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentId, setPaymentId] = useState<string | null>(null);

  useEffect(() => {
    // Create a payment record and get a client secret when the component mounts
    const createPayment = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!user) {
          throw new Error('User not authenticated');
        }

        // Create a payment record
        const payment = await stripeService.createPaymentWithDirectTransfer(
          taskId,
          offerId,
          amount
        );

        if (!payment) {
          throw new Error('Failed to create payment record');
        }

        setPaymentId(payment.id);

        // Get a client secret for the payment
        const secret = await stripeService.createPaymentIntent(payment.id);

        if (!secret) {
          throw new Error('Failed to create payment intent');
        }

        setClientSecret(secret);
      } catch (err) {
        console.error('Error creating payment:', err);
        setError('Failed to initialize payment. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    createPayment();
  }, [taskId, offerId, amount, user]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements || !clientSecret) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const cardElement = elements.getElement(CardElement);

      if (!cardElement) {
        throw new Error('Card element not found');
      }

      // Confirm the payment
      const { error: stripeError, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
          billing_details: {
            name: user?.user_metadata?.full_name || 'Unknown',
            email: user?.email || undefined,
          },
        },
      });

      if (stripeError) {
        throw new Error(stripeError.message);
      }

      if (paymentIntent?.status === 'succeeded') {
        setSuccess(true);

        // Update the task status
        await supabase
          .from('tasks')
          .update({ payment_status: 'processing' })
          .eq('id', taskId);

        // Call the onSuccess callback
        if (onSuccess) {
          onSuccess();
        }
      } else {
        throw new Error('Payment failed');
      }
    } catch (err) {
      console.error('Error processing payment:', err);
      setError(err instanceof Error ? err.message : 'Payment failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Payment Successful</CardTitle>
          <CardDescription>
            Your payment has been processed successfully
          </CardDescription>
        </CardHeader>

        <CardContent>
          <div className="flex flex-col items-center justify-center py-6">
            <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
            <p className="text-center text-gray-700">
              Thank you for your payment. The supplier will be notified and will begin work on your task.
            </p>
          </div>
        </CardContent>

        <CardFooter>
          <Button onClick={onSuccess} className="w-full">
            Return to Task
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Complete Payment</CardTitle>
        <CardDescription>
          Pay for your task to release funds to the supplier
        </CardDescription>
      </CardHeader>

      <CardContent>
        {loading && !clientSecret && (
          <div className="flex justify-center py-4">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        )}

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {clientSecret && (
          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div>
                <div className="text-sm font-medium mb-2">Payment Amount</div>
                <div className="text-2xl font-bold">£{amount.toFixed(2)}</div>
              </div>

              <div>
                <div className="text-sm font-medium mb-2">Card Details</div>
                <div className="border rounded-md p-3">
                  <CardElement
                    options={{
                      style: {
                        base: {
                          fontSize: '16px',
                          color: '#424770',
                          '::placeholder': {
                            color: '#aab7c4',
                          },
                        },
                        invalid: {
                          color: '#9e2146',
                        },
                      },
                    }}
                  />
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Your payment is secure and encrypted
                </div>
              </div>
            </div>
          </form>
        )}
      </CardContent>

      <CardFooter className="flex flex-col gap-2">
        <Button
          onClick={handleSubmit}
          disabled={loading || !stripe || !elements || !clientSecret}
          className="w-full"
        >
          {loading ? (
            <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
          ) : (
            `Pay £${amount.toFixed(2)}`
          )}
        </Button>

        <Button
          variant="outline"
          onClick={onCancel}
          disabled={loading}
          className="w-full"
        >
          Cancel
        </Button>
      </CardFooter>
    </Card>
  );
}