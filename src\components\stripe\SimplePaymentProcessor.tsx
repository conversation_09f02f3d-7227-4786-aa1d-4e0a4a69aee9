// SimplePaymentProcessor.tsx
import { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { stripeService } from '@/services/stripeService';
import { supabase } from '@/integrations/supabase/client';

// Import standardized Stripe configuration
import stripeConfig, { STRIPE_PUBLIC_KEY } from '@/config/stripeConfig';

// Initialize Stripe with the public key from the standardized configuration
const stripePromise = loadStripe(STRIPE_PUBLIC_KEY);

// Main payment processor component
export default function SimplePaymentProcessor({
  taskId,
  offerId,
  amount,
  onSuccess,
  onCancel
}: {
  taskId: string;
  offerId: string;
  amount: number;
  onSuccess?: () => void;
  onCancel?: () => void;
}) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const { user } = useAuth();

  const handlePayment = async () => {
    try {
      setLoading(true);
      setError(null);

      // Create a payment record in our database
      const payment = await stripeService.createPaymentWithDirectTransfer(
        taskId,
        offerId,
        amount
      );

      if (!payment) {
        throw new Error('Failed to create payment record');
      }

      // Create a checkout session using the standardized API URL
      console.log('Using API URL:', stripeConfig.apiUrl);
      const response = await fetch(`${stripeConfig.apiUrl}/api/stripe-connect/create-checkout-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentId: payment.id,
          successUrl: `${window.location.origin}/payment-success?task_id=${taskId}`,
          cancelUrl: `${window.location.origin}/task/${taskId}`,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const { sessionId, publicKey } = await response.json();
      console.log('Received session ID:', sessionId);
      console.log('Received public key from server:', publicKey ? `${publicKey.substring(0, 10)}...` : 'undefined');

      // Load Stripe with the public key from the server if available
      const stripe = publicKey
        ? await loadStripe(publicKey)
        : await stripePromise;

      if (!stripe) {
        throw new Error('Failed to load Stripe');
      }

      // Redirect to Stripe Checkout
      const { error: stripeError } = await stripe.redirectToCheckout({
        sessionId,
      });

      if (stripeError) {
        throw new Error(stripeError.message || 'Payment failed');
      }
    } catch (err) {
      console.error('Error processing payment:', err);
      setError(err instanceof Error ? err.message : 'Failed to process payment');
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Payment Successful</CardTitle>
          <CardDescription>
            Your payment has been processed successfully
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center p-6">
          <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
          <p className="text-center text-lg font-medium mb-2">
            Thank you for your payment of £{amount.toFixed(2)}
          </p>
          <p className="text-center text-muted-foreground mb-6">
            The supplier will be notified and the funds will be transferred once the task is completed.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Complete Payment</CardTitle>
        <CardDescription>
          Pay £{amount.toFixed(2)} to complete this task
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <p className="text-center text-muted-foreground">
            Click the button below to proceed to the payment page.
          </p>
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handlePayment}
          disabled={loading}
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            <>Pay £{amount.toFixed(2)}</>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
