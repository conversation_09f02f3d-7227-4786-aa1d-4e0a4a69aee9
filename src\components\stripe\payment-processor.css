/* Custom styles for the payment processor */

/* Override dialog width for payment dialog */
[data-state="open"] .DialogContent {
  width: 90vw !important;
  max-width: 800px !important;
}

/* Set a fixed width for the payment form container */
.StripeElement {
  width: 100%;
  min-width: 0;
}

/* Container for payment element */
.payment-element-container {
  width: 100%;
  padding: 8px;
  border-radius: 8px;
  background-color: #fafafa;
}

/* Make the payment form container wider */
.ElementsApp {
  width: 100% !important;
  max-width: none !important;
}

/* Ensure the payment form fits within the container */
.ElementsApp .TabContent {
  width: 100% !important;
  max-width: none !important;
}

/* Make sure the payment form doesn't overflow */
.ElementsApp .TabPanel {
  width: 100% !important;
  max-width: none !important;
}

/* Ensure the payment form is not too tall */
.ElementsApp .TabPanel-content {
  max-height: none !important;
}

/* Accordion styles for payment methods */
.ElementsApp .AccordionItem {
  margin-bottom: 8px !important;
}

.ElementsApp .AccordionTrigger {
  padding: 12px !important;
  border-radius: 6px !important;
}

/* Make sure the payment form tabs are wide enough */
.ElementsApp .TabList {
  width: 100% !important;
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 8px !important;
}

/* Ensure the payment form tabs are evenly spaced */
.ElementsApp .Tab {
  flex: 1 1 auto !important;
  min-width: 120px !important;
  margin-right: 0 !important;
}

/* Custom styles for the payment form container */
#payment-form {
  width: 100%;
}

/* Ensure the payment form container is not too tall */
.CardContent {
  max-height: none !important;
}

/* Make sure the dialog is wide enough */
.DialogContent {
  width: 90vw !important;
  max-width: 800px !important;
}

/* Ensure the dialog content has enough padding */
.DialogContent > div {
  width: 100%;
}

/* Make sure the payment form elements are properly sized */
.ElementsApp iframe {
  width: 100% !important;
  max-width: none !important;
}

/* Ensure payment methods display properly */
.ElementsApp [data-testid="payment-element"] {
  width: 100% !important;
}

/* Ensure payment methods are visible */
.ElementsApp [data-testid="payment-element-container"] {
  width: 100% !important;
  max-width: none !important;
}

/* Fix for payment method icons */
.ElementsApp .PaymentMethodIcon {
  display: inline-flex !important;
  align-items: center !important;
}

/* Ensure payment method labels are visible */
.ElementsApp .Label {
  font-size: 14px !important;
  white-space: nowrap !important;
}
