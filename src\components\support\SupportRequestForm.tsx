import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { supabase as supabaseClient } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

// Define the form schema
const formSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  organization: z.string().optional(),
  organization_id: z.string().optional(),
  support_type: z.string().min(1, { message: 'Please select a support type' }),
  message: z.string().min(10, { message: 'Message must be at least 10 characters' }),
});

type FormValues = z.infer<typeof formSchema>;

const SupportRequestForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [organizationInfo, setOrganizationInfo] = useState<{ name: string; id: string } | null>(null);

  // Use our custom auth context instead of Supabase's useUser hook
  const { user, profile, isAdmin } = useAuth();

  // Log detailed auth context information
  if (process.env.NODE_ENV === 'development') {
    console.log('SupportRequestForm - Auth Context: completed');

    }
  // Initialize form with react-hook-form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      organization: '',
      organization_id: '',
      support_type: '',
      message: '',
    },
  });

  // Populate form with user and organization info
  useEffect(() => {
    const populateForm = async () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('SupportRequestForm - Populating form with user data');

        }
      if (!user) {
        if (process.env.NODE_ENV === 'development') {
          console.log('SupportRequestForm - No user is logged in');
          }
        return;
      }

      if (process.env.NODE_ENV === 'development') {

        console.log('SupportRequestForm - User is authenticated: completed');


        }
      try {
        // Set email from auth user
        const userEmail = user.email || '';
        if (process.env.NODE_ENV === 'development') {
          console.log('SupportRequestForm - Setting email: completed');
          }
        form.setValue('email', userEmail);

        // Set name from profile or user metadata
        if (profile?.full_name) {
          if (process.env.NODE_ENV === 'development') {
            console.log('SupportRequestForm - Setting name from profile: completed');
            }
          form.setValue('name', profile.full_name);
        } else if (user.user_metadata?.full_name) {
          if (process.env.NODE_ENV === 'development') {
            console.log('SupportRequestForm - Setting name from user metadata: completed');
            }
          form.setValue('name', user.user_metadata.full_name);
        } else if (profile?.first_name && profile?.last_name) {
          const fullName = `${profile.first_name} ${profile.last_name}`.trim();
          if (process.env.NODE_ENV === 'development') {
            console.log('SupportRequestForm - Setting name from first_name and last_name:', fullName);
            }
          form.setValue('name', fullName);
        } else if (profile?.first_name) {
          if (process.env.NODE_ENV === 'development') {
            console.log('SupportRequestForm - Setting name from first_name only: completed');
            }
          form.setValue('name', profile.first_name);
        } else {
          // If no name is available, use the email prefix as a fallback
          const emailPrefix = user.email?.split('@')[0] || '';
          const formattedName = emailPrefix
            .split(/[._-]/)
            .map(part => part.charAt(0).toUpperCase() + part.slice(1))
            .join(' ');
          if (process.env.NODE_ENV === 'development') {
            console.log('SupportRequestForm - Setting name from email prefix:', formattedName);
            }
          form.setValue('name', formattedName);
        }

        // If profile has organization_id, get organization details
        if (profile?.organization_id) {
          if (process.env.NODE_ENV === 'development') {
            console.log('SupportRequestForm - Profile has organization_id: completed');

            }
          try {
            const { data: orgData, error: orgError } = await supabaseClient
              .from('organizations')
              .select('name')
              .eq('id', profile.organization_id)
              .single();

            if (orgError) {
              console.error('SupportRequestForm - Error fetching organization:', orgError);
            } else if (orgData) {
              if (process.env.NODE_ENV === 'development') {
                console.log('SupportRequestForm - Organization found:', orgData);

                }
              setOrganizationInfo({
                name: orgData.name,
                id: profile.organization_id
              });

              // Set organization fields
              form.setValue('organization', orgData.name);
              form.setValue('organization_id', profile.organization_id);
            }
          } catch (err) {
            console.error('SupportRequestForm - Error fetching organization:', err);
          }
        }

        // Make email field read-only
        const emailField = document.querySelector('input[name="email"]') as HTMLInputElement;
        if (emailField) {
          emailField.readOnly = true;
          emailField.classList.add('bg-gray-100');
        }
      } catch (error) {
        console.error('SupportRequestForm - Error populating form:', error);
      }
    };

    // Call immediately and also after a short delay to ensure form is mounted
    populateForm();

    const timer = setTimeout(() => {
      populateForm();
    }, 500);

    return () => clearTimeout(timer);
  }, [user, profile, form]);

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Format the data for the support request
      const supportRequest: any = {
        name: data.name,
        email: data.email,
        organization: data.organization || null,
        organization_id: data.organization_id ? data.organization_id : null,
        support_type: data.support_type,
        message: data.message,
        status: 'new'
      };

      if (process.env.NODE_ENV === 'development') {
    console.log('SupportRequestForm - Processing support request:', supportRequest);
  }
      // Check if user is authenticated
      if (user) {
        if (process.env.NODE_ENV === 'development') {
          console.log('SupportRequestForm - User is authenticated, submitting to database');

          }
        // Ensure the email matches the authenticated user's email for RLS policies
        if (data.email !== user.email) {
          if (process.env.NODE_ENV === 'development') {
            console.log('SupportRequestForm - Correcting email to match authenticated user');
            }
          supportRequest.email = user.email || '';
        }

        // We're not adding user_id as it doesn't exist in the table
        // Just log the user ID for reference
        if (process.env.NODE_ENV === 'development') {
          console.log('SupportRequestForm - User ID (not added to request.replace(/user.*/, 'hasUser: ' + !!user)):', user.id);

          }
        // Log the exact data we're sending to the database
        if (process.env.NODE_ENV === 'development') {
          console.log('SupportRequestForm - Sending to database:', JSON.stringify(supportRequest, null, 2));

          }
        try {
          // First, insert the support request into the database
          const result = await supabaseClient
            .from('support_requests')
            .insert(supportRequest)
            .select();

          if (result.error) {
            console.error('SupportRequestForm - Error inserting support request:', result.error);

            // Try a simplified version without organization_id
            const simplifiedRequest = {
              name: supportRequest.name,
              email: supportRequest.email,
              organization: supportRequest.organization,
              support_type: supportRequest.support_type,
              message: supportRequest.message,
              status: 'new'
            };

            if (process.env.NODE_ENV === 'development') {
    console.log('SupportRequestForm - Trying simplified request:', simplifiedRequest);
  }
            const retryResult = await supabaseClient
              .from('support_requests')
              .insert(simplifiedRequest)
              .select();

            if (retryResult.error) {
              console.error('SupportRequestForm - Simplified request failed:', retryResult.error);
              logSupportRequest(supportRequest);
            } else {
              if (process.env.NODE_ENV === 'development') {
                console.log('SupportRequestForm - Simplified request succeeded: completed');

                }
              // Try to send email via Edge Function
              await sendSupportEmail(supportRequest);

              // Log the support request details
              if (retryResult.data && retryResult.data[0]) {
                if (process.env.NODE_ENV === 'development') {
                  console.log(`
                  Support Request Details:
                  -----------------------
                  ID: ${retryResult.data[0].id || 'Unknown'}
                  Name: ${data.name}
                  Email: ${supportRequest.email}
                  Support Type: ${data.support_type}
                  Status: new
                  Created: ${new Date().toISOString()}
                `);
                  }
              }
            }
          } else {
            if (process.env.NODE_ENV === 'development') {
              console.log('SupportRequestForm - Support request inserted successfully: completed');

              }
            // Try to send email via Edge Function
            await sendSupportEmail(supportRequest);

            // Log the support request details
            if (result.data && result.data[0]) {
              if (process.env.NODE_ENV === 'development') {
                console.log(`
                Support Request Details:
                -----------------------
                ID: ${result.data[0].id || 'Unknown'}
                Name: ${data.name}
                Email: ${supportRequest.email}
                Organization: ${data.organization || 'N/A'}
                Support Type: ${data.support_type}
                Status: new
                Created: ${new Date().toISOString()}
              `);
                }
            }
          }
        } catch (dbError) {
          console.error('SupportRequestForm - Exception during database operation:', dbError);
          logSupportRequest(supportRequest);
        }
      } else {
        // User is not authenticated, just log the request
        if (process.env.NODE_ENV === 'development') {
          console.log('SupportRequestForm - User is not authenticated, logging support request to console');
          }
        logSupportRequest(supportRequest);
      }

      // Show success message and reset form
      setSubmitSuccess(true);
      form.reset();
    } catch (error) {
      console.error('SupportRequestForm - Error submitting support request:', error);

      // Show error message to the user
      setSubmitError('There was an error submitting your support request. Please try again later.');

      // Log the error for debugging
      console.warn('SupportRequestForm - SUPPORT REQUEST FAILED:');
      console.warn(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to log support requests to console (development only)
  const logSupportRequest = (supportRequest: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.warn('SUPPORT REQUEST LOGGED TO CONSOLE:');
      console.warn(JSON.stringify(supportRequest, null, 2));

      if (process.env.NODE_ENV === 'development') {

        console.log(`
        Support Request Details (Console Only):
        --------------------------------------
        Name: ${supportRequest.name}
        Email: ${supportRequest.email}
        Organization: ${supportRequest.organization || 'N/A'}
        Support Type: ${supportRequest.support_type}
        Message: ${supportRequest.message}
        Status: new
        Created: ${new Date().toISOString()}
      `);

        }
    }
  };

  // Helper function to send support email via Edge Function
  const sendSupportEmail = async (supportRequest: any) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('SupportRequestForm - Attempting to send email via Edge Function');

        }
      // Call the Edge Function
      if (process.env.NODE_ENV === 'development') {
        console.log('SupportRequestForm - Calling Edge Function with payload');

        }
      const emailPayload = {
        from: 'ClassTasker Support <<EMAIL>>',
        to: '<EMAIL>',
        subject: `Support Request: ${supportRequest.support_type}`,
        name: supportRequest.name,
        email: supportRequest.email,
        organization: supportRequest.organization,
        organization_id: supportRequest.organization_id,
        support_type: supportRequest.support_type,
        message: supportRequest.message
      };

      if (process.env.NODE_ENV === 'development') {

        console.log('SupportRequestForm - Email payload:', JSON.stringify(emailPayload, null, 2));


        }
      const { data, error } = await supabaseClient.functions.invoke('support-email-sender', {
        body: emailPayload
      });

      if (process.env.NODE_ENV === 'development') {
    console.log('SupportRequestForm - Edge Function response: completed');
  }
      if (error) {
        console.error('SupportRequestForm - Error sending email via Edge Function:', error);
        return false;
      }

      if (process.env.NODE_ENV === 'development') {
    console.log('SupportRequestForm - Email sent successfully via Edge Function');
  }
      return true;
    } catch (error) {
      console.error('SupportRequestForm - Exception sending email via Edge Function:', error);
      return false;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">Contact Support</CardTitle>
        <CardDescription>
          Fill out this form to get help from our support team. We'll respond to your request as soon as possible.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {submitSuccess ? (
          <Alert className="bg-green-50 border-green-200 mb-4">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Support Request Submitted</AlertTitle>
            <AlertDescription className="text-green-700">
              Thank you for contacting us. We've received your support request and will get back to you shortly at {form.getValues('email')}. Our support team has been notified of your request.
            </AlertDescription>
          </Alert>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Your name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Your email address"
                          {...field}
                          readOnly={!!user}
                          className={user ? "bg-gray-100" : ""}
                        />
                      </FormControl>
                      {user && (
                        <FormDescription>
                          This email is linked to your account and cannot be changed.
                        </FormDescription>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="organization"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization</FormLabel>
                    <FormControl>
                      <Input placeholder="Your school or organization" {...field} />
                    </FormControl>
                    <FormDescription>
                      {organizationInfo ? 'Your organization has been automatically filled in.' : 'Enter the name of your school or organization.'}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="support_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Support Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select the type of support you need" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="account">Account Issues</SelectItem>
                        <SelectItem value="billing">Billing & Payments</SelectItem>
                        <SelectItem value="technical">Technical Support</SelectItem>
                        <SelectItem value="feature">Feature Request</SelectItem>
                        <SelectItem value="bug">Bug Report</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Message</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Please describe your issue or question in detail"
                        className="min-h-[150px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {submitError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{submitError}</AlertDescription>
                </Alert>
              )}

              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  "Submit Support Request"
                )}
              </Button>
            </form>
          </Form>
        )}
      </CardContent>
      <CardFooter className="flex flex-col space-y-2 items-start">
        <p className="text-sm text-gray-500">
          For urgent issues, please email us directly at{" "}
          <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
            <EMAIL>
          </a>
        </p>
      </CardFooter>
    </Card>
  );
};

export default SupportRequestForm;
