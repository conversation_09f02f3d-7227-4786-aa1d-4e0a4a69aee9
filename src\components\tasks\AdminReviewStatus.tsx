import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Clipboard<PERSON>he<PERSON>, Clock } from 'lucide-react';
import { Task } from '@/types/tasks';

interface AdminReviewStatusProps {
  task: Task;
}

/**
 * Component to display the admin review status for tasks with 'admin' visibility
 * This is shown to teachers when they view their own tasks that are pending admin review
 */
const AdminReviewStatus: React.FC<AdminReviewStatusProps> = ({ task }) => {
  return (
    <Card className="mb-6 border-2 border-amber-300">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="h-5 w-5 mr-2 text-amber-500" />
          Awaiting Review
        </CardTitle>
        <CardDescription>
          This task is awaiting review by a school administrator
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-start">
            <ClipboardCheck className="h-5 w-5 mr-2 mt-0.5 text-gray-500" />
            <div>
              <p className="text-sm text-gray-700">
                Your task has been submitted and is pending review by an administrator. 
                Once reviewed, it will be either:
              </p>
              <ul className="list-disc list-inside mt-2 text-sm text-gray-700 ml-4">
                <li>Assigned to internal maintenance staff</li>
                <li>Posted to the marketplace for external suppliers</li>
              </ul>
            </div>
          </div>
          
          <div className="bg-amber-50 p-3 rounded-md border border-amber-200">
            <p className="text-sm text-amber-800">
              <strong>Note:</strong> You'll be notified when an administrator takes action on your task.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminReviewStatus;
