/**
 * Custom Info Window for Google Maps with proper scrolling
 * This file provides a function to create an info window with proper scrolling behavior
 */

/**
 * Creates an info window with proper scrolling for multiple tasks
 * @param googleMaps The Google Maps API object
 * @param marker The marker to attach the info window to
 * @param tasksContent The HTML content for the tasks
 * @param tasksCount The number of tasks
 * @returns The created info window
 */
export function createTasksInfoWindow(googleMaps: any, marker: any, tasksContent: string, tasksCount: number) {
  // Create the info window with custom content
  const infoWindow = new googleMaps.InfoWindow({
    content: `
      <div style="padding: 12px; width: 300px;">
        <h3 style="font-weight: 600; font-size: 16px; margin-bottom: 12px;">
          ${tasksCount} Tasks at This Location
        </h3>
        <div id="scrollable-task-list" style="max-height: 300px; overflow-y: auto; padding-right: 5px;">
          ${tasksContent}
        </div>
        <style>
          /* Custom scrollbar styles */
          #scrollable-task-list::-webkit-scrollbar {
            width: 8px;
          }
          #scrollable-task-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
          }
          #scrollable-task-list::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
          }
          #scrollable-task-list::-webkit-scrollbar-thumb:hover {
            background: #555;
          }
          /* Fix for Google Maps info window */
          .gm-style-iw {
            max-height: none !important;
          }
          .gm-style-iw-d {
            overflow: hidden !important;
            max-height: none !important;
          }
          .gm-style-iw-c {
            max-height: none !important;
          }
        </style>
      </div>
    `,
    pixelOffset: new googleMaps.Size(0, -5),
    maxWidth: 320
  });

  // Add event listener for when the info window opens to ensure scrolling works
  googleMaps.event.addListener(infoWindow, 'domready', () => {
    // Fix scrolling issues by ensuring the content is properly sized
    const iwOuter = document.querySelector('.gm-style-iw');
    if (iwOuter) {
      // Remove max-height restrictions
      const iwContainer = iwOuter.parentElement as HTMLElement;
      if (iwContainer) {
        iwContainer.style.maxHeight = 'none';
      }

      // Fix the inner container
      const iwInner = iwOuter.querySelector('.gm-style-iw-d') as HTMLElement;
      if (iwInner) {
        iwInner.style.maxHeight = 'none';
        iwInner.style.overflow = 'visible';
      }

      // Find the scrollable container and ensure it's scrollable
      const scrollableContainer = document.getElementById('scrollable-task-list');
      if (scrollableContainer) {
        scrollableContainer.style.maxHeight = '300px';
        scrollableContainer.style.overflowY = 'auto';
        scrollableContainer.style.paddingRight = '5px';
      }
    }
  });

  // Add click listener to the marker
  marker.addListener('click', () => {
    infoWindow.open(marker.getMap(), marker);
  });

  // Store the info window with the marker for later reference
  marker.infoWindow = infoWindow;

  return infoWindow;
}