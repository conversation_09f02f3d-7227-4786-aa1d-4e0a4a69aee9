import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';

interface DirectDatabaseCheckProps {
  taskId: string;
}

const DirectDatabaseCheck = ({ taskId }: DirectDatabaseCheckProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [taskData, setTaskData] = useState<any>(null);
  const [userData, setUserData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkDatabase = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // First, get the current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        
        if (userError) {
          throw new Error(`Error getting user: ${userError.message}`);
        }
        
        if (!user) {
          throw new Error('No user is logged in');
        }

        // Get the user's profile
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
          
        if (profileError) {
          throw new Error(`Error getting profile: ${profileError.message}`);
        }

        // Get the task
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('*')
          .eq('id', taskId)
          .single();
          
        if (taskError) {
          throw new Error(`Error getting task: ${taskError.message}`);
        }

        setUserData({
          id: user.id,
          email: user.email,
          role: profileData?.role,
          isMaintenance: profileData?.role === 'maintenance'
        });
        
        setTaskData({
          id: taskData.id,
          title: taskData.title,
          visibility: taskData.visibility,
          status: taskData.status,
          assigned_to: taskData.assigned_to,
          isInternal: taskData.visibility === 'internal',
          isAssigned: taskData.status === 'assigned',
          isAssignedToUser: taskData.assigned_to === user.id
        });
      } catch (err: any) {
        console.error('Error in DirectDatabaseCheck:', err);
        setError(err.message || 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    checkDatabase();
  }, [taskId]);

  const handleUpdateStatus = async (newStatus: string) => {
    try {
      setIsLoading(true);
      
      const { error } = await supabase
        .from('tasks')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', taskId);
        
      if (error) {
        throw error;
      }
      
      // Refresh the task data
      const { data: updatedTask, error: refreshError } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .single();
        
      if (refreshError) {
        throw refreshError;
      }
      
      setTaskData({
        ...taskData,
        status: updatedTask.status,
        isAssigned: updatedTask.status === 'assigned'
      });
      
      alert(`Task status updated to ${newStatus}`);
    } catch (err: any) {
      console.error('Error updating task status:', err);
      setError(err.message || 'Failed to update task status');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Card className="mb-4">
        <CardContent className="p-6 flex justify-center items-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="mb-4 bg-red-50 border-red-200">
        <CardContent className="p-6">
          <p className="text-red-600 font-medium">Error: {error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-4 bg-blue-50 border-2 border-blue-300">
      <CardHeader>
        <CardTitle>Direct Database Check</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Button
              onClick={() => handleUpdateStatus('in_progress')}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              Start Work
            </Button>
            <Button
              onClick={() => handleUpdateStatus('completed')}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              Mark Completed
            </Button>
          </div>
          
          <div className="p-3 bg-white rounded shadow-sm text-xs">
            <h4 className="font-bold mb-2">User Info:</h4>
            <pre className="whitespace-pre-wrap">{JSON.stringify(userData, null, 2)}</pre>
            
            <h4 className="font-bold mt-3 mb-2">Task Info:</h4>
            <pre className="whitespace-pre-wrap">{JSON.stringify(taskData, null, 2)}</pre>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DirectDatabaseCheck;
