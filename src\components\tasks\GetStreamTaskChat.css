/**
 * GetStream Task Chat Component Styles
 */

/* Container */
.getstream-chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Override GetStream styles to match our app's design */
.str-chat {
  --str-chat__primary-color: #3b82f6;
  --str-chat__active-primary-color: #2563eb;
  --str-chat__surface-color: #ffffff;
  --str-chat__avatar-background-color: #dbeafe;
  --str-chat__avatar-text-color: #2563eb;
  height: 100%;
  width: 100%;
  font-family: inherit;
}

/* Channel header */
.str-chat__header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.75rem 1rem;
  height: auto;
}

.str-chat__header-title {
  font-size: 1rem;
  font-weight: 500;
}

/* Message list */
.str-chat__list {
  padding: 1rem;
  background-color: #f9fafb;
}

/* Messages */
.str-chat__message-simple {
  margin-bottom: 0.75rem;
}

.str-chat__message-simple--me {
  justify-content: flex-end;
}

.str-chat__message-simple__content {
  background-color: #f3f4f6;
  border-radius: 0.5rem;
  padding: 0.75rem;
  max-width: 80%;
}

.str-chat__message-simple--me .str-chat__message-simple__content {
  background-color: #3b82f6;
  color: white;
}

.str-chat__message-simple__actions {
  display: none;
}

.str-chat__message-simple:hover .str-chat__message-simple__actions {
  display: flex;
}

/* Message input */
.str-chat__input {
  background-color: white;
  border-top: 1px solid #e5e7eb;
  padding: 0.75rem;
}

.str-chat__input-flat {
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
}

.str-chat__input-flat:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.str-chat__textarea {
  font-size: 0.875rem;
  padding: 0.5rem;
}

.str-chat__textarea::placeholder {
  color: #9ca3af;
}

.str-chat__input-flat-wrapper {
  padding: 0.25rem;
}

.str-chat__send-button {
  background-color: #3b82f6;
  border-radius: 0.375rem;
  margin-right: 0.25rem;
}

.str-chat__send-button:hover {
  background-color: #2563eb;
}

/* Thread */
.str-chat__thread {
  background-color: white;
  border-left: 1px solid #e5e7eb;
}

.str-chat__thread-header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.75rem 1rem;
}

.str-chat__thread-header-title {
  font-size: 0.875rem;
  font-weight: 500;
}

/* System messages */
.str-chat__message--system {
  margin: 0.75rem 0;
  display: flex;
  justify-content: center;
}

.str-chat__message--system .str-chat__message-simple__content {
  background-color: #dbeafe;
  color: #1e40af;
  font-style: italic;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
}

/* Loading indicator */
.str-chat__loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* Empty state */
.str-chat__empty-channel {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #6b7280;
}

/* Tabs */
.chat-tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.chat-tab {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.chat-tab:hover {
  color: #3b82f6;
}

.chat-tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

/* Disabled state */
.str-chat__input--disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.str-chat__input--disabled .str-chat__textarea {
  background-color: #f3f4f6;
}

/* Closed thread banner */
.closed-thread-banner {
  background-color: #f3f4f6;
  padding: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  color: #6b7280;
  border-top: 1px solid #e5e7eb;
}
