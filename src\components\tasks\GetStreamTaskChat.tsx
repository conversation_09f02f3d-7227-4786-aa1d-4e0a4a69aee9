/**
 * GetStream Task Chat Component
 *
 * A drop-in replacement for the TaskChat component that uses GetStream
 * for chat functionality while maintaining compatibility with the existing
 * task and user data structure.
 */

import React, { useState, useEffect, useRef } from 'react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Send, Loader2, MessageSquare, User, PoundSterling, Info, ArrowRight } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { useGetStreamChat } from '@/hooks/use-getstream-chat';
import { getTaskChannel, createTaskChannel } from '@/integrations/getstream/client';
import { <PERSON><PERSON>, Channel, ChannelHeader, MessageInput, MessageList, Thread, Window } from 'stream-chat-react';
import { Skeleton } from '@/components/ui/skeleton';
import systemMessageService from '@/services/systemMessageService';

// Import custom styles
import './GetStreamTaskChat.css';

interface GetStreamTaskChatProps {
  taskId: string;
  taskOwnerId: string;
}

const GetStreamTaskChat: React.FC<GetStreamTaskChatProps> = ({ taskId, taskOwnerId }) => {
  const [activeThreadId, setActiveThreadId] = useState<string | undefined>(undefined);
  const [threads, setThreads] = useState<any[]>([]);
  const [hasThreads, setHasThreads] = useState(false);
  const [isThreadClosed, setIsThreadClosed] = useState(false);
  const [isLoadingThreads, setIsLoadingThreads] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { user, isSupplier, isAdmin, isSupport, isMaintenance, userRole, profile } = useAuth();
  const { toast } = useToast();

  // Log profile and role information for debugging
  console.log('GetStreamTaskChat - Profile:', profile);
  console.log('GetStreamTaskChat - User role from AuthContext:', userRole);

  // Use the GetStream chat hook
  const {
    client,
    channel,
    messages,
    isLoading: isLoadingMessages,
    isSending,
    error,
    sendMessage: sendStreamMessage
  } = useGetStreamChat({
    taskId,
    threadId: activeThreadId
  });

  // Log any errors for debugging
  useEffect(() => {
    if (error) {
      console.error('[GetStreamTaskChat] Error from useGetStreamChat:', error);
    }
  }, [error]);

  // Fetch threads from Supabase
  const fetchThreads = async () => {
    if (!taskId || !user) return;

    setIsLoadingThreads(true);
    try {
      console.log('[GetStreamTaskChat] Fetching threads for task:', taskId);

      // Get threads for this task
      // If user is a supplier, only show threads where they are the supplier
      let query = supabase
        .from('chat_threads')
        .select(`
          *,
          supplier:supplier_id(id, first_name, last_name, email)
        `)
        .eq('task_id', taskId);

      // If user is a supplier, only show their own threads
      if (isSupplier) {
        console.log('[GetStreamTaskChat] User is a supplier, filtering threads to only show their own');
        query = query.eq('supplier_id', user.id);
      }

      // Order by last message time
      const { data: threadData, error: threadError } = await query.order('last_message_at', { ascending: false });

      if (threadError) {
        console.error('[GetStreamTaskChat] Error fetching threads:', threadError);
        return;
      }

      console.log('[GetStreamTaskChat] Threads fetched:', threadData?.length);
      setThreads(threadData || []);
      setHasThreads(threadData && threadData.length > 0);

      // If we have threads but no active thread, set the first one as active
      if (threadData && threadData.length > 0 && !activeThreadId) {
        setActiveThread(threadData[0].id);
      }
    } catch (err) {
      console.error('[GetStreamTaskChat] Error in fetchThreads:', err);
    } finally {
      setIsLoadingThreads(false);
    }
  };

  // Set active thread and check if it's closed
  const setActiveThread = async (threadId: string) => {
    setActiveThreadId(threadId);

    // Check if thread is closed
    const thread = threads.find(t => t.id === threadId);
    setIsThreadClosed(thread?.is_closed || false);
  };

  // Initialize threads
  useEffect(() => {
    fetchThreads();

    // Set up real-time subscription for threads
    const threadsChannel = supabase
      .channel(`chat_threads_${taskId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chat_threads',
          filter: `task_id=eq.${taskId}`
        },
        () => {
          console.log('[GetStreamTaskChat] Thread changes detected, refreshing threads');
          fetchThreads();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(threadsChannel);
    };
  }, [taskId, user]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle sending a message
  const handleSendMessage = async (text: string) => {
    if (!text.trim() || !user) return;

    try {
      const result = await sendStreamMessage(text);

      if (!result.success) {
        toast({
          title: 'Error sending message',
          description: result.reason || 'Please try again',
          variant: 'destructive'
        });
      }
    } catch (err) {
      console.error('[GetStreamTaskChat] Error sending message:', err);
      toast({
        title: 'Error sending message',
        description: 'Please try again',
        variant: 'destructive'
      });
    }
  };

  // Render thread tabs
  const renderThreadTabs = () => {
    if (isLoadingThreads) {
      return (
        <div className="p-4 border-b">
          <Skeleton className="h-10 w-full" />
        </div>
      );
    }

    if (!hasThreads) {
      return null;
    }

    return (
      <TabsList className="w-full border-b p-0 h-auto">
        {threads.map((thread) => {
          const supplierName = thread.supplier?.first_name && thread.supplier?.last_name
            ? `${thread.supplier.first_name} ${thread.supplier.last_name}`
            : thread.supplier?.email?.[0] || 'Supplier';

          return (
            <TabsTrigger
              key={thread.id}
              value={thread.id}
              className="flex-1 py-3 px-4 rounded-none border-b-2 border-transparent data-[state=active]:border-blue-500"
              onClick={() => setActiveThread(thread.id)}
            >
              <div className="flex items-center">
                <Avatar className="h-6 w-6 mr-2">
                  <AvatarFallback className="text-xs">
                    {supplierName.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <span className="truncate max-w-[100px]">{supplierName}</span>
                {thread.is_closed && (
                  <Badge variant="outline" className="ml-2 bg-gray-100">Closed</Badge>
                )}
              </div>
            </TabsTrigger>
          );
        })}
      </TabsList>
    );
  };

  // Check if user is a teacher based on profile role
  const isTeacher = userRole === 'teacher';

  // Allow viewing and sending messages for all roles EXCEPT teachers
  // Teachers should not be able to chat with service providers
  const canViewMessages = user && (
    // User is admin, support, maintenance, or supplier
    isAdmin || isSupport || isMaintenance || isSupplier ||
    // User has any role other than teacher
    (userRole && userRole !== 'teacher') ||
    // Special case: if the teacher is the task owner AND an admin has already
    // started the conversation, they can view (but not participate)
    (isTeacher && user.id === taskOwnerId && messages.length > 0)
  );

  // Special check for maintenance and support staff
  const isMaintenanceOrSupport = profile?.role === 'maintenance' || profile?.role === 'support';
  console.log('GetStreamTaskChat - Is maintenance or support staff:', isMaintenanceOrSupport);

  if (isLoadingMessages && !error) {
    return (
      <div className="h-full flex flex-col">
        <div className="flex justify-center items-center p-8 flex-1">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center bg-red-50 rounded-lg">
        <p className="text-red-500">Error loading chat: {error.message}</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {!user ? (
        <div className="p-6 text-center">
          <p className="text-gray-500">Please sign in to join the conversation</p>
        </div>
      ) : !canViewMessages ? (
        <div className="p-6 text-center">
          {isTeacher ? (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-700 mb-2">Communication Note</h3>
              <p className="text-gray-700 mb-2">
                Direct communication with service providers is handled by administrators.
              </p>
              <p className="text-gray-700">
                An administrator will manage all communications regarding this task.
              </p>
            </div>
          ) : (
            <p className="text-gray-500">You don't have permission to view this conversation</p>
          )}
        </div>
      ) : !client || !channel ? (
        <div className="p-6 text-center">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500 mx-auto mb-4" />
          <p className="text-gray-500">Connecting to chat...</p>
        </div>
      ) : (
        <Tabs value={activeThreadId || 'default'} className="flex flex-col h-full">
          {/* Thread tabs */}
          {renderThreadTabs()}

          {/* GetStream Chat */}
          <div className="flex-1 overflow-hidden">
            <Chat client={client} theme="messaging light">
              <Channel channel={channel}>
                <Window>
                  <ChannelHeader />
                  <MessageList />
                  <MessageInput disabled={isThreadClosed} />
                </Window>
                <Thread />
              </Channel>
            </Chat>
          </div>
        </Tabs>
      )}
    </div>
  );
};

export default GetStreamTaskChat;
