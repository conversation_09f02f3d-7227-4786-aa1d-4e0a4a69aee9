/**
 * HorizontalTaskTimeline Component
 *
 * This component displays a horizontal timeline for tasks, showing the progression
 * through different statuses with detailed information about the current status.
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  AlertCircle,
  Clock,
  CheckCircle,
  DollarSign,
  Hourglass,
  MessageSquare,
  PlayCircle,
  ThumbsUp,
  User
} from 'lucide-react';
import { Task, isInternalTask, isExternalTask } from '@/types/tasks';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

// Define status descriptions for internal tasks
const INTERNAL_STATUS_DESCRIPTIONS = {
  'assigned': 'Task has been assigned to a staff member',
  'in_progress': 'Staff member is working on the task',
  'completed': 'Task has been marked as completed by the staff member',
  'closed': 'Task has been reviewed and closed'
};

// Define status descriptions for external tasks
const EXTERNAL_STATUS_DESCRIPTIONS = {
  'open': 'Task is open for offers from suppliers',
  'interest': 'Suppliers have expressed interest in this task',
  'questions': 'Suppliers are asking questions about this task',
  'offer': 'Suppliers have submitted offers for this task',
  'assigned': 'Task has been assigned to a supplier',
  'in_progress': 'Supplier is working on the task',
  'completed': 'Task has been marked as completed by the supplier',
  'closed': 'Task has been reviewed and closed',
  'pending_payment': 'Payment is required to complete this task'
};

// Define status descriptions for admin review tasks
const ADMIN_REVIEW_STATUS_DESCRIPTIONS = {
  'open': 'Task has been submitted and is awaiting review by a school administrator'
};

// Define status order for internal tasks
const INTERNAL_STATUS_ORDER = [
  'assigned',
  'in_progress',
  'completed',
  'closed'
];

// Define status order for external tasks
const EXTERNAL_STATUS_ORDER = [
  'open',
  'interest',
  'questions',
  'offer',
  'assigned',
  'in_progress',
  'completed',
  'closed',
  'pending_payment'
];

// Define status order for admin review tasks
const ADMIN_REVIEW_STATUS_ORDER = [
  'open',
  'admin_review',
  'assigned'
];

// Define status labels for display
const STATUS_LABELS: Record<string, string> = {
  'open': 'Posted',
  'interest': 'Interest',
  'questions': 'Discussion',
  'offer': 'Offers',
  'assigned': 'Assigned',
  'in_progress': 'In Progress',
  'completed': 'Completed',
  'closed': 'Closed',
  'pending_payment': 'Payment Due',
  'admin_review': 'Admin Review'
};

// Define icons for each status
const STATUS_ICONS: Record<string, React.ReactNode> = {
  'open': <Clock size={16} />,
  'interest': <MessageSquare size={16} />,
  'questions': <MessageSquare size={16} />,
  'offer': <DollarSign size={16} />,
  'assigned': <User size={16} />,
  'in_progress': <PlayCircle size={16} />,
  'completed': <CheckCircle size={16} />,
  'pending_payment': <DollarSign size={16} />,
  'closed': <ThumbsUp size={16} />,
  'admin_review': <AlertCircle size={16} />
};

// Define next steps for external tasks
const EXTERNAL_NEXT_STEPS: Record<string, string> = {
  'open': 'Suppliers: Express interest in this task to start a conversation with the admin.',
  'interest': 'Suppliers and Admin: Discuss task requirements and details before submitting formal offers.',
  'questions': 'Suppliers: Submit formal offers after discussing requirements. Admin: Review conversations and wait for offers.',
  'offer': 'Admin: Review the offers from suppliers and select one to assign the task to. Suppliers: Wait for admin decision.',
  'assigned': 'Supplier: Accept the assignment and start work on the task.',
  'in_progress': 'Supplier: Complete the work and mark the task as "Completed" when finished.',
  'completed': 'Admin: Review the completed work and approve it if it meets requirements.',
  'closed': 'Admin: Process payment for this task.',
  'pending_payment': 'Payment is being processed. Once complete, the task will be fully closed.'
};

// Define next steps for internal tasks
const INTERNAL_NEXT_STEPS: Record<string, string> = {
  'assigned': 'Staff: Start work on the task when ready.',
  'in_progress': 'Staff: Complete the work and mark the task as "Completed" when finished.',
  'completed': 'Admin: Review the completed work and close it if it meets requirements.',
  'closed': 'Task has been completed and closed.'
};

// Define next steps for admin review tasks
const ADMIN_REVIEW_NEXT_STEPS: Record<string, string> = {
  'open': 'Admin: Review this task and decide whether to assign it internally or make it public for suppliers.',
  'admin_review': 'Admin: Review this task and decide whether to assign it internally or make it public for suppliers.',
  'assigned': 'The task has been assigned and is now in progress.'
};

interface HorizontalTaskTimelineProps {
  task: Task | null;
  isLoading: boolean;
  error?: Error | null;
  className?: string;
}

/**
 * HorizontalTaskTimeline component
 */
const HorizontalTaskTimeline: React.FC<HorizontalTaskTimelineProps> = ({
  task,
  isLoading,
  error,
  className
}) => {
  // Early return for loading state
  if (isLoading) {
    return (
      <Card className={cn("w-full mb-6", className)}>
        <CardHeader className="pb-2">
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    );
  }

  // Early return for error state
  if (error) {
    return (
      <Alert variant="destructive" className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load task timeline: {error.message}
        </AlertDescription>
      </Alert>
    );
  }

  // Early return if no task
  if (!task) {
    return null;
  }

  // Determine which status order and descriptions to use based on task type and visibility
  let statusOrder = EXTERNAL_STATUS_ORDER;
  let statusDescriptions = EXTERNAL_STATUS_DESCRIPTIONS;
  let nextSteps = EXTERNAL_NEXT_STEPS;

  // For internal tasks
  if (isInternalTask(task)) {
    statusOrder = INTERNAL_STATUS_ORDER;
    statusDescriptions = INTERNAL_STATUS_DESCRIPTIONS;
    nextSteps = INTERNAL_NEXT_STEPS;
  }
  // For tasks with admin visibility (pending review)
  else if (task.visibility === 'admin') {
    statusOrder = ADMIN_REVIEW_STATUS_ORDER;
    statusDescriptions = ADMIN_REVIEW_STATUS_DESCRIPTIONS;
    nextSteps = ADMIN_REVIEW_NEXT_STEPS;
  }

  // Handle legacy statuses by mapping them to current statuses
  const normalizedStatus = (task.status === 'confirmed' || task.status === 'approved') ? 'closed' : task.status;

  // Get the current status index
  // If the status is not in the status order, default to the first status
  const currentStatusIndex = statusOrder.indexOf(normalizedStatus) !== -1
    ? statusOrder.indexOf(normalizedStatus)
    : 0;

  return (
    <Card className={cn("w-full mb-6", className)}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center">
          <div className="mr-2 p-1 rounded-full bg-blue-100">
            <Clock size={18} className="text-blue-600" />
          </div>
          Task Timeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Horizontal Timeline */}
        <div className="mb-6">
          <div className="flex items-center justify-between w-full relative">
            {/* Connecting line behind the icons */}
            <div className="absolute top-5 left-0 right-0 h-0.5 bg-gray-200 -z-10"></div>

            {statusOrder.map((status, index) => {
              // Determine if this step is active, completed, or upcoming
              const isActive = status === normalizedStatus;
              const isCompleted = index < currentStatusIndex;
              const isUpcoming = index > currentStatusIndex;

              return (
                <div
                  key={status}
                  className={cn(
                    "flex flex-col items-center",
                    isActive ? "text-blue-600" :
                    isCompleted ? "text-green-600" :
                    "text-gray-300"
                  )}
                >
                  {/* Status icon with larger size and better styling */}
                  <div className={cn(
                    "rounded-full p-2 border-2 shadow-sm",
                    isActive ? "bg-blue-100 border-blue-300" :
                    isCompleted ? "bg-green-100 border-green-300" :
                    "bg-gray-100 border-gray-200"
                  )}>
                    {STATUS_ICONS[status]}
                  </div>

                  {/* Status label with better spacing */}
                  <span className={cn(
                    "text-xs mt-2",
                    isActive ? "font-bold" :
                    isCompleted ? "font-medium" :
                    "font-normal"
                  )}>
                    {STATUS_LABELS[status]}
                  </span>
                </div>
              );
            })}
          </div>

          {/* Progress bar with better styling */}
          <div className="w-full h-2 bg-gray-200 mt-6 rounded-full overflow-hidden">
            <div
              className="h-full bg-green-500 rounded-full transition-all duration-300"
              style={{
                width: `${Math.max(
                  (currentStatusIndex / (statusOrder.length - 1)) * 100,
                  5
                )}%`
              }}
            />
          </div>
        </div>

        {/* Current status details */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
          <h3 className="font-medium text-lg mb-2 flex items-center">
            Current Status:
            <span className={
              task.status === 'open' ? ' text-green-600 ml-2' :
              task.status === 'interest' ? ' text-cyan-600 ml-2' :
              task.status === 'questions' ? ' text-cyan-700 ml-2' :
              task.status === 'offer' ? ' text-yellow-600 ml-2' :
              task.status === 'assigned' ? ' text-blue-600 ml-2' :
              task.status === 'in_progress' ? ' text-indigo-600 ml-2' :
              task.status === 'pending_payment' ? ' text-yellow-600 ml-2' :
              task.status === 'completed' ? ' text-purple-600 ml-2' :
              ' text-teal-600 ml-2'
            }>
              {task.status === 'pending_payment' ? 'Payment Required' :
               task.status === 'in_progress' ? 'In Progress' :
               task.status === 'interest' ? 'Interest Expressed' :
               task.status === 'questions' ? 'Discussion Phase' :
               task.status === 'offer' ? 'Offers Received' :
               task.status === 'confirmed' ? 'Closed' :
               task.status === 'approved' ? 'Closed' :
               task.status.charAt(0).toUpperCase() + task.status.slice(1)}
            </span>
          </h3>
          <p className="text-gray-600 mb-4">
            {statusDescriptions[normalizedStatus as keyof typeof statusDescriptions] ||
             `Task is in ${normalizedStatus.replace('_', ' ')} status`}
          </p>

          {/* Status-specific details */}
          <div className="space-y-3 text-sm">
            <div className="flex justify-between items-center border-b pb-2">
              <span className="text-gray-500">Created:</span>
              <span className="font-medium">
                {task.created_at ? format(new Date(task.created_at), 'PPpp') : 'Not available'}
              </span>
            </div>

            <div className="flex justify-between items-center border-b pb-2">
              <span className="text-gray-500">Last Updated:</span>
              <span className="font-medium">
                {task.updated_at ? format(new Date(task.updated_at), 'PPpp') : 'Not available'}
              </span>
            </div>

            {task.status !== 'open' && task.assigned_to && (
              <div className="flex justify-between items-center border-b pb-2">
                <span className="text-gray-500">Assigned To:</span>
                <span className="font-medium">{task.assigned_to_name || task.assigned_to}</span>
              </div>
            )}

            {(task.status === 'open' || task.status === 'offer') && task.offers_count > 0 && (
              <div className="flex justify-between items-center border-b pb-2">
                <span className="text-gray-500">Offers Received:</span>
                <span className="font-medium">{task.offers_count}</span>
              </div>
            )}
          </div>
        </div>

        {/* Next steps */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
          <h3 className="font-medium text-lg mb-2 flex items-center text-blue-700">
            <div className="mr-2 p-1 rounded-full bg-blue-200">
              <Hourglass size={16} className="text-blue-700" />
            </div>
            Next Steps
          </h3>
          <p className="text-gray-700">
            {nextSteps[normalizedStatus] || 'No specific next steps available for this status.'}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default HorizontalTaskTimeline;
