import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Image, X, Upload, Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface ImageUploaderProps {
  taskId: string;
  onImageUploaded: (url: string) => void;
  disabled?: boolean;
}

const ImageUploader = ({ taskId, onImageUploaded, disabled = false }: ImageUploaderProps) => {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        variant: "destructive",
        title: "Invalid file type",
        description: "Please select an image file (JPEG, PNG, etc.)",
      });
      return;
    }

    // Check file size (limit to 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        variant: "destructive",
        title: "File too large",
        description: "Image must be less than 5MB",
      });
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = () => {
      setPreviewUrl(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleUpload = async () => {
    if (!fileInputRef.current?.files?.[0]) return;

    const file = fileInputRef.current.files[0];
    setIsUploading(true);

    try {
      // Generate a unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${taskId}/${Date.now()}.${fileExt}`;
      const filePath = `task-images/${fileName}`;

      console.log('Uploading image to path:', filePath);

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from('attachments')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Supabase storage upload error:', error);

        // Check for specific error types
        if (error.message.includes('row-level security')) {
          throw new Error('Permission denied: You do not have permission to upload images. This may be a configuration issue.');
        } else if (error.statusCode === '413') {
          throw new Error('File too large: The image exceeds the maximum allowed size.');
        } else if (error.statusCode === '400') {
          throw new Error('Invalid request: The file could not be processed. Please try a different image.');
        }

        throw error;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('attachments')
        .getPublicUrl(filePath);

      console.log('Image uploaded successfully, public URL:', urlData.publicUrl);

      // Call the callback with the URL
      onImageUploaded(urlData.publicUrl);

      // Clear the preview and input
      setPreviewUrl(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      toast({
        title: "Image uploaded",
        description: "Your image has been uploaded successfully",
      });
    } catch (error: any) {
      console.error('Error uploading image:', error);

      // Provide a more user-friendly error message
      let errorMessage = "Failed to upload image. Please try again.";

      if (error.message) {
        errorMessage = error.message;

        // Log detailed error information for debugging
        console.error('Upload error details:', {
          message: error.message,
          statusCode: error.statusCode,
          error: error
        });
      }

      toast({
        variant: "destructive",
        title: "Upload failed",
        description: errorMessage,
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleCancel = () => {
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="mt-2">
      {!previewUrl ? (
        <div className="flex items-center">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept="image/*"
            className="hidden"
            disabled={isUploading || disabled}
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading || disabled}
            className="flex items-center text-gray-600"
          >
            <Image className="h-4 w-4 mr-2" />
            Add Image
          </Button>
        </div>
      ) : (
        <div className="space-y-2">
          <div className="relative inline-block">
            <img
              src={previewUrl}
              alt="Preview"
              className="max-h-32 max-w-full rounded-md"
            />
            <button
              onClick={handleCancel}
              className="absolute top-1 right-1 bg-gray-800 bg-opacity-70 rounded-full p-1 text-white"
              disabled={isUploading || disabled}
            >
              <X className="h-4 w-4" />
            </button>
          </div>
          <div className="flex space-x-2">
            <Button
              type="button"
              size="sm"
              onClick={handleUpload}
              disabled={isUploading || disabled}
              className="bg-green-600 hover:bg-green-700"
            >
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Send
                </>
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleCancel}
              disabled={isUploading || disabled}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
