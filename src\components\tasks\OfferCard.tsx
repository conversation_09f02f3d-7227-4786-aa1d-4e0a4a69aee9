import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Loader2, MessageSquare } from 'lucide-react';
import { format } from 'date-fns';
import { useAuth } from '@/contexts/AuthContext';
import { useFixedOffers } from '@/hooks/fixed-use-offers';
import { Offer, Task } from '@/services/taskService';

interface OfferCardProps {
  offer: Offer;
  task: Task;
  onRequestInfo: () => void;
  showMessages: boolean;
}

const normalizeStatus = (status: string): string => {
  if (status === 'pending') return 'awaiting';
  return status;
};

const OfferCard = ({ offer, task, onRequestInfo, showMessages }: OfferCardProps) => {
  const { user } = useAuth();
  const fixedOffers = useFixedOffers();
  const { acceptOffer, isAcceptingOffer, updateOfferStatus, isUpdatingOfferStatus } = fixedOffers;

  // Check user roles - use AuthContext's approach to determine admin role
  const { userRole } = useAuth();
  const isAdmin = userRole === 'admin';
  const isTaskOwner = user && task && user.id === task.user_id;

  // DEBUG: Log detailed information about admin role determination
  console.log('OfferCard - Admin role check:', {
    userMetadata: user?.user_metadata,
    userRole, // Use AuthContext's userRole instead of metadata
    isAdmin,
    isTaskOwner,
    userId: user?.id,
    taskUserId: task?.user_id
  });

  // Only admins should be able to interact with offers
  const canManageOffers = isAdmin;
  const displayStatus = normalizeStatus(offer.status);

  console.log('OfferCard render:', {
    offerId: offer.id,
    status: offer.status,
    displayStatus,
    isTaskOwner,
    isAdmin,
    canManageOffers,
    userRole, // Use AuthContext's userRole instead of metadata
    userId: user?.id,
    taskUserId: task?.user_id
  });

  // Debug info - moved outside JSX
  console.log('OfferCard buttons check:', {
    isAdmin,
    canManageOffers,
    displayStatus,
    shouldShowButtons: canManageOffers && displayStatus === 'awaiting'
  });

  const handleAcceptOffer = () => {
    console.log(`Accepting offer: ${offer.id} for task: ${task.id}`);

    acceptOffer({
      taskId: task.id,
      offerId: offer.id
    });
  };

  const handleRejectOffer = () => {
    console.log(`Rejecting offer: ${offer.id}`);

    updateOfferStatus(offer.id, 'rejected');
  };

  return (
    <Card className={
      displayStatus === 'accepted' ? 'border-green-400' :
      displayStatus === 'rejected' ? 'border-red-300' : ''
    }>
      <CardContent className="p-4">
        <div className="flex justify-between mb-2">
          <div className="font-medium flex items-center">
            £{Number(offer.amount).toFixed(2)}
            {Number(task.budget) !== Number(offer.amount) && displayStatus === 'awaiting' && (
              <Badge className="ml-2 bg-blue-100 text-blue-800 border-blue-200">
                Counter Offer
              </Badge>
            )}
          </div>
          <Badge
            variant="outline"
            className={
              `${displayStatus === 'awaiting' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                displayStatus === 'accepted' ? 'bg-green-100 text-green-800 border-green-200' :
                'bg-red-100 text-red-800 border-red-200'}`
            }
          >
            {displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1)}
          </Badge>
        </div>
        <p className="text-gray-700 mb-3">
          {Number(task.budget) !== Number(offer.amount) && displayStatus === 'awaiting' ? (
            <span className="font-medium">
              Supplier suggested a different price. Reason:
              <span className="italic">{offer.message}</span>
            </span>
          ) : (
            offer.message
          )}
        </p>
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            {format(new Date(offer.created_at), 'PP')}
          </div>

          {/* Only show buttons for admins when offer is awaiting */}
          {canManageOffers && displayStatus === 'awaiting' && (
            <div className="flex gap-2">
              <Button
                variant="default"
                size="sm"
                className="bg-classtasker-blue hover:bg-blue-600"
                onClick={handleAcceptOffer}
                disabled={isAcceptingOffer || isUpdatingOfferStatus}
              >
                {isAcceptingOffer ?
                  <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Accepting...</> :
                  'Accept Offer'}
              </Button>

              <Button
                variant="outline"
                size="sm"
                className="text-red-600 hover:bg-red-50"
                onClick={handleRejectOffer}
                disabled={isUpdatingOfferStatus || isAcceptingOffer}
              >
                {isUpdatingOfferStatus ?
                  <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Rejecting...</> :
                  'Reject'}
              </Button>

              {!showMessages && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRequestInfo}
                >
                  Message Supplier
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default OfferCard;
