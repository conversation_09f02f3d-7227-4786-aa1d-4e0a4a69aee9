/**
 * StreamUIChatVercel Component
 *
 * A simplified version of the StreamUIChat component that is compatible with Vercel deployment.
 * This component doesn't import any external CSS files and uses only inline styles.
 */

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import {
  getStreamClient,
  connectUser,
  disconnectUser,
  incrementConnectionCount,
  decrementConnectionCount
} from '@/integrations/getstream/client';
import { getStreamApiUrl } from '@/utils/apiConfig';

// Import only the necessary GetStream components
import {
  StreamChat,
  Channel as StreamChannel,
  Message as StreamMessage,
} from 'stream-chat';

import {
  Chat,
  Channel,
  ChannelHeader,
  MessageInput,
  MessageList,
  Thread,
  Window,
  LoadingIndicator,
  MessageSimple,
  useMessageContext,
} from 'stream-chat-react';

// Import GetStream CSS - using only the official GetStream styles
import 'stream-chat-react/dist/css/v2/index.css';
import '@stream-io/stream-chat-css/dist/v2/css/index.css';

// Import custom CSS for GetStream chat
import '@/styles/getstream-chat.css';

// Custom SystemMessage component
const SystemMessage: React.FC = () => {
  const { message } = useMessageContext();

  // Check if this is a system message
  const isSystemMessage = message.type === 'system';

  if (!isSystemMessage) {
    // For regular messages, use the default MessageSimple component
    return <MessageSimple />;
  }

  return (
    <div className="str-chat__message str-chat__message--system">
      <div className="str-chat__message-text">
        <div className="str-chat__message-text-inner">
          {message.text}
        </div>
      </div>
    </div>
  );
};

interface StreamUIChatVercelProps {
  taskId: string;
  taskTitle: string;
  threadId?: string;
  onSendMessage?: (message: string) => void;
  onSystemMessage?: (message: string) => void;
  className?: string;
}

export const StreamUIChatVercel: React.FC<StreamUIChatVercelProps> = ({
  taskId,
  taskTitle,
  threadId,
  onSendMessage,
  onSystemMessage,
  className = '',
}) => {
  const { user, profile } = useAuth();
  const [channel, setChannel] = useState<any>(null);
  const [client, setClient] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Define the sendSystemMessage function
  const sendSystemMessage = async (text: string) => {
    if (!channel) return false;

    try {
      // Send a system message to the channel via the API route
      const response = await fetch(getStreamApiUrl('/system-message'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channelId: channel.id,
          text
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send system message via server');
      }

      // Call the onSystemMessage callback if provided
      if (onSystemMessage) {
        onSystemMessage(text);
      }

      console.log('[StreamUIChat] System message sent via server:', text);
      return true;
    } catch (error) {
      console.error('[StreamUIChat] Error sending system message:', error);
      return false;
    }
  };

  // Expose the sendSystemMessage function to the parent component
  useEffect(() => {
    if (channel && onSystemMessage) {
      // Make the sendSystemMessage function available to the parent component
      (window as any).sendStreamSystemMessage = sendSystemMessage;
    }

    return () => {
      // Clean up when the component unmounts
      if ((window as any).sendStreamSystemMessage === sendSystemMessage) {
        delete (window as any).sendStreamSystemMessage;
      }
    };
  }, [channel, onSystemMessage]);

  // Initialize the chat
  useEffect(() => {
    const initializeChat = async () => {
      if (!user || !taskId) return;

      try {
        setLoading(true);
        setError(null);

        // Increment connection count
        incrementConnectionCount();

        // No need to check for API key availability anymore
        // We're using a hardcoded API key in the client.ts file

        // Get the user's name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        // Connect the user to Stream
        const streamClient = await connectUser(
          user.id,
          userName,
          user.id,
          profile?.avatar_url
        );

        // Set the client
        setClient(streamClient);

        // Get the task owner
        const { data: taskData, error: taskDataError } = await supabase
          .from('tasks')
          .select('user_id')
          .eq('id', taskId)
          .single();

        if (taskDataError) {
          throw new Error(`Error fetching task: ${taskDataError.message}`);
        }

        // Create an array of members including both the task owner and current user
        const members = [user.id];
        if (taskData?.user_id && taskData.user_id !== user.id) {
          members.push(taskData.user_id);
        }

        // Define the channel ID
        const channelId = `task-${taskId}`;

        try {
          // First try to get the channel if it exists
          try {
            // Try to get the channel with the client
            const taskChannel = streamClient.channel('messaging', channelId);
            await taskChannel.watch();

            // Channel exists, set it and we're done
            setChannel(taskChannel);
            setLoading(false);
            console.log('[StreamUIChat] Successfully connected to existing channel:', channelId);
          } catch (channelError) {
            // Channel doesn't exist or user doesn't have access, create it
            console.log('[StreamUIChat] Channel not found or not accessible, creating new channel...');

            // Create the channel using the API route
            const response = await fetch(getStreamApiUrl('/channels'), {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                taskId,
                taskTitle: taskTitle || 'Task Chat',
                members
              }),
            });

            if (!response.ok) {
              throw new Error('Failed to create channel via server');
            }

            const data = await response.json();
            console.log('[StreamUIChat] Channel created successfully via server:', data);

            // Now try to get the channel with the client again
            const taskChannel = streamClient.channel('messaging', channelId);
            await taskChannel.watch();

            setChannel(taskChannel);
            setLoading(false);
          }
        } catch (error) {
          console.error('[StreamUIChat] Error creating or accessing channel:', error);
          throw new Error('Failed to create or access channel');
        }
      } catch (err: any) {
        console.error('[StreamUIChat] Error initializing chat:', err);
        setError(err.message || 'Failed to initialize chat');
        setLoading(false);

        // Decrement connection count on error
        decrementConnectionCount(false);
      }
    };

    initializeChat();

    // Cleanup function
    return () => {
      console.log('[StreamUIChatVercel] Component unmounting');

      // Decrement connection count and let the manager handle disconnection
      try {
        decrementConnectionCount(true);
      } catch (error) {
        console.warn('[StreamUIChatVercel] Error in cleanup function:', error);
      }
    };
  }, [user, taskId, profile]);

  // Handle sending messages
  const handleSendMessage = (message: any) => {
    if (onSendMessage) {
      onSendMessage(message.text);
    }
  };

  // Custom theme options - using GetStream's built-in themes
  const theme = 'messaging light';

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <LoadingIndicator size={32} />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 text-red-500 ${className}`}>
        <p>Error: {error}</p>
        <p className="mt-2">There was a problem connecting to the chat. Please try refreshing the page.</p>
      </div>
    );
  }

  if (!client || !channel) {
    return (
      <div className={`p-4 text-gray-500 ${className}`}>
        <p>Chat is initializing. Please wait a moment...</p>
      </div>
    );
  }

  // No need for inline styles anymore, we're using the CSS file

  return (
    <div className={`str-chat-container ${className}`} style={{ height: '600px', maxHeight: '75vh' }}>
      <Chat client={client} theme={theme}>
        <Channel channel={channel} Message={SystemMessage}>
          <Window>
            <ChannelHeader />
            <MessageList />
            <MessageInput onSubmit={handleSendMessage} />
          </Window>
          <Thread />
        </Channel>
      </Chat>
    </div>
  );
};

export default StreamUIChatVercel;
