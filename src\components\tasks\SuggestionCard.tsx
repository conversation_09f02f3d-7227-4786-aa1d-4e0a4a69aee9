
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from "@/components/ui/card";
import { cn } from '@/lib/utils';

export interface SuggestionCardProps {
  id: string;
  title: string;
  image: string;
  category: string;
  onClick?: () => void;
  className?: string;
}

const SuggestionCard = ({ 
  id, 
  title, 
  image, 
  category,
  onClick,
  className 
}: SuggestionCardProps) => {
  const navigate = useNavigate();
  
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(`/post-task?category=${category}`);
    }
  };

  return (
    <Card 
      className={cn(
        "overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1",
        className
      )}
      onClick={handleClick}
    >
      <div className="relative h-40 overflow-hidden">
        <img 
          src={image} 
          alt={title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
        <div className="absolute bottom-0 left-0 p-3 text-white">
          <h3 className="text-lg font-semibold line-clamp-2">{title}</h3>
          <span className="text-xs bg-classtasker-orange/80 px-2 py-1 rounded-full">
            {category}
          </span>
        </div>
      </div>
    </Card>
  );
};

export default SuggestionCard;
