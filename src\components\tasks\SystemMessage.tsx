/**
 * SystemMessage Component
 *
 * A custom message component for system messages in GetStream chat.
 * System messages are displayed differently from regular user messages.
 */

import React from 'react';
import { useMessageContext, MessageSimple } from 'stream-chat-react';

export const SystemMessage: React.FC = () => {
  const { message } = useMessageContext();

  // Check if this is a system message
  const isSystemMessage = message.type === 'system';

  if (!isSystemMessage) {
    // For regular messages, use the default MessageSimple component
    return <MessageSimple />;
  }

  return (
    <div className="str-chat__message str-chat__message--system">
      <div className="str-chat__message-text">
        <div className="str-chat__message-text-inner">
          {message.text}
        </div>
      </div>
    </div>
  );
};

export default SystemMessage;
