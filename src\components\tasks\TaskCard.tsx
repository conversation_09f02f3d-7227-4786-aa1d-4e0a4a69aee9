
import { Link } from "react-router-dom";
import { Calendar, MapPin, PoundSterling, Tag } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { format } from "date-fns";
import TaskCardSupplierActions from "./TaskCardSupplierActions";
import { Task } from "@/services/taskService";

export interface TaskProps {
  id: string;
  title: string;
  description: string;
  location: string;
  location_formatted?: string;
  location_lat?: number;
  location_lng?: number;
  location_place_id?: string;
  dueDate: string;
  budget: number;
  category: string;
  status: 'open' | 'assigned' | 'in_progress' | 'pending_payment' | 'completed' | 'confirmed';
  offers?: number;
  visibility?: 'admin' | 'internal' | 'public';
  fullTask?: Task; // Optional full task object for supplier actions
  // Relationship badges
  showCreatedByMe?: boolean;
  showAssignedToMe?: boolean;
}

const TaskCard = ({
  id,
  title,
  description,
  location,
  location_formatted,
  location_lat,
  location_lng,
  location_place_id,
  dueDate,
  budget,
  category,
  status,
  offers = 0,
  visibility,
  fullTask,
  showCreatedByMe = false,
  showAssignedToMe = false,
}: TaskProps) => {
  // Extract just the town/city from the formatted location
  const extractTownFromLocation = (loc: string): string => {
    if (!loc) return '';

    // Try to extract the town/city from a formatted address
    // Format is typically: "Street, Town/City PostCode, Country"
    const parts = loc.split(',').map(part => part.trim());

    // Common UK postcodes pattern (e.g., GU14 8AY, SW1A 1AA)
    const postcodePattern = /\b[A-Z]{1,2}[0-9][0-9A-Z]?\s?[0-9][A-Z]{2}\b/;

    // If we have at least 2 parts, the second part is usually the town/city with postcode
    if (parts.length >= 2) {
      // For UK addresses, the town is typically in the second part
      let townPart = parts[1];

      // Remove any postcodes
      townPart = townPart.replace(postcodePattern, '').trim();

      // If the town part is now empty (was just a postcode), try the first part
      if (!townPart && parts.length > 0) {
        return parts[0].split(' ')[0]; // Just take the first word of the first part
      }

      // Return just the first word of the town (e.g., "Farnborough" from "Farnborough GU14 8AY")
      return townPart.split(' ')[0];
    }

    // For simple locations that might just be a town name
    if (parts.length === 1) {
      // Remove any postcodes and return the first word
      const simplePart = parts[0].replace(postcodePattern, '').trim();
      return simplePart.split(' ')[0];
    }

    // Fallback to just returning the first part if we can't parse it
    return parts[0];
  };

  // Use formatted location if available, otherwise use the original location
  const fullLocation = location_formatted || location;
  const displayLocation = extractTownFromLocation(fullLocation);
  return (
    <Card className="card-hover overflow-hidden h-full">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <h3 className="text-lg font-semibold line-clamp-1 hover:text-classtasker-blue transition-colors">{title}</h3>
          <div className="flex flex-col gap-1 items-end">
            {/* Status badge */}
            {visibility === 'admin' ? (
              <Badge
                variant="outline"
                className="bg-amber-100 text-amber-800 border-amber-200"
              >
                Pending Review
              </Badge>
            ) : (
              <Badge
                variant="outline"
                className={
                  status === 'open'
                    ? 'bg-green-100 text-green-800 border-green-200'
                    : status === 'interest'
                      ? 'bg-cyan-100 text-cyan-800 border-cyan-200'
                    : status === 'questions'
                      ? 'bg-cyan-100 text-cyan-800 border-cyan-200'
                    : status === 'assigned'
                      ? 'bg-blue-100 text-blue-800 border-blue-200'
                      : status === 'in_progress'
                        ? 'bg-indigo-100 text-indigo-800 border-indigo-200'
                        : status === 'pending_payment'
                          ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                          : status === 'completed'
                            ? 'bg-purple-100 text-purple-800 border-purple-200'
                            : status === 'confirmed'
                              ? 'bg-teal-100 text-teal-800 border-teal-200'
                              : 'bg-gray-100 text-gray-800 border-gray-200'
                }
              >
                {status === 'pending_payment' ? 'Payment Required' :
                 status === 'in_progress' ? 'In Progress' :
                 status === 'interest' ? 'Interest Expressed' :
                 status === 'questions' ? 'Discussion Phase' :
                 status.charAt(0).toUpperCase() + status.slice(1)}
              </Badge>
            )}

            {/* Relationship badge */}
            {(showCreatedByMe || showAssignedToMe) && (
              <Badge
                variant="outline"
                className={
                  showCreatedByMe && showAssignedToMe
                    ? 'bg-purple-100 text-purple-800 border-purple-200 text-xs'
                    : showCreatedByMe
                      ? 'bg-blue-100 text-blue-800 border-blue-200 text-xs'
                      : 'bg-green-100 text-green-800 border-green-200 text-xs'
                }
              >
                {showCreatedByMe && showAssignedToMe
                  ? 'Created & Assigned'
                  : showCreatedByMe
                    ? 'Created by me'
                    : 'Assigned to me'}
              </Badge>
            )}
          </div>
        </div>
        <div className="flex items-center text-sm text-gray-500">
          <MapPin size={14} className="mr-1" /> {displayLocation}
          {location_lat && location_lng && (
            <span className="ml-1 text-xs text-green-600">
              (Mapped)
            </span>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <p className="text-gray-600 line-clamp-2 mb-3 text-sm">{description}</p>
        <div className="flex justify-between items-center text-sm">
          <div className="flex items-center text-gray-600">
            <Calendar size={14} className="mr-1" />
            {typeof dueDate === 'string' ? format(new Date(dueDate), 'PP') : 'No due date'}
          </div>
          <div className="flex items-center text-gray-600">
            <Tag size={14} className="mr-1" />
            {category}
          </div>
        </div>
      </CardContent>

      <CardFooter className="border-t pt-3 flex flex-col">
        <div className="flex justify-between items-center w-full">
          <div className="flex items-center font-medium text-classtasker-blue">
            <PoundSterling size={16} />
            <span>{typeof budget === 'number' ? Number(budget).toFixed(2) : budget}</span>
          </div>

          <div className="text-sm text-gray-600">
            {offers} {offers === 1 ? 'offer' : 'offers'}
          </div>
        </div>

        {/* Show supplier actions for public tasks */}
        {visibility === 'public' && fullTask && (
          <TaskCardSupplierActions task={fullTask} />
        )}
      </CardFooter>
    </Card>
  );
};

export default TaskCard;
