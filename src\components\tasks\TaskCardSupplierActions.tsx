import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useOffers } from '@/hooks/use-offers';
import { Loader2, CheckCircle, PoundSterling } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Task } from '@/services/taskService';

interface TaskCardSupplierActionsProps {
  task: Task;
  onClick?: (e: React.MouseEvent) => void;
}

const TaskCardSupplierActions = ({ task, onClick }: TaskCardSupplierActionsProps) => {
  const [offerAmount, setOfferAmount] = useState(task?.budget?.toString() || '');
  const [offerMessage, setOfferMessage] = useState('');
  const [showCounterOffer, setShowCounterOffer] = useState(false);
  const { user, isSupplier } = useAuth();
  const { toast } = useToast();
  const { createOffer, isCreatingOffer } = useOffers();

  // Only show for suppliers and public tasks
  if (!isSupplier || task.visibility !== 'public') {
    return null;
  }

  const handleAcceptJob = (e: React.MouseEvent) => {
    // Stop propagation to prevent navigation
    e.preventDefault();
    e.stopPropagation();
    
    if (!user || !isSupplier) {
      console.warn("Cannot accept job - user not logged in or not a supplier", {
        loggedIn: !!user,
        isSupplier
      });
      return;
    }

    // Submit offer with exact budget amount
    if (process.env.NODE_ENV === 'development') {
      console.log("Accepting job at budget amount:", task.budget);
      }
    createOffer({
      task_id: task.id,
      amount: Number(task.budget),
      message: `I can complete this task for the listed budget of £${Number(task.budget).toFixed(2)}.`,
    });
  };

  const handleSubmitCounterOffer = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !isSupplier) {
      console.warn("Cannot submit counter offer - user not logged in or not a supplier", {
        loggedIn: !!user,
        isSupplier
      });
      return;
    }

    if (!offerAmount || !offerMessage) {
      console.warn("Missing required fields for counter offer");
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please provide both an amount and a message for your offer.",
      });
      return;
    }

    if (process.env.NODE_ENV === 'development') {

      console.log("Submitting counter offer:", {
      taskId: task.id,
      amount: offerAmount,
      message: offerMessage
    });


      }
    createOffer({
      task_id: task.id,
      amount: parseFloat(offerAmount),
      message: offerMessage,
    });

    // Reset form and close popover
    setOfferAmount(task?.budget?.toString() || '');
    setOfferMessage('');
    setShowCounterOffer(false);
  };

  return (
    <div className="mt-3 flex flex-col gap-2" onClick={(e) => e.stopPropagation()}>
      <Button
        onClick={handleAcceptJob}
        className="w-full bg-green-600 hover:bg-green-700 text-xs py-1 h-auto"
        disabled={isCreatingOffer}
        size="sm"
      >
        {isCreatingOffer ? (
          <><Loader2 className="mr-1 h-3 w-3 animate-spin" /> Processing...</>
        ) : (
          <><CheckCircle className="mr-1 h-3 w-3" /> Accept for £{Number(task?.budget).toFixed(2)}</>
        )}
      </Button>

      <Popover open={showCounterOffer} onOpenChange={setShowCounterOffer}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="w-full text-xs py-1 h-auto"
            disabled={isCreatingOffer}
            size="sm"
          >
            <PoundSterling className="mr-1 h-3 w-3" /> Suggest a different price
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" onClick={(e) => e.stopPropagation()}>
          <form onSubmit={handleSubmitCounterOffer} className="space-y-4">
            <div>
              <label htmlFor="amount" className="block text-sm font-medium mb-1">
                Your Price (£)
              </label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="1"
                value={offerAmount}
                onChange={(e) => setOfferAmount(e.target.value)}
                className="w-full"
              />
            </div>

            <div>
              <label htmlFor="message" className="block text-sm font-medium mb-1">
                Message
              </label>
              <Textarea
                id="message"
                value={offerMessage}
                onChange={(e) => setOfferMessage(e.target.value)}
                placeholder="Explain your price and what you can offer..."
                className="w-full"
                rows={3}
              />
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={isCreatingOffer}
            >
              {isCreatingOffer ? (
                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Submitting...</>
              ) : (
                "Submit Offer"
              )}
            </Button>
          </form>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default TaskCardSupplierActions;
