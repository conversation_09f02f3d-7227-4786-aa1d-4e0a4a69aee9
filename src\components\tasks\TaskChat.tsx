/**
 * TaskChat Component (Dummy)
 * 
 * This is a dummy component to fix the Vercel build error.
 * It is not used anywhere in the application, but it's needed
 * to satisfy the import in MessagesSection.tsx.
 */

import React from 'react';

interface TaskChatProps {
  taskId: string;
  taskOwnerId: string;
}

const TaskChat: React.FC<TaskChatProps> = () => {
  return (
    <div className="p-4 text-center">
      <p className="text-gray-500">This component has been replaced by GetStreamTaskChat.</p>
    </div>
  );
};

export default TaskChat;
