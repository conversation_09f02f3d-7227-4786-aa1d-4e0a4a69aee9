import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, RefreshCw } from 'lucide-react';
import { useFixedOffers } from '@/hooks/fixed-use-offers';
import { Task, Offer } from '@/services/taskService';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface TaskOwnerOfferActionsProps {
  task: Task;
  onTaskUpdated: () => void;
}

const TaskOwnerOfferActions = ({ task, onTaskUpdated }: TaskOwnerOfferActionsProps) => {
  const [offers, setOffers] = useState<Offer[]>([]);
  const [isLoadingOffers, setIsLoadingOffers] = useState(true);

  // Fetch offers for the task
  useEffect(() => {
    const fetchOffers = async () => {
      setIsLoadingOffers(true);
      try {
        if (process.env.NODE_ENV === 'development') {
    console.log(`TaskOwnerOfferActions: Fetching offers for task ${task.id}`);
  }
        const { data, error } = await supabase
          .from('offers')
          .select('*')
          .eq('task_id', task.id);

        if (error) {
          console.error('Error fetching offers:', error);
          // Set empty array to prevent crashes
          setOffers([]);
          return;
        }

        if (process.env.NODE_ENV === 'development') {
    console.log(`TaskOwnerOfferActions: Found ${data?.length || 0} offers for task ${task.id}`, data);
  }
        // Ensure we always set a valid array, even if data is null or undefined
        setOffers(data || []);
      } catch (error) {
        console.error('Error fetching offers:', error);
        // Set empty array to prevent crashes
        setOffers([]);
      } finally {
        setIsLoadingOffers(false);
      }
    };

    fetchOffers();
  }, [task.id]);
  // Initialize all hooks first, before any conditional logic
  const { user, userRole } = useAuth();
  const { acceptOffer, isAcceptingOffer, updateOfferStatus, isUpdatingOfferStatus } = useFixedOffers();

  // Now we can use variables and conditional logic
  const isAdmin = userRole === 'admin';
  const isTaskOwner = user && task && user.id === task.user_id;

  // DEBUG: Log detailed information about admin role determination
  if (process.env.NODE_ENV === 'development') {

    console.log('TaskOwnerOfferActions - Admin role check: completed');


    }
  // ONLY admins should be able to manage offers - teachers should not manage offers
  const canManageOffers = isAdmin; // Strict admin-only check

  if (process.env.NODE_ENV === 'development') {


    console.log('TaskOwnerOfferActions render: completed');



    }
  // Only show for admins (strict admin-only check)
  if (!canManageOffers) {
    if (process.env.NODE_ENV === 'development') {

      console.log('TaskOwnerOfferActions: Not rendering - user cannot manage offers (not admin.replace(/user.*/, 'hasUser: ' + !!user))', {
      isAdmin,
      userRole,
      canManageOffers
    });

      }
    return null;
  }

  if (process.env.NODE_ENV === 'development') {


    console.log('TaskOwnerOfferActions: User CAN manage offers completed');



    }
  // Only show offers that need action
  // If the task is already assigned, we shouldn't show pending offers
  const pendingOffers = task.status === 'assigned' || task.status === 'in_progress' ||
                        task.status === 'completed' || task.status === 'confirmed' ?
    [] :
    offers.filter(offer => {
      if (!offer) {
        console.warn('TaskOwnerOfferActions: Found null or undefined offer in offers array');
        return false;
      }

      if (process.env.NODE_ENV === 'development') {
    console.log(`Checking offer ${offer.id} status:`, {
        status: offer.status,
        taskStatus: task.status,
        rawStatus: offer.status
      });
  }
      // Show any offer that is not explicitly 'accepted' or 'rejected'
      // This ensures we show all offers that need action, regardless of specific status
      const needsAction = offer.status !== 'accepted' && offer.status !== 'rejected';

      // Log the decision for debugging
      if (process.env.NODE_ENV === 'development') {
    console.log(`Offer ${offer.id} needs action: ${needsAction}`);
  }
      return needsAction;
    });

  if (process.env.NODE_ENV === 'development') {


    console.log('TaskOwnerOfferActions: Pending offers:', {
    pendingOffers,
    count: pendingOffers.length,
    allOffers: offers,
    allOffersStatuses: offers.map(o => ({ id: o.id, status: o.status }))
  });



    }
  if (pendingOffers.length === 0) {
    if (process.env.NODE_ENV === 'development') {

      console.log('TaskOwnerOfferActions: No pending offers', {
      offersCount: offers.length,
      offersStatuses: offers.map(o => o.status),
      taskStatus: task.status
    });


      }
    // Show different messages based on task status
    if (task.status === 'assigned' || task.status === 'in_progress') {
      return (
        <Card className="mb-4 border-blue-300">
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">Task Assigned</h3>
            <p className="text-sm text-gray-600">
              This task has been assigned to a supplier and is {task.status === 'in_progress' ? 'in progress' : 'pending start'}.
            </p>
          </CardContent>
        </Card>
      );
    } else if (task.status === 'completed' || task.status === 'confirmed') {
      return (
        <Card className="mb-4 border-green-300">
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">Task {task.status === 'completed' ? 'Completed' : 'Confirmed'}</h3>
            <p className="text-sm text-gray-600">
              This task has been {task.status === 'completed' ? 'marked as completed by the supplier' : 'confirmed as completed'}.
            </p>
          </CardContent>
        </Card>
      );
    } else if (task.status === 'offer') {
      // Special case for offer status
      if (process.env.NODE_ENV === 'development') {
    console.log('TaskOwnerOfferActions: Task is in offer status but no pending offers found');
  }
      return (
        <Card className="mb-4 border-yellow-300">
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">Offers Received</h3>
            <p className="text-sm text-gray-600">
              This task has received offers, but they may be loading or there was an error fetching them.
            </p>
            <div className="mt-2">
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                size="sm"
              >
                <RefreshCw className="mr-2 h-4 w-4" /> Refresh Page
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    } else {
      // Default message for tasks with no offers
      return (
        <Card className="mb-4 border-yellow-300">
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">No Pending Offers</h3>
            <p className="text-sm text-gray-600">
              This task has not received any pending offers yet.
            </p>
          </CardContent>
        </Card>
      );
    }
  }

  const handleAcceptOffer = async (offerId: string) => {
    if (process.env.NODE_ENV === 'development') {
    console.log(`TaskOwnerOfferActions: Accepting offer ${offerId} for task ${task.id}`);
  }
    await acceptOffer({
      taskId: task.id,
      offerId: offerId
    });

    // Call onTaskUpdated to refresh the task data
    onTaskUpdated();
  };

  const handleRejectOffer = async (offerId: string) => {
    if (process.env.NODE_ENV === 'development') {
    console.log(`TaskOwnerOfferActions: Rejecting offer ${offerId}`);
  }
    await updateOfferStatus(offerId, 'rejected');

    // Refresh the offers list
    const { data } = await supabase
      .from('offers')
      .select('*')
      .eq('task_id', task.id);

    if (data) {
      setOffers(data);
    }
  };

  // Show loading state
  if (isLoadingOffers) {
    return (
      <Card className="mb-4 border-yellow-300">
        <CardContent className="p-4">
          <h3 className="font-semibold mb-2">Loading Offers</h3>
          <div className="flex items-center justify-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-blue-500 mr-2" />
            <span>Loading offers...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show message if no offers
  if (offers.length === 0) {
    return (
      <Card className="mb-4 border-yellow-300">
        <CardContent className="p-4">
          <h3 className="font-semibold mb-2">No Offers</h3>
          <p className="text-sm text-gray-600">
            This task has not received any offers yet.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-4 border-yellow-300">
      <CardContent className="p-4">
        <h3 className="font-semibold mb-2">Pending Offers</h3>
        <p className="text-sm text-gray-600 mb-4">
          You have {pendingOffers.length} pending offer{pendingOffers.length !== 1 ? 's' : ''} for this task.
        </p>

        <div className="space-y-3">
          {pendingOffers.map(offer => (
            <div key={offer.id} className="flex justify-between items-center p-2 border rounded-md">
              <div>
                <div className="font-medium flex items-center">
                  £{Number(offer.amount).toFixed(2)}
                  {Number(task.budget) !== Number(offer.amount) && (
                    <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                      Counter Offer
                    </span>
                  )}
                </div>
                <div className="text-sm text-gray-500 truncate max-w-[200px]">
                  {Number(task.budget) !== Number(offer.amount) ? (
                    <span className="font-medium text-gray-700">
                      Reason: <span className="italic">{offer.message}</span>
                    </span>
                  ) : (
                    offer.message
                  )}
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="default"
                  size="sm"
                  className="bg-classtasker-blue hover:bg-blue-600"
                  onClick={() => handleAcceptOffer(offer.id)}
                  disabled={isAcceptingOffer || isUpdatingOfferStatus}
                >
                  {isAcceptingOffer ?
                    <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Accepting...</> :
                    'Accept'}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:bg-red-50"
                  onClick={() => handleRejectOffer(offer.id)}
                  disabled={isUpdatingOfferStatus || isAcceptingOffer}
                >
                  {isUpdatingOfferStatus ?
                    <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Rejecting...</> :
                    'Reject'}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default TaskOwnerOfferActions;
