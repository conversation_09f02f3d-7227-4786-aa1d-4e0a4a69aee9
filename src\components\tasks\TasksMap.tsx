/// <reference path="../../types/google-maps.d.ts" />

import { useEffect, useRef, useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { loadGoogleMapsApi, isGoogleMapsLoaded } from '@/utils/load-google-maps';
import { createTasksInfoWindow } from './CustomInfoWindow';

interface Task {
  id: string;
  title: string;
  description: string;
  location: string;
  location_lat?: number;
  location_lng?: number;
  budget: number;
  category: string;
  [key: string]: any;
}

interface TasksMapProps {
  tasks: Task[];
  filterLocation?: string; // The location string from the filter
  centerLocation?: { lat: number; lng: number } | null;
  radius?: number;
}

const TasksMap = ({ tasks, filterLocation, centerLocation, radius }: TasksMapProps) => {
  const mapRef = useRef<HTMLDivElement>(null);
  // Use any type to avoid TypeScript errors
  const [map, setMap] = useState<any>(null);
  const [markers, setMarkers] = useState<any[]>([]);
  const [circle, setCircle] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isApiLoaded, setIsApiLoaded] = useState(false);

  // Refs for task hashing to prevent unnecessary updates
  const prevTaskHashRef = useRef<string>('');
  const prevCenterHashRef = useRef<string>('');
  const [filterCoordinates, setFilterCoordinates] = useState<{lat: number, lng: number} | null>(null);

  // Load Google Maps API directly - but only once
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {

      console.log('DEBUG: Checking Google Maps API status...');


      }
    // Check if API is already loaded
    if ((window as any).google && (window as any).google.maps) {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Google Maps API already loaded');

        }
      setIsApiLoaded(true);
      setIsLoading(false);
      return;
    }

    // Check if script is already in the document
    if (document.getElementById('google-maps-script')) {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Google Maps script tag already exists, waiting for it to load');

        }
      return;
    }

    if (process.env.NODE_ENV === 'development') {


      console.log('DEBUG: Loading Google Maps API...');



      }
    // Create a unique callback function name with timestamp to avoid conflicts
    const callbackName = 'googleMapsCallback_' + Date.now();

    // Create the callback function
    (window as any)[callbackName] = () => {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Google Maps API loaded successfully');

        }
      setIsApiLoaded(true);
      setIsLoading(false);
      delete (window as any)[callbackName];
    };

    // Get API key from Vite's built-in environment variables
    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
    if (!apiKey) {
      console.error('ERROR: Google Maps API key not found');
      setIsLoading(false);
      return;
    }

    // Create script element
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=${callbackName}`;
    script.async = true;
    script.defer = true;
    script.id = 'google-maps-script';

    // Handle errors
    script.onerror = (error) => {
      console.error('ERROR: Failed to load Google Maps API:', error);
      setIsLoading(false);
    };

    // Add script to document
    document.head.appendChild(script);

    // Cleanup
    return () => {
      delete (window as any)[callbackName];
    };
  }, []);

  // Initialize the map - only once when API is loaded
  useEffect(() => {
    // Only initialize map when API is loaded and ref is available
    if (!isApiLoaded || !mapRef.current) {
      return;
    }

    // Skip if map is already initialized
    if (map) {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Map already initialized, skipping initialization');

        }
      return;
    }

    try {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Initializing map...');


        }
      // Default to London
      const defaultCenter = { lat: 51.5074, lng: -0.1278 };

      // Simple map options
      const mapOptions = {
        center: defaultCenter,
        zoom: 10,
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: true,
        zoomControl: true,
        // Add styles to match the test view
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }]
          }
        ]
      };

      if (process.env.NODE_ENV === 'development') {


        console.log('DEBUG: Creating map with default center');



        }
      // Create the map
      const googleMaps = (window as any).google.maps;
      const newMap = new googleMaps.Map(mapRef.current, mapOptions);

      if (process.env.NODE_ENV === 'development') {


        console.log('DEBUG: Map created successfully');


        }
      setMap(newMap);
      setIsLoading(false);
    } catch (error) {
      console.error('ERROR: Failed to initialize map:', error);
      setIsLoading(false);
    }
  }, [isApiLoaded, mapRef.current]);

  // Get coordinates for filter location
  useEffect(() => {
    if (!isApiLoaded || !filterLocation) {
      setFilterCoordinates(null);
      return;
    }

    const getFilterCoords = async () => {
      try {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Getting coordinates for filter location:', filterLocation);


          }
        // Import the getCoordinates function from location-utils
        const { getCoordinates } = await import('@/utils/location-utils');

        const coords = await getCoordinates(filterLocation);
        if (coords && typeof coords.lat === 'number' && typeof coords.lng === 'number') {
          if (process.env.NODE_ENV === 'development') {

            console.log('DEBUG: Got coordinates for filter location:', coords);

            }
          setFilterCoordinates(coords);
        } else {
          console.error('DEBUG: Failed to get valid coordinates for filter location');
          setFilterCoordinates(null);
        }
      } catch (error) {
        console.error('ERROR: Failed to get coordinates for filter location:', error);
        setFilterCoordinates(null);
      }
    };

    getFilterCoords();
  }, [isApiLoaded, filterLocation]);

  // Update map center and bounds based on tasks and centerLocation - with task hash to prevent unnecessary updates
  useEffect(() => {
    if (!map || !isApiLoaded) {
      return;
    }

    // Create a hash of the tasks to detect real changes
    const tasksWithCoords = tasks.filter(task =>
      task.location_lat && task.location_lng &&
      !isNaN(task.location_lat) && !isNaN(task.location_lng)
    );

    // Create a hash based on task IDs and coordinates
    const taskHash = tasksWithCoords.map(task =>
      `${task.id}:${task.location_lat},${task.location_lng}`
    ).join('|');

    // Create a hash for the filter/center location
    const filterHash = filterLocation || '';
    const centerHash = filterCoordinates ?
      `${filterCoordinates.lat},${filterCoordinates.lng}` :
      (centerLocation ? `${centerLocation.lat},${centerLocation.lng}` : 'none');
    const radiusHash = radius ? radius.toString() : 'none';
    const locationHash = `${filterHash}|${centerHash}|${radiusHash}`;

    // Skip if nothing has changed
    if (taskHash === prevTaskHashRef.current && centerHash === prevCenterHashRef.current) {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Skipping map update - tasks and center unchanged');

        }
      return;
    }

    // Update the hashes
    prevTaskHashRef.current = taskHash;
    prevCenterHashRef.current = centerHash;

    try {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Updating map center and bounds...');


        }
      const googleMaps = (window as any).google.maps;
      let center;

      // Priority 1: Use filter coordinates if available - ALWAYS center on filter location if it exists
      if (filterCoordinates) {
        center = filterCoordinates;
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Using filter location coordinates:', center);

          }
        map.setCenter(center);

        // Set zoom based on radius
        if (radius) {
          // Calculate zoom level based on radius
          // Smaller radius = higher zoom level
          const zoomLevel = Math.max(7, Math.min(14, Math.round(14 - Math.log(radius) / Math.log(2))));
          map.setZoom(zoomLevel);
          if (process.env.NODE_ENV === 'development') {

            console.log('DEBUG: Set zoom level based on radius:', zoomLevel);

            }
        } else {
          map.setZoom(10); // Default zoom
        }

        // Force the map to render by triggering a resize event
        setTimeout(() => {
          window.dispatchEvent(new Event('resize'));
          if (process.env.NODE_ENV === 'development') {

            console.log('DEBUG: Triggered resize event to force map render');

            }
        }, 100);
      }
      // Priority 2: Use provided center location
      else if (centerLocation &&
          typeof centerLocation.lat === 'number' &&
          typeof centerLocation.lng === 'number' &&
          !isNaN(centerLocation.lat) &&
          !isNaN(centerLocation.lng)) {
        center = centerLocation;
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Using provided center location:', center);

          }
        map.setCenter(center);
      }
      // Priority 3: Use task coordinates
      else if (tasksWithCoords.length > 0) {
        // Check if all tasks have the same coordinates
        const allSameCoordinates = tasksWithCoords.every(task =>
          task.location_lat === tasksWithCoords[0].location_lat &&
          task.location_lng === tasksWithCoords[0].location_lng
        );

        if (allSameCoordinates) {
          // Just center on the single location
          center = {
            lat: tasksWithCoords[0].location_lat,
            lng: tasksWithCoords[0].location_lng
          };
          map.setCenter(center);
          map.setZoom(12); // Good zoom level for a single location
          if (process.env.NODE_ENV === 'development') {

            console.log('DEBUG: Centered on single location for all tasks');

            }
        } else {
          if (process.env.NODE_ENV === 'development') {

            console.log('DEBUG: Fitting bounds to include all tasks');

            }
          const bounds = new googleMaps.LatLngBounds();

          tasksWithCoords.forEach(task => {
            bounds.extend({
              lat: task.location_lat,
              lng: task.location_lng
            });
          });

          map.fitBounds(bounds);

          // Adjust zoom if too close
          const listener = googleMaps.event.addListenerOnce(map, 'idle', () => {
            if (map.getZoom() > 15) {
              map.setZoom(15);
            }
          });
        }
      }
    } catch (error) {
      console.error('ERROR: Failed to update map center and bounds:', error);
    }
  }, [map, isApiLoaded, centerLocation, filterCoordinates, radius, tasks]);

  // Add/update radius circle when filter coordinates or radius changes
  useEffect(() => {
    // Always check if we have the necessary components for creating a circle
    if (!map || !isApiLoaded) {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Map or API not loaded, cannot create circle');

        }
      return;
    }

    // If we don't have a radius, remove any existing circle
    if (!radius) {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: No radius specified, removing circle if it exists');

        }
      if (circle) {
        circle.setMap(null);
        setCircle(null);
      }
      return;
    }

    // Get center coordinates - prefer filter coordinates
    const centerCoords = filterCoordinates || centerLocation;

    // If we don't have valid center coordinates, remove any existing circle
    if (!centerCoords) {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: No center coordinates available, removing circle if it exists');

        }
      if (circle) {
        circle.setMap(null);
        setCircle(null);
      }
      return;
    }

    // Validate center coordinates
    if (typeof centerCoords.lat !== 'number' || typeof centerCoords.lng !== 'number' ||
        isNaN(centerCoords.lat) || isNaN(centerCoords.lng)) {
      console.error('DEBUG: Invalid center coordinates:', centerCoords);
      if (circle) {
        circle.setMap(null);
        setCircle(null);
      }
      return;
    }

    try {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Creating radius circle at', centerCoords, 'with radius', radius, 'miles');


        }
      // Remove old circle if it exists
      if (circle) {
        circle.setMap(null);
      }

      // Get Google Maps API
      const googleMaps = (window as any).google.maps;

      // Create new circle with improved visibility
      const newCircle = new googleMaps.Circle({
        map,
        center: centerCoords,
        radius: radius * 1609.34, // Convert miles to meters
        strokeColor: '#3b82f6',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#3b82f6',
        fillOpacity: 0.1,
        clickable: false, // Don't capture clicks
        zIndex: 1 // Ensure it's below markers
      });

      setCircle(newCircle);
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Circle created successfully');


        }
      // Force the map to render by triggering a resize event
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 100);

      // Only adjust bounds if we're not already centered on the filter location
      if (!filterCoordinates) {
        // Fit bounds to include the circle
        const bounds = new googleMaps.LatLngBounds();

        // Create points to extend the bounds
        const northEast = new googleMaps.LatLng(
          centerCoords.lat + (radius * 0.01),
          centerCoords.lng + (radius * 0.015)
        );

        const southWest = new googleMaps.LatLng(
          centerCoords.lat - (radius * 0.01),
          centerCoords.lng - (radius * 0.015)
        );

        bounds.extend(northEast);
        bounds.extend(southWest);
        map.fitBounds(bounds);

        if (process.env.NODE_ENV === 'development') {


          console.log('DEBUG: Map bounds adjusted for circle');


          }
      }
    } catch (error) {
      console.error('ERROR: Failed to create circle:', error);
    }
  }, [map, filterCoordinates, centerLocation, radius, isApiLoaded]);

  // Add/update markers when tasks change - with memoization to prevent unnecessary updates
  useEffect(() => {
    if (!map || !isApiLoaded) return;

    // Create a hash of the tasks to detect real changes
    const tasksWithCoords = tasks.filter(task =>
      task.location_lat && task.location_lng &&
      !isNaN(task.location_lat) && !isNaN(task.location_lng)
    );

    // Create a hash based on task IDs and coordinates
    const taskHash = tasksWithCoords.map(task =>
      `${task.id}:${task.location_lat},${task.location_lng}`
    ).join('|');

    // Skip if nothing has changed
    if (taskHash === prevTaskHashRef.current && markers.length > 0) {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Skipping marker creation - tasks unchanged');

        }
      return;
    }

    // Update the hash
    prevTaskHashRef.current = taskHash;

    // Use a timeout to debounce marker creation
    const timeoutId = setTimeout(() => {
      try {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Creating markers for', tasks.length, 'tasks');


          }
        // Remove old markers
        markers.forEach(marker => marker.setMap(null));

        // Get Google Maps API
        const googleMaps = (window as any).google.maps;

        if (process.env.NODE_ENV === 'development') {


          console.log('DEBUG: Found', tasksWithCoords.length, 'tasks with valid coordinates');



          }
        if (tasksWithCoords.length === 0) {
          if (process.env.NODE_ENV === 'development') {

            console.log('DEBUG: No tasks with valid coordinates found');

            }
          setMarkers([]);

          // Even with no tasks, ensure the map is centered on the filter location
          if (filterCoordinates && radius) {
            if (process.env.NODE_ENV === 'development') {

              console.log('DEBUG: Centering map on filter location with no tasks');

              }
            map.setCenter(filterCoordinates);

            // Calculate zoom level based on radius
            const zoomLevel = Math.max(7, Math.min(14, Math.round(14 - Math.log(radius) / Math.log(2))));
            map.setZoom(zoomLevel);
          }
          return;
        }

        // Check if all tasks have the same coordinates
        const allSameCoordinates = tasksWithCoords.every(task =>
          task.location_lat === tasksWithCoords[0].location_lat &&
          task.location_lng === tasksWithCoords[0].location_lng
        );

        if (allSameCoordinates) {
          if (process.env.NODE_ENV === 'development') {

            console.log('DEBUG: All tasks have the same coordinates, using a single marker with count');


            }
          // Create a single marker for all tasks
          const position = {
            lat: tasksWithCoords[0].location_lat,
            lng: tasksWithCoords[0].location_lng
          };

          // Create marker with custom icon - NO ANIMATION
          const marker = new googleMaps.Marker({
            position,
            map,
            title: `${tasksWithCoords.length} tasks at this location`,
            // No animation to prevent flickering
            icon: {
              url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png'
            }
          });

          // Create more compact content for info window with all tasks
          const tasksContent = tasksWithCoords.map(task => `
            <div style="margin-bottom: 8px; padding-bottom: 8px; border-bottom: 1px solid #eee;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <h4 style="font-weight: 600; font-size: 13px; margin: 0; flex: 1; overflow: hidden; text-overflow: ellipsis;">${task.title}</h4>
                <span style="font-weight: 600; font-size: 12px; margin-left: 8px;">£${task.budget}</span>
              </div>
              <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 4px;">
                <span style="font-size: 11px; color: #666; flex: 1; overflow: hidden; text-overflow: ellipsis;">${task.location}</span>
                <a href="/tasks/${task.id}?messages=true"
                   style="color: #3b82f6; font-size: 11px; text-decoration: none; white-space: nowrap;">
                   View
                </a>
              </div>
            </div>
          `).join('');

          // Log the number of tasks in the info window
          if (process.env.NODE_ENV === 'development') {

            console.log('DEBUG: Creating info window with', tasksWithCoords.length, 'tasks');


            }
          // Use our custom info window implementation with proper scrolling
          createTasksInfoWindow(googleMaps, marker, tasksContent, tasksWithCoords.length);

          setMarkers([marker]);
          if (process.env.NODE_ENV === 'development') {

            console.log('DEBUG: Created 1 marker for all tasks');

            }
          return;
        }

        // Create new markers - without animation to prevent flickering
        const newMarkers = tasksWithCoords.map(task => {
          const position = {
            lat: task.location_lat,
            lng: task.location_lng
          };

          // Create marker with custom icon but NO animation
          const marker = new googleMaps.Marker({
            position,
            map,
            title: task.title,
            // No animation to prevent flickering
            icon: {
              url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png'
            }
          });

          // Create content for single task info window
          const taskContent = `
            <div style="margin-bottom: 8px; padding-bottom: 8px;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <h4 style="font-weight: 600; font-size: 13px; margin: 0; flex: 1; overflow: hidden; text-overflow: ellipsis;">${task.title}</h4>
                <span style="font-weight: 600; font-size: 12px; margin-left: 8px;">£${task.budget}</span>
              </div>
              <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 4px;">
                <span style="font-size: 11px; color: #666; flex: 1; overflow: hidden; text-overflow: ellipsis;">${task.location}</span>
                <a href="/tasks/${task.id}?messages=true"
                   style="color: #3b82f6; font-size: 11px; text-decoration: none; white-space: nowrap;">
                   View
                </a>
              </div>
            </div>
          `;

          // Use our custom info window implementation for single task
          createTasksInfoWindow(googleMaps, marker, taskContent, 1);

          return marker;
        });

        setMarkers(newMarkers);
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Created', newMarkers.length, 'markers');

          }
      } catch (error) {
        console.error('ERROR: Failed to create markers:', error);
      }
    }, 300); // 300ms debounce

    // Clean up timeout
    return () => clearTimeout(timeoutId);
  }, [map, tasks, isApiLoaded]);

  if (isLoading || !isApiLoaded) {
    return (
      <div className="w-full h-[500px] rounded-lg flex items-center justify-center bg-gray-100 border">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-classtasker-blue border-opacity-50 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">Loading map...</p>
          <p className="text-sm text-gray-500 mt-2">API Loaded: {isApiLoaded ? 'Yes' : 'No'}</p>
          <p className="text-sm text-gray-500">Map Loading: {isLoading ? 'Yes' : 'No'}</p>

          <div className="flex flex-col space-y-2 mt-4">
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              onClick={() => {
                if (process.env.NODE_ENV === 'development') {

                  console.log('DEBUG: Manually refreshing page to reload map...');

                  }
                // Instead of trying to reload the API, just refresh the page
                // This is more reliable than trying to reload the API
                window.location.reload();
              }}
            >
              Refresh Page
            </button>

            <button
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
              onClick={() => {
                window.open('/test-maps-api.html', '_blank');
              }}
            >
              Open Maps API Test Page
            </button>

            <button
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
              onClick={() => {
                // Load the test script
                const script = document.createElement('script');
                script.src = '/src/utils/test-google-maps.js';
                document.head.appendChild(script);

                // Wait for the script to load
                script.onload = () => {
                  alert('Test script loaded. Check the console for test functions.');
                  if (process.env.NODE_ENV === 'development') {

                    console.log('Run window.testGoogleMaps.runAllTests() to test the API');

                    }
                };
              }}
            >
              Load Test Script
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-[500px] rounded-lg overflow-hidden border">
      <div ref={mapRef} className="w-full h-full" />
    </div>
  );
};

export default TasksMap;