import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LucideIcon, Info, MapPin, School, Briefcase, Users } from 'lucide-react';

interface FriendlyAccessMessageProps {
  /**
   * Title of the message
   */
  title?: string;
  
  /**
   * Main message to display
   */
  message: string;
  
  /**
   * Optional additional information
   */
  additionalInfo?: string;
  
  /**
   * Optional how it works explanation
   */
  howItWorks?: string;
  
  /**
   * Icon to display (defaults to Info)
   */
  icon?: LucideIcon;
  
  /**
   * Icon color class (defaults to 'text-blue-500')
   */
  iconColorClass?: string;
  
  /**
   * Icon background color class (defaults to 'bg-blue-50')
   */
  iconBgClass?: string;
  
  /**
   * Primary button text
   */
  primaryButtonText?: string;
  
  /**
   * Primary button action
   */
  primaryButtonAction?: () => void;
  
  /**
   * Secondary button text
   */
  secondaryButtonText?: string;
  
  /**
   * Secondary button action
   */
  secondaryButtonAction?: () => void;
  
  /**
   * Path to navigate to when primary button is clicked (alternative to primaryButtonAction)
   */
  primaryButtonPath?: string;
  
  /**
   * Path to navigate to when secondary button is clicked (alternative to secondaryButtonAction)
   */
  secondaryButtonPath?: string;
  
  /**
   * Additional content to render
   */
  children?: React.ReactNode;
}

/**
 * A friendly access message component that provides information instead of a harsh "Access Denied" message
 */
const FriendlyAccessMessage: React.FC<FriendlyAccessMessageProps> = ({
  title = 'Information',
  message,
  additionalInfo,
  howItWorks,
  icon: Icon = Info,
  iconColorClass = 'text-blue-500',
  iconBgClass = 'bg-blue-50',
  primaryButtonText = 'Go to Dashboard',
  primaryButtonAction,
  secondaryButtonText,
  secondaryButtonAction,
  primaryButtonPath = '/dashboard',
  secondaryButtonPath,
  children,
}) => {
  const navigate = useNavigate();
  
  const handlePrimaryClick = () => {
    if (primaryButtonAction) {
      primaryButtonAction();
    } else if (primaryButtonPath) {
      navigate(primaryButtonPath);
    }
  };
  
  const handleSecondaryClick = () => {
    if (secondaryButtonAction) {
      secondaryButtonAction();
    } else if (secondaryButtonPath) {
      navigate(secondaryButtonPath);
    }
  };
  
  return (
    <div className="p-4">
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col items-center text-center">
            <div className={`p-3 ${iconBgClass} rounded-full mb-4`}>
              <Icon className={`h-10 w-10 ${iconColorClass}`} />
            </div>
            
            <h2 className="text-xl font-semibold text-gray-800 mb-2">{title}</h2>
            
            <div className="space-y-4 mb-6">
              <p className="text-gray-600">
                {message}
              </p>
              
              {howItWorks && (
                <div className="bg-amber-50 p-4 rounded-md border border-amber-100">
                  <p className="text-amber-800">
                    <span className="font-medium">How it works:</span> {howItWorks}
                  </p>
                </div>
              )}
              
              {additionalInfo && (
                <p className="text-gray-600">
                  {additionalInfo}
                </p>
              )}
              
              {children}
            </div>
            
            <div className="w-full space-y-2">
              <Button
                className="w-full"
                onClick={handlePrimaryClick}
              >
                {primaryButtonText}
              </Button>
              
              {secondaryButtonText && (
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={handleSecondaryClick}
                >
                  {secondaryButtonText}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FriendlyAccessMessage;
