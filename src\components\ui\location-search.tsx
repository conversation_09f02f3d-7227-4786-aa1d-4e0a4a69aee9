import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { MapPin, X, Search } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Slider
} from '@/components/ui/slider';

// Track script loading state globally to prevent multiple load attempts
let isLoadingScript = false;
let isScriptLoaded = false;
const scriptLoadCallbacks: (() => void)[] = [];

// Load Google Maps API script - optimized to load only once across the application
const loadGoogleMapsScript = (callback: () => void) => {
  // If script is already loaded, call callback immediately
  if (isScriptLoaded && window.google && window.google.maps && window.google.maps.places) {
    if (process.env.NODE_ENV === 'development') {
      console.log('DEBUG: Google Maps script already loaded, calling callback immediately');
      }
    callback();
    return;
  }

  // If script is currently loading, add callback to queue
  if (isLoadingScript) {
    if (process.env.NODE_ENV === 'development') {
      console.log('DEBUG: Google Maps script is loading, adding callback to queue');
      }
    scriptLoadCallbacks.push(callback);
    return;
  }

  // Start loading the script
  isLoadingScript = true;
  scriptLoadCallbacks.push(callback);

  if (process.env.NODE_ENV === 'development') {

    console.log('DEBUG: Starting Google Maps script load');


    }
  // Create the script element - get API key from environment variables only
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

  if (!apiKey) {
    console.warn('Google Maps API key is missing. Location search functionality will be limited.');
    // Call all callbacks even without loading the script
    scriptLoadCallbacks.forEach(cb => setTimeout(cb, 0));
    scriptLoadCallbacks.length = 0;
    isLoadingScript = false;
    return;
  }

  // Set a timeout to ensure callbacks are called even if the script fails to load
  const timeoutId = setTimeout(() => {
    console.warn('Google Maps API loading timed out. Using basic location search.');
    scriptLoadCallbacks.forEach(cb => cb());
    scriptLoadCallbacks.length = 0;
    isLoadingScript = false;
  }, 5000); // 5 second timeout

  const script = document.createElement('script');
  script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
  script.async = true;
  script.defer = true;

  script.onload = () => {
    clearTimeout(timeoutId);
    // Check if the Places API is actually available
    if (window.google && window.google.maps && window.google.maps.places) {
      if (process.env.NODE_ENV === 'development') {
        console.log('DEBUG: Google Maps script loaded successfully');
        }
      isScriptLoaded = true;
      // Call all queued callbacks
      scriptLoadCallbacks.forEach(cb => cb());
    } else {
      console.warn('Google Maps Places API not available. Using basic location search.');
      // Call all callbacks anyway
      scriptLoadCallbacks.forEach(cb => cb());
    }
    scriptLoadCallbacks.length = 0;
    isLoadingScript = false;
  };

  script.onerror = () => {
    clearTimeout(timeoutId);
    console.error('Failed to load Google Maps API. Location search functionality will be limited.');
    // Call all callbacks even on error
    scriptLoadCallbacks.forEach(cb => cb());
    scriptLoadCallbacks.length = 0;
    isLoadingScript = false;
  };

  document.head.appendChild(script);
};

interface LocationSearchProps {
  value: string;
  onChange: (location: string) => void;
  onRadiusChange?: (radius: number) => void;
  radius?: number;
  placeholder?: string;
  label?: string;
  className?: string;
  showRadius?: boolean;
}

const LocationSearch: React.FC<LocationSearchProps> = ({
  value,
  onChange,
  onRadiusChange,
  radius = 10,
  placeholder = 'Enter location',
  label = 'Location',
  className = '',
  showRadius = false
}) => {
  const [suggestions, setSuggestions] = useState<google.maps.places.AutocompletePrediction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const sessionToken = useRef<google.maps.places.AutocompleteSessionToken | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Load Google Maps script
  useEffect(() => {
    try {
      loadGoogleMapsScript(() => {
        setScriptLoaded(true);

        // Initialize autocomplete service
        if (window.google && window.google.maps && window.google.maps.places) {
          try {
            autocompleteService.current = new google.maps.places.AutocompleteService();
            sessionToken.current = new google.maps.places.AutocompleteSessionToken();

            // Create a dummy div for PlacesService (it requires a DOM element)
            const dummyDiv = document.createElement('div');
            placesService.current = new google.maps.places.PlacesService(dummyDiv);
          } catch (error) {
            console.error('Error initializing Google Maps services:', error);
            // Continue without autocomplete functionality
          }
        } else {
          console.warn('Google Maps Places API not available. Using basic location search.');
        }
      });
    } catch (error) {
      console.error('Error loading Google Maps script:', error);
      // Continue without Google Maps functionality
    }
  }, []);

  // Update input value when the value prop changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Debounce function to limit API calls
  const debounce = (func: Function, wait: number) => {
    let timeout: ReturnType<typeof setTimeout> | null = null;

    return (...args: any[]) => {
      const later = () => {
        timeout = null;
        func(...args);
      };

      if (timeout) clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  // Handle input change with debouncing
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Update the parent component with the new value
    onChange(newValue);

    // Don't make API calls for short inputs
    if (newValue.length <= 2) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    // Use debounced function for API calls
    debouncedGetPredictions(newValue);
  };

  // Debounced function to get predictions
  const debouncedGetPredictions = debounce((value: string) => {
    // Only attempt to use Google Places API if it's available
    if (scriptLoaded && autocompleteService.current) {
      setIsLoading(true);
      setShowSuggestions(true);

      try {
        if (process.env.NODE_ENV === 'development') {
          console.log('DEBUG: Getting place predictions for:', value);
          }
        autocompleteService.current.getPlacePredictions(
          {
            input: value,
            sessionToken: sessionToken.current,
            componentRestrictions: { country: 'gb' }, // Restrict to UK
            types: ['geocode', 'establishment'] // Include both addresses and establishments
          },
          (predictions, status) => {
            setIsLoading(false);

            if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
              if (process.env.NODE_ENV === 'development') {
                console.log(`DEBUG: Received ${predictions.length} predictions`);
                }
              setSuggestions(predictions);
            } else {
              if (process.env.NODE_ENV === 'development') {
                console.log('DEBUG: No predictions received, status:', status);
                }
              setSuggestions([]);
            }
          }
        );
      } catch (error) {
        console.error('Error getting place predictions:', error);
        setIsLoading(false);
        setSuggestions([]);
      }
    } else {
      // If Google Places API is not available, just use the input as-is
      if (process.env.NODE_ENV === 'development') {
        console.log('DEBUG: Google Places API not available, skipping predictions');
        }
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, 300); // 300ms debounce

  // Handle suggestion selection
  const handleSelectSuggestion = (suggestion: google.maps.places.AutocompletePrediction) => {
    try {
      if (placesService.current && suggestion.place_id) {
        placesService.current.getDetails(
          {
            placeId: suggestion.place_id,
            fields: ['formatted_address', 'name'],
            sessionToken: sessionToken.current
          },
          (place, status) => {
            if (status === google.maps.places.PlacesServiceStatus.OK && place) {
              const locationName = place.formatted_address || place.name || suggestion.description;
              setInputValue(locationName);
              onChange(locationName);
              setShowSuggestions(false);

              // Reset session token after selection
              try {
                sessionToken.current = new google.maps.places.AutocompleteSessionToken();
              } catch (error) {
                console.error('Error creating new session token:', error);
              }
            } else {
              // Fallback to using the suggestion description
              setInputValue(suggestion.description);
              onChange(suggestion.description);
              setShowSuggestions(false);
            }
          }
        );
      } else {
        setInputValue(suggestion.description);
        onChange(suggestion.description);
        setShowSuggestions(false);
      }
    } catch (error) {
      console.error('Error selecting suggestion:', error);
      // Fallback to using the suggestion description
      setInputValue(suggestion.description);
      onChange(suggestion.description);
      setShowSuggestions(false);
    }
  };

  // Handle radius change
  const handleRadiusChange = (value: number[]) => {
    if (onRadiusChange) {
      const radiusValue = value[0];

      // Ensure radius is at least 5 miles
      const effectiveRadius = Math.max(5, radiusValue);

      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Radius changed to:', effectiveRadius,
        effectiveRadius !== radiusValue ? `(adjusted from ${radiusValue})` : '');


        }
      // If the radius was adjusted, update the UI
      if (effectiveRadius !== radiusValue) {
        // Use setTimeout to avoid React state update conflicts
        setTimeout(() => onRadiusChange(effectiveRadius), 0);
      } else {
        onRadiusChange(effectiveRadius);
      }
    }
  };

  // Clear input
  const handleClear = () => {
    setInputValue('');
    onChange('');
    setSuggestions([]);
    setShowSuggestions(false);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <div className={className}>
      <div className="space-y-2">
        <Label htmlFor="location" className="flex items-center">
          <MapPin className="h-4 w-4 mr-1.5 text-gray-500" />
          {label}
        </Label>

        <div className="relative">
          <Input
            id="location"
            ref={inputRef}
            placeholder={placeholder}
            value={inputValue}
            onChange={handleInputChange}
            onFocus={() => inputValue.length > 2 && setShowSuggestions(true)}
            onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
            className="pr-8 border-gray-300 focus:border-classtasker-blue focus:ring-classtasker-blue/20"
          />

          {inputValue && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-2 text-gray-400 hover:text-gray-600"
              onClick={handleClear}
            >
              <X className="h-4 w-4" />
            </Button>
          )}

          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
              {suggestions.map((suggestion) => (
                <div
                  key={suggestion.place_id}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-start"
                  onMouseDown={() => handleSelectSuggestion(suggestion)}
                >
                  <MapPin className="h-4 w-4 mr-2 mt-1 text-gray-500 flex-shrink-0" />
                  <div>
                    <div className="text-sm font-medium">{suggestion.structured_formatting.main_text}</div>
                    <div className="text-xs text-gray-500">{suggestion.structured_formatting.secondary_text}</div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {isLoading && (
            <div className="absolute right-8 top-0 h-full flex items-center pr-2">
              <div className="animate-spin h-4 w-4 border-2 border-classtasker-blue border-opacity-50 border-t-transparent rounded-full"></div>
            </div>
          )}
        </div>
      </div>

      {showRadius && (
        <div className="mt-4 space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="radius" className="flex items-center text-sm">
              <Search className="h-4 w-4 mr-1.5 text-gray-500" />
              Search Radius
            </Label>
            <span className="text-sm font-medium bg-gray-100 px-2 py-1 rounded-md">
              {radius} miles
            </span>
          </div>

          <Slider
            id="radius"
            defaultValue={[radius]}
            min={5}  // Minimum radius of 5 miles
            max={45}  // Maximum radius of 45 miles for effective filtering
            step={1}
            value={[radius < 5 ? 5 : (radius > 45 ? 45 : radius)]}  // Ensure radius is between 5 and 45
            onValueChange={handleRadiusChange}
            className="py-2"
            aria-label="Search radius"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>5 miles</span>
            <span>25 miles</span>
            <span>45 miles</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationSearch;
