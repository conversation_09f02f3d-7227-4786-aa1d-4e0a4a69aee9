import { useNotifications } from '@/contexts/NotificationContext';
import { Bell } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';

interface NotificationBadgeProps {
  variant?: 'icon' | 'button';
}

const NotificationBadge = ({ variant = 'icon' }: NotificationBadgeProps) => {
  const { unreadCount } = useNotifications();

  if (variant === 'button') {
    return (
      <Button variant="ghost" size="sm" asChild>
        <Link to="/notifications" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] text-white">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
          <span className="sr-only">Notifications</span>
        </Link>
      </Button>
    );
  }

  return (
    <Link to="/notifications" className="relative">
      <Bell className="h-5 w-5" />
      {unreadCount > 0 && (
        <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] text-white">
          {unreadCount > 9 ? '9+' : unreadCount}
        </span>
      )}
      <span className="sr-only">Notifications</span>
    </Link>
  );
};

export default NotificationBadge;
