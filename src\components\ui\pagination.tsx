
import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface PaginationProps {
  total: number;
  perPage: number;
  currentPage: number;
  onPageChange: (page: number) => void;
}

export function Pagination({ total, perPage, currentPage, onPageChange }: PaginationProps) {
  // Add debug logging
  if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Pagination component received:', { total, perPage, currentPage });
  }
  // Ensure we have valid values
  const validTotal = Math.max(0, total || 0);
  const validPerPage = Math.max(1, perPage || 1);
  const totalPages = Math.max(1, Math.ceil(validTotal / validPerPage));

  // Ensure current page is within valid range
  const validCurrentPage = Math.max(1, Math.min(currentPage || 1, totalPages));

  // Log calculated values
  if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Pagination calculated:', {
    validTotal,
    validPerPage,
    totalPages,
    validCurrentPage
  });
  }
  const pages = Array.from({ length: totalPages }, (_, i) => i + 1);

  // Show a limited number of pages with ellipsis for better UX
  const getVisiblePages = () => {
    if (totalPages <= 7) return pages;

    if (validCurrentPage <= 3) {
      return [...pages.slice(0, 5), '...', totalPages];
    } else if (validCurrentPage >= totalPages - 2) {
      return [1, '...', ...pages.slice(totalPages - 5)];
    } else {
      return [1, '...', validCurrentPage - 1, validCurrentPage, validCurrentPage + 1, '...', totalPages];
    }
  };

  const visiblePages = getVisiblePages();

  return (
    <div className="flex items-center justify-center space-x-2">
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(validCurrentPage - 1)}
        disabled={validCurrentPage === 1}
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>

      {visiblePages.map((page, i) => {
        if (page === '...') {
          return (
            <span key={`ellipsis-${i}`} className="px-2">...</span>
          );
        }

        return (
          <Button
            key={`page-${page}`}
            variant={validCurrentPage === page ? 'default' : 'outline'}
            size="sm"
            onClick={() => {
              if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Pagination button clicked for page', page);
  }
              onPageChange(page as number);
            }}
            className={validCurrentPage === page ? 'bg-classtasker-blue hover:bg-blue-600' : ''}
          >
            {page}
          </Button>
        );
      })}

      <Button
        variant="outline"
        size="icon"
        onClick={() => {
          if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Next page button clicked, going from', validCurrentPage, 'to', validCurrentPage + 1);
  }
          onPageChange(validCurrentPage + 1);
        }}
        disabled={validCurrentPage >= totalPages}
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
}
