// API configuration
const isDevelopment = import.meta.env.DEV || process.env.NODE_ENV === 'development';

// Base API URL - use the admin server in development
export const API_BASE_URL = isDevelopment 
  ? 'http://localhost:3001' 
  : import.meta.env.VITE_API_URL || window.location.origin;

// Stripe Connect API endpoints
export const STRIPE_CONNECT_API = {
  CREATE_ACCOUNT: `${API_BASE_URL}/api/stripe-connect/create-account`,
  ONBOARDING_LINK: `${API_BASE_URL}/api/stripe-connect/onboarding-link`,
  DASHBOARD_LINK: `${API_BASE_URL}/api/stripe-connect/dashboard-link`,
  ACCOUNT_STATUS: (accountId: string) => `${API_BASE_URL}/api/stripe-connect/account-status/${accountId}`,
  DELETE_ACCOUNT: (accountId: string) => `${API_BASE_URL}/api/stripe-connect/delete-account/${accountId}`,
};

export default {
  API_BASE_URL,
  STRIPE_CONNECT_API,
};
