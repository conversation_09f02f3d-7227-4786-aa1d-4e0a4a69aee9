/**
 * Stripe Configuration
 *
 * This file centralizes all Stripe configuration to ensure consistency across the application.
 * IMPORTANT: Never hardcode API keys in this file. Always use environment variables.
 */

// Get Stripe public key from environment variables
// Try both import.meta.env and window.env for compatibility
export const STRIPE_PUBLIC_KEY = import.meta.env.VITE_STRIPE_PUBLIC_KEY ||
  (typeof window !== 'undefined' && window.env?.VITE_STRIPE_PUBLIC_KEY);

// Platform fee percentage
export const PLATFORM_FEE_PERCENTAGE = Number(import.meta.env.VITE_PLATFORM_FEE_PERCENTAGE || 20);

// API URL for Stripe server - use production URL if available, fallback to localhost for development
export const STRIPE_API_URL = import.meta.env.VITE_API_URL ||
  (typeof window !== 'undefined' && window.location.origin.includes('localhost')
    ? 'http://localhost:3001'
    : `${window.location.origin}/api`);

// Stripe API version
export const STRIPE_API_VERSION = '2025-03-31.basil';

// Check if Stripe is available
export const STRIPE_ENABLED = !!STRIPE_PUBLIC_KEY;

// Validate configuration
if (!STRIPE_PUBLIC_KEY) {
  console.warn('⚠️ Stripe public key not found. Payment functionality will be disabled.');
  console.warn('To enable payments, set VITE_STRIPE_PUBLIC_KEY in your environment variables.');
} else {
  // Log configuration (without exposing full keys)
  console.log('✅ Stripe public key loaded:', `${STRIPE_PUBLIC_KEY.substring(0, 7)}...${STRIPE_PUBLIC_KEY.substring(STRIPE_PUBLIC_KEY.length - 4)}`);
}

console.log('Platform fee percentage:', PLATFORM_FEE_PERCENTAGE);
console.log('Stripe API URL:', STRIPE_API_URL);
console.log('Stripe API version:', STRIPE_API_VERSION);
console.log('Stripe enabled:', STRIPE_ENABLED);

// Export default configuration object
export default {
  publicKey: STRIPE_PUBLIC_KEY,
  platformFeePercentage: PLATFORM_FEE_PERCENTAGE,
  apiUrl: STRIPE_API_URL,
  apiVersion: STRIPE_API_VERSION,
  enabled: STRIPE_ENABLED,
};
