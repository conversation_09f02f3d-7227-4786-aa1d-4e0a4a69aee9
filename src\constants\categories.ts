/**
 * Task Categories for ClassTasker
 *
 * This file defines the centralized list of task categories used throughout the application.
 * Update this file to modify categories across all components.
 */

/**
 * Complete list of task categories
 */
export const TASK_CATEGORIES = [
  'Caretaking',
  'Cleaning',
  'Plumbing',
  'Electrical',
  'Carpentry',
  'Flooring',
  'Glazing',
  'Painting',
  'Heating',
  'AC unit',
  'Intruder Alarm',
  'Fire System',
  'Lift (DDA)',
  'Automatic Door',
  'Grounds',
  'Car Park',
  'Health & Safety'
] as const;

/**
 * Categories with "All Categories" option for filters
 */
export const FILTER_CATEGORIES = [
  'All Categories',
  ...TASK_CATEGORIES
] as const;

/**
 * Categories with "Other" option for task creation
 */
export const CREATION_CATEGORIES = [
  ...TASK_CATEGORIES,
  'Other'
] as const;

/**
 * Type for task category values
 */
export type TaskCategory = typeof TASK_CATEGORIES[number];

/**
 * Type for filter category values (includes "All Categories")
 */
export type FilterCategory = typeof FILTER_CATEGORIES[number];

/**
 * Type for creation category values (includes "Other")
 */
export type CreationCategory = typeof CREATION_CATEGORIES[number];

/**
 * Helper function to check if a category is valid
 */
export function isValidCategory(category: string): category is TaskCategory {
  return TASK_CATEGORIES.includes(category as TaskCategory);
}

/**
 * Helper function to get category display name
 */
export function getCategoryDisplayName(category: string): string {
  return category;
}
