import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { taskService, Offer, NewOffer } from "@/services/taskService";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { fixedAcceptOffer, manualFixedAcceptOffer } from "@/services/fixedTaskService";
import notificationService from "@/services/notificationService";
import { supabase } from "@/integrations/supabase/client";

/**
 * Fixed version of the useOffers hook with improved error handling
 */
export function useFixedOffers() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { user } = useAuth();

  // Get offers for a specific task
  const getOffersForTask = (taskId: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log("[FIXED] useOffers.getOffersForTask called with taskId:", taskId);
    }
    return useQuery({
      queryKey: ['offers', taskId],
      queryFn: () => {
        if (process.env.NODE_ENV === 'development') {
          console.log("[FIXED] Executing getOffersByTaskId query function for taskId:", taskId);
        }
        return taskService.getOffersByTaskId(taskId);
      },
      enabled: !!taskId && !!user,
      staleTime: 0, // Always get fresh data
      refetchInterval: 3000, // Poll every 3 seconds
    });
  };

  // Get offers made by the current user
  const getUserOffers = () => {
    console.log("[FIXED] useOffers.getUserOffers called for user:", user?.id);
    return useQuery({
      queryKey: ['userOffers', user?.id],
      queryFn: () => {
        if (!user) {
          console.log("[FIXED] No user found, returning empty offers array");
          return Promise.resolve([]);
        }
        console.log("[FIXED] Executing getUserOffers query function for userId:", user.id);
        return taskService.getUserOffers(user.id);
      },
      enabled: !!user?.id,
      staleTime: 0, // Always get fresh data
      refetchInterval: 3000, // Poll every 3 seconds
    });
  };

  // Create a new offer
  const createOfferMutation = useMutation({
    mutationFn: (newOffer: NewOffer) => {
      console.log("[FIXED] createOfferMutation.mutationFn called with:", newOffer);
      return taskService.createOffer(newOffer);
    },
    onSuccess: async (data, variables) => {
      console.log("[FIXED] createOfferMutation.onSuccess:", data);

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['offers'],
        refetchType: 'all',
      });
      queryClient.invalidateQueries({
        queryKey: ['userOffers'],
        refetchType: 'all',
      });
      queryClient.invalidateQueries({
        queryKey: ['tasks'],
        refetchType: 'all',
      });

      if (data) {
        toast({
          title: "Offer submitted",
          description: "Your offer has been submitted successfully.",
        });

        // Create notification for the task owner
        try {
          // Get task details
          const { data: taskData } = await supabase
            .from('tasks')
            .select('title, user_id')
            .eq('id', data.task_id)
            .single();

          if (taskData) {
            // Get user profile for provider name
            const { data: userData } = await supabase.auth.getUser();
            const { data: profileData } = await supabase
              .from('profiles')
              .select('first_name, last_name')
              .eq('id', userData.user?.id)
              .single();

            // Create a display name for the provider
            const providerName = profileData?.first_name && profileData?.last_name
              ? `${profileData.first_name} ${profileData.last_name}`
              : userData.user?.email?.split('@')[0] || 'A supplier';

            // Send notification to task owner
            await notificationService.createOfferNotification(
              taskData.user_id,
              providerName,
              data.task_id,
              taskData.title,
              data.amount,
              true // Send email
            );
          }
        } catch (error) {
          console.error('[FIXED] Error creating notification for offer:', error);
        }
      } else {
        console.warn("[FIXED] createOfferMutation returned null");
        toast({
          variant: "destructive",
          title: "Error submitting offer",
          description: "There was an error submitting your offer. Please try again.",
        });
      }
    },
    onError: (error) => {
      console.error('[FIXED] Error creating offer:', error);
      toast({
        variant: "destructive",
        title: "Error submitting offer",
        description: "There was an error submitting your offer. Please try again.",
      });
    }
  });

  // Update offer status
  const updateOfferStatusMutation = useMutation({
    mutationFn: ({ id, status, options }: {
      id: string,
      status: 'pending' | 'accepted' | 'rejected',
      options?: any
    }) => {
      console.log(`[FIXED] updateOfferStatusMutation.mutationFn called: id=${id}, status=${status}`);
      return taskService.updateOfferStatus(id, status);
    },
    onSuccess: (data, variables) => {
      console.log("[FIXED] updateOfferStatusMutation.onSuccess:", data);

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['offers'],
        refetchType: 'all',
      });
      queryClient.invalidateQueries({
        queryKey: ['userOffers'],
        refetchType: 'all',
      });
      queryClient.invalidateQueries({
        queryKey: ['tasks'],
        refetchType: 'all',
      });

      // Format status for display
      const statusText = variables.status.charAt(0).toUpperCase() + variables.status.slice(1);

      if (data) {
        toast({
          title: `Offer ${statusText}`,
          description: `The offer has been ${variables.status} successfully.`,
        });

        // Call the onSuccess callback if provided in options
        if (variables.options?.onSuccess) {
          console.log("[FIXED] Calling onSuccess callback from updateOfferStatusMutation");
          variables.options.onSuccess(data);
        }
      } else {
        console.warn("[FIXED] updateOfferStatusMutation returned null but we'll still show success message");
        toast({
          title: `Offer ${statusText}`,
          description: `The offer status has been updated.`,
        });

        // Still call onSuccess to refresh UI even if data is null
        if (variables.options?.onSuccess) {
          console.log("[FIXED] Calling onSuccess callback from updateOfferStatusMutation despite null result");
          variables.options.onSuccess(null);
        }
      }
    },
    onError: (error, variables) => {
      console.error('[FIXED] Error updating offer:', error);
      toast({
        variant: "destructive",
        title: `Error updating offer`,
        description: `There was an error updating the offer. Please try again.`,
      });

      // Call the onError callback if provided in options
      if (variables.options?.onError) {
        console.log("[FIXED] Calling onError callback from updateOfferStatusMutation");
        variables.options.onError(error);
      }
    }
  });

  // Accept an offer - improved version
  const acceptOfferMutation = useMutation({
    mutationFn: async ({ taskId, offerId, options }: {
      taskId: string,
      offerId: string,
      options?: any
    }) => {
      console.log(`[FIXED] acceptOfferMutation.mutationFn called: taskId=${taskId}, offerId=${offerId}`);

      try {
        // First try the manual fixed implementation
        const result = await manualFixedAcceptOffer(taskId, offerId);
        console.log(`[FIXED] manualFixedAcceptOffer result:`, result);
        return result;
      } catch (error) {
        console.error('[FIXED] Error in manualFixedAcceptOffer, falling back to original implementation:', error);
        // Fall back to the original implementation
        return taskService.acceptOffer(taskId, offerId);
      }
    },
    onSuccess: (success, variables) => {
      console.log("[FIXED] acceptOfferMutation.onSuccess:", success, variables);

      // Regardless of success value, invalidate queries to refresh data
      console.log("[FIXED] Invalidating queries after accept offer attempt");
      queryClient.invalidateQueries({
        queryKey: ['offers'],
        refetchType: 'all',
      });
      queryClient.invalidateQueries({
        queryKey: ['userOffers'],
        refetchType: 'all',
      });
      queryClient.invalidateQueries({
        queryKey: ['tasks'],
        refetchType: 'all',
      });

      if (success) {
        console.log("[FIXED] Offer accepted successfully in mutation!");

        toast({
          title: "Offer accepted",
          description: "You've accepted the offer. The task has been assigned to the supplier.",
        });

        // Call the onSuccess callback if provided in options
        if (variables.options?.onSuccess) {
          console.log("[FIXED] Calling onSuccess callback from acceptOfferMutation");
          variables.options.onSuccess(success);
        }
      } else {
        console.warn("[FIXED] acceptOfferMutation.onSuccess called but success is false");
        toast({
          variant: "destructive",
          title: "Error accepting offer",
          description: "There was an error accepting the offer. Please try again.",
        });

        // Still call onSuccess for UI refresh even if operation failed
        if (variables.options?.onSuccess) {
          console.log("[FIXED] Calling onSuccess callback despite failed operation");
          variables.options.onSuccess(false);
        }
      }
    },
    onError: (error, variables) => {
      console.error('[FIXED] Error accepting offer:', error);
      toast({
        variant: "destructive",
        title: "Error accepting offer",
        description: "There was an error accepting the offer. Please try again.",
      });

      // Call the onError callback if provided in options
      if (variables.options?.onError) {
        console.log("[FIXED] Calling onError callback from acceptOfferMutation");
        variables.options.onError(error);
      }
    }
  });

  // Delete an offer
  const deleteOfferMutation = useMutation({
    mutationFn: (id: string) => {
      console.log(`[FIXED] deleteOfferMutation.mutationFn called with id=${id}`);
      return taskService.deleteOffer(id);
    },
    onSuccess: () => {
      console.log("[FIXED] deleteOfferMutation.onSuccess");
      queryClient.invalidateQueries({ queryKey: ['offers'] });
      queryClient.invalidateQueries({ queryKey: ['userOffers'] });
      toast({
        title: "Offer withdrawn",
        description: "Your offer has been withdrawn successfully.",
      });
    },
    onError: (error) => {
      console.error('[FIXED] Error deleting offer:', error);
      toast({
        variant: "destructive",
        title: "Error withdrawing offer",
        description: "There was an error withdrawing your offer. Please try again.",
      });
    }
  });

  return {
    getOffersForTask,
    getUserOffers,
    createOffer: createOfferMutation.mutate,
    isCreatingOffer: createOfferMutation.isPending,
    updateOfferStatus: (id: string, status: 'awaiting' | 'accepted' | 'rejected', options?: any) => {
      console.log(`[FIXED] updateOfferStatus called with: id=${id}, status=${status}`);
      return updateOfferStatusMutation.mutate({ id, status, options });
    },
    isUpdatingOfferStatus: updateOfferStatusMutation.isPending,
    acceptOffer: (params: { taskId: string, offerId: string }, options?: any) => {
      console.log(`[FIXED] acceptOffer called with params:`, params);
      return acceptOfferMutation.mutate({ ...params, options });
    },
    isAcceptingOffer: acceptOfferMutation.isPending,
    deleteOffer: deleteOfferMutation.mutate,
    isDeletingOffer: deleteOfferMutation.isPending
  };
}
