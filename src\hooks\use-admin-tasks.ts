import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { taskService, Task } from '@/services/taskService';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export function useAdminTasks() {
  const { user, isAdmin } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get tasks that need admin review (visibility='admin')
  const {
    data: adminReviewTasks,
    isLoading: isLoadingAdminReviewTasks,
    error: adminReviewTasksError,
    refetch: refetchAdminReviewTasks
  } = useQuery({
    queryKey: ['admin-review-tasks'],
    queryFn: () => taskService.getAdminReviewTasks(),
    enabled: !!user && isAdmin,
  });

  // Get tasks that have been assigned or made public
  const {
    data: adminAssignedTasks,
    isLoading: isLoadingAdminAssignedTasks,
    error: adminAssignedTasksError,
    refetch: refetchAdminAssignedTasks
  } = useQuery({
    queryKey: ['admin-assigned-tasks'],
    queryFn: () => taskService.getAdminAssignedTasks(),
    enabled: !!user && isAdmin,
  });

  // Assign a task to a user
  const {
    mutate: assignTask,
    isPending: isAssigningTask,
    error: assignTaskError
  } = useMutation({
    mutationFn: ({
      taskId,
      userId,
      visibility,
      budget
    }: {
      taskId: string;
      userId: string; // This can be empty for public assignments
      visibility: 'internal' | 'public';
      budget?: number; // Optional budget parameter for public tasks
    }) => {
      console.log(`useAdminTasks: Assigning task ${taskId} with visibility ${visibility}`);
      console.log(`useAdminTasks: userId=${userId || 'empty (public assignment)'}`);
      if (budget !== undefined) {
        console.log(`useAdminTasks: budget=${budget}`);
      }
      return taskService.assignTask(taskId, userId, visibility, budget);
    },
    onSuccess: async () => {
      // Invalidate and immediately refetch relevant queries
      console.log('Task assigned successfully, refreshing data...');

      // Invalidate all relevant queries first
      await queryClient.invalidateQueries({ queryKey: ['admin-review-tasks'] });
      await queryClient.invalidateQueries({ queryKey: ['admin-assigned-tasks'] });
      await queryClient.invalidateQueries({ queryKey: ['tasks'] });

      // Then explicitly refetch to ensure UI updates immediately
      await queryClient.refetchQueries({ queryKey: ['admin-review-tasks'] });
      await queryClient.refetchQueries({ queryKey: ['admin-assigned-tasks'] });
      await queryClient.refetchQueries({ queryKey: ['tasks'] });

      toast({
        title: 'Task assigned successfully',
        description: 'The task has been assigned and the task lists have been updated.',
        variant: 'default',
      });
    },
    onError: (error) => {
      console.error('Error assigning task:', error);
      toast({
        title: 'Error assigning task',
        description: 'There was an error assigning the task. Please try again.',
        variant: 'destructive',
      });
    }
  });

  return {
    adminReviewTasks,
    isLoadingAdminReviewTasks,
    adminReviewTasksError,
    refetchAdminReviewTasks,
    adminAssignedTasks,
    isLoadingAdminAssignedTasks,
    adminAssignedTasksError,
    refetchAdminAssignedTasks,
    assignTask,
    isAssigningTask,
    assignTaskError
  };
}
