import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';

/**
 * Hook to manage chat visibility state
 */
export function useChatVisibility(taskId: string | undefined) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [hasMessages, setHasMessages] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  
  // Get current visibility from URL
  const isVisible = searchParams.get('messages') === 'true';
  
  // Check if task has messages in the database
  useEffect(() => {
    if (!taskId) {
      setIsChecking(false);
      return;
    }
    
    const checkForMessages = async () => {
      setIsChecking(true);
      try {
        // Check localStorage first (faster)
        try {
          const tasksWithMessages = JSON.parse(localStorage.getItem('tasksWithMessages') || '[]');
          if (tasksWithMessages.includes(taskId)) {
            if (process.env.NODE_ENV === 'development') {

              console.log(`[useChatVisibility] Task ${taskId} has messages according to localStorage`);

              }
            setHasMessages(true);
            setIsChecking(false);
            return;
          }
        } catch (e) {
          console.error('[useChatVisibility] Error checking localStorage:', e);
        }
        
        // Then check database
        if (process.env.NODE_ENV === 'development') {

          console.log(`[useChatVisibility] Checking database for messages for task ${taskId}`);

          }
        const { data, error } = await supabase
          .rpc('get_task_messages_with_sender', {
            task_id_param: taskId
          });
          
        if (error) {
          console.error('[useChatVisibility] Error checking for messages:', error);
        } else if (data && Array.isArray(data) && data.length > 0) {
          if (process.env.NODE_ENV === 'development') {

            console.log(`[useChatVisibility] Found ${data.length} messages for task ${taskId}`);

            }
          setHasMessages(true);
          
          // Store in localStorage for future checks
          try {
            const tasksWithMessages = JSON.parse(localStorage.getItem('tasksWithMessages') || '[]');
            if (!tasksWithMessages.includes(taskId)) {
              tasksWithMessages.push(taskId);
              localStorage.setItem('tasksWithMessages', JSON.stringify(tasksWithMessages));
            }
          } catch (e) {
            console.error('[useChatVisibility] Error updating localStorage:', e);
          }
        }
      } catch (err) {
        console.error('[useChatVisibility] Unexpected error:', err);
      } finally {
        setIsChecking(false);
      }
    };
    
    checkForMessages();
  }, [taskId]);
  
  // Update URL if messages exist but chat isn't visible
  useEffect(() => {
    if (hasMessages && !isVisible) {
      if (process.env.NODE_ENV === 'development') {

        console.log(`[useChatVisibility] Task ${taskId} has messages but chat isn't visible, updating URL`);

        }
      setSearchParams(prev => {
        const newParams = new URLSearchParams(prev);
        newParams.set('messages', 'true');
        return newParams;
      });
    }
  }, [hasMessages, isVisible, taskId, setSearchParams]);
  
  // Function to show chat
  const showChat = () => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.set('messages', 'true');
      return newParams;
    });
  };
  
  // Function to hide chat
  const hideChat = () => {
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      newParams.delete('messages');
      return newParams;
    });
  };
  
  return {
    isVisible,
    hasMessages,
    isChecking,
    showChat,
    hideChat
  };
}
