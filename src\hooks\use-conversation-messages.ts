import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Message } from '@/hooks/use-task-chat';
import notificationService from '@/services/notificationService';

/**
 * Hook to fetch and manage messages for a specific conversation (task)
 */
export function useConversationMessages(taskId: string | null) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  // Function to fetch messages
  const fetchMessages = async () => {
    if (!taskId || !user) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      if (process.env.NODE_ENV === 'development') {

        console.log('[useConversationMessages] Fetching messages for task:', taskId);

        }
      const { data, error: fetchError } = await supabase
        .rpc('get_task_messages_with_sender', {
          task_id_param: taskId
        });

      if (fetchError) {
        console.error('[useConversationMessages] Error fetching messages:', fetchError);
        setError('Failed to fetch messages');
        setMessages([]);
      } else if (data && Array.isArray(data)) {
        if (process.env.NODE_ENV === 'development') {

          console.log(`[useConversationMessages] Received ${data.length} messages`);

          }
        const formattedMessages = data.map((msg) => ({
          id: msg.id,
          task_id: msg.task_id,
          sender_id: msg.sender_id,
          content: msg.content,
          created_at: msg.created_at,
          first_name: msg.first_name,
          last_name: msg.last_name,
          sender_name: msg.first_name
            ? `${msg.first_name} ${msg.last_name || ''}`
            : 'User'
        })) as Message[];

        setMessages(formattedMessages);
      }
    } catch (err) {
      console.error('[useConversationMessages] Unexpected error:', err);
      setError('An unexpected error occurred');
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to send a message
  const sendMessage = async (content: string) => {
    if (!taskId || !user || !content.trim()) return false;

    try {
      const messageToSend = content.trim();

      // Try the RPC function first
      try {
        const { error } = await supabase
          .rpc('create_task_message', {
            task_id_param: taskId,
            sender_id_param: user.id,
            content_param: messageToSend
          });

        if (error) {
          console.error('[useConversationMessages] Error sending message via RPC:', error);
          throw error; // Try fallback method
        }

        if (process.env.NODE_ENV === 'development') {


          console.log('[useConversationMessages] Message sent successfully via RPC');


          }
        await fetchMessages();

        // Create notification for the task owner
        try {
          // Get task details
          const { data: taskData } = await supabase
            .from('tasks')
            .select('title, user_id')
            .eq('id', taskId)
            .single();

          if (taskData && taskData.user_id !== user.id) {
            // Get user profile for sender name
            const { data: profileData } = await supabase
              .from('profiles')
              .select('first_name, last_name')
              .eq('id', user.id)
              .single();

            // Create a display name for the sender
            const senderName = profileData?.first_name && profileData?.last_name
              ? `${profileData.first_name} ${profileData.last_name}`
              : user.email?.split('@')[0] || 'A user';

            // Send notification to task owner
            await notificationService.createMessageNotification(
              taskData.user_id,
              senderName,
              taskId,
              taskData.title,
              true // Send email
            );
          }
        } catch (error) {
          console.error('[useConversationMessages] Error creating notification for message:', error);
        }

        return true;
      } catch (rpcError) {
        // Fallback: Insert directly into the task_messages table
        const { error: insertError } = await supabase
          .from('task_messages')
          .insert({
            task_id: taskId,
            sender_id: user.id,
            content: messageToSend
          });

        if (insertError) {
          console.error('[useConversationMessages] Error sending message via direct insert:', insertError);
          return false;
        }

        if (process.env.NODE_ENV === 'development') {


          console.log('[useConversationMessages] Message sent successfully via direct insert');


          }
        await fetchMessages();

        // Create notification for the task owner
        try {
          // Get task details
          const { data: taskData } = await supabase
            .from('tasks')
            .select('title, user_id')
            .eq('id', taskId)
            .single();

          if (taskData && taskData.user_id !== user.id) {
            // Get user profile for sender name
            const { data: profileData } = await supabase
              .from('profiles')
              .select('first_name, last_name')
              .eq('id', user.id)
              .single();

            // Create a display name for the sender
            const senderName = profileData?.first_name && profileData?.last_name
              ? `${profileData.first_name} ${profileData.last_name}`
              : user.email?.split('@')[0] || 'A user';

            // Send notification to task owner
            await notificationService.createMessageNotification(
              taskData.user_id,
              senderName,
              taskId,
              taskData.title,
              true // Send email
            );
          }
        } catch (error) {
          console.error('[useConversationMessages] Error creating notification for message:', error);
        }

        return true;
      }
    } catch (err) {
      console.error('[useConversationMessages] Unexpected error sending message:', err);
      return false;
    }
  };

  // Set up initial fetch and real-time subscription
  useEffect(() => {
    if (!taskId) {
      setMessages([]);
      setIsLoading(false);
      return;
    }

    fetchMessages();

    // Set up real-time subscription
    const channel = supabase
      .channel(`task_messages_${taskId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'task_messages',
          filter: `task_id=eq.${taskId}`
        },
        () => {
          if (process.env.NODE_ENV === 'development') {

            console.log('[useConversationMessages] New message received, refreshing messages');

            }
          fetchMessages();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [taskId, user]);

  return {
    messages,
    isLoading,
    error,
    sendMessage
  };
}
