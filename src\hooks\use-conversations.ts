import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export interface Conversation {
  taskId: string;
  taskTitle: string;
  lastMessage: {
    id: string;
    content: string;
    created_at: string;
    sender_id: string;
    sender_name: string;
  };
  otherParticipant: {
    id: string;
    name: string;
  };
  unread?: boolean; // For future implementation
}

/**
 * Hook to fetch and manage user conversations
 */
export function useConversations() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    const fetchConversations = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {

            console.log('[useConversations] Fetching conversations for user: completed');

            }
        }

        // First, try to get tasks the user is involved with (either as owner or through offers)
        let { data: userTasks, error: tasksError } = await supabase
          .rpc('fetch_recent_messages', {
            user_id_param: user.id,
            limit_param: 100 // Get more to ensure we have enough conversations
          });

        // If no messages found or there was an error, try a direct approach
        if (tasksError || !userTasks || userTasks.length === 0) {
          if (process.env.NODE_ENV === 'development') {
    console.log('[useConversations] No messages found via RPC, trying direct query');
  }
          // Get all messages where the user is either the sender or the task owner
          const { data: directMessages, error: directError } = await supabase
            .from('task_messages')
            .select(`
              id,
              task_id,
              sender_id,
              content,
              created_at,
              profiles!task_messages_sender_id_fkey(first_name, last_name),
              tasks!task_messages_task_id_fkey(title, user_id)
            `)
            .or(`sender_id.eq.${user.id},tasks.user_id.eq.${user.id}`)
            .order('created_at', { ascending: false })
            .limit(100);

          if (directError) {
            console.error('[useConversations] Error with direct query:', directError);
          } else if (directMessages && directMessages.length > 0) {
            if (process.env.NODE_ENV === 'development') {
    console.log(`[useConversations] Found ${directMessages.length} messages via direct query`);
  }
            // Format the direct messages to match the expected format
            userTasks = directMessages.map(msg => {
              const profile = msg.profiles || {};
              const task = msg.tasks || {};
              return {
                id: msg.id,
                task_id: msg.task_id,
                sender_id: msg.sender_id,
                content: msg.content,
                created_at: msg.created_at,
                first_name: profile.first_name,
                last_name: profile.last_name,
                sender_name: profile.first_name && profile.last_name ?
                  `${profile.first_name} ${profile.last_name}` : 'User',
                task_title: task.title || 'Untitled Task'
              };
            });
          }
        }

        if (tasksError) {
          console.error('[useConversations] Error fetching tasks:', tasksError);
          setError('Failed to fetch conversations');
          setConversations([]);
          setIsLoading(false);
          return;
        }

        if (!userTasks || !Array.isArray(userTasks) || userTasks.length === 0) {
          if (process.env.NODE_ENV === 'development') {
    console.log('[useConversations] No conversations found');
  }
          setConversations([]);
          setIsLoading(false);
          return;
        }

        if (process.env.NODE_ENV === 'development') {


          console.log(`[useConversations] Found ${userTasks.length} conversations`);



          }
        // Group by task_id to get unique conversations
        const conversationMap = new Map<string, any>();

        for (const message of userTasks) {
          // Skip if we already have this task in our map
          if (!conversationMap.has(message.task_id)) {
            // Get the other participant (not the current user)
            // If the user is both the sender and the task owner, use the task title as the participant name
            let otherParticipantId = message.sender_id;
            let otherParticipantName = message.sender_name || 'Unknown';

            // If the sender is the current user, try to find the task owner
            if (message.sender_id === user.id) {
              // If we have task owner info, use that
              if (message.tasks && message.tasks.user_id && message.tasks.user_id !== user.id) {
                otherParticipantId = message.tasks.user_id;
                // We don't have the owner's name in this query, so use the task title
                otherParticipantName = `Task: ${message.task_title || 'Untitled Task'}`;
              } else {
                // Fallback to task title if we can't determine the other participant
                otherParticipantName = `Task: ${message.task_title || 'Untitled Task'}`;
              }
            }

            conversationMap.set(message.task_id, {
              taskId: message.task_id,
              taskTitle: message.task_title || 'Untitled Task',
              lastMessage: {
                id: message.id,
                content: message.content,
                created_at: message.created_at,
                sender_id: message.sender_id,
                sender_name: message.sender_name || 'Unknown User'
              },
              otherParticipant: {
                id: otherParticipantId || '',
                name: otherParticipantName
              }
            });
          }
        }

        // Convert map to array and sort by last message date (newest first)
        const conversationsArray = Array.from(conversationMap.values())
          .sort((a, b) => {
            return new Date(b.lastMessage.created_at).getTime() -
                   new Date(a.lastMessage.created_at).getTime();
          });

        setConversations(conversationsArray);
      } catch (err) {
        console.error('[useConversations] Unexpected error:', err);
        setError('An unexpected error occurred');
        setConversations([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchConversations();

    // Set up real-time subscription for new messages
    const channel = supabase
      .channel('task_messages_changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'task_messages'
        },
        () => {
          if (process.env.NODE_ENV === 'development') {
    console.log('[useConversations] New message detected, refreshing conversations');
  }
          fetchConversations();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user]);

  return {
    conversations,
    isLoading,
    error
  };
}
