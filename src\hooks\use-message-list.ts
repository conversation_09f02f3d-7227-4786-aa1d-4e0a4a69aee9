import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export interface MessageThread {
  taskId: string;
  taskTitle: string;
  lastMessage: string;
  lastMessageTime: string;
  otherParticipantName: string;
}

/**
 * Hook to fetch message threads for the Messages page
 */
export function useMessageList() {
  const [messageThreads, setMessageThreads] = useState<MessageThread[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    const fetchMessageThreads = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        if (process.env.NODE_ENV === 'development') {

          console.log('[useMessageList] Fetching message threads for user: completed');
        

          }
        // This is the same function used in the dashboard MessagesSection
        const { data, error } = await supabase
          .rpc('fetch_recent_messages', {
            user_id_param: user.id,
            limit_param: 100
          });
        
        if (error) {
          console.error('[useMessageList] Error fetching message threads:', error);
          setError('Failed to fetch message threads');
          setMessageThreads([]);
          setIsLoading(false);
          return;
        }
        
        if (!data || !Array.isArray(data) || data.length === 0) {
          if (process.env.NODE_ENV === 'development') {
    console.log('[useMessageList] No message threads found');
  }
          setMessageThreads([]);
          setIsLoading(false);
          return;
        }
        
        if (process.env.NODE_ENV === 'development') {
    console.log(`[useMessageList] Found ${data.length} message threads`);
  }
        // Group by task_id to get unique threads
        const threadMap = new Map<string, MessageThread>();
        
        for (const message of data) {
          if (!threadMap.has(message.task_id)) {
            // Determine the other participant name
            let otherParticipantName = message.sender_name || 'Unknown User';
            if (message.sender_id === user.id) {
              otherParticipantName = `Task: ${message.task_title || 'Untitled Task'}`;
            }
            
            threadMap.set(message.task_id, {
              taskId: message.task_id,
              taskTitle: message.task_title || 'Untitled Task',
              lastMessage: message.content,
              lastMessageTime: message.created_at,
              otherParticipantName
            });
          }
        }
        
        // Convert map to array and sort by last message time (newest first)
        const threadsArray = Array.from(threadMap.values())
          .sort((a, b) => {
            return new Date(b.lastMessageTime).getTime() - 
                   new Date(a.lastMessageTime).getTime();
          });
          
        setMessageThreads(threadsArray);
      } catch (err) {
        console.error('[useMessageList] Unexpected error:', err);
        setError('An unexpected error occurred');
        setMessageThreads([]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchMessageThreads();
    
    // Set up real-time subscription for new messages
    const channel = supabase
      .channel('task_messages_changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'task_messages'
        },
        () => {
          if (process.env.NODE_ENV === 'development') {
    console.log('[useMessageList] New message detected, refreshing threads');
  }
          fetchMessageThreads();
        }
      )
      .subscribe();
      
    return () => {
      supabase.removeChannel(channel);
    };
  }, [user]);
  
  return {
    messageThreads,
    isLoading,
    error
  };
}
