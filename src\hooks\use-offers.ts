
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { taskService, Offer, NewOffer } from "@/services/taskService";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import notificationService from "@/services/notificationService";
import { supabase } from "@/integrations/supabase/client";

export function useOffers() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { user } = useAuth();

  // Get offers for a specific task
  const getOffersForTask = (taskId: string) => {
    console.log("useOffers.getOffersForTask called with taskId:", taskId);
    return useQuery({
      queryKey: ['offers', taskId],
      queryFn: () => {
        console.log("Executing getOffersByTaskId query function for taskId:", taskId);
        return taskService.getOffersByTaskId(taskId);
      },
      enabled: !!taskId && !!user,
      staleTime: 0, // Always get fresh data
      refetchInterval: 3000, // Poll every 3 seconds
    });
  };

  // Get offers made by the current user
  const getUserOffers = () => {
    console.log("useOffers.getUserOffers called for user:", user?.id);
    return useQuery({
      queryKey: ['userOffers', user?.id],
      queryFn: () => {
        if (!user) {
          console.log("No user found, returning empty offers array");
          return Promise.resolve([]);
        }
        console.log("Executing getUserOffers query function for userId:", user.id);
        return taskService.getUserOffers(user.id);
      },
      enabled: !!user?.id,
      staleTime: 0, // Always get fresh data
      refetchInterval: 3000, // Poll every 3 seconds
    });
  };

  // Create a new offer
  const createOfferMutation = useMutation({
    mutationFn: (newOffer: NewOffer) => {
      console.log("createOfferMutation.mutationFn called with:", newOffer);
      return taskService.createOffer(newOffer);
    },
    onSuccess: async (data, variables) => {
      console.log("createOfferMutation.onSuccess:", data);
      if (data) {
        queryClient.invalidateQueries({ queryKey: ['offers'] });
        queryClient.invalidateQueries({ queryKey: ['userOffers'] });
        queryClient.invalidateQueries({ queryKey: ['tasks'] });

        toast({
          title: "Offer submitted",
          description: "Your offer has been submitted successfully.",
        });

        // Create notification for the task owner
        try {
          // Get task details
          const { data: taskData } = await supabase
            .from('tasks')
            .select('title, user_id')
            .eq('id', data.task_id)
            .single();

          if (taskData) {
            // Get user profile for provider name
            const { data: userData } = await supabase.auth.getUser();
            const { data: profileData } = await supabase
              .from('profiles')
              .select('first_name, last_name')
              .eq('id', userData.user?.id)
              .single();

            // Create a display name for the provider
            const providerName = profileData?.first_name && profileData?.last_name
              ? `${profileData.first_name} ${profileData.last_name}`
              : userData.user?.email?.split('@')[0] || 'A supplier';

            // Send notification to task owner
            await notificationService.createOfferNotification(
              taskData.user_id,
              providerName,
              data.task_id,
              taskData.title,
              data.amount,
              true // Send email
            );
          }
        } catch (error) {
          console.error('Error creating notification for offer:', error);
        }
      }
    },
    onError: (error) => {
      console.error('Error creating offer:', error);
      toast({
        variant: "destructive",
        title: "Error submitting offer",
        description: "There was an error submitting your offer. Please try again.",
      });
    }
  });

  // Update an offer's status
  const updateOfferStatusMutation = useMutation({
    mutationFn: ({ id, status, options }: {
      id: string,
      status: 'awaiting' | 'accepted' | 'rejected',
      options?: any
    }) => {
      console.log(`updateOfferStatusMutation.mutationFn called: id=${id}, status=${status}`);
      return taskService.updateOfferStatus(id, status);
    },
    onSuccess: (data, variables) => {
      console.log("updateOfferStatusMutation.onSuccess:", data, variables);

      // Invalidate related queries to refresh data
      console.log("Invalidating queries after status update");
      queryClient.invalidateQueries({
        queryKey: ['offers'],
        refetchType: 'all'
      });
      queryClient.invalidateQueries({
        queryKey: ['userOffers'],
        refetchType: 'all'
      });
      queryClient.invalidateQueries({
        queryKey: ['tasks'],
        refetchType: 'all'
      });

      // Format status for display
      const statusText = variables.status.charAt(0).toUpperCase() + variables.status.slice(1);

      if (data) {
        toast({
          title: `Offer ${statusText}`,
          description: `The offer has been ${variables.status} successfully.`,
        });

        // Call the onSuccess callback if provided in options
        if (variables.options?.onSuccess) {
          console.log("Calling onSuccess callback from updateOfferStatusMutation");
          variables.options.onSuccess(data);
        }
      } else {
        console.warn("updateOfferStatusMutation returned null but we'll still show success message");
        toast({
          title: `Offer ${statusText}`,
          description: `The offer status has been updated.`,
        });

        // Still call onSuccess to refresh UI even if data is null
        if (variables.options?.onSuccess) {
          console.log("Calling onSuccess callback from updateOfferStatusMutation despite null result");
          variables.options.onSuccess(null);
        }
      }
    },
    onError: (error, variables) => {
      console.error('Error updating offer:', error);
      toast({
        variant: "destructive",
        title: `Error updating offer`,
        description: `There was an error updating the offer. Please try again.`,
      });

      // Call the onError callback if provided in options
      if (variables.options?.onError) {
        console.log("Calling onError callback from updateOfferStatusMutation");
        variables.options.onError(error);
      }
    }
  });

  // Accept an offer - simplified approach
  const acceptOfferMutation = useMutation({
    mutationFn: ({ taskId, offerId, options }: {
      taskId: string,
      offerId: string,
      options?: any
    }) => {
      console.log(`acceptOfferMutation.mutationFn called: taskId=${taskId}, offerId=${offerId}`);
      return taskService.acceptOffer(taskId, offerId);
    },
    onSuccess: (success, variables) => {
      console.log("acceptOfferMutation.onSuccess:", success, variables);

      // Regardless of success value, invalidate queries to refresh data
      console.log("Invalidating queries after accept offer attempt");
      queryClient.invalidateQueries({
        queryKey: ['offers'],
        refetchType: 'all',
      });
      queryClient.invalidateQueries({
        queryKey: ['userOffers'],
        refetchType: 'all',
      });
      queryClient.invalidateQueries({
        queryKey: ['tasks'],
        refetchType: 'all',
      });

      if (success) {
        console.log("Offer accepted successfully in mutation!");

        toast({
          title: "Offer accepted",
          description: "You've accepted the offer. The task has been assigned to the supplier.",
        });

        // Call the onSuccess callback if provided in options
        if (variables.options?.onSuccess) {
          console.log("Calling onSuccess callback from acceptOfferMutation");
          variables.options.onSuccess(success);
        }
      } else {
        console.warn("acceptOfferMutation.onSuccess called but success is false");
        toast({
          variant: "destructive",
          title: "Error accepting offer",
          description: "There was an error accepting the offer. Please try again.",
        });

        // Still call onSuccess for UI refresh even if operation failed
        if (variables.options?.onSuccess) {
          console.log("Calling onSuccess callback despite failed operation");
          variables.options.onSuccess(false);
        }
      }
    },
    onError: (error, variables) => {
      console.error('Error accepting offer:', error);
      toast({
        variant: "destructive",
        title: "Error accepting offer",
        description: "There was an error accepting the offer. Please try again.",
      });

      // Call the onError callback if provided in options
      if (variables.options?.onError) {
        console.log("Calling onError callback from acceptOfferMutation");
        variables.options.onError(error);
      }
    }
  });

  // Delete an offer
  const deleteOfferMutation = useMutation({
    mutationFn: (id: string) => {
      console.log(`deleteOfferMutation.mutationFn called with id=${id}`);
      return taskService.deleteOffer(id);
    },
    onSuccess: () => {
      console.log("deleteOfferMutation.onSuccess");
      queryClient.invalidateQueries({ queryKey: ['offers'] });
      queryClient.invalidateQueries({ queryKey: ['userOffers'] });
      toast({
        title: "Offer withdrawn",
        description: "Your offer has been withdrawn successfully.",
      });
    },
    onError: (error) => {
      console.error('Error deleting offer:', error);
      toast({
        variant: "destructive",
        title: "Error withdrawing offer",
        description: "There was an error withdrawing your offer. Please try again.",
      });
    }
  });

  return {
    getOffersForTask,
    getUserOffers,
    createOffer: createOfferMutation.mutate,
    isCreatingOffer: createOfferMutation.isPending,
    updateOfferStatus: (id: string, status: 'awaiting' | 'accepted' | 'rejected', options?: any) => {
      console.log(`updateOfferStatus called with: id=${id}, status=${status}`);
      return updateOfferStatusMutation.mutate({ id, status, options });
    },
    isUpdatingOfferStatus: updateOfferStatusMutation.isPending,
    acceptOffer: (params: { taskId: string, offerId: string }, options?: any) => {
      console.log(`acceptOffer called with params:`, params);
      return acceptOfferMutation.mutate({ ...params, options });
    },
    isAcceptingOffer: acceptOfferMutation.isPending,
    deleteOffer: deleteOfferMutation.mutate,
    isDeletingOffer: deleteOfferMutation.isPending
  };
}
