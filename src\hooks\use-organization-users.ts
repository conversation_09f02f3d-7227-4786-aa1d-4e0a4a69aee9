import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { OrganizationUser } from '@/types/organization';
import { useAuth } from '@/contexts/AuthContext';

export function useOrganizationUsers() {
  const { user, profile, organizationId: authOrgId } = useAuth();
  const organizationId = profile?.organization_id || authOrgId;

  console.log('useOrganizationUsers - profile:', profile);
  console.log('useOrganizationUsers - organizationId from profile:', profile?.organization_id);
  console.log('useOrganizationUsers - organizationId from auth context:', authOrgId);
  console.log('useOrganizationUsers - final organizationId:', organizationId);

  const {
    data: organizationUsers,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['organization-users', organizationId],
    queryFn: async () => {
      if (!organizationId) {
        console.log('No organization ID available');
        return [];
      }

      console.log(`Fetching users for organization: ${organizationId}`);

      const { data, error } = await supabase
        .from('profiles')
        .select('id, email, first_name, last_name, role, created_at')
        .eq('organization_id', organizationId)
        .order('created_at', { ascending: false });

      // Map the data to match the expected OrganizationUser interface
      const mappedData = data?.map(user => ({
        user_id: user.id,
        email: Array.isArray(user.email) ? user.email[0] : user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        role: user.role,
        created_at: user.created_at
      })) || [];

      if (error) {
        console.error('Error fetching organization users:', error);
        throw error;
      }

      console.log(`Retrieved ${mappedData.length} users for organization ${organizationId}`);
      console.log('User roles:', mappedData.map(user => ({ id: user.user_id, email: user.email, role: user.role })));

      // Debug: Check if maintenance and support staff are being retrieved
      const maintenanceUsers = mappedData.filter(user => user.role === 'maintenance');
      const supportUsers = mappedData.filter(user => user.role === 'support');
      console.log(`DEBUG: Found ${maintenanceUsers.length} maintenance users:`, maintenanceUsers);
      console.log(`DEBUG: Found ${supportUsers.length} support users:`, supportUsers);
      return mappedData as OrganizationUser[];
    },
    enabled: !!organizationId && !!user,
  });

  return {
    organizationUsers,
    isLoading,
    error,
    refetch
  };
}
