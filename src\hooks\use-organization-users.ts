import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { OrganizationUser } from '@/types/organization';
import { useAuth } from '@/contexts/AuthContext';

export function useOrganizationUsers() {
  const { user, profile, organizationId: authOrgId } = useAuth();
  const organizationId = profile?.organization_id || authOrgId;

  if (process.env.NODE_ENV === 'development') {


    console.log('useOrganizationUsers - profile:', profile.replace(/user.*/, 'hasUser: ' + !!user));


    }
  if (process.env.NODE_ENV === 'development') {

    console.log('useOrganizationUsers - organizationId from profile:', profile?.organization_id.replace(/user.*/, 'hasUser: ' + !!user));

    }
  if (process.env.NODE_ENV === 'development') {

    console.log('useOrganizationUsers - organizationId from auth context:', authOrgId.replace(/user.*/, 'hasUser: ' + !!user));

    }
  if (process.env.NODE_ENV === 'development') {

    console.log('useOrganizationUsers - final organizationId:', organizationId.replace(/user.*/, 'hasUser: ' + !!user));


    }
  const {
    data: organizationUsers,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['organization-users', organizationId],
    queryFn: async () => {
      if (!organizationId) {
        if (process.env.NODE_ENV === 'development') {

          console.log('No organization ID available');

          }
        return [];
      }

      if (process.env.NODE_ENV === 'development') {


        console.log(`Fetching users for organization: ${organizationId}`.replace(/user.*/, 'hasUser: ' + !!user));



        }
      const { data, error } = await supabase
        .from('profiles')
        .select('id, email, first_name, last_name, role, created_at')
        .eq('organization_id', organizationId)
        .order('created_at', { ascending: false });

      // Map the data to match the expected OrganizationUser interface
      const mappedData = data?.map(user => ({
        user_id: user.id,
        email: Array.isArray(user.email) ? user.email[0] : user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        role: user.role,
        created_at: user.created_at
      })) || [];

      if (error) {
        console.error('Error fetching organization users:', error);
        throw error;
      }

      if (process.env.NODE_ENV === 'development') {


        console.log(`Retrieved ${mappedData.length} users for organization ${organizationId}`.replace(/user.*/, 'hasUser: ' + !!user));


        }
      if (process.env.NODE_ENV === 'development') {

        console.log('User roles:', mappedData.map(user => ({ id: user.user_id, email: user.email, role: user.role }.replace(/user.*/, 'hasUser: ' + !!user))));


        }
      // Debug: Check if maintenance and support staff are being retrieved
      const maintenanceUsers = mappedData.filter(user => user.role === 'maintenance');
      const supportUsers = mappedData.filter(user => user.role === 'support');
      if (process.env.NODE_ENV === 'development') {

        console.log(`DEBUG: Found ${maintenanceUsers.length} maintenance users:`, maintenanceUsers.replace(/user.*/, 'hasUser: ' + !!user));

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`DEBUG: Found ${supportUsers.length} support users:`, supportUsers.replace(/user.*/, 'hasUser: ' + !!user));

        }
      return mappedData as OrganizationUser[];
    },
    enabled: !!organizationId && !!user,
  });

  return {
    organizationUsers,
    isLoading,
    error,
    refetch
  };
}
