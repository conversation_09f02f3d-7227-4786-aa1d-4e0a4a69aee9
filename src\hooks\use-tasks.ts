
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { taskService, Task, NewTask, Offer, NewO<PERSON> } from "@/services/taskService";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";

export function useTasks() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { user } = useAuth();
  
  // Get all tasks
  const { data: tasks, isLoading: isLoadingTasks, error: tasksError } = useQuery({
    queryKey: ['tasks'],
    queryFn: taskService.getTasks,
    enabled: !!user,
  });
  
  // Get a single task by id
  const getTask = (id: string) => {
    return useQuery({
      queryKey: ['task', id],
      queryFn: () => taskService.getTaskById(id),
      enabled: !!id && !!user,
    });
  };
  
  // Get tasks by user id (useful for profile pages)
  const getTasksByUserId = (userId: string | undefined) => {
    return useQuery({
      queryKey: ['userTasks', userId],
      queryFn: () => userId ? taskService.getUserTasks(userId) : Promise.resolve([]),
      enabled: !!userId,
    });
  };
  
  // Get tasks created by the current user
  const { data: userTasks, isLoading: isLoadingUserTasks } = useQuery({
    queryKey: ['userTasks', user?.id],
    queryFn: () => user ? taskService.getUserTasks(user.id) : Promise.resolve([]),
    enabled: !!user?.id,
  });
  
  // Create a new task
  const createTaskMutation = useMutation({
    mutationFn: (newTask: NewTask) => taskService.createTask(newTask),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['userTasks'] });
      toast({
        title: "Task created successfully",
        description: "Your task has been posted and is now visible to suppliers.",
      });
    },
    onError: (error) => {
      console.error('Error creating task:', error);
      toast({
        variant: "destructive",
        title: "Error creating task",
        description: "There was an error creating your task. Please try again.",
      });
    }
  });
  
  // Update a task
  const updateTaskMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<Task> }) => 
      taskService.updateTask(id, updates),
    onSuccess: (data) => {
      if (data) {
        queryClient.invalidateQueries({ queryKey: ['tasks'] });
        queryClient.invalidateQueries({ queryKey: ['task', data.id] });
        queryClient.invalidateQueries({ queryKey: ['userTasks'] });
        toast({
          title: "Task updated",
          description: "Your task has been updated successfully.",
        });
      }
    },
    onError: (error) => {
      console.error('Error updating task:', error);
      toast({
        variant: "destructive",
        title: "Error updating task",
        description: "There was an error updating your task. Please try again.",
      });
    }
  });
  
  // Delete a task
  const deleteTaskMutation = useMutation({
    mutationFn: (id: string) => taskService.deleteTask(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['userTasks'] });
      toast({
        title: "Task deleted",
        description: "Your task has been deleted successfully.",
      });
    },
    onError: (error) => {
      console.error('Error deleting task:', error);
      toast({
        variant: "destructive",
        title: "Error deleting task",
        description: "There was an error deleting your task. Please try again.",
      });
    }
  });
  
  return {
    tasks,
    userTasks,
    isLoadingTasks,
    isLoadingUserTasks,
    tasksError,
    getTask,
    getTasksByUserId,
    createTask: createTaskMutation.mutate,
    isCreatingTask: createTaskMutation.isPending,
    updateTask: updateTaskMutation.mutate,
    isUpdatingTask: updateTaskMutation.isPending,
    deleteTask: deleteTaskMutation.mutate,
    isDeletingTask: deleteTaskMutation.isPending,
  };
}
