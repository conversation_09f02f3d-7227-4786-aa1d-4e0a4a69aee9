
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 221 83% 60%;
    --primary-foreground: 210 40% 98%;

    --secondary: 160 84% 39%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 24 94% 53%;
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 60%;

    --radius: 0.75rem;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 221 83% 60%;
    --primary-foreground: 210 40% 98%;

    --secondary: 160 84% 39%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 18%;
    --muted-foreground: 215 20% 65%;

    --accent: 24 94% 53%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 18%;
    --input: 217 33% 18%;
    --ring: 221 83% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold;
  }
}

@layer components {
  .btn-primary {
    @apply bg-classtasker-blue text-white hover:bg-blue-600 transition-colors;
  }

  .btn-secondary {
    @apply bg-classtasker-green text-white hover:bg-green-600 transition-colors;
  }

  .btn-accent {
    @apply bg-classtasker-orange text-white hover:bg-orange-600 transition-colors;
  }

  .card-hover {
    @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
  }

  /* Mobile safe area utilities */
  .h-safe-area-bottom {
    height: 0px;
  }

  .pb-safe-area-bottom {
    padding-bottom: 1rem;
  }
}
