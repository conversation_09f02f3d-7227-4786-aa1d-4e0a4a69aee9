import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

// Use Vite's built-in environment variables - no hardcoded fallbacks for security
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Validate that required environment variables are present
if (!SUPABASE_URL) {
  throw new Error('Missing required environment variable: VITE_SUPABASE_URL');
}

if (!SUPABASE_ANON_KEY) {
  throw new Error('Missing required environment variable: VITE_SUPABASE_ANON_KEY');
}

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY);