/**
 * Mobile App Layout
 *
 * A comprehensive layout for mobile views that includes:
 * - Navigation bar
 * - Content area with proper spacing
 * - Version information
 */

import React, { useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Home, PlusCircle, MessageSquare, LayoutDashboard, ClipboardList } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import { isInRoleGroup, ROLE_GROUPS } from '@/constants/roles';

// Navigation item component
interface NavItemProps {
  icon: React.ReactNode;
  label: string;
  path: string;
  isActive: boolean;
  onClick: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ icon, label, isActive, onClick }) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "flex flex-col items-center justify-center flex-1 py-2 px-1 transition-colors",
        isActive
          ? "text-blue-600"
          : "text-gray-500 hover:text-blue-500"
      )}
    >
      <div className="relative">
        {icon}
      </div>
      <span className="text-xs mt-1 font-medium">{label}</span>
    </button>
  );
};

// Version indicator component
const VersionIndicator: React.FC = () => {
  const buildTime = new Date().toISOString().substring(0, 10);

  return (
    <div className="fixed top-0 left-0 right-0 bg-blue-500 text-white text-xs px-2 py-1 z-50 flex justify-between">
      <span>Mobile View</span>
      <span>v{buildTime}</span>
    </div>
  );
};

// Main layout component
const MobileAppLayout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, profile, userRole } = useAuth();

  // Log when the layout is rendered
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {

      console.log('MobileAppLayout rendered completed');

      }
  }, [location.pathname, user]);

  // Don't render navigation for unauthenticated users
  if (!user) {
    return (
      <div className="flex flex-col min-h-screen bg-gray-50">
        <VersionIndicator />
        <main className="flex-1">
          <Outlet />
        </main>
      </div>
    );
  }

  // Determine if the user is an admin or can create tasks
  const isAdmin = userRole === 'admin';
  const canCreateTasks = isInRoleGroup(userRole, ROLE_GROUPS.TASK_CREATORS);
  const isSupplier = profile?.account_type === 'supplier';

  // Check if the current path matches a nav item
  const isActive = (path: string) => {
    if (path === '/dashboard' && location.pathname === '/dashboard') {
      return true;
    }
    if (path === '/tasks/my-tasks' &&
        (location.pathname === '/tasks/my-tasks' || location.pathname === '/dashboard?tab=tasks')) {
      return true;
    }
    if (path === '/post-task' && location.pathname === '/post-task') {
      return true;
    }
    if (path === '/mobile/chats' &&
        (location.pathname === '/mobile/chats' || location.pathname.startsWith('/mobile/chat/'))) {
      return true;
    }
    if (path === '/organization/dashboard' && location.pathname === '/organization/dashboard') {
      return true;
    }
    return false;
  };

  // Navigate to a path
  const navigateTo = (path: string) => {
    navigate(path);
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Version indicator */}
      <VersionIndicator />

      {/* Main content */}
      <main className="flex-1 pb-16 pt-6">
        <Outlet />
      </main>

      {/* Navigation bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
        <div className="flex items-center justify-around">
          <NavItem
            icon={<Home size={20} />}
            label="Home"
            path="/dashboard"
            isActive={isActive('/dashboard')}
            onClick={() => navigateTo('/dashboard')}
          />

          <NavItem
            icon={<ClipboardList size={20} />}
            label="My Tasks"
            path="/tasks/my-tasks"
            isActive={isActive('/tasks/my-tasks')}
            onClick={() => navigateTo('/tasks/my-tasks')}
          />

          {/* Only show Create Task for users who can create tasks */}
          {(canCreateTasks || isSupplier) && (
            <NavItem
              icon={<PlusCircle size={20} />}
              label="New Task"
              path="/post-task"
              isActive={isActive('/post-task')}
              onClick={() => navigateTo('/post-task')}
            />
          )}

          <NavItem
            icon={<MessageSquare size={20} />}
            label="Messages"
            path="/mobile/chats"
            isActive={isActive('/mobile/chats')}
            onClick={() => navigateTo('/mobile/chats')}
          />

          {/* Only show Organisation Dashboard for admins */}
          {isAdmin && (
            <NavItem
              icon={<LayoutDashboard size={20} />}
              label="Org"
              path="/organisation/dashboard"
              isActive={isActive('/organisation/dashboard')}
              onClick={() => navigateTo('/organisation/dashboard')}
            />
          )}
        </div>

        {/* Add a spacer at the bottom to prevent content from being hidden behind the nav bar */}
        <div className="h-safe-area-bottom bg-white" />
      </div>
    </div>
  );
};

export default MobileAppLayout;
