/**
 * Mobile Layout
 * 
 * A simple, reliable layout for mobile views with a fixed bottom navigation bar.
 */

import React from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { Home, PlusCircle, MessageSquare, LayoutDashboard, ClipboardList } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import { isInRoleGroup, ROLE_GROUPS } from '@/constants/roles';

// Navigation item component
const NavItem = ({ icon, label, path, isActive, onClick }) => (
  <button
    onClick={onClick}
    className={cn(
      "flex flex-col items-center justify-center flex-1 py-2 px-1 transition-colors",
      isActive
        ? "text-blue-600"
        : "text-gray-500 hover:text-blue-500"
    )}
  >
    <div className="relative">
      {icon}
    </div>
    <span className="text-xs mt-1 font-medium">{label}</span>
  </button>
);

// Main layout component
const MobileLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, profile, userRole } = useAuth();
  
  // Don't render navigation for unauthenticated users
  if (!user) {
    return (
      <div className="flex flex-col min-h-screen bg-gray-50">
        <main className="flex-1">
          <Outlet />
        </main>
      </div>
    );
  }
  
  // Determine if the user is an admin or can create tasks
  const isAdmin = userRole === 'admin';
  const canCreateTasks = isInRoleGroup(userRole, ROLE_GROUPS.TASK_CREATORS);
  const isSupplier = profile?.account_type === 'supplier';
  
  // Check if the current path matches a nav item
  const isActive = (path) => {
    if (path === '/dashboard' && location.pathname === '/dashboard') {
      return true;
    }
    if (path === '/tasks/my-tasks' &&
        (location.pathname === '/tasks/my-tasks' || location.pathname === '/dashboard?tab=tasks')) {
      return true;
    }
    if (path === '/post-task' && location.pathname === '/post-task') {
      return true;
    }
    if (path === '/mobile/chats' &&
        (location.pathname === '/mobile/chats' || location.pathname.startsWith('/mobile/chat/'))) {
      return true;
    }
    if (path === '/organization/dashboard' && location.pathname === '/organization/dashboard') {
      return true;
    }
    return false;
  };
  
  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Version indicator */}
      <div className="fixed top-0 left-0 right-0 bg-blue-500 text-white text-xs px-2 py-1 z-50 flex justify-between">
        <span>Mobile View</span>
        <span>v{new Date().toISOString().substring(0, 10)}</span>
      </div>
      
      {/* Main content */}
      <main className="flex-1 pb-16 pt-6">
        <Outlet />
      </main>
      
      {/* Navigation bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
        <div className="flex items-center justify-around">
          <NavItem
            icon={<Home size={20} />}
            label="Home"
            path="/dashboard"
            isActive={isActive('/dashboard')}
            onClick={() => navigate('/dashboard')}
          />
          
          <NavItem
            icon={<ClipboardList size={20} />}
            label="My Tasks"
            path="/tasks/my-tasks"
            isActive={isActive('/tasks/my-tasks')}
            onClick={() => navigate('/tasks/my-tasks')}
          />
          
          {/* Only show Create Task for users who can create tasks */}
          {(canCreateTasks || isSupplier) && (
            <NavItem
              icon={<PlusCircle size={20} />}
              label="New Task"
              path="/post-task"
              isActive={isActive('/post-task')}
              onClick={() => navigate('/post-task')}
            />
          )}
          
          <NavItem
            icon={<MessageSquare size={20} />}
            label="Messages"
            path="/mobile/chats"
            isActive={isActive('/mobile/chats')}
            onClick={() => navigate('/mobile/chats')}
          />
          
          {/* Only show Organization Dashboard for admins */}
          {isAdmin && (
            <NavItem
              icon={<LayoutDashboard size={20} />}
              label="Org"
              path="/organization/dashboard"
              isActive={isActive('/organization/dashboard')}
              onClick={() => navigate('/organization/dashboard')}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default MobileLayout;
