/**
 * Security middleware for API routes and components
 */

import { supabase } from '@/integrations/supabase/client';
import { validate, sanitize, rateLimit, securityHeaders } from '@/utils/security';

/**
 * Authentication middleware
 */
export const requireAuth = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();
  
  if (error || !session) {
    throw new Error('Authentication required');
  }
  
  return session;
};

/**
 * Role-based authorization middleware
 */
export const requireRole = async (requiredRoles: string | string[]) => {
  const session = await requireAuth();
  
  const { data: profile, error } = await supabase
    .from('profiles')
    .select('role, is_site_admin')
    .eq('id', session.user.id)
    .single();
    
  if (error || !profile) {
    throw new Error('Profile not found');
  }
  
  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  
  // Site admins have access to everything
  if (profile.is_site_admin) {
    return { session, profile };
  }
  
  if (!roles.includes(profile.role)) {
    throw new Error('Insufficient permissions');
  }
  
  return { session, profile };
};

/**
 * Organization membership middleware
 */
export const requireOrganizationMembership = async (organizationId: string) => {
  const session = await requireAuth();
  
  const { data: profile, error } = await supabase
    .from('profiles')
    .select('organization_id, role, is_site_admin')
    .eq('id', session.user.id)
    .single();
    
  if (error || !profile) {
    throw new Error('Profile not found');
  }
  
  // Site admins have access to all organizations
  if (profile.is_site_admin) {
    return { session, profile };
  }
  
  if (profile.organization_id !== organizationId) {
    throw new Error('Access denied: Not a member of this organization');
  }
  
  return { session, profile };
};

/**
 * Input validation middleware
 */
export const validateInput = (data: any, rules: Record<string, any>) => {
  const validation = validate.required(data);
  if (!validation) {
    throw new Error('Request data is required');
  }
  
  // Validate each field according to rules
  for (const [field, rule] of Object.entries(rules)) {
    const value = data[field];
    
    if (rule.required && !validate.required(value)) {
      throw new Error(`${field} is required`);
    }
    
    if (value && rule.type) {
      switch (rule.type) {
        case 'email':
          if (!validate.email(value)) {
            throw new Error(`Invalid ${field} format`);
          }
          break;
        case 'uuid':
          if (!validate.uuid(value)) {
            throw new Error(`Invalid ${field} format`);
          }
          break;
        case 'numeric':
          if (!validate.numeric(value, rule.min, rule.max)) {
            throw new Error(`Invalid ${field} value`);
          }
          break;
      }
    }
    
    if (value && rule.length) {
      if (!validate.length(value, rule.length.min || 0, rule.length.max || Infinity)) {
        throw new Error(`${field} length is invalid`);
      }
    }
  }
  
  return true;
};

/**
 * Sanitize input data
 */
export const sanitizeInput = (data: any): any => {
  if (typeof data === 'string') {
    return sanitize.text(data);
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitizeInput);
  }
  
  if (typeof data === 'object' && data !== null) {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return data;
};

/**
 * Rate limiting middleware
 */
export const applyRateLimit = (key: string, maxAttempts: number, windowMs: number) => {
  if (rateLimit.isLimited(key, maxAttempts, windowMs)) {
    throw new Error('Rate limit exceeded. Please try again later.');
  }
};

/**
 * CSRF protection middleware
 */
export const validateCSRF = (token: string, expectedToken: string) => {
  if (!token || token !== expectedToken) {
    throw new Error('CSRF token validation failed');
  }
};

/**
 * File upload security middleware
 */
export const validateFileUpload = (file: File, options: {
  maxSize?: number;
  allowedTypes?: string[];
  allowedExtensions?: string[];
}) => {
  const { maxSize = 5 * 1024 * 1024, allowedTypes = [], allowedExtensions = [] } = options;
  
  // Check file size
  if (file.size > maxSize) {
    throw new Error(`File size exceeds maximum allowed size of ${maxSize / 1024 / 1024}MB`);
  }
  
  // Check file type
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    throw new Error(`File type ${file.type} is not allowed`);
  }
  
  // Check file extension
  if (allowedExtensions.length > 0) {
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension || !allowedExtensions.includes(extension)) {
      throw new Error(`File extension .${extension} is not allowed`);
    }
  }
  
  // Sanitize filename
  const sanitizedName = sanitize.filename(file.name);
  if (sanitizedName !== file.name) {
    console.warn('Filename was sanitized:', { original: file.name, sanitized: sanitizedName });
  }
  
  return true;
};

/**
 * SQL injection protection
 */
export const sanitizeForSQL = (input: string): string => {
  if (typeof input !== 'string') return '';
  
  // Remove or escape potentially dangerous characters
  return input
    .replace(/'/g, "''")  // Escape single quotes
    .replace(/;/g, '')    // Remove semicolons
    .replace(/--/g, '')   // Remove SQL comments
    .replace(/\/\*/g, '') // Remove SQL block comments start
    .replace(/\*\//g, '') // Remove SQL block comments end
    .trim();
};

/**
 * XSS protection for HTML content
 */
export const sanitizeHTML = (html: string): string => {
  return sanitize.html(html);
};

/**
 * Security headers for responses
 */
export const getSecurityHeaders = (): Record<string, string> => {
  return { ...securityHeaders };
};

/**
 * Comprehensive security check for API requests
 */
export const securityCheck = async (options: {
  requireAuth?: boolean;
  requiredRole?: string | string[];
  organizationId?: string;
  rateLimit?: { key: string; maxAttempts: number; windowMs: number };
  validateData?: { data: any; rules: Record<string, any> };
}) => {
  const { requireAuth: needsAuth, requiredRole, organizationId, rateLimit: rateLimitConfig, validateData } = options;
  
  let session = null;
  let profile = null;
  
  // Apply rate limiting if configured
  if (rateLimitConfig) {
    applyRateLimit(rateLimitConfig.key, rateLimitConfig.maxAttempts, rateLimitConfig.windowMs);
  }
  
  // Check authentication if required
  if (needsAuth) {
    session = await requireAuth();
  }
  
  // Check role if required
  if (requiredRole && session) {
    const result = await requireRole(requiredRole);
    profile = result.profile;
  }
  
  // Check organization membership if required
  if (organizationId && session) {
    const result = await requireOrganizationMembership(organizationId);
    profile = result.profile;
  }
  
  // Validate input data if provided
  if (validateData) {
    validateInput(validateData.data, validateData.rules);
  }
  
  return { session, profile };
};

/**
 * Error handler for security middleware
 */
export const handleSecurityError = (error: Error) => {
  console.error('Security middleware error:', error);
  
  // Don't expose internal error details
  const publicMessage = error.message.includes('Authentication required') ? 'Authentication required' :
                       error.message.includes('Insufficient permissions') ? 'Access denied' :
                       error.message.includes('Rate limit') ? 'Too many requests' :
                       error.message.includes('validation') ? 'Invalid input' :
                       'Security check failed';
  
  return {
    error: publicMessage,
    status: error.message.includes('Authentication required') ? 401 :
            error.message.includes('permissions') || error.message.includes('Access denied') ? 403 :
            error.message.includes('Rate limit') ? 429 :
            error.message.includes('validation') ? 400 :
            500
  };
};
