import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Shield, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';

interface AccessDeniedProps {
  message?: string;
}

/**
 * Access Denied page shown when a user tries to access a page they don't have permission for
 */
const AccessDenied: React.FC<AccessDeniedProps> = ({ message }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, userRole, isSiteAdmin } = useAuth();

  const from = location.state?.from?.pathname || '/dashboard';

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="max-w-md w-full p-8 bg-white rounded-lg shadow-md">
        <div className="flex flex-col items-center text-center">
          <div className="p-3 bg-red-100 rounded-full">
            <Shield className="h-12 w-12 text-red-500" />
          </div>

          <h1 className="mt-4 text-2xl font-bold text-gray-900">Access Denied</h1>

          <p className="mt-2 text-gray-600">
            {message || "You don't have permission to access this page."}
          </p>

          {user && (
            <div className="mt-4 p-3 bg-gray-50 rounded-md w-full">
              <p className="text-sm text-gray-500">
                Current role: <span className="font-medium">{userRole || 'None'}</span>
              </p>
              {isSiteAdmin && (
                <p className="text-sm text-gray-500 mt-1">
                  <span className="font-medium text-red-600">Site Administrator</span>
                </p>
              )}
              <p className="text-sm text-gray-500 mt-1">
                This page requires a different role or permission level.
              </p>
            </div>
          )}

          <div className="mt-6 flex flex-col gap-2 w-full">
            <Button
              onClick={() => navigate('/dashboard')}
              className="w-full"
            >
              Go to Dashboard
            </Button>

            <Button
              variant="outline"
              onClick={() => navigate(-1)}
              className="w-full"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccessDenied;
