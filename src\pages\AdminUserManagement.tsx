import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/hooks/use-toast';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, Trash2, UserX, Alert<PERSON>riangle, RefreshCw } from 'lucide-react';

// Types for our component
interface Profile {
  id: string;
  first_name?: string;
  last_name?: string;
  account_type?: string;
  role?: string;
  created_at: string;
}

interface User {
  id: string;
  email?: string;
  created_at: string;
  last_sign_in_at?: string;
  user_metadata?: any;
}

interface UserWithProfile extends User {
  profile?: Profile;
}

interface DuplicateEmail {
  email: string;
  users: UserWithProfile[];
}

const AdminUserManagement = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<UserWithProfile[]>([]);
  const [profiles, setProfiles] = useState<Profile[]>([]);
  const [duplicateEmails, setDuplicateEmails] = useState<DuplicateEmail[]>([]);
  const [orphanedProfiles, setOrphanedProfiles] = useState<Profile[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<UserWithProfile | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleteProfileDialogOpen, setIsDeleteProfileDialogOpen] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState<Profile | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [activeTab, setActiveTab] = useState('all-users');

  // Check if user is admin using the profiles table as the source of truth
  const { userRole } = useAuth();
  useEffect(() => {
    if (user) {
      const isAdmin = userRole === 'admin';
      if (!isAdmin) {
        toast({
          title: 'Access Denied',
          description: 'You do not have permission to access this page.',
          variant: 'destructive',
        });
        navigate('/dashboard');
      }
    }
  }, [user, userRole, navigate]);

  // Fetch users and profiles
  const fetchData = async () => {
    setIsLoading(true);
    try {
      // Fetch all users
      const { data: userData, error: userError } = await supabase.auth.admin.listUsers();

      if (userError) throw userError;

      // Fetch all profiles
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*');

      if (profileError) throw profileError;

      // Create a map of profiles by ID for easy lookup
      const profileMap = new Map<string, Profile>();
      profileData.forEach((profile: Profile) => {
        profileMap.set(profile.id, profile);
      });

      // Combine users with their profiles
      const usersWithProfiles = userData.users.map((user: User) => ({
        ...user,
        profile: profileMap.get(user.id),
      }));

      // Find duplicate emails
      const emailMap = new Map<string, UserWithProfile[]>();
      usersWithProfiles.forEach((user: UserWithProfile) => {
        if (user.email) {
          const email = user.email.toLowerCase();
          if (!emailMap.has(email)) {
            emailMap.set(email, []);
          }
          emailMap.get(email)?.push(user);
        }
      });

      const duplicates = Array.from(emailMap.entries())
        .filter(([_, users]) => users.length > 1)
        .map(([email, users]) => ({
          email,
          users: users.sort((a, b) => {
            const aDate = a.last_sign_in_at ? new Date(a.last_sign_in_at).getTime() : 0;
            const bDate = b.last_sign_in_at ? new Date(b.last_sign_in_at).getTime() : 0;
            return bDate - aDate; // Sort by most recent sign-in
          }),
        }));

      // Find orphaned profiles
      const userIds = new Set(usersWithProfiles.map(user => user.id));
      const orphaned = profileData.filter((profile: Profile) => !userIds.has(profile.id));

      setUsers(usersWithProfiles);
      setProfiles(profileData);
      setDuplicateEmails(duplicates);
      setOrphanedProfiles(orphaned);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load user data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [user]);

  // Filter users based on search term
  const filteredUsers = users.filter(user => {
    const searchLower = searchTerm.toLowerCase();
    return (
      user.email?.toLowerCase().includes(searchLower) ||
      user.id.toLowerCase().includes(searchLower) ||
      user.profile?.first_name?.toLowerCase().includes(searchLower) ||
      user.profile?.last_name?.toLowerCase().includes(searchLower) ||
      user.profile?.role?.toLowerCase().includes(searchLower)
    );
  });

  // Delete a user
  const deleteUser = async (userId: string) => {
    setIsDeleting(true);
    try {
      const { error } = await supabase.auth.admin.deleteUser(userId);

      if (error) throw error;

      // Remove the user from the local state
      setUsers(users.filter(u => u.id !== userId));

      toast({
        title: 'Success',
        description: 'User has been deleted successfully.',
      });

      setIsDeleteDialogOpen(false);
      setSelectedUser(null);
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete user. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Delete a profile
  const deleteProfile = async (profileId: string) => {
    setIsDeleting(true);
    try {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', profileId);

      if (error) throw error;

      // Remove the profile from the local state
      setProfiles(profiles.filter(p => p.id !== profileId));
      setOrphanedProfiles(orphanedProfiles.filter(p => p.id !== profileId));

      // Update the users state to reflect the deleted profile
      setUsers(users.map(user => {
        if (user.profile?.id === profileId) {
          return { ...user, profile: undefined };
        }
        return user;
      }));

      toast({
        title: 'Success',
        description: 'Profile has been deleted successfully.',
      });

      setIsDeleteProfileDialogOpen(false);
      setSelectedProfile(null);
    } catch (error) {
      console.error('Error deleting profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete profile. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  // Get user display name
  const getUserName = (user: UserWithProfile) => {
    if (user.profile?.first_name || user.profile?.last_name) {
      return `${user.profile.first_name || ''} ${user.profile.last_name || ''}`.trim();
    }
    return 'No Name';
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>User Management</CardTitle>
                <CardDescription>
                  Manage users and profiles in your application
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchData}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : (
              <>
                <div className="mb-6">
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by email, name, or role..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>

                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="mb-4">
                    <TabsTrigger value="all-users">
                      All Users ({users.length})
                    </TabsTrigger>
                    <TabsTrigger value="duplicate-emails">
                      Duplicate Emails ({duplicateEmails.length})
                    </TabsTrigger>
                    <TabsTrigger value="orphaned-profiles">
                      Orphaned Profiles ({orphanedProfiles.length})
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="all-users">
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Email</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Role</TableHead>
                            <TableHead>Created</TableHead>
                            <TableHead>Last Sign In</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredUsers.length === 0 ? (
                            <TableRow>
                              <TableCell colSpan={6} className="text-center py-4">
                                No users found
                              </TableCell>
                            </TableRow>
                          ) : (
                            filteredUsers.map((user) => (
                              <TableRow key={user.id}>
                                <TableCell className="font-medium">
                                  {user.email || 'No Email'}
                                </TableCell>
                                <TableCell>{getUserName(user)}</TableCell>
                                <TableCell>
                                  {user.profile?.role ? (
                                    <Badge variant="outline">{user.profile.role}</Badge>
                                  ) : (
                                    'Not set'
                                  )}
                                </TableCell>
                                <TableCell>{formatDate(user.created_at)}</TableCell>
                                <TableCell>{formatDate(user.last_sign_in_at)}</TableCell>
                                <TableCell className="text-right">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => {
                                      setSelectedUser(user);
                                      setIsDeleteDialogOpen(true);
                                    }}
                                  >
                                    <Trash2 className="h-4 w-4 text-destructive" />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </TabsContent>

                  <TabsContent value="duplicate-emails">
                    {duplicateEmails.length === 0 ? (
                      <div className="text-center py-8 border rounded-md">
                        <p>No duplicate emails found</p>
                      </div>
                    ) : (
                      duplicateEmails.map((duplicate) => (
                        <Card key={duplicate.email} className="mb-4">
                          <CardHeader>
                            <CardTitle className="text-base flex items-center">
                              <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
                              Duplicate Email: {duplicate.email}
                            </CardTitle>
                            <CardDescription>
                              Found {duplicate.users.length} accounts with this email
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="rounded-md border">
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    <TableHead>User ID</TableHead>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Created</TableHead>
                                    <TableHead>Last Sign In</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {duplicate.users.map((user) => (
                                    <TableRow key={user.id}>
                                      <TableCell className="font-mono text-xs">
                                        {user.id}
                                      </TableCell>
                                      <TableCell>{getUserName(user)}</TableCell>
                                      <TableCell>{formatDate(user.created_at)}</TableCell>
                                      <TableCell>{formatDate(user.last_sign_in_at)}</TableCell>
                                      <TableCell className="text-right">
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          onClick={() => {
                                            setSelectedUser(user);
                                            setIsDeleteDialogOpen(true);
                                          }}
                                        >
                                          <Trash2 className="h-4 w-4 text-destructive" />
                                        </Button>
                                      </TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </TabsContent>

                  <TabsContent value="orphaned-profiles">
                    {orphanedProfiles.length === 0 ? (
                      <div className="text-center py-8 border rounded-md">
                        <p>No orphaned profiles found</p>
                      </div>
                    ) : (
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Profile ID</TableHead>
                              <TableHead>Name</TableHead>
                              <TableHead>Account Type</TableHead>
                              <TableHead>Created</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {orphanedProfiles.map((profile) => (
                              <TableRow key={profile.id}>
                                <TableCell className="font-mono text-xs">
                                  {profile.id}
                                </TableCell>
                                <TableCell>
                                  {`${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'No Name'}
                                </TableCell>
                                <TableCell>{profile.account_type || 'Not set'}</TableCell>
                                <TableCell>{formatDate(profile.created_at)}</TableCell>
                                <TableCell className="text-right">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => {
                                      setSelectedProfile(profile);
                                      setIsDeleteProfileDialogOpen(true);
                                    }}
                                  >
                                    <Trash2 className="h-4 w-4 text-destructive" />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Delete User Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete User</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this user? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">Email</Label>
                  <p className="font-medium">{selectedUser.email || 'No Email'}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Name</Label>
                  <p className="font-medium">{getUserName(selectedUser)}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Created</Label>
                  <p>{formatDate(selectedUser.created_at)}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Last Sign In</Label>
                  <p>{formatDate(selectedUser.last_sign_in_at)}</p>
                </div>
              </div>
              <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
                <div className="flex items-start">
                  <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 mt-0.5" />
                  <div>
                    <p className="text-amber-800 text-sm font-medium">Warning</p>
                    <p className="text-amber-700 text-sm">
                      Deleting this user will permanently remove their account and all associated data.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => selectedUser && deleteUser(selectedUser.id)}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <UserX className="mr-2 h-4 w-4" />
                  Delete User
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Profile Dialog */}
      <Dialog open={isDeleteProfileDialogOpen} onOpenChange={setIsDeleteProfileDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Profile</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this profile? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          {selectedProfile && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground">Profile ID</Label>
                  <p className="font-mono text-xs">{selectedProfile.id}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Name</Label>
                  <p className="font-medium">
                    {`${selectedProfile.first_name || ''} ${selectedProfile.last_name || ''}`.trim() || 'No Name'}
                  </p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Account Type</Label>
                  <p>{selectedProfile.account_type || 'Not set'}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">Created</Label>
                  <p>{formatDate(selectedProfile.created_at)}</p>
                </div>
              </div>
              <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
                <div className="flex items-start">
                  <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 mt-0.5" />
                  <div>
                    <p className="text-amber-800 text-sm font-medium">Warning</p>
                    <p className="text-amber-700 text-sm">
                      This profile has no associated user account. Deleting it will remove all profile data.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteProfileDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => selectedProfile && deleteProfile(selectedProfile.id)}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Profile
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
};

export default AdminUserManagement;
