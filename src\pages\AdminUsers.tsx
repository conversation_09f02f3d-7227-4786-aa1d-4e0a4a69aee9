import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/hooks/use-toast';
import { Loader2, Search, MoreHorizontal, Trash2, UserX, RefreshCw, Shield, Filter, Download } from 'lucide-react';

// Types
interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  account_type: string | null;
  role: string | null;
  created_at: string;
  organization_id: string | null;
}

interface User {
  id: string;
  email: string;
  created_at: string;
  last_sign_in_at: string | null;
  user_metadata: any;
}

interface UserWithProfile extends User {
  profile: Profile | null;
  organization_name?: string | null;
}

const AdminUsers = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<UserWithProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserWithProfile[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<UserWithProfile | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [totalUsers, setTotalUsers] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [roleFilter, setRoleFilter] = useState('all');
  const [accountTypeFilter, setAccountTypeFilter] = useState('all');
  const [sortField, setSortField] = useState('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [organizations, setOrganizations] = useState<{[key: string]: string}>({});

  // Check if user is admin
  useEffect(() => {
    if (user) {
      const isAdmin = user.user_metadata?.role === 'admin';
      if (!isAdmin) {
        toast({
          title: 'Access Denied',
          description: 'You do not have permission to access this page.',
          variant: 'destructive',
        });
        navigate('/dashboard');
      }
    }
  }, [user, navigate]);

  // Fetch organizations for lookup
  const fetchOrganizations = async () => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('id, name');

      if (error) throw error;

      const orgMap: {[key: string]: string} = {};
      data.forEach((org: any) => {
        orgMap[org.id] = org.name;
      });

      setOrganizations(orgMap);
    } catch (error) {
      console.error('Error fetching organizations:', error);
    }
  };

  // Fetch users and profiles
  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      // For now, let's use a simpler approach with direct Supabase calls
      // This is a temporary solution until we get the admin API working

      // Fetch users from auth.users table (this is a public table in our setup)
      const { data: userData, error: userError } = await supabase
        .from('users') // This assumes you have a users view or table that's accessible
        .select('*')
        .range((currentPage - 1) * pageSize, currentPage * pageSize - 1);

      if (userError) throw userError;

      // Get approximate total count
      const { count, error: countError } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true });

      if (countError) throw countError;
      setTotalUsers(count || 0);

      // Fetch all profiles
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*');

      if (profileError) throw profileError;

      // Create a map of profiles by ID for easy lookup
      const profileMap = new Map<string, Profile>();
      profileData.forEach((profile: Profile) => {
        profileMap.set(profile.id, profile);
      });

      // Combine users with their profiles
      const usersWithProfiles: UserWithProfile[] = userData.map((user: any) => ({
        id: user.id,
        email: user.email,
        created_at: user.created_at,
        last_sign_in_at: user.last_sign_in_at,
        user_metadata: user.raw_user_meta_data,
        profile: profileMap.get(user.id) || null,
        organization_name: profileMap.get(user.id)?.organization_id
          ? organizations[profileMap.get(user.id)?.organization_id || '']
          : null
      }));

      setUsers(usersWithProfiles);
      applyFilters(usersWithProfiles, searchTerm, roleFilter, accountTypeFilter);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: 'Error',
        description: 'Failed to load user data. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (user) {
      fetchOrganizations();
    }
  }, [user]);

  // Fetch users when pagination, sorting, or organizations change
  useEffect(() => {
    if (user && Object.keys(organizations).length > 0) {
      fetchUsers();
    }
  }, [user, currentPage, pageSize, sortField, sortDirection, organizations]);

  // Apply filters
  const applyFilters = (
    userList: UserWithProfile[],
    search: string,
    role: string,
    accountType: string
  ) => {
    let filtered = [...userList];

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(user =>
        user.email.toLowerCase().includes(searchLower) ||
        user.id.toLowerCase().includes(searchLower) ||
        user.profile?.first_name?.toLowerCase().includes(searchLower) ||
        user.profile?.last_name?.toLowerCase().includes(searchLower) ||
        user.organization_name?.toLowerCase().includes(searchLower)
      );
    }

    // Apply role filter
    if (role !== 'all') {
      filtered = filtered.filter(user => user.profile?.role === role);
    }

    // Apply account type filter
    if (accountType !== 'all') {
      filtered = filtered.filter(user => user.profile?.account_type === accountType);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let valueA: any;
      let valueB: any;

      switch (sortField) {
        case 'email':
          valueA = a.email.toLowerCase();
          valueB = b.email.toLowerCase();
          break;
        case 'name':
          valueA = `${a.profile?.first_name || ''} ${a.profile?.last_name || ''}`.toLowerCase();
          valueB = `${b.profile?.first_name || ''} ${b.profile?.last_name || ''}`.toLowerCase();
          break;
        case 'role':
          valueA = a.profile?.role || '';
          valueB = b.profile?.role || '';
          break;
        case 'last_sign_in':
          valueA = a.last_sign_in_at ? new Date(a.last_sign_in_at).getTime() : 0;
          valueB = b.last_sign_in_at ? new Date(b.last_sign_in_at).getTime() : 0;
          break;
        case 'created_at':
        default:
          valueA = new Date(a.created_at).getTime();
          valueB = new Date(b.created_at).getTime();
      }

      if (sortDirection === 'asc') {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });

    setFilteredUsers(filtered);
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    applyFilters(users, value, roleFilter, accountTypeFilter);
  };

  // Handle role filter change
  const handleRoleFilterChange = (value: string) => {
    setRoleFilter(value);
    applyFilters(users, searchTerm, value, accountTypeFilter);
  };

  // Handle account type filter change
  const handleAccountTypeFilterChange = (value: string) => {
    setAccountTypeFilter(value);
    applyFilters(users, searchTerm, roleFilter, value);
  };

  // Handle sort change
  const handleSortChange = (field: string) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // New field, default to descending
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Delete a user
  const deleteUser = async (userId: string) => {
    setIsDeleting(true);
    try {
      // For now, we'll use a simpler approach with direct Supabase calls
      // This is a temporary solution until we get the admin API working

      // First, delete the profile
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileError) throw profileError;

      // Then, delete from the users table if it exists
      const { error: userError } = await supabase
        .from('users')
        .delete()
        .eq('id', userId);

      // Note: userError might occur if the table doesn't exist, so we don't throw here
      if (userError) {
        console.warn('Could not delete from users table:', userError);
      }

      // Remove the user from the local state
      setUsers(users.filter(u => u.id !== userId));
      setFilteredUsers(filteredUsers.filter(u => u.id !== userId));

      toast({
        title: 'Success',
        description: 'User has been deleted successfully.',
      });

      setIsDeleteDialogOpen(false);
      setSelectedUser(null);
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete user. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Format date for display
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  // Get user display name
  const getUserName = (user: UserWithProfile) => {
    if (user.profile?.first_name || user.profile?.last_name) {
      return `${user.profile.first_name || ''} ${user.profile.last_name || ''}`.trim();
    }
    return 'No Name';
  };

  // Export users to CSV
  const exportUsers = () => {
    const headers = ['Email', 'Name', 'Role', 'Account Type', 'Organization', 'Created', 'Last Sign In'];

    const csvData = filteredUsers.map(user => [
      user.email,
      getUserName(user),
      user.profile?.role || 'Not set',
      user.profile?.account_type || 'Not set',
      user.organization_name || 'None',
      formatDate(user.created_at),
      formatDate(user.last_sign_in_at)
    ]);

    const csvContent = [
      headers.join(','),
      ...csvData.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `users_export_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Calculate pagination
  const totalPages = Math.ceil(totalUsers / pageSize);

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-amber-500" />
                  <CardTitle>User Management</CardTitle>
                </div>
                <CardDescription>
                  View and manage all users registered on the platform
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchUsers}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportUsers}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : (
              <>
                <div className="mb-6 space-y-4">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search by email, name, or organization..."
                        className="pl-8"
                        value={searchTerm}
                        onChange={handleSearchChange}
                      />
                    </div>
                    <div className="flex gap-2">
                      <div className="w-40">
                        <Select value={roleFilter} onValueChange={handleRoleFilterChange}>
                          <SelectTrigger>
                            <div className="flex items-center">
                              <Filter className="h-4 w-4 mr-2" />
                              <span className="truncate">
                                {roleFilter === 'all' ? 'All Roles' : roleFilter}
                              </span>
                            </div>
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Roles</SelectItem>
                            <SelectItem value="admin">Admin</SelectItem>
                            <SelectItem value="teacher">Teacher</SelectItem>
                            <SelectItem value="support">Support</SelectItem>
                            <SelectItem value="maintenance">Maintenance</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="w-40">
                        <Select value={accountTypeFilter} onValueChange={handleAccountTypeFilterChange}>
                          <SelectTrigger>
                            <div className="flex items-center">
                              <Filter className="h-4 w-4 mr-2" />
                              <span className="truncate">
                                {accountTypeFilter === 'all' ? 'All Types' : accountTypeFilter}
                              </span>
                            </div>
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Types</SelectItem>
                            <SelectItem value="school">School</SelectItem>
                            <SelectItem value="supplier">Supplier</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <div className="text-sm text-muted-foreground">
                    Showing {filteredUsers.length} of {totalUsers} users
                  </div>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => handleSortChange('email')}
                        >
                          Email
                          {sortField === 'email' && (
                            <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                          )}
                        </TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => handleSortChange('name')}
                        >
                          Name
                          {sortField === 'name' && (
                            <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                          )}
                        </TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => handleSortChange('role')}
                        >
                          Role
                          {sortField === 'role' && (
                            <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                          )}
                        </TableHead>
                        <TableHead>Organization</TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => handleSortChange('created_at')}
                        >
                          Created
                          {sortField === 'created_at' && (
                            <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                          )}
                        </TableHead>
                        <TableHead
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => handleSortChange('last_sign_in')}
                        >
                          Last Sign In
                          {sortField === 'last_sign_in' && (
                            <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                          )}
                        </TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredUsers.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-4">
                            No users found
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredUsers.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell className="font-medium">
                              {user.email}
                            </TableCell>
                            <TableCell>{getUserName(user)}</TableCell>
                            <TableCell>
                              {user.profile?.role ? (
                                <Badge variant="outline">
                                  {user.profile.role}
                                </Badge>
                              ) : (
                                'Not set'
                              )}
                            </TableCell>
                            <TableCell>
                              {user.organization_name || 'None'}
                            </TableCell>
                            <TableCell>{formatDate(user.created_at)}</TableCell>
                            <TableCell>{formatDate(user.last_sign_in_at)}</TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Actions</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    className="text-destructive focus:text-destructive"
                                    onClick={() => {
                                      setSelectedUser(user);
                                      setIsDeleteDialogOpen(true);
                                    }}
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete User
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Delete User Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete User</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this user? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Email</p>
                  <p className="font-medium">{selectedUser.email}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Name</p>
                  <p className="font-medium">{getUserName(selectedUser)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Role</p>
                  <p>{selectedUser.profile?.role || 'Not set'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Organization</p>
                  <p>{selectedUser.organization_name || 'None'}</p>
                </div>
              </div>
              <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
                <div className="flex items-start">
                  <Trash2 className="h-5 w-5 text-amber-500 mr-2 mt-0.5" />
                  <div>
                    <p className="text-amber-800 text-sm font-medium">Warning</p>
                    <p className="text-amber-700 text-sm">
                      Deleting this user will permanently remove their account and all associated data.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => selectedUser && deleteUser(selectedUser.id)}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <UserX className="mr-2 h-4 w-4" />
                  Delete User
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
};

export default AdminUsers;
