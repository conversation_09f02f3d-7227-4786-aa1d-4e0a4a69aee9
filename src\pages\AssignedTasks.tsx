import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Link } from 'react-router-dom';
import { MapPin, Calendar, Tag, User, Clock } from 'lucide-react';
import { format } from 'date-fns';

interface Task {
  id: string;
  title: string;
  description: string;
  location: string;
  location_formatted?: string;
  due_date: string;
  category: string;
  status: string;
  assigned_to: string;
  assigned_to_name?: string;
  organization_id?: string;
  organization_name?: string;
  visibility: string;
  created_at: string;
  updated_at: string;
}

const AssignedTasks = () => {
  const { user, profile } = useAuth();
  const navigate = useNavigate();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchAssignedTasks = async () => {
      if (!user) {
        navigate('/login');
        return;
      }

      try {
        setIsLoading(true);
        
        // Fetch tasks assigned to the current user
        const { data, error } = await supabase
          .from('tasks')
          .select(`
            *,
            organization:organizations(name)
          `)
          .eq('assigned_to', user.id)
          .order('updated_at', { ascending: false });

        if (error) {
          throw error;
        }

        // Process the tasks to add assigned_to_name
        const processedTasks = await Promise.all(
          (data || []).map(async (task) => {
            // If the task has an assigned_to value, fetch the assigned user's profile
            if (task.assigned_to) {
              const { data: profileData } = await supabase
                .from('profiles')
                .select('first_name, last_name, email')
                .eq('id', task.assigned_to)
                .maybeSingle();
                
              if (profileData) {
                // Create a property for the assigned user's name
                const fullName = `${profileData.first_name || ''} ${profileData.last_name || ''}`.trim();
                
                // Add it directly to the task data
                task.assigned_to_name = fullName;
              }
            }
            
            return {
              ...task,
              organization_name: task.organization?.name
            };
          })
        );

        setTasks(processedTasks);
      } catch (err) {
        console.error('Error fetching assigned tasks:', err);
        setError(err as Error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssignedTasks();
  }, [user, navigate]);

  // Function to get badge color based on status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'assigned':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">Assigned</Badge>;
      case 'in_progress':
        return <Badge variant="outline" className="bg-indigo-100 text-indigo-800 border-indigo-200">In Progress</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-200">Completed</Badge>;
      case 'confirmed':
        return <Badge variant="outline" className="bg-teal-100 text-teal-800 border-teal-200">Closed</Badge>;
      default:
        return <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">{status}</Badge>;
    }
  };

  // Function to get badge color based on visibility
  const getVisibilityBadge = (visibility: string) => {
    switch (visibility) {
      case 'internal':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">Internal</Badge>;
      case 'public':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">Public</Badge>;
      default:
        return <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">{visibility}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl font-bold mb-6">Assigned Tasks</h1>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full mb-4" />
                  <div className="grid grid-cols-2 gap-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl font-bold mb-6">Assigned Tasks</h1>
          <Card>
            <CardHeader>
              <CardTitle>Error</CardTitle>
              <CardDescription>
                There was an error loading your assigned tasks.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-red-500">{error.message}</p>
              <Button 
                className="mt-4"
                onClick={() => window.location.reload()}
              >
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Assigned Tasks</h1>
        
        {tasks.length === 0 ? (
          <Card>
            <CardHeader>
              <CardTitle>No Assigned Tasks</CardTitle>
              <CardDescription>
                You don't have any tasks assigned to you at the moment.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500">
                When tasks are assigned to you, they will appear here.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {tasks.map((task) => (
              <Link 
                key={task.id} 
                to={`/tasks/enhanced/${task.id}?messages=true`}
                className="block transition-transform hover:scale-[1.01] duration-200"
              >
                <Card className="overflow-hidden h-full flex flex-col">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg">{task.title}</CardTitle>
                      <div className="flex flex-col gap-1 items-end">
                        {getStatusBadge(task.status)}
                        {getVisibilityBadge(task.visibility)}
                      </div>
                    </div>
                    <div className="flex items-center text-gray-600 mt-1">
                      <MapPin size={16} className="mr-1" /> {task.location_formatted || task.location}
                    </div>
                  </CardHeader>
                  <CardContent className="pb-4 flex-grow">
                    <p className="text-sm text-gray-600 line-clamp-2 mb-4">{task.description}</p>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center text-gray-600">
                        <Calendar size={14} className="mr-1" /> 
                        <span>Due: {format(new Date(task.due_date), 'PP')}</span>
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Tag size={14} className="mr-1" /> 
                        <span>{task.category}</span>
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Clock size={14} className="mr-1" /> 
                        <span>Updated: {format(new Date(task.updated_at), 'PP')}</span>
                      </div>
                      {task.organization_name && (
                        <div className="flex items-center text-gray-600">
                          <User size={14} className="mr-1" /> 
                          <span>{task.organization_name}</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default AssignedTasks;
