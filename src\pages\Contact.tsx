import React from 'react';
import { Link } from 'react-router-dom';

const Contact: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">

      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-gray-900">Contact Us</h1>
        </div>
      </header>

      {/* Main content */}
      <main>
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
              <h2 className="text-lg leading-6 font-medium text-gray-900">
                Get in Touch
              </h2>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                We're here to help with any questions or feedback you may have.
              </p>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
              <div className="space-y-6">
                {/* Email Contact */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Email Us</h3>
                  <div className="mt-2 text-sm text-gray-500">
                    <p>For general inquiries, please contact us at:</p>
                    <p className="mt-2">
                      <a
                        href="mailto:<EMAIL>"
                        className="text-blue-600 hover:text-blue-800 font-medium"
                      >
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>

                {/* Support */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Support</h3>
                  <div className="mt-2 text-sm text-gray-500">
                    <p>For technical support or help with your account, please visit our support page:</p>
                    <p className="mt-2">
                      <Link
                        to="/help"
                        className="text-blue-600 hover:text-blue-800 font-medium"
                      >
                        Support Centre
                      </Link>
                    </p>
                  </div>
                </div>

                {/* Business Hours */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Business Hours</h3>
                  <div className="mt-2 text-sm text-gray-500">
                    <p>Our team is available Monday through Friday, 9:00 AM to 5:00 PM (UK time).</p>
                    <p className="mt-1">We typically respond to all inquiries within 24-48 business hours.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
              <h2 className="text-lg leading-6 font-medium text-gray-900">
                About Classtasker
              </h2>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
              <p className="text-sm text-gray-500">
                Classtasker is a comprehensive task management platform designed specifically for schools and educational institutions.
                Our mission is to simplify maintenance, IT, and administrative tasks, allowing schools to focus on what matters most: education.
              </p>
              <div className="mt-4">
                <Link
                  to="/"
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  Learn more about Classtasker
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Contact;
