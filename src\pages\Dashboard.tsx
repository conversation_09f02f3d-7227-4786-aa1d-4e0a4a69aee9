import { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { useTasks } from '@/hooks/use-tasks';
import { useOffers } from '@/hooks/use-offers';
import { Notification } from '@/types/notifications';
import {
  ListTodo,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  Clock,
  PoundSterling
} from 'lucide-react';

// Import dashboard components
import DashboardStats from '@/components/dashboard/DashboardStats';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import ActionItems from '@/components/dashboard/ActionItems';
import RecentTasks from '@/components/dashboard/RecentTasks';
import NotificationsList from '@/components/dashboard/NotificationsList';
import TasksList from '@/components/dashboard/TasksList';
import OffersSection from '@/components/dashboard/OffersSection';
import JobsSection from '@/components/dashboard/JobsSection';
import GetStreamMessagesSection from '@/components/dashboard/GetStreamMessagesSection';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const Dashboard = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const tabParam = searchParams.get('tab');

  // Set initial active tab from URL parameter or default to 'overview'
  const [activeTab, setActiveTab] = useState(tabParam || 'overview');
  const [processingInvitation, setProcessingInvitation] = useState(false);
  const {
    user,
    profile,
    isSupplier,
    isSchool,
    isAdmin,
    isTeacher,
    isMaintenance,
    isSupport
  } = useAuth();

  // Update URL when tab changes
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setSearchParams(tab === 'overview' ? {} : { tab });
  };

  // Update active tab when URL parameter changes
  useEffect(() => {
    if (tabParam && ['overview', 'my-tasks', 'offers', 'my-jobs', 'my-offers', 'messages'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Check if there's a pending invitation being processed
  useEffect(() => {
    const pendingToken = localStorage.getItem('pendingInvitationToken');
    const pendingEmail = localStorage.getItem('pendingInvitationEmail');
    const isNewUserWithInvitation = localStorage.getItem('newUserWithInvitation') === 'true';

    if (pendingToken && pendingEmail && user?.email === pendingEmail) {
      if (process.env.NODE_ENV === 'development') {
    console.log('Dashboard detected pending invitation processing');
  }
      setProcessingInvitation(true);

      // Set up a listener to check when the invitation processing is complete
      const checkInterval = setInterval(() => {
        // If the tokens are gone, processing is complete
        if (!localStorage.getItem('pendingInvitationToken')) {
          if (process.env.NODE_ENV === 'development') {
    console.log('Invitation processing complete');
  }
          setProcessingInvitation(false);
          clearInterval(checkInterval);
        }
      }, 1000);

      // Safety cleanup after 30 seconds
      setTimeout(() => {
        setProcessingInvitation(false);
        clearInterval(checkInterval);
      }, 30000);

      return () => {
        clearInterval(checkInterval);
      };
    }
  }, [user]);

  const {
    userTasks,
    isLoadingUserTasks,
  } = useTasks();

  const { getUserOffers } = useOffers();
  const { data: userOffers, isLoading: isLoadingUserOffers } = getUserOffers();

  // Notifications are now handled by the NotificationContext

  // Enhanced dashboard stats with more role-specific metrics
  const [dashboardStats, setDashboardStats] = useState({
    activeTasks: 0,
    offersCount: 0,
    completedTasks: 0,
    acceptedJobsCount: 0,
    pendingReviewTasks: 0,
    assignedTasks: 0,
    inProgressTasks: 0,
    urgentTasks: 0
  });

  useEffect(() => {
    if (userTasks && userOffers) {
      // Common calculations
      const pendingReviewTasks = userTasks.filter(task =>
        task.visibility === 'admin' && task.status === 'open'
      ).length;

      // Active tasks now include all tasks that are not closed (closed or pending_payment)
      const activeTasks = userTasks.filter(task =>
        !['closed', 'pending_payment'].includes(task.status)
      ).length;

      const completedTasks = userTasks.filter(task => task.status === 'completed').length;
      const assignedTasks = userTasks.filter(task => task.status === 'assigned').length;
      const inProgressTasks = userTasks.filter(task => task.status === 'in_progress').length;

      // Calculate tasks with due dates within the next 3 days
      const now = new Date();
      const threeDaysFromNow = new Date();
      threeDaysFromNow.setDate(now.getDate() + 3);

      const urgentTasks = userTasks.filter(task => {
        const dueDate = new Date(task.due_date);
        return dueDate >= now && dueDate <= threeDaysFromNow &&
               ['open', 'assigned', 'in_progress'].includes(task.status);
      }).length;

      if (isTeacher) {
        // For teachers
        const pendingOffersCount = userTasks.reduce((count, task) => count + task.offers_count, 0);

        setDashboardStats({
          activeTasks,
          offersCount: pendingOffersCount,
          completedTasks,
          acceptedJobsCount: 0,
          pendingReviewTasks,
          assignedTasks,
          inProgressTasks,
          urgentTasks
        });
      } else if (isAdmin) {
        // For admins - focus on tasks needing review and overall stats
        const pendingOffersCount = userTasks.reduce((count, task) => count + task.offers_count, 0);

        setDashboardStats({
          activeTasks,
          offersCount: pendingOffersCount,
          completedTasks,
          acceptedJobsCount: 0,
          pendingReviewTasks,
          assignedTasks,
          inProgressTasks,
          urgentTasks
        });
      } else if (isMaintenance || isSupport) {
        // For maintenance/support staff - focus on assigned tasks
        setDashboardStats({
          activeTasks: 0, // Not as relevant
          offersCount: 0, // Not relevant
          completedTasks,
          acceptedJobsCount: 0,
          pendingReviewTasks: 0,
          assignedTasks,
          inProgressTasks,
          urgentTasks
        });
      } else if (isSupplier) {
        // For suppliers
        const pendingOffersCount = userOffers.filter(offer => offer.status === 'pending').length;
        const acceptedJobsCount = userOffers.filter(offer => offer.status === 'accepted').length;
        const completedTasksCount = userOffers.filter(offer =>
          offer.status === 'accepted' &&
          offer.task_id &&
          userTasks?.find(task => task.id === offer.task_id && task.status === 'completed')
        ).length;

        setDashboardStats({
          activeTasks: 0, // Not relevant for suppliers
          offersCount: pendingOffersCount,
          completedTasks: completedTasksCount,
          acceptedJobsCount,
          pendingReviewTasks: 0,
          assignedTasks: 0,
          inProgressTasks: 0,
          urgentTasks
        });
      }
    }
  }, [userTasks, userOffers, isSchool, isSupplier, isTeacher, isAdmin, isMaintenance, isSupport]);

  if (!user) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Please log in to view your dashboard</h2>
          <Button asChild>
            <Link to="/login">Login</Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  // Prepare dashboard content

  return (
    <MainLayout>
      {processingInvitation && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg text-center max-w-md">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-classtasker-blue mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold mb-2">Processing Invitation</h3>
            <p className="text-gray-600 mb-4">
              We're setting up your account with the organization. This may take a few moments...
            </p>
          </div>
        </div>
      )}

      <div className="container mx-auto px-4 py-8">
        {/* Dashboard Header with personalized welcome and key actions */}
        <DashboardHeader
          userName={user?.user_metadata?.name || profile?.first_name}
          isTeacher={isTeacher}
          isAdmin={isAdmin}
          isMaintenance={isMaintenance}
          isSupport={isSupport}
          isSupplier={isSupplier}
          pendingReviewCount={dashboardStats.pendingReviewTasks as number}
          urgentTasksCount={dashboardStats.urgentTasks as number}
        />

        {/* Main Dashboard Content */}
        <Tabs defaultValue="overview" value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            {isTeacher || isAdmin || isSupport ? (
              <TabsTrigger value="my-tasks">My Tasks</TabsTrigger>
            ) : null}
            {isMaintenance ? (
              <TabsTrigger value="my-jobs">Assigned Tasks</TabsTrigger>
            ) : null}
            {isSupplier ? (
              <>
                <TabsTrigger value="my-jobs">My Jobs</TabsTrigger>
                <TabsTrigger value="my-offers">My Offers</TabsTrigger>
              </>
            ) : null}
            {isAdmin && !isSupplier ? (
              <TabsTrigger value="offers">Task Offers</TabsTrigger>
            ) : null}
            <TabsTrigger value="messages">Messages</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview">
            {/* Role-specific stats */}
            <DashboardStats
              activeTasks={dashboardStats.activeTasks}
              offers={dashboardStats.offersCount}
              completedTasks={dashboardStats.completedTasks}
              pendingReviewTasks={dashboardStats.pendingReviewTasks}
              assignedTasks={dashboardStats.assignedTasks}
              inProgressTasks={dashboardStats.inProgressTasks}
              urgentTasks={dashboardStats.urgentTasks}
              acceptedJobsCount={dashboardStats.acceptedJobsCount}
              isTeacher={isTeacher}
              isAdmin={isAdmin}
              isMaintenance={isMaintenance}
              isSupport={isSupport}
              isSupplier={isSupplier}
              isLoading={isLoadingUserTasks}
            />

            {/* Action items that need attention */}
            <ActionItems
              tasks={userTasks}
              isLoading={isLoadingUserTasks}
              isTeacher={isTeacher}
              isAdmin={isAdmin}
              isMaintenance={isMaintenance}
              isSupport={isSupport}
              isSupplier={isSupplier}
            />

            {/* Recent tasks/jobs */}
            <RecentTasks
              tasks={userTasks}
              isLoading={isLoadingUserTasks}
              isSchool={isSchool}
            />

            {/* Notifications */}
            <NotificationsList />
          </TabsContent>

          {/* My Tasks Tab */}
          <TabsContent value="my-tasks">
            <TasksList tasks={userTasks} isLoading={isLoadingUserTasks} />
          </TabsContent>

          {/* Other Tab Contents */}
          <TabsContent value="offers">
            <OffersSection isSchool={true} />
          </TabsContent>

          <TabsContent value="my-jobs">
            <JobsSection />
          </TabsContent>

          <TabsContent value="my-offers">
            <OffersSection isSchool={false} />
          </TabsContent>

          {/* Messages Tab - Uses GetStream for chat functionality */}
          <TabsContent value="messages">
            <GetStreamMessagesSection />
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default Dashboard;
