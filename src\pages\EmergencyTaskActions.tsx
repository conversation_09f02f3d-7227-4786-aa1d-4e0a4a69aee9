import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';

const EmergencyTaskActions = () => {
  const { id } = useParams<{ id: string }>();
  const [isLoading, setIsLoading] = useState(true);
  const [taskData, setTaskData] = useState<any>(null);
  const [userData, setUserData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    const checkDatabase = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!id) {
          throw new Error('No task ID provided');
        }

        // First, get the current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError) {
          throw new Error(`Error getting user: ${userError.message}`);
        }

        if (!user) {
          throw new Error('No user is logged in');
        }

        // Get the user's profile
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (profileError) {
          throw new Error(`Error getting profile: ${profileError.message}`);
        }

        // Get the task
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('*')
          .eq('id', id)
          .single();

        if (taskError) {
          throw new Error(`Error getting task: ${taskError.message}`);
        }

        setUserData({
          id: user.id,
          email: user.email,
          role: profileData?.role,
          isMaintenance: profileData?.role === 'maintenance'
        });

        setTaskData({
          id: taskData.id,
          title: taskData.title,
          visibility: taskData.visibility,
          status: taskData.status,
          assigned_to: taskData.assigned_to,
          isInternal: taskData.visibility === 'internal',
          isAssigned: taskData.status === 'assigned',
          isAssignedToUser: taskData.assigned_to === user.id
        });
      } catch (err: any) {
        console.error('Error in EmergencyTaskActions:', err);
        setError(err.message || 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    checkDatabase();
  }, [id]);

  const handleUpdateStatus = async (newStatus: string) => {
    try {
      setIsUpdating(true);

      if (!id) {
        throw new Error('No task ID provided');
      }

      const { error } = await supabase
        .from('tasks')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        throw error;
      }

      // Refresh the task data
      const { data: updatedTask, error: refreshError } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', id)
        .single();

      if (refreshError) {
        throw refreshError;
      }

      setTaskData({
        ...taskData,
        status: updatedTask.status,
        isAssigned: updatedTask.status === 'assigned'
      });

      alert(`Task status updated to ${newStatus}`);
    } catch (err: any) {
      console.error('Error updating task status:', err);
      setError(err.message || 'Failed to update task status');
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Emergency Task Actions</h1>

        {isLoading ? (
          <Card>
            <CardContent className="p-6 flex justify-center items-center">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            </CardContent>
          </Card>
        ) : error ? (
          <Card className="bg-red-50 border-red-200">
            <CardContent className="p-6">
              <p className="text-red-600 font-medium">Error: {error}</p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            <Card className="bg-blue-50 border-2 border-blue-300">
              <CardHeader>
                <CardTitle>Task: {taskData?.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Button
                      onClick={() => handleUpdateStatus('in_progress')}
                      className="w-full bg-blue-600 hover:bg-blue-700"
                      disabled={isUpdating}
                    >
                      {isUpdating ? (
                        <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
                      ) : (
                        'Start Work'
                      )}
                    </Button>
                    <Button
                      onClick={() => handleUpdateStatus('completed')}
                      className="w-full bg-green-600 hover:bg-green-700"
                      disabled={isUpdating}
                    >
                      {isUpdating ? (
                        <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
                      ) : (
                        'Mark Completed'
                      )}
                    </Button>
                  </div>

                  <div className="p-3 bg-white rounded shadow-sm text-xs">
                    <h4 className="font-bold mb-2">User Info:</h4>
                    <pre className="whitespace-pre-wrap">{JSON.stringify(userData, null, 2)}</pre>

                    <h4 className="font-bold mt-3 mb-2">Task Info:</h4>
                    <pre className="whitespace-pre-wrap">{JSON.stringify(taskData, null, 2)}</pre>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Debug Information</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="mb-2">This is a standalone emergency page for managing internal tasks.</p>
                <p>If you're seeing this page, it means the normal task actions aren't working correctly.</p>
                <p className="mt-4">
                  <a
                    href={`/tasks/${id}`}
                    className="text-blue-600 hover:underline"
                  >
                    Return to normal task page
                  </a>
                </p>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default EmergencyTaskActions;
