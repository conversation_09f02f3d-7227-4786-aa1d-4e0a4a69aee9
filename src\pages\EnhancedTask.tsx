/**
 * EnhancedTask Page
 *
 * This page implements the "always mount, conditionally render" pattern.
 * It serves as a unified task page for both internal and external tasks.
 */

import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { ArrowLeft, MapPin, Calendar, PoundSterling, Tag, User, Building, AlertCircle, Loader2, Image } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { format } from 'date-fns';
import { Task, isInternalTask, isExternalTask } from '@/types/tasks';
import { TaskFactory } from '@/services/taskFactory';
import TaskActions from '@/components/tasks/TaskActions';
import HorizontalTaskTimeline from '@/components/tasks/HorizontalTaskTimeline';
import StreamUIChatVercel from '@/components/tasks/StreamUIChatVercel';
import { useChatVisibility } from '@/hooks/use-chat-visibility';
import { shouldShowChatForTask } from '@/utils/chatUtils';
import { TaskImageGallery } from '@/components/tasks/TaskImageGallery';

const EnhancedTask: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user, profile, isAdmin, isSupplier } = useAuth();
  const { toast } = useToast();
  const [task, setTask] = useState<Task | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [userOffer, setUserOffer] = useState<any>(null);
  const [acceptedOffer, setAcceptedOffer] = useState<any>(null);
  const [showChatSection, setShowChatSection] = useState(false);
  // No migration-related state needed as we're always using GetStream directly
  const { isVisible: showMessages, hasMessages, showChat } = useChatVisibility(id);

  // Fetch task data
  useEffect(() => {
    const fetchTask = async () => {
      if (!id) return;

      setIsLoading(true);
      setError(null);

      try {
        // Fetch task data directly, bypassing permission checks
        console.log(`EnhancedTask: Fetching task with ID ${id}`);

        // First try to fetch the task directly from the database with organization name
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select(`
            *,
            organization:organizations(name)
          `)
          .eq('id', id)
          .maybeSingle();

        // If we have a task with an assigned_to value, fetch the assigned user's profile separately
        let assignedUserProfile = null;
        if (taskData && taskData.assigned_to) {
          const { data: profileData } = await supabase
            .from('profiles')
            .select('first_name, last_name, email')
            .eq('id', taskData.assigned_to)
            .maybeSingle();

          if (profileData) {
            assignedUserProfile = profileData;
          }
        }

        // Fetch the creator's profile
        let creatorProfile = null;
        if (taskData && taskData.user_id) {
          const { data: creatorData } = await supabase
            .from('profiles')
            .select('first_name, last_name, email')
            .eq('id', taskData.user_id)
            .maybeSingle();

          if (creatorData) {
            creatorProfile = creatorData;
          }
        }

        if (taskError) {
          console.error('Error fetching task:', taskError);
          throw new Error(taskError.message);
        }

        if (!taskData) {
          console.error(`Task with ID ${id} not found in database`);
          throw new Error('Task not found');
        }

        console.log('Task data retrieved successfully:', taskData);

        // Add the assigned user profile data to the task data if available
        if (assignedUserProfile) {
          // Create a property for the assigned user's name
          const fullName = `${assignedUserProfile.first_name || ''} ${assignedUserProfile.last_name || ''}`.trim();

          // Add it directly to the task data
          taskData.assigned_to_name = fullName;
        }

        // Add the creator profile data to the task data if available
        if (creatorProfile) {
          // Create a property for the creator's name
          const creatorName = `${creatorProfile.first_name || ''} ${creatorProfile.last_name || ''}`.trim();

          // Add it directly to the task data
          taskData.creator_name = creatorName;
        }

        // Convert to typed task using TaskFactory
        const typedTask = TaskFactory.createFromDatabase(taskData);
        setTask(typedTask);

        // Fetch user's offer if they are a supplier
        if (user && profile?.account_type === 'supplier') {
          console.log(`EnhancedTask: Fetching user offer for task ${id} and user ${user.id}`);
          const { data: offerData, error: offerError } = await supabase
            .from('offers')
            .select('*')
            .eq('task_id', id)
            .eq('user_id', user.id)
            .maybeSingle();

          if (!offerError && offerData) {
            setUserOffer(offerData);
          }
        }

        // Fetch accepted offer if there is one
        console.log(`EnhancedTask: Fetching accepted offer for task ${id}`);
        const { data: acceptedOfferData, error: acceptedOfferError } = await supabase
          .from('offers')
          .select('*')
          .eq('task_id', id)
          .eq('status', 'accepted')
          .maybeSingle();

        if (!acceptedOfferError && acceptedOfferData) {
          setAcceptedOffer(acceptedOfferData);
        }

        // Determine if chat should be shown
        setShowChatSection(shouldShowChatForTask(taskData, user?.id) || hasMessages);

      } catch (err: any) {
        console.error('Error fetching task:', err);
        setError(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTask();
  }, [id, user, profile?.account_type, hasMessages]);

  // No migration function needed as we're using GetStream directly

  // Handle task update
  const handleTaskUpdated = () => {
    queryClient.invalidateQueries({ queryKey: ['task', id] });
    // Refetch the task data
    const fetchTask = async () => {
      if (!id) return;

      try {
        console.log(`EnhancedTask: Refetching task with ID ${id}`);

        // Fetch task data directly, bypassing permission checks
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select(`
            *,
            organization:organizations(name)
          `)
          .eq('id', id)
          .maybeSingle();

        // If we have a task with an assigned_to value, fetch the assigned user's profile separately
        let assignedUserProfile = null;
        if (taskData && taskData.assigned_to) {
          const { data: profileData } = await supabase
            .from('profiles')
            .select('first_name, last_name, email')
            .eq('id', taskData.assigned_to)
            .maybeSingle();

          if (profileData) {
            assignedUserProfile = profileData;
          }
        }

        // Fetch the creator's profile
        let creatorProfile = null;
        if (taskData && taskData.user_id) {
          const { data: creatorData } = await supabase
            .from('profiles')
            .select('first_name, last_name, email')
            .eq('id', taskData.user_id)
            .maybeSingle();

          if (creatorData) {
            creatorProfile = creatorData;
          }
        }

        if (taskError) {
          console.error('Error refetching task:', taskError);
          throw new Error(taskError.message);
        }

        if (!taskData) {
          console.error(`Task with ID ${id} not found during refetch`);
          throw new Error('Task not found');
        }

        console.log('Task data refetched successfully:', taskData);

        // Add the assigned user profile data to the task data if available
        if (assignedUserProfile) {
          // Create a property for the assigned user's name
          const fullName = `${assignedUserProfile.first_name || ''} ${assignedUserProfile.last_name || ''}`.trim();

          // Add it directly to the task data
          taskData.assigned_to_name = fullName;
        }

        // Add the creator profile data to the task data if available
        if (creatorProfile) {
          // Create a property for the creator's name
          const creatorName = `${creatorProfile.first_name || ''} ${creatorProfile.last_name || ''}`.trim();

          // Add it directly to the task data
          taskData.creator_name = creatorName;
        }

        // Convert to typed task using TaskFactory
        const typedTask = TaskFactory.createFromDatabase(taskData);
        setTask(typedTask);
      } catch (err: any) {
        console.error('Error refetching task:', err);
      }
    };

    fetchTask();
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link
            to={isSupplier ? "/tasks" : "/dashboard?tab=my-tasks"}
            className="text-gray-600 hover:text-blue-600 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            {isSupplier ? "Back to Tasks" : "Back to My Tasks"}
          </Link>
        </div>
        <div className="mb-6">
          <Skeleton className="h-10 w-3/4" />
          <div className="flex items-center mt-2">
            <Skeleton className="h-6 w-32 mr-2" />
            <Skeleton className="h-6 w-32" />
          </div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Card className="mb-6">
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-24 w-full" />
              </CardContent>
            </Card>
            <Skeleton className="h-64 w-full mb-6" />
            <Skeleton className="h-64 w-full" />
          </div>
          <div>
            <Skeleton className="h-64 w-full mb-6" />
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link
            to={isSupplier ? "/tasks" : "/dashboard?tab=my-tasks"}
            className="text-gray-600 hover:text-blue-600 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            {isSupplier ? "Back to Tasks" : "Back to My Tasks"}
          </Link>
        </div>
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load task: {error.message}
          </AlertDescription>
        </Alert>
        <Button onClick={() => navigate(isSupplier ? '/tasks' : '/dashboard?tab=my-tasks')}>
          {isSupplier ? "Return to Tasks" : "Return to My Tasks"}
        </Button>
      </div>
    );
  }

  // Render no task state
  if (!task) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link
            to={isSupplier ? "/tasks" : "/dashboard?tab=my-tasks"}
            className="text-gray-600 hover:text-blue-600 flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            {isSupplier ? "Back to Tasks" : "Back to My Tasks"}
          </Link>
        </div>
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Task Not Found</AlertTitle>
          <AlertDescription>
            The requested task could not be found.
          </AlertDescription>
        </Alert>
        <Button onClick={() => navigate(isSupplier ? '/tasks' : '/dashboard?tab=my-tasks')}>
          {isSupplier ? "Return to Tasks" : "Return to My Tasks"}
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Link
          to={isSupplier ? "/tasks" : "/dashboard?tab=my-tasks"}
          className="text-gray-600 hover:text-blue-600 flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          {isSupplier ? "Back to Tasks" : "Back to My Tasks"}
        </Link>
      </div>

      {/* Task Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">{task.title}</h1>
          <div className="flex items-center">
            <Badge
              variant="outline"
              className={
                isInternalTask(task)
                  ? 'bg-blue-100 text-blue-800 border-blue-200 mr-2'
                  : task.type === 'pending'
                  ? 'bg-purple-100 text-purple-800 border-purple-200 mr-2'
                  : 'bg-amber-100 text-amber-800 border-amber-200 mr-2'
              }
            >
              {isInternalTask(task)
                ? 'Internal'
                : task.type === 'pending'
                ? 'Pending Review'
                : 'External'}
            </Badge>
            <Badge
              variant="outline"
              className={
                task.status === 'open'
                  ? 'bg-green-100 text-green-800 border-green-200'
                  : task.status === 'in_progress'
                  ? 'bg-blue-100 text-blue-800 border-blue-200'
                  : task.status === 'completed'
                  ? 'bg-purple-100 text-purple-800 border-purple-200'
                  : task.status === 'confirmed'
                  ? 'bg-indigo-100 text-indigo-800 border-indigo-200'
                  : 'bg-gray-100 text-gray-800 border-gray-200'
              }
            >
              {task.status.charAt(0).toUpperCase() + task.status.slice(1).replace('_', ' ')}
            </Badge>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          {/* Task Details */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Task Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p>{task.description}</p>

                {task.images && task.images.length > 0 && (
                  <div className="mb-4">
                    <TaskImageGallery images={task.images} />
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 mr-2 text-gray-500" />
                    <span>{task.location_formatted || task.location}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2 text-gray-500" />
                    <span>Due: {format(new Date(task.due_date), 'PPP')}</span>
                  </div>
                  {isExternalTask(task) && (
                    <div className="flex items-center">
                      <PoundSterling className="h-5 w-5 mr-2 text-gray-500" />
                      <span>Budget: £{task.budget.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="flex items-center">
                    <Tag className="h-5 w-5 mr-2 text-gray-500" />
                    <span>Category: {task.category}</span>
                  </div>

                  {/* Task Creator Information */}
                  {task.creator_name && (
                    <div className="flex items-center">
                      <User className="h-5 w-5 mr-2 text-gray-500" />
                      <span>Created By: {task.creator_name}</span>
                    </div>
                  )}

                  {/* Task Assigned To Information */}
                  {task.assigned_to_name && (
                    <div className="flex items-center">
                      <User className="h-5 w-5 mr-2 text-gray-500" />
                      <span>Assigned To: {task.assigned_to_name}</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Task Timeline */}
          <HorizontalTaskTimeline
            task={task}
            isLoading={false}
            error={null}
          />

          {/* Task Actions */}
          <TaskActions
            task={task}
            isLoading={false}
            error={null}
            userOffer={userOffer}
            acceptedOffer={acceptedOffer}
            onTaskUpdated={handleTaskUpdated}
          />

          {/* Task Chat */}
          {showChatSection && (
            <Card className="mb-6">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Task Communication</CardTitle>
                    <CardDescription>
                      Communicate with {isInternalTask(task) ? 'staff' : 'suppliers'} about this task
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div>
                  <StreamUIChatVercel
                    taskId={task.id}
                    taskTitle={task.title || 'Task Chat'}
                    onSystemMessage={(message) => {
                      console.log('System message sent:', message);
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <div>
          {/* Task Sidebar */}
          {/* Task Information card removed to avoid duplication */}
        </div>
      </div>
    </div>
  );
};

export default EnhancedTask;
