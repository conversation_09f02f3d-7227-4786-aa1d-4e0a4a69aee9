import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar, MapPin, PoundSterling, Tag, Clock, User, ArrowLeft, Loader2, AlertCircle, MessageSquare } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useTasks } from '@/hooks/use-tasks';
import { useOffers } from '@/hooks/use-offers';
import { useFixedOffers } from '@/hooks/fixed-use-offers'; // Import the fixed version
import { useAuth } from '@/contexts/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useQueryClient } from '@tanstack/react-query';
import GetStreamTaskChat from '@/components/tasks/GetStreamTaskChat';
import SupplierActions from '@/components/tasks/SupplierActions';
import OfferCard from '@/components/tasks/OfferCard';
import TaskOwnerOfferActions from '@/components/tasks/TaskOwnerOfferActions';
import TaskCompletionActions from '@/components/tasks/TaskCompletionActions';
import TaskPaymentActions from '@/components/tasks/TaskPaymentActions';
import TaskDetailTimeline from '@/components/tasks/TaskDetailTimeline';
import systemMessageService from '@/services/systemMessageService';
import type { Task as TaskType, Offer } from '@/services/taskService';
import { useChatVisibility } from '@/hooks/use-chat-visibility';
import { supabase } from '@/integrations/supabase/client';

const normalizeStatus = (status: string): string => {
  if (status === 'pending') return 'awaiting';
  return status;
};

/**
 * Fixed version of the Task page with improved error handling
 */
const FixedTaskPage = () => {
  const { id } = useParams<{ id: string }>();
  const [offerAmount, setOfferAmount] = useState('');
  const [offerMessage, setOfferMessage] = useState('');
  const { isVisible: showMessages, hasMessages, isChecking, showChat } = useChatVisibility(id);
  const messagesRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user, isSupplier, isSchool } = useAuth();
  const queryClient = useQueryClient();

  const { data: task, isLoading: isLoadingTask, error: taskError } = useTasks().getTask(id || '');

  // Use the fixed version of useOffers
  const fixedOffers = useFixedOffers();
  const { data: offers, isLoading: isLoadingOffers, refetch: refetchOffers } = fixedOffers.getOffersForTask(id || '');

  const { isAdmin, isTeacher, isSupport, isMaintenance, profile } = useAuth();
  const isTaskOwner = user && task && user.id === task.user_id;

  // Log profile and role information for debugging
  console.log('FixedTask - Profile (source of truth):', profile);
  console.log('FixedTask - Database role:', profile?.role);
  console.log('FixedTask - Is teacher (from database):', isTeacher);
  console.log('FixedTask - Is admin (from database):', isAdmin);
  console.log('FixedTask - Is support (from database):', isSupport);
  console.log('FixedTask - Is maintenance (from database):', isMaintenance);
  console.log('Task ownership check:', {
    userId: user?.id,
    taskUserId: task?.user_id,
    isTaskOwner,
    isAdmin,
    taskId: id,
    databaseRole: profile?.role, // Use database role, not metadata
    isSchool
  });

  const userOffer = offers?.find(offer => user && offer.user_id === user.id);
  const hasSubmittedOffer = !!userOffer;

  const { createOffer, isCreatingOffer } = fixedOffers;

  const { acceptOffer, isAcceptingOffer, updateOfferStatus, isUpdatingOfferStatus } = fixedOffers;

  // Track if this is the first load
  const isFirstLoad = useRef(true);

  useEffect(() => {
    if (id) {
      console.log("[FIXED] Task page loaded for task ID:", id);
      refetchOffers();
      queryClient.invalidateQueries({ queryKey: ['task', id] });

      // Set up real-time subscription for task updates
      const taskChannel = supabase
        .channel(`fixed_task_${id}`)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'tasks',
            filter: `id=eq.${id}`
          },
          (payload) => {
            console.log('[FixedTask] Task updated:', payload);

            // Refresh task data
            queryClient.invalidateQueries({ queryKey: ['task', id] });

            // If visibility changed, show a toast
            if (payload.new && payload.old && payload.new.visibility !== payload.old.visibility) {
              toast({
                title: "Task Visibility Updated",
                description: `Task is now ${payload.new.visibility === 'public' ? 'visible to suppliers' : 'internal only'}`,
                duration: 3000
              });
            }
          }
        )
        .subscribe();

      // Clean up subscription when component unmounts
      return () => {
        supabase.removeChannel(taskChannel);
      };

      // Send a system message on first load if the task is new
      if (isFirstLoad.current && task) {
        isFirstLoad.current = false;

        // Check if the task was created recently (within the last hour)
        const createdAt = new Date(task.created_at);
        const now = new Date();
        const timeDiff = now.getTime() - createdAt.getTime();
        const isNewTask = timeDiff < 60 * 60 * 1000; // 1 hour in milliseconds

        // Only send the message for new tasks
        if (isNewTask && task.status === 'open') {
          // Check if a system message already exists for this task
          const checkForExistingMessage = async () => {
            try {
              const { data: existingMessages } = await supabase
                .from('task_messages')
                .select('id')
                .eq('task_id', id)
                .eq('sender_id', '00000000-0000-0000-0000-000000000000')
                .eq('content', 'Task created and is now open for offers.')
                .limit(1);

              // Only create a message if none exists
              if (!existingMessages || existingMessages.length === 0) {
                console.log('No existing system message found, creating one');
                // No thread ID needed for initial task creation message
                systemMessageService.createStatusChangeMessage(id, 'open');
              } else {
                console.log('Existing system message found, skipping creation');
              }
            } catch (error) {
              console.error('Error checking for existing system messages:', error);
            }
          };

          checkForExistingMessage();
        }
      }
    }
  }, [id, refetchOffers, queryClient, task]);

  // Debug offers data
  useEffect(() => {
    if (offers) {
      console.log('Offers data:', offers);
    }
  }, [offers]);

  useEffect(() => {
    if (taskError) {
      console.error("[FIXED] Task error:", taskError);
      toast({
        variant: "destructive",
        title: "Error loading task",
        description: "The task could not be found or you don't have permission to view it.",
      });
      navigate('/tasks');
    }
  }, [taskError, toast, navigate]);

  const handleRequestInfo = () => {
    // Show chat using our hook
    showChat();

    // Scroll to messages
    setTimeout(() => {
      messagesRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const handleSubmitOffer = (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast({
        variant: "destructive",
        title: "Authentication required",
        description: "Please log in to submit an offer.",
      });
      return;
    }

    if (!isSupplier) {
      toast({
        variant: "destructive",
        title: "Permission denied",
        description: "Only suppliers can submit offers on tasks.",
      });
      return;
    }

    if (!offerAmount || !offerMessage) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please provide both an amount and a message for your offer.",
      });
      return;
    }

    if (!id) return;

    createOffer({
      task_id: id,
      amount: parseFloat(offerAmount),
      message: offerMessage,
    }, {
      onSuccess: () => {
        setOfferAmount('');
        setOfferMessage('');
      }
    });
  };

  const handleAcceptOffer = (offerId: string) => {
    if (!id) {
      console.error("[FIXED] Cannot accept offer - missing task ID");
      return;
    }

    if (!user || !isTaskOwner) {
      console.warn("[FIXED] Permission denied - user is not task owner", {
        userId: user?.id,
        taskOwnerId: task?.user_id,
        isTaskOwner
      });
      toast({
        variant: "destructive",
        title: "Permission denied",
        description: "Only the task creator can accept offers.",
      });
      return;
    }

    // Check if task is in the correct state
    if (task?.status !== 'open' && task?.status !== 'offer') {
      console.warn("[FIXED] Cannot accept offer - task is not in 'open' or 'offer' status", {
        taskStatus: task?.status
      });
      toast({
        variant: "destructive",
        title: "Cannot accept offer",
        description: "This task is not in the correct state to accept offers.",
      });
      return;
    }

    console.log(`[FIXED] Attempting to accept offer: ${offerId} for task: ${id}`);
    console.log("[FIXED] User status:", { userId: user?.id, isSchool, isTaskOwner });

    // Show a loading toast
    toast({
      title: "Processing",
      description: "Accepting offer, please wait...",
    });

    acceptOffer(
      {
        taskId: id,
        offerId: offerId
      },
      {
        onSuccess: (result) => {
          console.log("[FIXED] Offer acceptance result:", result);

          if (result) {
            console.log("[FIXED] Offer accepted successfully via callback!");
            toast({
              title: "Success",
              description: "Offer accepted successfully. The task has been assigned to the supplier.",
            });

            // Send system message about the assignment
            const acceptedOffer = offers?.find(o => o.id === offerId);
            if (acceptedOffer) {
              // Get thread ID if available
              const getThreadId = async () => {
                try {
                  const { data: threadData } = await supabase
                    .from('chat_threads')
                    .select('id')
                    .eq('task_id', id)
                    .eq('supplier_id', acceptedOffer.user_id)
                    .maybeSingle();

                  return threadData?.id;
                } catch (error) {
                  console.error('Error fetching thread ID:', error);
                  return undefined;
                }
              };

              // Get thread ID and send system message
              getThreadId().then(threadId => {
                systemMessageService.createStatusChangeMessage(id, 'assigned', acceptedOffer.user_id, threadId);
              });
            }
          } else {
            console.error("[FIXED] Offer acceptance returned false");
            toast({
              variant: "destructive",
              title: "Error accepting offer",
              description: "There was an error accepting the offer. Please try again.",
            });
          }

          // Refresh data regardless of result
          setTimeout(() => {
            console.log("[FIXED] Refreshing data after offer acceptance attempt");
            refetchOffers();
            queryClient.invalidateQueries({ queryKey: ['task', id] });
          }, 500);
        },
        onError: (error) => {
          console.error("[FIXED] Error accepting offer via callback:", error);
          toast({
            variant: "destructive",
            title: "Error accepting offer",
            description: "There was an error accepting the offer. Please try again.",
          });
        }
      }
    );
  };

  const handleRejectOffer = (offerId: string) => {
    if (!id || !user || !isTaskOwner) {
      console.warn("[FIXED] Permission denied for rejecting offer", {
        userId: user?.id,
        taskOwnerId: task?.user_id,
        isTaskOwner
      });
      toast({
        variant: "destructive",
        title: "Permission denied",
        description: "Only the task creator can reject offers.",
      });
      return;
    }

    console.log(`[FIXED] Attempting to reject offer: ${offerId} for task: ${id}`);

    updateOfferStatus(
      offerId,
      'rejected',
      {
        onSuccess: () => {
          console.log("[FIXED] Offer rejected successfully via callback!");
          toast({
            title: "Offer rejected",
            description: "The offer has been rejected successfully."
          });
          setTimeout(() => {
            console.log("[FIXED] Refreshing data after successful offer rejection");
            refetchOffers();
            queryClient.invalidateQueries({ queryKey: ['task', id] });
          }, 500);
        },
        onError: (error) => {
          console.error("[FIXED] Error rejecting offer via callback:", error);
        }
      }
    );
  };

  if (isLoadingTask) {
    return (
      <MainLayout>
        <div className="container mx-auto py-8">
          <div className="flex items-center mb-6">
            <Button variant="ghost" onClick={() => navigate(-1)} className="mr-4">
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </Button>
            <Skeleton className="h-8 w-1/3" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-6">
              <Skeleton className="h-64 w-full" />
              <Skeleton className="h-40 w-full" />
            </div>
            <div className="space-y-6">
              <Skeleton className="h-40 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!task) {
    return (
      <MainLayout>
        <div className="container mx-auto py-8">
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Task not found</AlertTitle>
            <AlertDescription>
              The task you're looking for doesn't exist or you don't have permission to view it.
            </AlertDescription>
          </Alert>
          <Button onClick={() => navigate('/tasks')}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Tasks
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-8">
        {/* Header with back button and task title */}
        <div className="flex items-center mb-6">
          <Button variant="ghost" onClick={() => navigate(-1)} className="mr-4">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back
          </Button>
          <div className="flex-1">
            <h1 className="text-2xl font-bold">{task.title}</h1>
            <div className="flex items-center mt-1">
              <Badge
                variant="outline"
                className={
                  `${task?.status === 'open' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                    task?.status === 'assigned' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                    task?.status === 'in_progress' ? 'bg-indigo-100 text-indigo-800 border-indigo-200' :
                    task?.status === 'completed' ? 'bg-purple-100 text-purple-800 border-purple-200' :
                    task?.status === 'confirmed' ? 'bg-green-100 text-green-800 border-green-200' :
                    'bg-orange-100 text-orange-800 border-orange-200'}`
                }
              >
                {task?.status === 'in_progress' ? 'In Progress' :
                 task?.status === 'pending_payment' ? 'Payment Required' :
                 task?.status.charAt(0).toUpperCase() + task?.status.slice(1)}
              </Badge>
              {task.visibility && (
                <Badge variant="outline" className="ml-2 bg-gray-100 text-gray-800 border-gray-200">
                  {task.visibility.charAt(0).toUpperCase() + task.visibility.slice(1)}
                </Badge>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main content column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Task details card */}
            <Card>
              <CardContent className="p-6">
                {/* Task description */}
                <div className="mb-6">
                  <h2 className="text-xl font-semibold mb-2">Description</h2>
                  <p className="text-gray-700 whitespace-pre-line">{task.description}</p>
                </div>

                <Separator className="my-4" />

                {/* Task details grid */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div className="flex items-start">
                    <MapPin className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                    <div>
                      <h3 className="font-medium">Location</h3>
                      <p className="text-gray-700">{task.location}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <PoundSterling className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                    <div>
                      <h3 className="font-medium">Budget</h3>
                      <p className="text-gray-700">£{Number(task.budget).toFixed(2)}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Tag className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                    <div>
                      <h3 className="font-medium">Category</h3>
                      <p className="text-gray-700">{task.category}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Calendar className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                    <div>
                      <h3 className="font-medium">Due Date</h3>
                      <p className="text-gray-700">{format(new Date(task.due_date), 'PP')}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Clock className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                    <div>
                      <h3 className="font-medium">Posted</h3>
                      <p className="text-gray-700">{format(new Date(task.created_at), 'PP')}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <User className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                    <div>
                      <h3 className="font-medium">Posted by</h3>
                      <Link to={`/profile/${task?.user_id}`} className="text-gray-700 hover:text-classtasker-blue">
                        School Account
                      </Link>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Task timeline */}
            <TaskDetailTimeline
              status={task?.status}
              offersCount={offers?.length || 0}
              createdAt={task?.created_at}
              updatedAt={task?.updated_at}
              assignedTo={task?.assigned_to}
              assignedToName={task?.assigned_to ? "Assigned Staff" : undefined}
              visibility={task?.visibility}
            />

            {/* Offers section - only show if there are offers */}
            {offers && offers.length > 0 && (
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Offers ({offers.length})</h2>
                  <div className="space-y-4">
                    {offers.map(offer => (
                      <OfferCard
                        key={offer.id}
                        offer={offer}
                        task={task}
                        onRequestInfo={handleRequestInfo}
                        showMessages={showMessages}
                      />
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Messages section - conditionally shown */}
            {(showMessages || task?.status === 'assigned' || (offers && offers.length > 0)) && (
              <div ref={messagesRef}>
                <GetStreamTaskChat taskId={id || ''} taskOwnerId={task?.user_id || ''} />
              </div>
            )}
          </div>

          {/* Sidebar column */}
          <div className="space-y-6">
            {/* Action cards based on user role and task status */}

            {/* Admin offer actions */}
            {isAdmin && offers && offers.length > 0 && (
              <TaskOwnerOfferActions task={task} offers={offers} />
            )}

            {/* Supplier actions */}
            {(task?.status === 'open' || task?.status === 'assigned' || userOffer) && isSupplier && (
              <SupplierActions
                task={task}
                existingOffer={userOffer}
                showRequestInfo={showMessages}
                onRequestInfo={handleRequestInfo}
              />
            )}

            {/* Task owner/admin actions for task progression */}
            {isTaskOwner && (
              <TaskCompletionActions
                task={task}
                acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
                onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
              />
            )}

            {/* Payment actions */}
            {(isTaskOwner || isAdmin) && (
              <TaskPaymentActions
                task={task}
                acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
                onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
              />
            )}

            {/* Note for teachers who created the task but can't manage offers */}
            {isTaskOwner && !isAdmin && offers && offers.some(offer => offer.status === 'awaiting') && (
              <Card className="border-blue-300">
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-2 text-blue-700">Pending Offers</h3>
                  <p className="text-sm text-gray-600">
                    This task has pending offers from service providers. An administrator will review and manage these offers.
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Messaging buttons - consolidated */}
            {!showMessages && !hasMessages && !isChecking && (
              <>
                {/* For task owners (except teachers) */}
                {isTaskOwner && !isTeacher && (task?.status === 'assigned' || (offers && offers.length > 0)) && (
                  <Button
                    className="w-full"
                    onClick={handleRequestInfo}
                  >
                    <MessageSquare className="mr-2 h-4 w-4" /> Message Supplier
                  </Button>
                )}

                {/* For teachers */}
                {isTaskOwner && isTeacher && (task?.status === 'assigned' || (offers && offers.length > 0)) && (
                  <Card className="border-blue-200">
                    <CardContent className="p-4">
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Note:</span> Communication with service providers is handled by administrators.
                      </p>
                    </CardContent>
                  </Card>
                )}

                {/* For suppliers with offers */}
                {isSupplier && userOffer && (
                  <Button
                    className="w-full"
                    onClick={handleRequestInfo}
                  >
                    <MessageSquare className="mr-2 h-4 w-4" /> Message School
                  </Button>
                )}

                {/* For suppliers without offers */}
                {isSupplier && task?.status === 'open' && !userOffer && (
                  <Button
                    className="w-full"
                    onClick={handleRequestInfo}
                  >
                    <MessageSquare className="mr-2 h-4 w-4" /> Ask a Question
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default FixedTaskPage;
