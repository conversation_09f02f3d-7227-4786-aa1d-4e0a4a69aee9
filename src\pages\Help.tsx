
import React, { useState, useEffect, useRef } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import SupportRequestForm from '@/components/support/SupportRequestForm';
import {
  Search,
  BookOpen,
  FileText,
  Users,
  Settings,
  CreditCard,
  HelpCircle,
  School,
  Briefcase,
  UserPlus,
  MessageSquare,
  Star,
  PoundSterling,
  Shield,
  Bell,
  User,
  Building,
  CheckCircle,
  AlertCircle,
  Info,
  ArrowUp,
  ChevronRight,
  Clock,
  Calendar,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Wrench,
  Paintbrush,
  Hammer,
  Lightbulb,
  Laptop,
  Trash,
  Download,
  Upload,
  Printer,
  Image,
  X,
  Smartphone,
  BellRing,
  FileEdit
} from 'lucide-react';

const Help = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeSection, setActiveSection] = useState<string | null>(null);

  // Handle hash changes to show the appropriate content
  useEffect(() => {
    // Get the current hash from the URL
    const hash = window.location.hash.substring(1);
    if (hash) {
      setActiveSection(hash);
      // Scroll to the content section
      const element = document.getElementById(`content-${hash}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }

    // Listen for hash changes
    const handleHashChange = () => {
      const newHash = window.location.hash.substring(1);
      if (newHash) {
        setActiveSection(newHash);
        // Scroll to the content section
        const element = document.getElementById(`content-${newHash}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      } else {
        setActiveSection(null);
      }
    };

    window.addEventListener('hashchange', handleHashChange);

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, []);

  // Function to close the active section
  const closeActiveSection = () => {
    setActiveSection(null);
    // Remove the hash from the URL without refreshing the page
    history.pushState('', document.title, window.location.pathname + window.location.search);
  };

  return (
    <MainLayout>
      <div className="container mx-auto py-12 px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Classtasker Help Centre</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Find answers to common questions and learn how to get the most out of Classtasker
          </p>
        </div>

        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative">
            <Input
              placeholder="Search for help topics..."
              className="pl-10 py-6 text-lg"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          </div>
        </div>

        <Tabs defaultValue="categories" className="max-w-5xl mx-auto">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="categories">Help Categories</TabsTrigger>
            <TabsTrigger value="faq">Frequently Asked Questions</TabsTrigger>
          </TabsList>

          <TabsContent value="categories">
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {/* Getting Started */}
              <Card className="hover:shadow-lg transition-shadow border-t-4 border-t-blue-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center mb-2">
                    <div className="bg-blue-100 p-2 rounded-full mr-3">
                      <BookOpen className="h-6 w-6 text-blue-600" />
                    </div>
                    <CardTitle>Getting Started</CardTitle>
                  </div>
                  <CardDescription>
                    Everything you need to know to begin using ClassTasker
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <UserPlus className="h-4 w-4 mr-2 text-blue-500" />
                      <a href="#creating-account" className="text-blue-600 hover:underline">Creating an account</a>
                    </li>
                    <li className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-blue-500" />
                      <a href="#posting-task" className="text-blue-600 hover:underline">Posting your first task</a>
                    </li>
                    <li className="flex items-center">
                      <Users className="h-4 w-4 mr-2 text-blue-500" />
                      <a href="#finding-provider" className="text-blue-600 hover:underline">Finding the right provider</a>
                    </li>
                    <li className="flex items-center">
                      <CreditCard className="h-4 w-4 mr-2 text-blue-500" />
                      <a href="#payments-work" className="text-blue-600 hover:underline">How payments work</a>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* For Schools */}
              <Card className="hover:shadow-lg transition-shadow border-t-4 border-t-green-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center mb-2">
                    <div className="bg-green-100 p-2 rounded-full mr-3">
                      <School className="h-6 w-6 text-green-600" />
                    </div>
                    <CardTitle>For Schools</CardTitle>
                  </div>
                  <CardDescription>
                    Guidance for schools and educational institutions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-green-500" />
                      <a href="#task-description" className="text-blue-600 hover:underline">Writing effective task descriptions</a>
                    </li>
                    <li className="flex items-center">
                      <PoundSterling className="h-4 w-4 mr-2 text-green-500" />
                      <a href="#budget-setting" className="text-blue-600 hover:underline">Setting appropriate budgets</a>
                    </li>
                    <li className="flex items-center">
                      <MessageSquare className="h-4 w-4 mr-2 text-green-500" />
                      <a href="#provider-communication" className="text-blue-600 hover:underline">Communicating with providers</a>
                    </li>
                    <li className="flex items-center">
                      <Star className="h-4 w-4 mr-2 text-green-500" />
                      <a href="#reviewing-work" className="text-blue-600 hover:underline">Reviewing completed work</a>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* For Service Providers */}
              <Card className="hover:shadow-lg transition-shadow border-t-4 border-t-purple-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center mb-2">
                    <div className="bg-purple-100 p-2 rounded-full mr-3">
                      <Briefcase className="h-6 w-6 text-purple-600" />
                    </div>
                    <CardTitle>For Service Providers</CardTitle>
                  </div>
                  <CardDescription>
                    Tips and guides for maintenance and service providers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-purple-500" />
                      <a href="#profile-creation" className="text-blue-600 hover:underline">Creating an attractive profile</a>
                    </li>
                    <li className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-purple-500" />
                      <a href="#finding-tasks" className="text-blue-600 hover:underline">Finding tasks to complete</a>
                    </li>
                    <li className="flex items-center">
                      <Star className="h-4 w-4 mr-2 text-purple-500" />
                      <a href="#positive-reviews" className="text-blue-600 hover:underline">Getting positive reviews</a>
                    </li>
                    <li className="flex items-center">
                      <PoundSterling className="h-4 w-4 mr-2 text-purple-500" />
                      <a href="#getting-paid" className="text-blue-600 hover:underline">Getting paid for your work</a>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* Account & Settings */}
              <Card className="hover:shadow-lg transition-shadow border-t-4 border-t-orange-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center mb-2">
                    <div className="bg-orange-100 p-2 rounded-full mr-3">
                      <Settings className="h-6 w-6 text-orange-600" />
                    </div>
                    <CardTitle>Account & Settings</CardTitle>
                  </div>
                  <CardDescription>
                    Manage your account preferences and settings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-orange-500" />
                      <a href="#profile-management" className="text-blue-600 hover:underline">Managing your profile</a>
                    </li>
                    <li className="flex items-center">
                      <Shield className="h-4 w-4 mr-2 text-orange-500" />
                      <a href="#privacy-settings" className="text-blue-600 hover:underline">Privacy settings</a>
                    </li>
                    <li className="flex items-center">
                      <Bell className="h-4 w-4 mr-2 text-orange-500" />
                      <a href="#notification-preferences" className="text-blue-600 hover:underline">Notification preferences</a>
                    </li>
                    <li className="flex items-center">
                      <Settings className="h-4 w-4 mr-2 text-orange-500" />
                      <a href="#account-details" className="text-blue-600 hover:underline">Changing account details</a>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* Payments */}
              <Card className="hover:shadow-lg transition-shadow border-t-4 border-t-red-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center mb-2">
                    <div className="bg-red-100 p-2 rounded-full mr-3">
                      <CreditCard className="h-6 w-6 text-red-600" />
                    </div>
                    <CardTitle>Payments</CardTitle>
                  </div>
                  <CardDescription>
                    Everything about payments, invoices, and billing
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <CreditCard className="h-4 w-4 mr-2 text-red-500" />
                      <a href="#payment-methods" className="text-blue-600 hover:underline">Payment methods</a>
                    </li>
                    <li className="flex items-center">
                      <PoundSterling className="h-4 w-4 mr-2 text-red-500" />
                      <a href="#transaction-fees" className="text-blue-600 hover:underline">Transaction fees</a>
                    </li>
                    <li className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-red-500" />
                      <a href="#refund-policy" className="text-blue-600 hover:underline">Refund policy</a>
                    </li>
                    <li className="flex items-center">
                      <Shield className="h-4 w-4 mr-2 text-red-500" />
                      <a href="#payment-security" className="text-blue-600 hover:underline">Payment security</a>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* Organizations */}
              <Card className="hover:shadow-lg transition-shadow border-t-4 border-t-indigo-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center mb-2">
                    <div className="bg-indigo-100 p-2 rounded-full mr-3">
                      <Building className="h-6 w-6 text-indigo-600" />
                    </div>
                    <CardTitle>Organisations</CardTitle>
                  </div>
                  <CardDescription>
                    Managing Multi-Academy Trusts and school organisations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <Building className="h-4 w-4 mr-2 text-indigo-500" />
                      <a href="#organization-setup" className="text-blue-600 hover:underline">Setting up your organisation</a>
                    </li>
                    <li className="flex items-center">
                      <Users className="h-4 w-4 mr-2 text-indigo-500" />
                      <a href="#managing-members" className="text-blue-600 hover:underline">Managing members</a>
                    </li>
                    <li className="flex items-center">
                      <Settings className="h-4 w-4 mr-2 text-indigo-500" />
                      <a href="#organization-settings" className="text-blue-600 hover:underline">Organisation settings</a>
                    </li>
                    <li className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-indigo-500" />
                      <a href="#organisation-billing" className="text-blue-600 hover:underline">Billing and invoices</a>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            <div className="mt-12 text-center">
              <Card className="max-w-2xl mx-auto border-t-4 border-t-teal-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-center mb-2">
                    <div className="bg-teal-100 p-2 rounded-full mr-3">
                      <HelpCircle className="h-6 w-6 text-teal-600" />
                    </div>
                    <CardTitle>Need More Help?</CardTitle>
                  </div>
                  <CardDescription>
                    Can't find what you're looking for? Our support team is here to help.
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-4">
                  <Button
                    className="px-8 py-6 text-lg"
                    onClick={() => setActiveSection('support-request')}
                  >
                    Contact Support
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="faq">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">Frequently Asked Questions</CardTitle>
                <CardDescription>
                  Find quick answers to the most common questions about ClassTasker
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="item-1">
                    <AccordionTrigger>What is ClassTasker?</AccordionTrigger>
                    <AccordionContent>
                      <p className="mb-2">
                        ClassTasker is a platform that connects schools and educational institutions with qualified maintenance and service providers.
                      </p>
                      <p>
                        Our platform makes it easy for schools to post tasks, find reliable providers, and manage payments securely. For service providers,
                        ClassTasker offers opportunities to find work with educational institutions and build a portfolio of satisfied clients.
                      </p>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-2">
                    <AccordionTrigger>How do I create an account?</AccordionTrigger>
                    <AccordionContent>
                      <p className="mb-2">
                        Creating an account on ClassTasker is simple:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2">
                        <li>Click on the "Sign Up" button in the top right corner of the homepage</li>
                        <li>Enter your email address and create a password</li>
                        <li>Select your account type (School or Service Provider)</li>
                        <li>Complete your profile with the required information</li>
                        <li>Verify your email address by clicking the link sent to your inbox</li>
                      </ol>
                      <p className="mt-2">
                        Once these steps are completed, you can start using ClassTasker right away!
                      </p>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-3">
                    <AccordionTrigger>How does payment work?</AccordionTrigger>
                    <AccordionContent>
                      <p className="mb-2">
                        ClassTasker uses a secure payment system to handle transactions between schools and service providers:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2">
                        <li>Schools set a budget when posting a task</li>
                        <li>Service providers can submit offers within or below that budget</li>
                        <li>When a school accepts an offer, the payment is held securely by ClassTasker</li>
                        <li>Once the task is completed and approved, the payment is released to the service provider</li>
                        <li>Schools can pay using various methods including credit/debit cards, BACS, Apple Pay, and Google Pay</li>
                      </ol>
                      <p className="mt-2">
                        This system ensures that service providers get paid for their work and schools only pay for satisfactory completion of tasks.
                      </p>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-4">
                    <AccordionTrigger>What types of tasks can be posted?</AccordionTrigger>
                    <AccordionContent>
                      <p className="mb-2">
                        Schools can post a wide variety of maintenance and service tasks on ClassTasker, including:
                      </p>
                      <ul className="list-disc pl-5 space-y-1">
                        <li>Building maintenance and repairs</li>
                        <li>Plumbing and electrical work</li>
                        <li>Grounds maintenance and landscaping</li>
                        <li>IT support and equipment installation</li>
                        <li>Cleaning services</li>
                        <li>Painting and decorating</li>
                        <li>Furniture assembly and installation</li>
                        <li>Security system installation and maintenance</li>
                      </ul>
                      <p className="mt-2">
                        If you're unsure whether your task is suitable for ClassTasker, please contact our support team for guidance.
                      </p>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-5">
                    <AccordionTrigger>How do I manage my organization?</AccordionTrigger>
                    <AccordionContent>
                      <p className="mb-2">
                        Organization admins can manage their school or Multi-Academy Trust through the Organization Dashboard:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2">
                        <li>Access the Organization Dashboard from the dropdown menu</li>
                        <li>Add or update organization details including name, address, and contact information</li>
                        <li>Invite staff members to join your organization with appropriate roles</li>
                        <li>Manage tasks posted by your organization</li>
                        <li>View and download invoices for completed tasks</li>
                        <li>Update payment methods and billing information</li>
                      </ol>
                      <p className="mt-2">
                        Organization admins have access to all tasks and financial information related to their organization.
                      </p>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-6">
                    <AccordionTrigger>Is my information secure?</AccordionTrigger>
                    <AccordionContent>
                      <p className="mb-2">
                        Yes, ClassTasker takes data security and privacy very seriously:
                      </p>
                      <ul className="list-disc pl-5 space-y-1">
                        <li>All personal and financial data is encrypted using industry-standard protocols</li>
                        <li>We implement strict access controls and Row Level Security in our database</li>
                        <li>Payment processing is handled by Stripe, a PCI-compliant payment processor</li>
                        <li>We never store complete credit card information on our servers</li>
                        <li>Our platform is regularly tested for security vulnerabilities</li>
                        <li>We comply with all relevant data protection regulations</li>
                      </ul>
                      <p className="mt-2">
                        If you have specific security concerns, please contact our support team for more information.
                      </p>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Content Display Section */}
        {activeSection && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 overflow-y-auto">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="sticky top-0 bg-white p-4 border-b flex justify-between items-center">
                <h2 className="text-2xl font-bold" id={`content-${activeSection}`}>
                  {activeSection === 'creating-account' && 'Creating an Account'}
                  {activeSection === 'posting-task' && 'Posting Your First Task'}
                  {activeSection === 'finding-provider' && 'Finding the Right Provider'}
                  {activeSection === 'payments-work' && 'How Payments Work'}

                  {activeSection === 'task-description' && 'Writing Effective Task Descriptions'}
                  {activeSection === 'budget-setting' && 'Setting Appropriate Budgets'}
                  {activeSection === 'provider-communication' && 'Communicating with Providers'}
                  {activeSection === 'reviewing-work' && 'Reviewing Completed Work'}

                  {activeSection === 'profile-creation' && 'Creating an Attractive Profile'}
                  {activeSection === 'finding-tasks' && 'Finding Tasks to Complete'}
                  {activeSection === 'positive-reviews' && 'Getting Positive Reviews'}
                  {activeSection === 'getting-paid' && 'Getting Paid for Your Work'}

                  {activeSection === 'profile-management' && 'Managing Your Profile'}
                  {activeSection === 'privacy-settings' && 'Privacy Settings'}
                  {activeSection === 'notification-preferences' && 'Notification Preferences'}
                  {activeSection === 'account-details' && 'Changing Account Details'}

                  {activeSection === 'payment-methods' && 'Payment Methods'}
                  {activeSection === 'transaction-fees' && 'Transaction Fees'}
                  {activeSection === 'refund-policy' && 'Refund Policy'}
                  {activeSection === 'payment-security' && 'Payment Security'}

                  {activeSection === 'organization-setup' && 'Setting Up Your Organization'}
                  {activeSection === 'managing-members' && 'Managing Members'}
                  {activeSection === 'organization-settings' && 'Organization Settings'}
                  {activeSection === 'organization-billing' && 'Billing and Invoices'}

                  {activeSection === 'support-request' && 'Contact Support'}
                </h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={closeActiveSection}
                  aria-label="Close"
                >
                  <X className="h-6 w-6" />
                </Button>
              </div>
              <div className="p-6">
                {activeSection === 'creating-account' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-center mb-6">
                      <img
                        src="https://images.unsplash.com/photo-**********-fc759fdf7a8d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                        alt="Creating an account"
                        className="rounded-lg shadow-md max-w-full h-auto max-h-80"
                      />
                    </div>

                    <p className="text-lg">
                      Creating an account on ClassTasker is quick and straightforward. Follow these simple steps to get started:
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Step-by-Step Guide</h3>
                      <ol className="list-decimal pl-5 space-y-4">
                        <li className="text-gray-800">
                          <span className="font-medium">Visit the ClassTasker website</span>
                          <p className="mt-1 text-gray-600">
                            Navigate to the ClassTasker homepage and click the "Sign Up" button in the top right corner.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Enter your email address</span>
                          <p className="mt-1 text-gray-600">
                            Provide a valid email address that you have access to, as you'll need to verify it later.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Create a secure password</span>
                          <p className="mt-1 text-gray-600">
                            Choose a strong password with at least 8 characters, including uppercase letters, lowercase letters, numbers, and special characters.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Select your account type</span>
                          <p className="mt-1 text-gray-600">
                            Choose whether you're creating an account as a school/educational institution or as a service provider.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Complete your profile</span>
                          <p className="mt-1 text-gray-600">
                            Fill in your personal or organisation details, including your name, contact information, and any relevant professional details.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Verify your email</span>
                          <p className="mt-1 text-gray-600">
                            Check your inbox for a verification email from ClassTasker and click the link to verify your account.
                          </p>
                        </li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Account Types</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">School/Educational Institution</h4>
                          <p className="mt-1 text-gray-600">
                            This account type is for schools, colleges, universities, and Multi-Academy Trusts. You'll be able to post tasks, hire service providers, and manage your organisation's maintenance needs.
                          </p>
                        </div>
                        <div>
                          <h4 className="font-medium text-green-800">Service Provider</h4>
                          <p className="mt-1 text-gray-600">
                            This account type is for maintenance professionals, contractors, and service companies. You'll be able to find tasks, submit offers, and build your portfolio of work with educational institutions.
                          </p>
                        </div>
                      </div>
                    </div>

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Important</AlertTitle>
                      <AlertDescription>
                        Make sure to use a professional email address, especially if you're creating an account for a school or service business. This helps establish credibility with potential clients or service providers.
                      </AlertDescription>
                    </Alert>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Need More Help?</h3>
                      <p>
                        If you're having trouble creating an account, please contact our support team at <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a> or call us on <span className="font-medium">0800 123 4567</span>.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'posting-task' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-center mb-6">
                      <img
                        src="https://images.unsplash.com/photo-*************-6ca0a78fb36b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                        alt="Posting a task"
                        className="rounded-lg shadow-md max-w-full h-auto max-h-80"
                      />
                    </div>

                    <p className="text-lg">
                      Posting your first task on ClassTasker is simple. Follow these steps to get your maintenance or service needs addressed quickly and efficiently.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Step-by-Step Guide</h3>
                      <ol className="list-decimal pl-5 space-y-4">
                        <li className="text-gray-800">
                          <span className="font-medium">Log in to your ClassTasker account</span>
                          <p className="mt-1 text-gray-600">
                            Sign in with your school or educational institution account credentials.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Click on "Post a Task"</span>
                          <p className="mt-1 text-gray-600">
                            Find the "Post a Task" button in the navigation menu or on your dashboard.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Select a category</span>
                          <p className="mt-1 text-gray-600">
                            Choose the most appropriate category for your task (e.g., Plumbing, Electrical, Painting, IT Support).
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Write a clear title</span>
                          <p className="mt-1 text-gray-600">
                            Create a concise, descriptive title that summarises the task (e.g., "Classroom Ceiling Light Replacement").
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Provide a detailed description</span>
                          <p className="mt-1 text-gray-600">
                            Describe the task in detail, including specific requirements, dimensions, materials needed, and any other relevant information.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Set your budget</span>
                          <p className="mt-1 text-gray-600">
                            Specify how much you're willing to pay for the task. You can set a fixed price or a price range (e.g., £100-£150).
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Specify the location</span>
                          <p className="mt-1 text-gray-600">
                            Enter the address or location where the task needs to be performed.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Set a due date</span>
                          <p className="mt-1 text-gray-600">
                            Indicate when you need the task to be completed by.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Add photos or attachments (optional)</span>
                          <p className="mt-1 text-gray-600">
                            Upload images or documents that help illustrate the task or provide additional information.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Review and submit</span>
                          <p className="mt-1 text-gray-600">
                            Double-check all the information and click "Post Task" to publish it.
                          </p>
                        </li>
                      </ol>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Tips for Effective Task Posting</h3>
                      <ul className="list-disc pl-5 space-y-2">
                        <li className="text-gray-800">
                          <span className="font-medium">Be specific and detailed</span> - The more information you provide, the more accurate the offers you'll receive.
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Include measurements</span> - For tasks involving physical dimensions, always include precise measurements.
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Add photos</span> - Visual references help service providers understand the task better.
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Set a realistic budget</span> - Research typical costs for similar work to set an appropriate budget.
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Specify access requirements</span> - Mention any special access considerations (e.g., school hours, security procedures).
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Include safety requirements</span> - Mention any specific safety protocols that need to be followed.
                        </li>
                      </ul>
                    </div>

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Important</AlertTitle>
                      <AlertDescription>
                        For tasks that require specific qualifications or certifications (e.g., electrical work, gas fitting), make sure to mention these requirements in your task description. ClassTasker verifies service provider credentials, but it's always good to specify your requirements.
                      </AlertDescription>
                    </Alert>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">What Happens Next?</h3>
                      <p className="mb-2">
                        After posting your task, qualified service providers will be able to view it and submit offers. You'll receive notifications when offers are made, and you can review each provider's profile, ratings, and offer before accepting.
                      </p>
                      <p>
                        Once you accept an offer, you'll be able to communicate directly with the service provider to coordinate the details and schedule the work.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'finding-provider' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-center mb-6">
                      <img
                        src="https://images.unsplash.com/photo-1521791136064-7986c2920216?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                        alt="Finding the right provider"
                        className="rounded-lg shadow-md max-w-full h-auto max-h-80"
                      />
                    </div>

                    <p className="text-lg">
                      Finding the right service provider for your school's maintenance and service needs is crucial. ClassTasker makes it easy to find qualified, reliable professionals who can complete your tasks to a high standard.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">How to Find and Select Providers</h3>
                      <ol className="list-decimal pl-5 space-y-4">
                        <li className="text-gray-800">
                          <span className="font-medium">Post a detailed task</span>
                          <p className="mt-1 text-gray-600">
                            Start by creating a comprehensive task description with all necessary details. This helps attract the right providers.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Review incoming offers</span>
                          <p className="mt-1 text-gray-600">
                            Once your task is posted, service providers will submit offers. You'll receive notifications for each new offer.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Check provider profiles</span>
                          <p className="mt-1 text-gray-600">
                            Click on the provider's name to view their full profile, including ratings, reviews, qualifications, and previous work examples.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Evaluate ratings and reviews</span>
                          <p className="mt-1 text-gray-600">
                            Look at the provider's overall rating and read reviews from other schools and educational institutions.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Verify qualifications</span>
                          <p className="mt-1 text-gray-600">
                            Check that the provider has the necessary qualifications and certifications for your specific task.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Ask questions</span>
                          <p className="mt-1 text-gray-600">
                            Use the messaging system to ask providers any questions you have about their experience, approach, or offer.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Compare offers</span>
                          <p className="mt-1 text-gray-600">
                            Consider price, estimated completion time, and the provider's approach to the task when comparing offers.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Accept the best offer</span>
                          <p className="mt-1 text-gray-600">
                            Once you've found the right provider, accept their offer to begin the process.
                          </p>
                        </li>
                      </ol>
                    </div>

                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h3 className="text-xl font-semibold mb-3 text-purple-800">What to Look for in a Provider</h3>
                      <ul className="list-disc pl-5 space-y-2">
                        <li className="text-gray-800">
                          <span className="font-medium">Relevant experience</span> - Providers who have completed similar tasks for other educational institutions.
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Positive reviews</span> - High ratings and positive feedback from previous clients.
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Proper qualifications</span> - Verified certifications and qualifications relevant to the task.
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Clear communication</span> - Providers who respond promptly and communicate clearly.
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Detailed offers</span> - Offers that address all aspects of your task and provide a clear breakdown of costs.
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Professionalism</span> - A professional approach to their work and interactions.
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Insurance coverage</span> - Appropriate insurance for the type of work being performed.
                        </li>
                      </ul>
                    </div>

                    <Alert className="bg-yellow-50 border-yellow-200">
                      <AlertCircle className="h-4 w-4 text-yellow-800" />
                      <AlertTitle className="text-yellow-800">Provider Verification</AlertTitle>
                      <AlertDescription className="text-yellow-800">
                        ClassTasker verifies the identity and qualifications of all service providers on our platform. Look for the "Verified" badge on provider profiles, which indicates that we've confirmed their identity, qualifications, and insurance coverage.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Building Relationships with Providers</h3>
                      <p className="mb-3 text-gray-700">
                        Many schools find it beneficial to build ongoing relationships with reliable service providers. Once you've found providers who deliver quality work, you can:
                      </p>
                      <ul className="list-disc pl-5 space-y-2">
                        <li className="text-gray-800">
                          Add them to your "Preferred Providers" list for easy access in the future
                        </li>
                        <li className="text-gray-800">
                          Invite them directly to bid on new tasks
                        </li>
                        <li className="text-gray-800">
                          Set up recurring maintenance tasks with providers you trust
                        </li>
                        <li className="text-gray-800">
                          Provide detailed feedback and ratings to help them improve their service
                        </li>
                      </ul>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Safety and Security</h3>
                      <p className="mb-2">
                        All service providers working in educational settings must comply with safeguarding requirements. ClassTasker ensures that providers have:
                      </p>
                      <ul className="list-disc pl-5 space-y-1 mb-3">
                        <li>Valid DBS (Disclosure and Barring Service) checks</li>
                        <li>Appropriate insurance coverage</li>
                        <li>Verified qualifications for their area of expertise</li>
                      </ul>
                      <p>
                        You can request to see these documents directly through the ClassTasker messaging system before accepting an offer.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'payments-work' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-center mb-6">
                      <img
                        src="https://images.unsplash.com/photo-1580048915913-4f8f5cb481c4?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                        alt="How payments work"
                        className="rounded-lg shadow-md max-w-full h-auto max-h-80"
                      />
                    </div>

                    <p className="text-lg">
                      ClassTasker offers a secure, transparent payment system designed specifically for educational institutions. Our payment process protects both schools and service providers, ensuring fair transactions and quality work.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">The Payment Process</h3>
                      <ol className="list-decimal pl-5 space-y-4">
                        <li className="text-gray-800">
                          <span className="font-medium">Setting a budget</span>
                          <p className="mt-1 text-gray-600">
                            When posting a task, you'll set a budget indicating how much you're willing to pay. This can be a fixed amount or a range (e.g., £100-£150).
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Receiving offers</span>
                          <p className="mt-1 text-gray-600">
                            Service providers will submit offers based on your budget. Each offer will include the price they're proposing for the task.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Accepting an offer</span>
                          <p className="mt-1 text-gray-600">
                            When you accept an offer, you'll be prompted to make a payment. This payment is held securely by ClassTasker (known as an escrow system).
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Task completion</span>
                          <p className="mt-1 text-gray-600">
                            The service provider completes the task according to the agreed terms and timeline.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Approving the work</span>
                          <p className="mt-1 text-gray-600">
                            Once the task is completed, you'll review the work. If satisfied, you approve the task as complete.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Payment release</span>
                          <p className="mt-1 text-gray-600">
                            After you approve the work, the payment is released to the service provider. They typically receive the funds within 3-5 business days.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Invoice generation</span>
                          <p className="mt-1 text-gray-600">
                            An invoice is automatically generated for your records and financial reporting.
                          </p>
                        </li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Payment Methods</h3>
                      <p className="mb-3 text-gray-700">
                        ClassTasker accepts various payment methods to accommodate the needs of educational institutions:
                      </p>
                      <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <li className="bg-white p-3 rounded shadow-sm">
                          <div className="flex items-center">
                            <CreditCard className="h-5 w-5 text-green-600 mr-2" />
                            <span className="font-medium">Credit/Debit Cards</span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            All major cards accepted (Visa, Mastercard, American Express)
                          </p>
                        </li>
                        <li className="bg-white p-3 rounded shadow-sm">
                          <div className="flex items-center">
                            <Building className="h-5 w-5 text-green-600 mr-2" />
                            <span className="font-medium">BACS Transfer</span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            Direct bank transfers for schools and MATs
                          </p>
                        </li>
                        <li className="bg-white p-3 rounded shadow-sm">
                          <div className="flex items-center">
                            <Laptop className="h-5 w-5 text-green-600 mr-2" />
                            <span className="font-medium">Apple Pay</span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            Quick and secure payments from Apple devices
                          </p>
                        </li>
                        <li className="bg-white p-3 rounded shadow-sm">
                          <div className="flex items-center">
                            <Smartphone className="h-5 w-5 text-green-600 mr-2" />
                            <span className="font-medium">Google Pay</span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            Fast payments from Android devices and Google accounts
                          </p>
                        </li>
                      </ul>
                    </div>

                    <Alert className="bg-yellow-50 border-yellow-200">
                      <AlertCircle className="h-4 w-4 text-yellow-800" />
                      <AlertTitle className="text-yellow-800">Payment Protection</AlertTitle>
                      <AlertDescription className="text-yellow-800">
                        Our escrow system holds your payment securely until you confirm that the work has been completed satisfactorily. This protects you from paying for unsatisfactory work and gives service providers confidence that they will be paid for completed tasks.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h3 className="text-xl font-semibold mb-3 text-purple-800">Fees and Pricing</h3>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-medium text-purple-800">For Schools and Educational Institutions</h4>
                          <p className="mt-1 text-gray-600">
                            Schools pay the agreed price for the task plus a small service fee of 5% to cover payment processing and platform maintenance.
                          </p>
                        </div>
                        <div>
                          <h4 className="font-medium text-purple-800">For Service Providers</h4>
                          <p className="mt-1 text-gray-600">
                            Service providers pay a commission of 10% on each completed task. This fee is automatically deducted from the payment they receive.
                          </p>
                        </div>
                        <div>
                          <h4 className="font-medium text-purple-800">Example</h4>
                          <p className="mt-1 text-gray-600">
                            For a £100 task: The school pays £105 (£100 + 5% fee). The service provider receives £90 (£100 - 10% commission).
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Invoicing and Financial Records</h3>
                      <p className="mb-3">
                        ClassTasker automatically generates detailed invoices for all transactions. These invoices include:
                      </p>
                      <ul className="list-disc pl-5 space-y-1 mb-3">
                        <li>Task details and description</li>
                        <li>Service provider information</li>
                        <li>Itemised costs and fees</li>
                        <li>Payment date and method</li>
                        <li>VAT information (where applicable)</li>
                      </ul>
                      <p>
                        All invoices are accessible through your account dashboard and can be downloaded as PDF files for your financial records.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'task-description' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-center mb-6">
                      <img
                        src="https://images.unsplash.com/photo-*************-c639042777db?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                        alt="Writing effective task descriptions"
                        className="rounded-lg shadow-md max-w-full h-auto max-h-80"
                      />
                    </div>

                    <p className="text-lg">
                      A well-written task description is essential for attracting the right service providers and ensuring your maintenance or service needs are met accurately. Follow these guidelines to create effective task descriptions that get results.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Elements of an Effective Task Description</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-blue-800">Clear, Specific Title</h4>
                          <p className="mt-1 text-gray-600">
                            Start with a concise title that clearly states what needs to be done. Be specific rather than general.
                          </p>
                          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="bg-green-50 p-2 rounded border border-green-100">
                              <p className="text-sm font-medium text-green-800">Good Example:</p>
                              <p className="text-sm">"Replace 6 Fluorescent Light Fixtures in Science Lab"</p>
                            </div>
                            <div className="bg-red-50 p-2 rounded border border-red-100">
                              <p className="text-sm font-medium text-red-800">Poor Example:</p>
                              <p className="text-sm">"Lighting Work Needed"</p>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">Detailed Description</h4>
                          <p className="mt-1 text-gray-600">
                            Provide a thorough description of what needs to be done, including specific requirements, dimensions, materials, and any other relevant details.
                          </p>
                          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="bg-green-50 p-2 rounded border border-green-100">
                              <p className="text-sm font-medium text-green-800">Good Example:</p>
                              <p className="text-sm">"We need to replace 6 old fluorescent light fixtures (120cm x 30cm) with new LED panel lights in our main science laboratory. The current fixtures are ceiling-mounted with standard wiring. The room has a suspended ceiling. Work must be completed outside of school hours."</p>
                            </div>
                            <div className="bg-red-50 p-2 rounded border border-red-100">
                              <p className="text-sm font-medium text-red-800">Poor Example:</p>
                              <p className="text-sm">"Need to replace some lights in the science room. They're not working well."</p>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">Specific Measurements and Quantities</h4>
                          <p className="mt-1 text-gray-600">
                            Include precise measurements, quantities, and specifications where applicable.
                          </p>
                          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="bg-green-50 p-2 rounded border border-green-100">
                              <p className="text-sm font-medium text-green-800">Good Example:</p>
                              <p className="text-sm">"The classroom wall to be painted is 8m x 3m. We need two coats of washable matte paint in Dulux 'Goose Down' colour."</p>
                            </div>
                            <div className="bg-red-50 p-2 rounded border border-red-100">
                              <p className="text-sm font-medium text-red-800">Poor Example:</p>
                              <p className="text-sm">"Need a wall painted in a light colour."</p>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">Timeline and Scheduling Information</h4>
                          <p className="mt-1 text-gray-600">
                            Clearly state when the task needs to be completed and any scheduling constraints.
                          </p>
                          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="bg-green-50 p-2 rounded border border-green-100">
                              <p className="text-sm font-medium text-green-800">Good Example:</p>
                              <p className="text-sm">"This work must be completed during the half-term break (15-19 February). Access to the building is available between 8am and 4pm on these days."</p>
                            </div>
                            <div className="bg-red-50 p-2 rounded border border-red-100">
                              <p className="text-sm font-medium text-red-800">Poor Example:</p>
                              <p className="text-sm">"Need this done soon when the room isn't being used."</p>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">Access and Security Information</h4>
                          <p className="mt-1 text-gray-600">
                            Include details about access to the site, security procedures, and any special requirements.
                          </p>
                          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="bg-green-50 p-2 rounded border border-green-100">
                              <p className="text-sm font-medium text-green-800">Good Example:</p>
                              <p className="text-sm">"All visitors must report to the main reception upon arrival. You'll need to sign in and receive a visitor badge. Please bring photo ID. Parking is available in the staff car park."</p>
                            </div>
                            <div className="bg-red-50 p-2 rounded border border-red-100">
                              <p className="text-sm font-medium text-red-800">Poor Example:</p>
                              <p className="text-sm">"Come to the school when you're ready to start."</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Task Description Checklist</h3>
                      <ul className="space-y-2">
                        <li className="flex items-start">
                          <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span>Clear, specific title that accurately describes the task</span>
                        </li>
                        <li className="flex items-start">
                          <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span>Detailed description of what needs to be done</span>
                        </li>
                        <li className="flex items-start">
                          <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span>Specific measurements, quantities, and specifications</span>
                        </li>
                        <li className="flex items-start">
                          <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span>Timeline and scheduling information</span>
                        </li>
                        <li className="flex items-start">
                          <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span>Access and security information</span>
                        </li>
                        <li className="flex items-start">
                          <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span>Any required qualifications or certifications</span>
                        </li>
                        <li className="flex items-start">
                          <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span>Photos or diagrams to illustrate the task (when applicable)</span>
                        </li>
                        <li className="flex items-start">
                          <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span>Safety requirements or considerations</span>
                        </li>
                        <li className="flex items-start">
                          <CheckCircle className="h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span>Budget range or fixed price</span>
                        </li>
                      </ul>
                    </div>

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Important</AlertTitle>
                      <AlertDescription>
                        For tasks requiring specific qualifications (e.g., electrical work, gas fitting), always specify these requirements in your task description. This ensures that only appropriately qualified providers will submit offers.
                      </AlertDescription>
                    </Alert>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Adding Photos and Attachments</h3>
                      <p className="mb-3">
                        Visual information can significantly improve the clarity of your task description. Consider adding:
                      </p>
                      <ul className="list-disc pl-5 space-y-1 mb-3">
                        <li>Photos of the area or item that needs work</li>
                        <li>Diagrams or floor plans</li>
                        <li>Examples of the desired outcome</li>
                        <li>Technical specifications or manufacturer documentation</li>
                      </ul>
                      <p>
                        You can upload photos and documents when creating your task. Simply click the "Add Photos" or "Add Attachments" button in the task creation form.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'budget-setting' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-center mb-6">
                      <img
                        src="https://images.unsplash.com/photo-**********-6726b3ff858f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                        alt="Setting appropriate budgets"
                        className="rounded-lg shadow-md max-w-full h-auto max-h-80"
                      />
                    </div>

                    <p className="text-lg">
                      Setting an appropriate budget for your maintenance and service tasks is crucial for attracting qualified providers and ensuring value for money. This guide will help you establish realistic budgets for your school's tasks.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">How to Set an Appropriate Budget</h3>
                      <ol className="list-decimal pl-5 space-y-4">
                        <li className="text-gray-800">
                          <span className="font-medium">Research market rates</span>
                          <p className="mt-1 text-gray-600">
                            Before setting a budget, research the typical costs for similar services in your area. You can:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1">
                            <li>Look at similar completed tasks on ClassTasker</li>
                            <li>Contact local suppliers for rough estimates</li>
                            <li>Consult with other schools in your network</li>
                            <li>Check industry pricing guides for common maintenance tasks</li>
                          </ul>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Consider the complexity and scope</span>
                          <p className="mt-1 text-gray-600">
                            Assess how complex the task is and how much time it's likely to take. More complex tasks or those requiring specialised skills will naturally command higher budgets.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Factor in materials and equipment</span>
                          <p className="mt-1 text-gray-600">
                            Determine whether the service provider will need to supply materials or equipment. If so, research the cost of these items and include them in your budget.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Account for qualifications and certifications</span>
                          <p className="mt-1 text-gray-600">
                            Tasks requiring certified professionals (e.g., electricians, gas engineers) will typically cost more than general maintenance tasks.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Consider timing and urgency</span>
                          <p className="mt-1 text-gray-600">
                            Urgent tasks or those requiring work outside normal hours may require a higher budget to attract providers.
                          </p>
                        </li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Budget Types</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Fixed Price Budget</h4>
                          <p className="mt-1 text-gray-600">
                            A fixed price budget specifies exactly how much you're willing to pay for the task. This is suitable when:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1">
                            <li>You have a precise understanding of what the task involves</li>
                            <li>You've received quotes or estimates previously</li>
                            <li>The task is straightforward with minimal variables</li>
                            <li>You have a strict budget that cannot be exceeded</li>
                          </ul>
                          <p className="mt-2 text-gray-600">
                            Example: "£250 for replacing a classroom door"
                          </p>
                        </div>
                        <div>
                          <h4 className="font-medium text-green-800">Budget Range</h4>
                          <p className="mt-1 text-gray-600">
                            A budget range provides minimum and maximum amounts you're willing to pay. This is suitable when:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1">
                            <li>The task has some variables that might affect the final price</li>
                            <li>You're open to different quality levels or approaches</li>
                            <li>You want to see a range of offers from different providers</li>
                          </ul>
                          <p className="mt-2 text-gray-600">
                            Example: "£400-£600 for repainting the school reception area"
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Common Budget Mistakes to Avoid</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Setting budgets too low</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Unrealistically low budgets will either attract no offers or lead to poor quality work. Qualified professionals know their worth and will avoid underpriced tasks.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Not accounting for materials</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Forgetting to factor in the cost of materials can lead to budget shortfalls. Be clear about whether your budget includes materials or if they'll be provided separately.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Ignoring regional price differences</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Service costs can vary significantly by region. What's appropriate in one area might be too high or too low in another.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Setting arbitrary budgets</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Setting budgets without research or based solely on what you'd like to pay rather than market rates can lead to unsuccessful task postings.
                            </p>
                          </div>
                        </li>
                      </ul>
                    </div>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Budget Flexibility</AlertTitle>
                      <AlertDescription>
                        Remember that your budget is a starting point for offers. Service providers may submit offers above or below your budget based on their assessment of the task. You can always negotiate with providers before accepting an offer.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h3 className="text-xl font-semibold mb-3 text-purple-800">Sample Budget Guidelines</h3>
                      <p className="mb-3 text-gray-700">
                        Here are some general guidelines for common school maintenance tasks (prices may vary by region and specific requirements):
                      </p>
                      <div className="overflow-x-auto">
                        <table className="min-w-full bg-white border border-purple-100">
                          <thead>
                            <tr className="bg-purple-100">
                              <th className="py-2 px-4 border-b text-left">Task Type</th>
                              <th className="py-2 px-4 border-b text-left">Typical Budget Range</th>
                              <th className="py-2 px-4 border-b text-left">Factors Affecting Price</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td className="py-2 px-4 border-b">Classroom Painting</td>
                              <td className="py-2 px-4 border-b">£300-£600 per classroom</td>
                              <td className="py-2 px-4 border-b">Size, wall condition, paint quality, furniture moving</td>
                            </tr>
                            <tr>
                              <td className="py-2 px-4 border-b">Plumbing Repairs</td>
                              <td className="py-2 px-4 border-b">£80-£250</td>
                              <td className="py-2 px-4 border-b">Complexity, parts required, emergency timing</td>
                            </tr>
                            <tr>
                              <td className="py-2 px-4 border-b">Electrical Work</td>
                              <td className="py-2 px-4 border-b">£100-£500</td>
                              <td className="py-2 px-4 border-b">Certification requirements, complexity, materials</td>
                            </tr>
                            <tr>
                              <td className="py-2 px-4 border-b">Grounds Maintenance</td>
                              <td className="py-2 px-4 border-b">£150-£400</td>
                              <td className="py-2 px-4 border-b">Area size, type of work, equipment needed</td>
                            </tr>
                            <tr>
                              <td className="py-2 px-4 border-b">Furniture Assembly</td>
                              <td className="py-2 px-4 border-b">£20-£40 per hour</td>
                              <td className="py-2 px-4 border-b">Quantity, complexity, timing</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}

                {activeSection === 'provider-communication' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-center mb-6">
                      <img
                        src="https://images.unsplash.com/photo-1573497491765-dccce02b29df?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                        alt="Communicating with providers"
                        className="rounded-lg shadow-md max-w-full h-auto max-h-80"
                      />
                    </div>

                    <p className="text-lg">
                      Effective communication with service providers is essential for successful task completion. Clear, professional communication helps ensure that expectations are understood, questions are answered promptly, and any issues are resolved efficiently.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">When to Communicate with Providers</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <MessageSquare className="h-5 w-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Before accepting an offer</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Ask questions about the provider's approach, timeline, materials, or any other aspects of their offer that need clarification.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <MessageSquare className="h-5 w-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">After accepting an offer</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Coordinate details such as access arrangements, specific timing, and any preparation needed before the provider arrives.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <MessageSquare className="h-5 w-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">During task execution</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Address any questions or issues that arise while the work is being performed, or request progress updates for longer tasks.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <MessageSquare className="h-5 w-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">After task completion</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Provide feedback, ask any follow-up questions, or discuss potential future work.
                            </p>
                          </div>
                        </li>
                      </ul>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Communication Best Practices</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Be Clear and Specific</h4>
                          <p className="mt-1 text-gray-600">
                            Provide clear, detailed information and ask specific questions. Avoid vague requests or instructions that could be misinterpreted.
                          </p>
                          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="bg-white p-2 rounded border border-green-100">
                              <p className="text-sm font-medium text-green-800">Good Example:</p>
                              <p className="text-sm">"Can you confirm whether you'll need access to the electrical panel in the caretaker's office? If so, I'll ensure someone is available to unlock it."</p>
                            </div>
                            <div className="bg-white p-2 rounded border border-red-100">
                              <p className="text-sm font-medium text-red-800">Poor Example:</p>
                              <p className="text-sm">"Will you need access to anything else?"</p>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Respond Promptly</h4>
                          <p className="mt-1 text-gray-600">
                            Respond to messages from providers in a timely manner. Delayed responses can lead to scheduling issues or misunderstandings.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Keep Communication on the Platform</h4>
                          <p className="mt-1 text-gray-600">
                            Conduct all communication through the ClassTasker messaging system. This ensures that there's a record of all discussions and helps protect both parties.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Be Professional and Respectful</h4>
                          <p className="mt-1 text-gray-600">
                            Maintain a professional tone in all communications. Treat providers with respect and courtesy, even if issues arise.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Document Important Details</h4>
                          <p className="mt-1 text-gray-600">
                            Confirm key details in writing, such as agreed changes to the scope of work, additional costs, or timeline adjustments.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Handling Common Communication Scenarios</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-yellow-800">Requesting Additional Information</h4>
                          <p className="mt-1 text-gray-600">
                            If a provider needs more information about your task, provide detailed responses and any relevant documents or photos that might help clarify the requirements.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Discussing Changes to the Task</h4>
                          <p className="mt-1 text-gray-600">
                            If the scope of work needs to change after an offer has been accepted, discuss this clearly with the provider. Agree on any adjustments to the price or timeline before proceeding with the changes.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Addressing Delays or Issues</h4>
                          <p className="mt-1 text-gray-600">
                            If a provider encounters delays or issues, work collaboratively to find solutions. Be understanding but clear about your school's needs and constraints.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Providing Access Instructions</h4>
                          <p className="mt-1 text-gray-600">
                            Be detailed when explaining how the provider can access your school premises. Include information about parking, entry points, sign-in procedures, and any security measures.
                          </p>
                        </div>
                      </div>
                    </div>

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Important</AlertTitle>
                      <AlertDescription>
                        Never share sensitive information such as personal contact details, payment information, or security codes through the messaging system. ClassTasker's platform is designed to protect your privacy while facilitating effective communication.
                      </AlertDescription>
                    </Alert>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Using the Messaging System</h3>
                      <p className="mb-3">
                        ClassTasker's messaging system offers several features to enhance communication:
                      </p>
                      <ul className="list-disc pl-5 space-y-2 mb-3">
                        <li>
                          <span className="font-medium">File and Image Sharing</span> - Upload photos, documents, or other files directly in your messages
                        </li>
                        <li>
                          <span className="font-medium">Message Notifications</span> - Receive email and in-app notifications when you receive new messages
                        </li>
                        <li>
                          <span className="font-medium">Message History</span> - Access a complete history of your conversations with each provider
                        </li>
                        <li>
                          <span className="font-medium">Read Receipts</span> - See when your messages have been read by the provider
                        </li>
                      </ul>
                      <p>
                        To access your messages, click on the "Messages" tab in your dashboard or navigate to the specific task and click on the "Messages" button.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'reviewing-work' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-center mb-6">
                      <img
                        src="https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                        alt="Reviewing completed work"
                        className="rounded-lg shadow-md max-w-full h-auto max-h-80"
                      />
                    </div>

                    <p className="text-lg">
                      Properly reviewing completed work is a crucial step in the task process. It ensures that the work meets your requirements and expectations before finalising payment. This guide will help you conduct thorough, fair reviews of completed tasks.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">The Review Process</h3>
                      <ol className="list-decimal pl-5 space-y-4">
                        <li className="text-gray-800">
                          <span className="font-medium">Notification of completion</span>
                          <p className="mt-1 text-gray-600">
                            When a service provider marks a task as complete, you'll receive a notification. This is your cue to review the work.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Inspect the work</span>
                          <p className="mt-1 text-gray-600">
                            Physically inspect the completed work, comparing it against the original task description and any subsequent agreements made through the messaging system.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Document the results</span>
                          <p className="mt-1 text-gray-600">
                            Take photos of the completed work for your records, especially for tasks that involve physical changes or installations.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Communicate with the provider</span>
                          <p className="mt-1 text-gray-600">
                            If you have questions or concerns about the work, communicate these to the provider through the ClassTasker messaging system.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Approve or request changes</span>
                          <p className="mt-1 text-gray-600">
                            If the work meets your requirements, approve it through the platform. If changes are needed, request these through the messaging system.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Release payment</span>
                          <p className="mt-1 text-gray-600">
                            Once you're satisfied with the work, approve the task as complete. This will release the payment to the service provider.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Leave a review</span>
                          <p className="mt-1 text-gray-600">
                            Provide a fair and detailed review of the service provider's work, which helps maintain quality standards on the platform.
                          </p>
                        </li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">What to Look for When Reviewing Work</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Completeness</h4>
                          <p className="mt-1 text-gray-600">
                            Has all the work specified in the task description been completed? Check each element against your original requirements.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Quality</h4>
                          <p className="mt-1 text-gray-600">
                            Is the work of acceptable quality? Consider factors such as finish, durability, and attention to detail.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Functionality</h4>
                          <p className="mt-1 text-gray-600">
                            Does everything work as expected? Test any repaired or installed items to ensure they function properly.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Cleanliness</h4>
                          <p className="mt-1 text-gray-600">
                            Has the work area been left clean and tidy? Service providers should clean up after themselves and remove any debris.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Compliance</h4>
                          <p className="mt-1 text-gray-600">
                            Does the work comply with relevant regulations and standards? This is particularly important for electrical, plumbing, or structural work.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Documentation</h4>
                          <p className="mt-1 text-gray-600">
                            Have you received any necessary documentation, such as warranties, certificates, or instruction manuals?
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Handling Issues with Completed Work</h3>
                      <p className="mb-3 text-gray-700">
                        If you're not satisfied with the completed work, follow these steps:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2">
                        <li>
                          <span className="font-medium">Document the issues</span> - Take photos and make notes about specific problems
                        </li>
                        <li>
                          <span className="font-medium">Communicate clearly</span> - Explain your concerns to the provider through the ClassTasker messaging system
                        </li>
                        <li>
                          <span className="font-medium">Be specific about remedies</span> - Clearly state what needs to be fixed or completed
                        </li>
                        <li>
                          <span className="font-medium">Set a reasonable timeframe</span> - Agree on when the issues should be addressed
                        </li>
                        <li>
                          <span className="font-medium">Contact ClassTasker support if needed</span> - If you can't resolve the issues directly with the provider
                        </li>
                      </ol>
                    </div>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Payment Protection</AlertTitle>
                      <AlertDescription>
                        Remember that ClassTasker holds your payment in escrow until you approve the work as complete. This gives you time to thoroughly review the work and request any necessary changes before the provider is paid.
                      </AlertDescription>
                    </Alert>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Writing Helpful Reviews</h3>
                      <p className="mb-3">
                        After approving the work, you'll be prompted to leave a review for the service provider. Here are some tips for writing helpful reviews:
                      </p>
                      <ul className="list-disc pl-5 space-y-2 mb-3">
                        <li>Be honest and fair in your assessment</li>
                        <li>Provide specific details about what went well or could have been improved</li>
                        <li>Comment on professionalism, communication, and timeliness as well as the quality of work</li>
                        <li>Consider whether you would hire this provider again for similar work</li>
                        <li>Keep your review focused on the specific task and provider</li>
                      </ul>
                      <p>
                        Your reviews help maintain quality standards on ClassTasker and assist other schools in finding reliable service providers.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'profile-creation' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-center mb-6">
                      <img
                        src="https://images.unsplash.com/photo-1507537297725-24a1c029d3ca?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                        alt="Creating an attractive profile"
                        className="rounded-lg shadow-md max-w-full h-auto max-h-80"
                      />
                    </div>

                    <p className="text-lg">
                      As a service provider on ClassTasker, your profile is your digital shopfront. A well-crafted profile helps schools and educational institutions understand your expertise, experience, and reliability. This guide will help you create a profile that stands out and attracts quality task opportunities.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Key Elements of an Attractive Profile</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-blue-800">Professional Profile Photo</h4>
                          <p className="mt-1 text-gray-600">
                            Upload a clear, professional headshot or a photo of your team. Avoid casual selfies or low-quality images. For businesses, your company logo is appropriate.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">Comprehensive Bio</h4>
                          <p className="mt-1 text-gray-600">
                            Write a detailed bio that highlights your experience, qualifications, and specialties. Focus on aspects that are relevant to educational institutions.
                          </p>
                          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="bg-white p-2 rounded border border-green-100">
                              <p className="text-sm font-medium text-green-800">Good Example:</p>
                              <p className="text-sm">"Qualified electrician with 15 years of experience specialising in educational facilities. NICEIC registered with full public liability insurance. Experienced in classroom lighting upgrades, emergency lighting systems, and electrical safety testing for schools."</p>
                            </div>
                            <div className="bg-white p-2 rounded border border-red-100">
                              <p className="text-sm font-medium text-red-800">Poor Example:</p>
                              <p className="text-sm">"Electrician available for various jobs. Can do most electrical work."</p>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">Verified Qualifications and Certifications</h4>
                          <p className="mt-1 text-gray-600">
                            Upload and verify all relevant qualifications, certifications, and insurance documents. Schools need to know that you're properly qualified for the work you're offering.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">Portfolio of Previous Work</h4>
                          <p className="mt-1 text-gray-600">
                            Include high-quality photos of your previous work, especially projects completed in educational settings. Before-and-after photos are particularly effective.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">Detailed Service Descriptions</h4>
                          <p className="mt-1 text-gray-600">
                            List the specific services you offer with clear descriptions. Be specific about your areas of expertise rather than claiming to do everything.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h3 className="text-xl font-semibold mb-3 text-purple-800">Highlighting School-Specific Experience</h3>
                      <p className="mb-3 text-gray-700">
                        Schools and educational institutions have unique requirements and considerations. Emphasise any relevant experience:
                      </p>
                      <ul className="list-disc pl-5 space-y-2">
                        <li>
                          <span className="font-medium">Previous work in educational settings</span> - Mention specific schools or types of educational institutions you've worked with
                        </li>
                        <li>
                          <span className="font-medium">Understanding of school schedules</span> - Highlight your ability to work around term times, school hours, or exam periods
                        </li>
                        <li>
                          <span className="font-medium">Safeguarding awareness</span> - Note your understanding of safeguarding requirements when working in educational environments
                        </li>
                        <li>
                          <span className="font-medium">DBS checks</span> - Clearly state if you have an up-to-date DBS (Disclosure and Barring Service) check
                        </li>
                        <li>
                          <span className="font-medium">Health and safety compliance</span> - Emphasise your commitment to health and safety in educational environments
                        </li>
                      </ul>
                    </div>

                    <Alert className="bg-yellow-50 border-yellow-200">
                      <AlertCircle className="h-4 w-4 text-yellow-800" />
                      <AlertTitle className="text-yellow-800">Verification Process</AlertTitle>
                      <AlertDescription className="text-yellow-800">
                        ClassTasker verifies service providers' identities, qualifications, and insurance coverage. Complete the verification process to earn the "Verified" badge on your profile, which increases trust and improves your chances of being selected for tasks.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Setting Competitive Rates</h3>
                      <p className="mb-3 text-gray-700">
                        Your pricing strategy is an important part of your profile. Consider these factors when setting your rates:
                      </p>
                      <ul className="list-disc pl-5 space-y-2">
                        <li>Research market rates for similar services in your area</li>
                        <li>Consider your experience level, qualifications, and specialisations</li>
                        <li>Factor in travel costs, materials, and equipment</li>
                        <li>Be transparent about your pricing structure (hourly rates, fixed prices, etc.)</li>
                        <li>Consider offering special rates for recurring work or multiple tasks</li>
                      </ul>
                      <p className="mt-3 text-gray-700">
                        Remember that the lowest price doesn't always win. Schools often prioritise quality, reliability, and relevant experience over cost.
                      </p>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Maintaining and Updating Your Profile</h3>
                      <p className="mb-3">
                        Your profile should evolve as you gain more experience and complete more tasks on ClassTasker:
                      </p>
                      <ul className="list-disc pl-5 space-y-2 mb-3">
                        <li>Regularly update your portfolio with new completed projects</li>
                        <li>Add new qualifications or certifications as you earn them</li>
                        <li>Refresh your bio to highlight new areas of expertise or specialisation</li>
                        <li>Respond promptly to reviews, especially if they contain constructive feedback</li>
                        <li>Adjust your availability settings to reflect your current capacity for new work</li>
                      </ul>
                      <p>
                        A well-maintained profile demonstrates your professionalism and commitment to providing quality service.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'finding-tasks' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-center mb-6">
                      <img
                        src="https://images.unsplash.com/photo-1586281380349-632531db7ed4?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                        alt="Finding tasks to complete"
                        className="rounded-lg shadow-md max-w-full h-auto max-h-80"
                      />
                    </div>

                    <p className="text-lg">
                      As a service provider on ClassTasker, finding suitable tasks is key to building your business. This guide will help you navigate the platform efficiently to find and secure tasks that match your skills and availability.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Finding Available Tasks</h3>
                      <ol className="list-decimal pl-5 space-y-4">
                        <li className="text-gray-800">
                          <span className="font-medium">Browse the task marketplace</span>
                          <p className="mt-1 text-gray-600">
                            Navigate to the "Find Tasks" section of your dashboard to see all available tasks that match your service categories.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Use filters effectively</span>
                          <p className="mt-1 text-gray-600">
                            Narrow down tasks by location, category, budget range, and timeframe to find the most relevant opportunities.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Set up task alerts</span>
                          <p className="mt-1 text-gray-600">
                            Create custom alerts for specific task types or locations to receive notifications when new matching tasks are posted.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Check regularly</span>
                          <p className="mt-1 text-gray-600">
                            New tasks are posted throughout the day. Check the platform regularly, especially during school term times when more tasks are typically posted.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Review task details thoroughly</span>
                          <p className="mt-1 text-gray-600">
                            Before submitting an offer, carefully read the task description, requirements, location, and timeline to ensure it's a good fit for your skills and availability.
                          </p>
                        </li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Submitting Effective Offers</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Personalise Your Offer</h4>
                          <p className="mt-1 text-gray-600">
                            Address the specific requirements mentioned in the task description. Avoid generic, copy-paste responses.
                          </p>
                          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="bg-white p-2 rounded border border-green-100">
                              <p className="text-sm font-medium text-green-800">Good Example:</p>
                              <p className="text-sm">"I've completed similar classroom painting projects in three local schools. I can use low-VOC paints as specified and work during the half-term break. I've attached photos of my recent classroom renovation at St. Mary's Primary."</p>
                            </div>
                            <div className="bg-white p-2 rounded border border-red-100">
                              <p className="text-sm font-medium text-red-800">Poor Example:</p>
                              <p className="text-sm">"I can do this job. I'm available and have the right experience."</p>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Highlight Relevant Experience</h4>
                          <p className="mt-1 text-gray-600">
                            Emphasise your experience with similar tasks, especially in educational settings. Mention specific projects that demonstrate your expertise.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Be Clear About Pricing</h4>
                          <p className="mt-1 text-gray-600">
                            Provide a clear, itemised quote that explains what's included. If appropriate, offer options at different price points.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Specify Your Availability</h4>
                          <p className="mt-1 text-gray-600">
                            Clearly state when you're available to complete the task, being mindful of any timeframe requirements mentioned in the task description.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Include Relevant Attachments</h4>
                          <p className="mt-1 text-gray-600">
                            Attach photos of similar work you've completed, relevant certifications, or other documents that support your offer.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Understanding School Priorities</h3>
                      <p className="mb-3 text-gray-700">
                        Educational institutions often have specific priorities when selecting service providers:
                      </p>
                      <ul className="list-disc pl-5 space-y-2">
                        <li>
                          <span className="font-medium">Safeguarding compliance</span> - Schools prioritise providers who understand and comply with safeguarding requirements
                        </li>
                        <li>
                          <span className="font-medium">Minimal disruption</span> - Ability to work outside school hours or during holidays is highly valued
                        </li>
                        <li>
                          <span className="font-medium">Reliability</span> - Schools need providers who will show up on time and complete work as promised
                        </li>
                        <li>
                          <span className="font-medium">Value for money</span> - Schools have budget constraints but typically prioritise quality and reliability over the lowest price
                        </li>
                        <li>
                          <span className="font-medium">Proper qualifications</span> - Schools need to ensure all work complies with relevant regulations and standards
                        </li>
                      </ul>
                      <p className="mt-3 text-gray-700">
                        Address these priorities in your offers to increase your chances of being selected.
                      </p>
                    </div>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Building Relationships</AlertTitle>
                      <AlertDescription>
                        Schools often prefer to work with the same service providers repeatedly. Once you've successfully completed a task for a school, you may receive direct invitations for future work. Delivering quality work and excellent service can lead to long-term relationships with educational institutions.
                      </AlertDescription>
                    </Alert>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Managing Your Task Load</h3>
                      <p className="mb-3">
                        It's important to manage your workload effectively:
                      </p>
                      <ul className="list-disc pl-5 space-y-2 mb-3">
                        <li>Only submit offers for tasks you're confident you can complete within the specified timeframe</li>
                        <li>Update your availability status on your profile when your schedule changes</li>
                        <li>Consider the travel time between tasks when accepting work in different locations</li>
                        <li>Build in buffer time for unexpected issues or delays</li>
                        <li>Communicate promptly if your availability changes after submitting an offer</li>
                      </ul>
                      <p>
                        Maintaining a manageable workload ensures you can deliver high-quality work consistently, leading to positive reviews and more opportunities.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'positive-reviews' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      Positive reviews are essential for success on ClassTasker. They build your reputation, increase your visibility, and help you win more tasks. This guide provides strategies for consistently earning positive feedback from schools and educational institutions.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Why Reviews Matter</h3>
                      <p className="mb-3 text-gray-700">
                        Reviews on ClassTasker impact your success in several ways:
                      </p>
                      <ul className="list-disc pl-5 space-y-2">
                        <li>
                          <span className="font-medium">Visibility in search results</span> - Providers with higher ratings appear higher in search results
                        </li>
                        <li>
                          <span className="font-medium">Client trust</span> - Schools are more likely to select providers with positive reviews
                        </li>
                        <li>
                          <span className="font-medium">Higher acceptance rate</span> - Your offers are more likely to be accepted when you have a strong review history
                        </li>
                        <li>
                          <span className="font-medium">Premium pricing</span> - Well-reviewed providers can often charge higher rates
                        </li>
                        <li>
                          <span className="font-medium">Repeat business</span> - Positive reviews lead to long-term relationships with schools
                        </li>
                      </ul>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Strategies for Earning Positive Reviews</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Exceed Expectations</h4>
                          <p className="mt-1 text-gray-600">
                            Go beyond the minimum requirements of the task. Look for opportunities to add value or solve additional problems.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Communicate Professionally</h4>
                          <p className="mt-1 text-gray-600">
                            Maintain clear, prompt, and professional communication throughout the task. Keep clients informed of your progress and any issues that arise.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Be Punctual and Reliable</h4>
                          <p className="mt-1 text-gray-600">
                            Arrive on time and complete work within the agreed timeframe. If delays are unavoidable, communicate this proactively.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Maintain a Professional Appearance</h4>
                          <p className="mt-1 text-gray-600">
                            Dress appropriately for the educational environment. Wear clean, professional attire or a uniform if applicable.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Clean Up Thoroughly</h4>
                          <p className="mt-1 text-gray-600">
                            Leave the work area clean and tidy. Remove all debris, packaging, and tools when the job is complete.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Provide Documentation</h4>
                          <p className="mt-1 text-gray-600">
                            Offer clear documentation of the work completed, including any warranties, maintenance instructions, or certificates.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Follow Up Appropriately</h4>
                          <p className="mt-1 text-gray-600">
                            After completing the task, send a professional follow-up message thanking the client and asking if they're satisfied with the work.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Handling Feedback Effectively</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-yellow-800">Requesting Reviews</h4>
                          <p className="mt-1 text-gray-600">
                            Politely ask clients to leave a review after completing a task. Explain that reviews help you grow your business on the platform.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Responding to Positive Reviews</h4>
                          <p className="mt-1 text-gray-600">
                            Thank clients for positive reviews. A simple, professional thank-you message shows your appreciation and reinforces the positive relationship.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Addressing Negative Feedback</h4>
                          <p className="mt-1 text-gray-600">
                            If you receive negative feedback, respond professionally and constructively. Acknowledge any issues, explain what you've learned, and how you'll improve in the future.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Learning from Feedback</h4>
                          <p className="mt-1 text-gray-600">
                            Use all feedback as an opportunity to improve your services. Regularly review your feedback to identify patterns and areas for development.
                          </p>
                        </div>
                      </div>
                    </div>

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Important</AlertTitle>
                      <AlertDescription>
                        Never offer incentives or discounts in exchange for positive reviews. This violates ClassTasker's terms of service and can result in account suspension. Focus on providing excellent service that naturally earns positive feedback.
                      </AlertDescription>
                    </Alert>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Showcasing Your Reviews</h3>
                      <p className="mb-3">
                        Make the most of your positive reviews:
                      </p>
                      <ul className="list-disc pl-5 space-y-2 mb-3">
                        <li>Highlight your best reviews in your profile bio</li>
                        <li>Reference positive feedback from similar tasks when submitting new offers</li>
                        <li>Use testimonials from educational institutions to demonstrate your experience in school settings</li>
                        <li>Mention your overall rating when communicating with potential clients</li>
                      </ul>
                      <p>
                        A strong collection of positive reviews is one of your most valuable assets on ClassTasker. It represents the trust that schools place in your services and helps you build a sustainable business on the platform.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'getting-paid' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      Understanding how payments work on ClassTasker is essential for service providers. This guide explains the payment process, timeline, and best practices to ensure you get paid promptly and correctly for your work.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">The Payment Process</h3>
                      <ol className="list-decimal pl-5 space-y-4">
                        <li className="text-gray-800">
                          <span className="font-medium">Client accepts your offer</span>
                          <p className="mt-1 text-gray-600">
                            When a school or educational institution accepts your offer, they make a payment to ClassTasker, which is held in escrow until the task is completed.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">You complete the task</span>
                          <p className="mt-1 text-gray-600">
                            After completing the work as agreed, mark the task as complete in the ClassTasker platform.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Client approves the work</span>
                          <p className="mt-1 text-gray-600">
                            The client reviews the work and approves it as complete. If they have any concerns, they'll communicate these through the platform.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Payment is released</span>
                          <p className="mt-1 text-gray-600">
                            Once the client approves the work, ClassTasker releases the payment to your account, minus the service fee.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Funds are transferred</span>
                          <p className="mt-1 text-gray-600">
                            The payment is transferred to your designated bank account according to the payment schedule (typically within 3-5 business days).
                          </p>
                        </li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Setting Up Your Payment Details</h3>
                      <p className="mb-3 text-gray-700">
                        To receive payments, you need to set up your payment details in your ClassTasker account:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2">
                        <li>
                          Go to your account settings and select "Payment Details"
                        </li>
                        <li>
                          Enter your bank account information (account number, sort code, account holder name)
                        </li>
                        <li>
                          Verify your bank account as instructed
                        </li>
                        <li>
                          Set your preferred payment method (direct bank transfer is the default option)
                        </li>
                        <li>
                          Complete any required tax information
                        </li>
                      </ol>
                      <p className="mt-3 text-gray-700 italic">
                        Note: You must complete your payment setup before you can receive payments for completed tasks.
                      </p>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Understanding Fees and Pricing</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-yellow-800">Service Fees</h4>
                          <p className="mt-1 text-gray-600">
                            ClassTasker charges service providers a commission of 10% on each completed task. This fee is automatically deducted from the payment you receive.
                          </p>
                          <p className="mt-1 text-gray-600">
                            Example: For a £200 task, you'll receive £180 (£200 - 10% fee).
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Setting Your Rates</h4>
                          <p className="mt-1 text-gray-600">
                            When setting your rates or submitting offers, remember to factor in the service fee to ensure you're charging enough to cover your costs and desired profit margin.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Additional Costs</h4>
                          <p className="mt-1 text-gray-600">
                            Be clear about whether your price includes materials, travel expenses, or other costs. You can itemise these in your offer to provide transparency to clients.
                          </p>
                        </div>
                      </div>
                    </div>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Payment Protection</AlertTitle>
                      <AlertDescription>
                        ClassTasker's escrow system protects both you and the client. The client's payment is secured before you begin work, and you're guaranteed payment once the work is approved. If any disputes arise, ClassTasker's support team can help mediate.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h3 className="text-xl font-semibold mb-3 text-purple-800">Invoicing and Tax Considerations</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-purple-800">Automatic Invoices</h4>
                          <p className="mt-1 text-gray-600">
                            ClassTasker automatically generates invoices for all completed tasks. These invoices include all necessary details for your financial records and tax reporting.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-purple-800">Tax Responsibilities</h4>
                          <p className="mt-1 text-gray-600">
                            As a service provider, you're responsible for reporting your income and paying any applicable taxes. ClassTasker provides annual earnings statements to help with your tax reporting.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-purple-800">VAT Considerations</h4>
                          <p className="mt-1 text-gray-600">
                            If you're VAT registered, you can add your VAT number to your profile. ClassTasker will include VAT information on invoices as appropriate.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Tips for Smooth Payments</h3>
                      <ul className="list-disc pl-5 space-y-2 mb-3">
                        <li>Mark tasks as complete promptly once the work is finished</li>
                        <li>Ensure your banking details are accurate and up to date</li>
                        <li>Keep clear records of all tasks, payments, and invoices</li>
                        <li>Address any client concerns quickly to avoid payment delays</li>
                        <li>Contact ClassTasker support immediately if you encounter any payment issues</li>
                        <li>Consider setting up a separate business account for your ClassTasker earnings</li>
                      </ul>
                      <p>
                        By understanding the payment process and following these best practices, you can ensure a smooth, reliable income stream from your work on ClassTasker.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'profile-management' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      Managing your profile effectively is essential for maintaining your presence on ClassTasker. This guide covers how to update your profile information, manage your privacy settings, and make the most of your account.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Accessing Your Profile Settings</h3>
                      <p className="mb-3 text-gray-700">
                        To manage your profile:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2">
                        <li>Log in to your ClassTasker account</li>
                        <li>Click on your profile picture in the top right corner</li>
                        <li>Select "Profile Settings" from the dropdown menu</li>
                        <li>Navigate through the different tabs to access various settings</li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Updating Your Personal Information</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Profile Picture</h4>
                          <p className="mt-1 text-gray-600">
                            Upload a clear, professional photo that helps schools recognise you. For businesses, your company logo is appropriate.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Contact Information</h4>
                          <p className="mt-1 text-gray-600">
                            Keep your email address and phone number up to date to ensure you receive important notifications and communications.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Bio and Description</h4>
                          <p className="mt-1 text-gray-600">
                            Regularly update your bio to reflect your current experience, specialisations, and services offered.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Service Areas</h4>
                          <p className="mt-1 text-gray-600">
                            Specify the geographical areas where you're available to work. You can add multiple locations if you serve different areas.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Qualifications and Certifications</h4>
                          <p className="mt-1 text-gray-600">
                            Add and update your professional qualifications, certifications, and insurance details. Keep these current as they expire or as you gain new credentials.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Managing Your Portfolio</h3>
                      <p className="mb-3 text-gray-700">
                        Your portfolio showcases your previous work and helps schools understand your capabilities:
                      </p>
                      <ul className="list-disc pl-5 space-y-2">
                        <li>
                          <span className="font-medium">Add project examples</span> - Upload photos and descriptions of your best work, especially in educational settings
                        </li>
                        <li>
                          <span className="font-medium">Organise by category</span> - Group your portfolio items by service type for easier browsing
                        </li>
                        <li>
                          <span className="font-medium">Include before/after photos</span> - Show the transformation your work creates when applicable
                        </li>
                        <li>
                          <span className="font-medium">Add project details</span> - Include brief descriptions of the scope, challenges, and solutions for each project
                        </li>
                        <li>
                          <span className="font-medium">Update regularly</span> - Add new projects as you complete them to keep your portfolio current
                        </li>
                      </ul>
                    </div>

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Profile Visibility</AlertTitle>
                      <AlertDescription>
                        Your profile is visible to all schools and educational institutions on ClassTasker. Ensure all information is professional, accurate, and presents you in the best light. Avoid including personal information that isn't relevant to your services.
                      </AlertDescription>
                    </Alert>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Account Management</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium">Password Security</h4>
                          <p className="mt-1 text-gray-600">
                            Regularly update your password and ensure it's strong and unique. ClassTasker recommends changing your password every 3-6 months.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium">Linked Accounts</h4>
                          <p className="mt-1 text-gray-600">
                            You can link your ClassTasker account to social media profiles if desired. This can help verify your identity and build trust.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium">Account Deactivation</h4>
                          <p className="mt-1 text-gray-600">
                            If you need to take a break from ClassTasker, you can temporarily deactivate your account rather than deleting it. This preserves your reviews and history for when you return.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeSection === 'privacy-settings' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      Protecting your privacy while using ClassTasker is important. This guide explains the privacy settings available to you and how to manage what information is visible to others on the platform.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Accessing Privacy Settings</h3>
                      <p className="mb-3 text-gray-700">
                        To manage your privacy settings:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2">
                        <li>Log in to your ClassTasker account</li>
                        <li>Click on your profile picture in the top right corner</li>
                        <li>Select "Account Settings" from the dropdown menu</li>
                        <li>Navigate to the "Privacy" tab</li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Available Privacy Controls</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Contact Information Visibility</h4>
                          <p className="mt-1 text-gray-600">
                            Control who can see your contact details. Options include "Only ClassTasker Support," "Schools I've worked with," or "All platform users."
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Profile Visibility</h4>
                          <p className="mt-1 text-gray-600">
                            Choose whether your profile appears in search results and browsing. You can set your profile to "Active" (fully visible), "Hidden" (only visible to schools you've worked with), or "Inactive" (temporarily not visible).
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Location Precision</h4>
                          <p className="mt-1 text-gray-600">
                            Determine how precisely your location is displayed. Options include exact address, neighbourhood/area, or city/town only.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Review Management</h4>
                          <p className="mt-1 text-gray-600">
                            Control which reviews are featured prominently on your profile. You cannot remove legitimate reviews, but you can choose which ones to highlight.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Data Privacy and Protection</h3>
                      <p className="mb-3 text-gray-700">
                        ClassTasker is committed to protecting your personal data:
                      </p>
                      <ul className="list-disc pl-5 space-y-2">
                        <li>
                          <span className="font-medium">Data Access</span> - You can request a copy of all personal data ClassTasker holds about you
                        </li>
                        <li>
                          <span className="font-medium">Data Deletion</span> - You can request deletion of your personal data if you close your account
                        </li>
                        <li>
                          <span className="font-medium">Data Correction</span> - You can update or correct any inaccurate personal information
                        </li>
                        <li>
                          <span className="font-medium">Marketing Preferences</span> - Control whether you receive marketing communications
                        </li>
                        <li>
                          <span className="font-medium">Third-Party Sharing</span> - View and manage how your data is shared with third parties
                        </li>
                      </ul>
                    </div>

                    <Alert>
                      <Shield className="h-4 w-4" />
                      <AlertTitle>Security Best Practices</AlertTitle>
                      <AlertDescription>
                        To keep your account secure, use a strong, unique password, enable two-factor authentication if available, and never share your login credentials with others. Be cautious about the information you include in messages and task descriptions.
                      </AlertDescription>
                    </Alert>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Communication Privacy</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium">Messaging System</h4>
                          <p className="mt-1 text-gray-600">
                            All communications through ClassTasker's messaging system are private between you and the other party. ClassTasker staff can access messages only for support purposes or if required by law.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium">Personal Contact Information</h4>
                          <p className="mt-1 text-gray-600">
                            ClassTasker recommends keeping all communications within the platform for your protection. Avoid sharing personal contact details until you've established a trusted relationship.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium">Reporting Concerns</h4>
                          <p className="mt-1 text-gray-600">
                            If you receive inappropriate communications or have privacy concerns, you can report these to ClassTasker support for investigation.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeSection === 'notification-preferences' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      Managing your notification preferences helps you stay informed about important updates without being overwhelmed. This guide explains how to customise your notification settings on ClassTasker.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Accessing Notification Settings</h3>
                      <p className="mb-3 text-gray-700">
                        To manage your notification preferences:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2">
                        <li>Log in to your ClassTasker account</li>
                        <li>Click on your profile picture in the top right corner</li>
                        <li>Select "Account Settings" from the dropdown menu</li>
                        <li>Navigate to the "Notifications" tab</li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Types of Notifications</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Task Notifications</h4>
                          <p className="mt-1 text-gray-600">
                            Updates related to tasks you've posted or offers you've submitted.
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>New task offers (for schools)</li>
                            <li>Offer accepted/declined (for providers)</li>
                            <li>Task status updates</li>
                            <li>Task completion reminders</li>
                            <li>Payment confirmations</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Message Notifications</h4>
                          <p className="mt-1 text-gray-600">
                            Alerts about new messages from schools or service providers.
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>New messages</li>
                            <li>Message read receipts</li>
                            <li>File attachments</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Review Notifications</h4>
                          <p className="mt-1 text-gray-600">
                            Updates about reviews you've received or reminders to leave reviews.
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>New reviews received</li>
                            <li>Reminders to leave reviews</li>
                            <li>Responses to your reviews</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Account Notifications</h4>
                          <p className="mt-1 text-gray-600">
                            Important updates about your ClassTasker account.
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Account security alerts</li>
                            <li>Payment method updates</li>
                            <li>Profile verification status</li>
                            <li>Password changes</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Marketing Communications</h4>
                          <p className="mt-1 text-gray-600">
                            Optional updates about ClassTasker features, promotions, and news.
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Platform updates and new features</li>
                            <li>Special offers and promotions</li>
                            <li>Educational content and tips</li>
                            <li>Community news</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Notification Delivery Methods</h3>
                      <p className="mb-3 text-gray-700">
                        ClassTasker offers multiple ways to receive notifications. You can customise each type of notification to be delivered through one or more of these channels:
                      </p>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <BellRing className="h-5 w-5 text-yellow-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">In-App Notifications</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Notifications appear in your notification centre within the ClassTasker platform. These are always enabled for important account updates.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Mail className="h-5 w-5 text-yellow-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Email Notifications</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Notifications sent to your registered email address. You can choose which types of updates warrant an email.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Smartphone className="h-5 w-5 text-yellow-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Mobile Push Notifications</span>
                            <p className="text-sm text-gray-600 mt-1">
                              If you use the ClassTasker mobile app, you can receive push notifications on your device. These can be customised in your app settings.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <MessageSquare className="h-5 w-5 text-yellow-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">SMS Notifications</span>
                            <p className="text-sm text-gray-600 mt-1">
                              For urgent updates, you can opt to receive SMS notifications to your registered mobile number. These are typically limited to critical updates.
                            </p>
                          </div>
                        </li>
                      </ul>
                    </div>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Important Notifications</AlertTitle>
                      <AlertDescription>
                        Some critical notifications, such as account security alerts and payment confirmations, cannot be disabled as they contain important information about your account. You can still choose how you receive these notifications.
                      </AlertDescription>
                    </Alert>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Recommended Notification Settings</h3>
                      <p className="mb-3">
                        While notification preferences are personal, here are some recommended settings:
                      </p>
                      <ul className="list-disc pl-5 space-y-2 mb-3">
                        <li>Keep email notifications enabled for important updates like offer acceptances, payment confirmations, and account security alerts</li>
                        <li>Use in-app notifications for day-to-day communications and updates</li>
                        <li>Consider enabling mobile push notifications for time-sensitive updates if you use the mobile app</li>
                        <li>Review your notification settings periodically to ensure they still match your needs</li>
                      </ul>
                      <p>
                        Finding the right balance of notifications helps you stay informed about important updates without feeling overwhelmed by constant alerts.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'account-details' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      Managing your account details is essential for maintaining accurate information on ClassTasker. This guide explains how to update your personal information, change your password, and manage other account settings.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Accessing Account Settings</h3>
                      <p className="mb-3 text-gray-700">
                        To manage your account details:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2">
                        <li>Log in to your ClassTasker account</li>
                        <li>Click on your profile picture in the top right corner</li>
                        <li>Select "Account Settings" from the dropdown menu</li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Updating Personal Information</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Name</h4>
                          <p className="mt-1 text-gray-600">
                            You can update your first and last name in the "Personal Details" section. This name will be visible to other users on the platform.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Email Address</h4>
                          <p className="mt-1 text-gray-600">
                            To change your email address, go to the "Email & Password" section. You'll need to verify the new email address before the change takes effect.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Phone Number</h4>
                          <p className="mt-1 text-gray-600">
                            Update your phone number in the "Contact Information" section. This may require verification via SMS.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Address</h4>
                          <p className="mt-1 text-gray-600">
                            You can update your address in the "Location" section. This information helps match you with relevant tasks in your area.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Password and Security</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-yellow-800">Changing Your Password</h4>
                          <p className="mt-1 text-gray-600">
                            To change your password:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Go to the "Email & Password" section</li>
                            <li>Click on "Change Password"</li>
                            <li>Enter your current password</li>
                            <li>Enter and confirm your new password</li>
                            <li>Click "Save Changes"</li>
                          </ol>
                          <p className="mt-2 text-gray-600">
                            Choose a strong password that includes a mix of uppercase and lowercase letters, numbers, and special characters.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Two-Factor Authentication</h4>
                          <p className="mt-1 text-gray-600">
                            Enable two-factor authentication (2FA) for additional account security:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Go to the "Security" section</li>
                            <li>Click "Enable Two-Factor Authentication"</li>
                            <li>Follow the prompts to set up 2FA using your mobile device</li>
                          </ol>
                          <p className="mt-2 text-gray-600">
                            With 2FA enabled, you'll need both your password and a verification code to log in.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Login History</h4>
                          <p className="mt-1 text-gray-600">
                            Review your recent login activity in the "Security" section. If you notice any suspicious activity, change your password immediately and contact ClassTasker support.
                          </p>
                        </div>
                      </div>
                    </div>

                    <Alert>
                      <Shield className="h-4 w-4" />
                      <AlertTitle>Account Security</AlertTitle>
                      <AlertDescription>
                        Regularly updating your password and enabling two-factor authentication significantly enhances your account security. ClassTasker will never ask for your password via email or phone.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h3 className="text-xl font-semibold mb-3 text-purple-800">Account Type and Preferences</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-purple-800">Account Type</h4>
                          <p className="mt-1 text-gray-600">
                            Your account type (school/educational institution or service provider) determines what features are available to you. If you need to change your account type, contact ClassTasker support.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-purple-800">Language Preferences</h4>
                          <p className="mt-1 text-gray-600">
                            Set your preferred language in the "Preferences" section. This affects the language used in notifications and communications.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-purple-800">Currency</h4>
                          <p className="mt-1 text-gray-600">
                            Select your preferred currency for displaying prices and payments in the "Preferences" section.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Account Deactivation and Deletion</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium">Temporary Deactivation</h4>
                          <p className="mt-1 text-gray-600">
                            If you need to take a break from ClassTasker, you can temporarily deactivate your account:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Go to the "Account" section</li>
                            <li>Select "Deactivate Account"</li>
                            <li>Choose a reason for deactivation</li>
                            <li>Confirm your decision</li>
                          </ol>
                          <p className="mt-2 text-gray-600">
                            Your account will be hidden from other users, but you can reactivate it at any time by logging in.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium">Permanent Deletion</h4>
                          <p className="mt-1 text-gray-600">
                            To permanently delete your account and all associated data:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Go to the "Account" section</li>
                            <li>Select "Delete Account"</li>
                            <li>Read the information about what will be deleted</li>
                            <li>Confirm your decision</li>
                          </ol>
                          <p className="mt-2 text-gray-600 font-medium">
                            Note: Account deletion is permanent and cannot be undone. All your data will be removed according to our data retention policy.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeSection === 'organization-setup' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      Setting up your organization on ClassTasker allows you to manage multiple users, centralize billing, and streamline maintenance tasks across your educational institution. This guide walks you through the process of creating and configuring your organization.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Creating Your Organization</h3>
                      <ol className="list-decimal pl-5 space-y-4">
                        <li className="text-gray-800">
                          <span className="font-medium">Sign up or log in</span>
                          <p className="mt-1 text-gray-600">
                            Create a ClassTasker account or log in to your existing account.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Access organization setup</span>
                          <p className="mt-1 text-gray-600">
                            From your dashboard, click on "Organizations" in the main navigation, then select "Create Organization."
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Enter organization details</span>
                          <p className="mt-1 text-gray-600">
                            Provide your organization's name, type (school, college, university, or Multi-Academy Trust), and contact information.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Add your organization's address</span>
                          <p className="mt-1 text-gray-600">
                            Enter the primary address of your organization. For Multi-Academy Trusts, this would be your head office address.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Upload your organization logo</span>
                          <p className="mt-1 text-gray-600">
                            Add your school or trust logo, which will appear on your organization profile and communications.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Verify your organization</span>
                          <p className="mt-1 text-gray-600">
                            Complete the verification process by providing official documentation that confirms your organization's status as an educational institution.
                          </p>
                        </li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Organization Structure</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Single School Setup</h4>
                          <p className="mt-1 text-gray-600">
                            For individual schools, the organization structure is straightforward. You'll have one organization with various members who have different roles and permissions.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Multi-Academy Trust Setup</h4>
                          <p className="mt-1 text-gray-600">
                            For MATs, you can create a hierarchical structure:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Create the main MAT organization</li>
                            <li>Add individual schools as sub-organizations</li>
                            <li>Assign administrators at both the MAT and school levels</li>
                            <li>Configure permissions to determine what each school can manage independently</li>
                          </ol>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Departments and Locations</h4>
                          <p className="mt-1 text-gray-600">
                            Within each organization or sub-organization, you can create:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Departments (e.g., Facilities, IT, Administration)</li>
                            <li>Locations or buildings for larger campuses</li>
                            <li>Specific areas or rooms for detailed task assignment</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Organization Settings</h3>
                      <p className="mb-3 text-gray-700">
                        Configure your organization settings to match your specific needs:
                      </p>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <FileEdit className="h-5 w-5 text-yellow-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Task Approval Workflow</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Define how tasks are approved before posting. Options include automatic approval, approval by department heads, or approval by organization administrators.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <CreditCard className="h-5 w-5 text-yellow-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Billing and Payment Settings</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Set up centralized billing for your organization, add payment methods, and configure approval thresholds for different spending levels.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Users className="h-5 w-5 text-yellow-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">User Roles and Permissions</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Define custom roles beyond the default ones (admin, teacher, maintenance staff) and set specific permissions for each role.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Bell className="h-5 w-5 text-yellow-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Notification Preferences</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Configure organization-wide notification settings, including who receives alerts for new tasks, approvals, and completions.
                            </p>
                          </div>
                        </li>
                      </ul>
                    </div>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Organization Verification</AlertTitle>
                      <AlertDescription>
                        Verified organizations receive a badge on their profile, which builds trust with service providers. The verification process typically takes 1-2 business days and requires official documentation such as a school registration number or official letterhead.
                      </AlertDescription>
                    </Alert>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Best Practices for Organization Setup</h3>
                      <ul className="list-disc pl-5 space-y-2 mb-3">
                        <li>Start with a clear organizational structure that reflects your real-world hierarchy</li>
                        <li>Define roles and permissions carefully to ensure users have access to what they need without compromising security</li>
                        <li>Set up approval workflows that balance efficiency with proper oversight</li>
                        <li>Create standardized task templates for common maintenance requests</li>
                        <li>Establish clear guidelines for task creation and provider selection</li>
                        <li>Configure detailed reporting to track maintenance spending and patterns</li>
                      </ul>
                      <p>
                        A well-configured organization setup will streamline your maintenance management process, improve accountability, and provide valuable insights into your facility management operations.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'managing-members' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      Managing members in your organization is essential for maintaining an efficient workflow. This guide explains how to add, remove, and manage users within your school or Multi-Academy Trust on ClassTasker.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Accessing Member Management</h3>
                      <p className="mb-3 text-gray-700">
                        To manage members in your organization:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2">
                        <li>Log in to your ClassTasker account with administrator privileges</li>
                        <li>Navigate to the "Organization" section in the main menu</li>
                        <li>Select "Members" from the organization dashboard</li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Adding New Members</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Inviting Users</h4>
                          <p className="mt-1 text-gray-600">
                            To add new members to your organization:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Click the "Invite Members" button in the Members section</li>
                            <li>Enter the email addresses of the people you want to invite</li>
                            <li>Select the appropriate role for each invitee</li>
                            <li>Add a personalized message (optional)</li>
                            <li>Click "Send Invitations"</li>
                          </ol>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Bulk Invitations</h4>
                          <p className="mt-1 text-gray-600">
                            For adding multiple members at once:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Click "Bulk Import" in the Members section</li>
                            <li>Download the template CSV file</li>
                            <li>Fill in the required information for each member</li>
                            <li>Upload the completed CSV file</li>
                            <li>Review the information and confirm</li>
                          </ol>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Managing User Roles</h3>
                      <p className="mb-3 text-gray-700">
                        ClassTasker offers several predefined roles with different permissions:
                      </p>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <Users className="h-5 w-5 text-yellow-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Organization Admin</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Full access to all organization settings, member management, billing, and task management. Can add and remove members and change their roles.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <School className="h-5 w-5 text-yellow-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Teacher</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Can create and manage tasks, communicate with service providers, and approve completed work. Limited access to organization settings.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Wrench className="h-5 w-5 text-yellow-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Maintenance Staff</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Can view and update internal maintenance tasks. Cannot create tasks for external service providers or access billing information.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <FileText className="h-5 w-5 text-yellow-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Finance</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Can access billing information, payment history, and invoices. Limited access to task management.
                            </p>
                          </div>
                        </li>
                      </ul>
                      <p className="mt-3 text-gray-700">
                        To change a member's role:
                      </p>
                      <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                        <li>Find the member in the Members list</li>
                        <li>Click the "Edit" button next to their name</li>
                        <li>Select the new role from the dropdown menu</li>
                        <li>Click "Save Changes"</li>
                      </ol>
                    </div>

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Important</AlertTitle>
                      <AlertDescription>
                        Always maintain at least two Organization Admins to ensure you don't lose access to administrative functions if one admin is unavailable. Be cautious when changing roles, as this immediately affects what users can access.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h3 className="text-xl font-semibold mb-3 text-purple-800">Removing Members</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-purple-800">Removing Active Members</h4>
                          <p className="mt-1 text-gray-600">
                            To remove a member from your organization:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Find the member in the Members list</li>
                            <li>Click the "Remove" button next to their name</li>
                            <li>Confirm the removal when prompted</li>
                          </ol>
                          <p className="mt-2 text-gray-600">
                            Removed members will lose access to your organization immediately. They will receive an email notification about this change.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-purple-800">Cancelling Pending Invitations</h4>
                          <p className="mt-1 text-gray-600">
                            To cancel an invitation that hasn't been accepted yet:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Go to the "Pending Invitations" tab in the Members section</li>
                            <li>Find the invitation you want to cancel</li>
                            <li>Click "Cancel Invitation"</li>
                          </ol>
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Best Practices for Member Management</h3>
                      <ul className="list-disc pl-5 space-y-2 mb-3">
                        <li>Regularly review your member list and remove accounts that are no longer needed</li>
                        <li>Assign roles based on the principle of least privilege - give users only the access they need to perform their job</li>
                        <li>Create a standardized onboarding process for new members, including training on how to use ClassTasker</li>
                        <li>Document your organization's role structure and the permissions associated with each role</li>
                        <li>Consider implementing approval workflows for sensitive actions like creating high-value tasks</li>
                      </ul>
                      <p>
                        Effective member management ensures that your organization operates securely and efficiently on ClassTasker, with the right people having access to the right features.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'organization-billing' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      Managing billing and invoices for your organization is essential for financial oversight and budgeting. This guide explains how to set up payment methods, view invoices, and manage billing settings for your school or Multi-Academy Trust on ClassTasker.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Accessing Billing Settings</h3>
                      <p className="mb-3 text-gray-700">
                        To manage your organization's billing:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2">
                        <li>Log in to your ClassTasker account with administrator privileges</li>
                        <li>Navigate to the "Organization" section in the main menu</li>
                        <li>Select "Billing & Invoices" from the organization dashboard</li>
                      </ol>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Setting Up Payment Methods</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Adding a Payment Method</h4>
                          <p className="mt-1 text-gray-600">
                            To add a new payment method:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Go to the "Payment Methods" tab in the Billing section</li>
                            <li>Click "Add Payment Method"</li>
                            <li>Select the type of payment method (credit/debit card, direct debit, etc.)</li>
                            <li>Enter the required details</li>
                            <li>Set as default if desired</li>
                            <li>Click "Save Payment Method"</li>
                          </ol>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">BACS/Bank Transfer Setup</h4>
                          <p className="mt-1 text-gray-600">
                            For organizations that prefer to pay via bank transfer:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Select "BACS/Bank Transfer" as your payment method</li>
                            <li>Set up ClassTasker as a supplier in your finance system</li>
                            <li>Note the payment reference format required for proper allocation</li>
                            <li>Set up approval workflows if needed</li>
                          </ol>
                          <p className="mt-2 text-gray-600">
                            Note: When using BACS, payments may take 3-5 business days to process. Plan accordingly for time-sensitive tasks.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Managing Invoices</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-yellow-800">Viewing Invoices</h4>
                          <p className="mt-1 text-gray-600">
                            To access your organization's invoices:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Go to the "Invoices" tab in the Billing section</li>
                            <li>View a list of all invoices, sorted by date</li>
                            <li>Use filters to find specific invoices by date range, status, or amount</li>
                            <li>Click on an invoice to view its details</li>
                          </ol>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Downloading Invoices</h4>
                          <p className="mt-1 text-gray-600">
                            To download invoices for your records:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Open the invoice you want to download</li>
                            <li>Click the "Download PDF" button</li>
                            <li>Alternatively, select multiple invoices and click "Bulk Download" to get them all at once</li>
                          </ol>
                          <p className="mt-2 text-gray-600">
                            All invoices include detailed information about the tasks, service providers, and applicable fees.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Invoice Settings</h4>
                          <p className="mt-1 text-gray-600">
                            Customize your invoice settings:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Add purchase order numbers to invoices</li>
                            <li>Set up automatic invoice delivery to specific email addresses</li>
                            <li>Configure invoice grouping (e.g., daily, weekly, monthly)</li>
                            <li>Add custom fields required by your finance department</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Payment Terms</AlertTitle>
                      <AlertDescription>
                        ClassTasker's standard payment terms are 30 days from the invoice date. Late payments may affect your ability to post new tasks. If you require different payment terms, please contact our support team to discuss options.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h3 className="text-xl font-semibold mb-3 text-purple-800">Billing Reports</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-purple-800">Generating Reports</h4>
                          <p className="mt-1 text-gray-600">
                            To create financial reports:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Go to the "Reports" tab in the Billing section</li>
                            <li>Select the type of report you want to generate</li>
                            <li>Choose the date range and other parameters</li>
                            <li>Click "Generate Report"</li>
                            <li>Download the report in your preferred format (CSV, PDF, Excel)</li>
                          </ol>
                        </div>

                        <div>
                          <h4 className="font-medium text-purple-800">Available Report Types</h4>
                          <p className="mt-1 text-gray-600">
                            ClassTasker offers several types of financial reports:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Spending Summary - Overview of all expenses by category and time period</li>
                            <li>Provider Analysis - Breakdown of spending by service provider</li>
                            <li>Department Expenses - Expenses categorized by department or location</li>
                            <li>Task Type Analysis - Spending broken down by type of maintenance task</li>
                            <li>Budget Tracking - Comparison of actual spending against budgeted amounts</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Budget Management</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium">Setting Budgets</h4>
                          <p className="mt-1 text-gray-600">
                            To set maintenance budgets for your organization:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Go to the "Budgets" tab in the Billing section</li>
                            <li>Click "Create Budget"</li>
                            <li>Set the budget amount, time period, and category</li>
                            <li>Assign the budget to specific departments or locations if needed</li>
                            <li>Set up alerts for when spending approaches budget limits</li>
                          </ol>
                        </div>

                        <div>
                          <h4 className="font-medium">Approval Thresholds</h4>
                          <p className="mt-1 text-gray-600">
                            Configure approval workflows based on task value:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Set different approval requirements for tasks based on their value</li>
                            <li>Designate specific approvers for different spending levels</li>
                            <li>Create multi-level approval workflows for high-value tasks</li>
                          </ul>
                          <p className="mt-2 text-gray-600">
                            Example: Tasks under £500 might require only department head approval, while tasks over £1,000 might need both department head and finance director approval.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeSection === 'payment-methods' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      ClassTasker offers a variety of payment methods to accommodate the needs of schools and educational institutions. This guide explains the available payment options and how to use them.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Available Payment Methods</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-blue-800">Credit and Debit Cards</h4>
                          <p className="mt-1 text-gray-600">
                            ClassTasker accepts all major credit and debit cards, including:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Visa</li>
                            <li>Mastercard</li>
                            <li>American Express</li>
                            <li>Maestro</li>
                          </ul>
                          <p className="mt-2 text-gray-600">
                            Card payments are processed immediately, allowing tasks to be posted without delay.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">BACS/Bank Transfer</h4>
                          <p className="mt-1 text-gray-600">
                            For schools and MATs that prefer to pay via bank transfer:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Receive invoice with bank details</li>
                            <li>Process payment through your finance system</li>
                            <li>Include the invoice reference number with your payment</li>
                          </ul>
                          <p className="mt-2 text-gray-600">
                            Note: Tasks paid via BACS will be activated once payment is confirmed (typically 3-5 business days).
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">Digital Wallets</h4>
                          <p className="mt-1 text-gray-600">
                            For convenient, secure payments, ClassTasker supports:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Apple Pay</li>
                            <li>Google Pay</li>
                          </ul>
                          <p className="mt-2 text-gray-600">
                            Digital wallet payments are processed instantly and provide an additional layer of security.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">Purchase Orders</h4>
                          <p className="mt-1 text-gray-600">
                            For educational institutions that use purchase order systems:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Generate a purchase order in your finance system</li>
                            <li>Enter the PO number when posting a task</li>
                            <li>Receive an invoice referencing your PO number</li>
                            <li>Pay according to your standard payment process</li>
                          </ul>
                          <p className="mt-2 text-gray-600">
                            Tasks with purchase orders are subject to approval before being activated.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Setting Up Payment Methods</h3>
                      <ol className="list-decimal pl-5 space-y-4">
                        <li className="text-gray-800">
                          <span className="font-medium">Access payment settings</span>
                          <p className="mt-1 text-gray-600">
                            Navigate to your organization dashboard and select "Billing & Payments" from the menu.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Add a payment method</span>
                          <p className="mt-1 text-gray-600">
                            Click "Add Payment Method" and select the type of payment method you want to add.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Enter payment details</span>
                          <p className="mt-1 text-gray-600">
                            Provide the required information for your chosen payment method.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Set default payment method</span>
                          <p className="mt-1 text-gray-600">
                            Choose which payment method should be used by default for new tasks.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Save your settings</span>
                          <p className="mt-1 text-gray-600">
                            Confirm your changes to update your payment preferences.
                          </p>
                        </li>
                      </ol>
                    </div>

                    <Alert>
                      <Shield className="h-4 w-4" />
                      <AlertTitle>Secure Payments</AlertTitle>
                      <AlertDescription>
                        All payment information is encrypted and processed securely. ClassTasker uses Stripe, a PCI-compliant payment processor, to handle all transactions. Your payment details are never stored on ClassTasker's servers.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Payment Method Recommendations</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-yellow-800">For Quick Tasks</h4>
                          <p className="mt-1 text-gray-600">
                            For urgent maintenance tasks that need immediate attention, credit/debit cards or digital wallets are recommended as they provide instant payment confirmation.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">For Planned Maintenance</h4>
                          <p className="mt-1 text-gray-600">
                            For scheduled maintenance or larger projects, BACS transfers or purchase orders may be more suitable, allowing time for internal approval processes.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">For Multi-Academy Trusts</h4>
                          <p className="mt-1 text-gray-600">
                            MATs may benefit from setting up centralized billing with purchase orders to maintain oversight of spending across multiple schools.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Changing Payment Methods</h3>
                      <p className="mb-3">
                        You can change the payment method for a task at any time before payment is processed:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2 mb-3">
                        <li>Navigate to the task details page</li>
                        <li>Click "Edit Payment Method"</li>
                        <li>Select a different payment method from your saved options</li>
                        <li>Confirm the change</li>
                      </ol>
                      <p>
                        If you need to use a payment method not currently set up in your account, you'll need to add it to your payment methods first.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'transaction-fees' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      Understanding the fee structure on ClassTasker helps you budget effectively for your maintenance and service needs. This guide explains all fees associated with using the platform.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Fee Structure Overview</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-blue-800">For Schools and Educational Institutions</h4>
                          <p className="mt-1 text-gray-600">
                            Schools pay a service fee of 5% on each task. This fee is added to the task amount.
                          </p>
                          <p className="mt-2 text-gray-600">
                            <span className="font-medium">Example:</span> For a £200 task, the school pays £210 (£200 + 5% service fee).
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">For Service Providers</h4>
                          <p className="mt-1 text-gray-600">
                            Service providers pay a commission of 10% on each completed task. This fee is deducted from the payment received.
                          </p>
                          <p className="mt-2 text-gray-600">
                            <span className="font-medium">Example:</span> For a £200 task, the service provider receives £180 (£200 - 10% commission).
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">Payment Processing Fees</h4>
                          <p className="mt-1 text-gray-600">
                            Payment processing fees are included in the service fee and commission. There are no additional charges for standard payment methods.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Fee Calculation Examples</h3>
                      <div className="overflow-x-auto">
                        <table className="min-w-full bg-white border border-green-100">
                          <thead>
                            <tr className="bg-green-100">
                              <th className="py-2 px-4 border-b text-left">Task Amount</th>
                              <th className="py-2 px-4 border-b text-left">School Pays</th>
                              <th className="py-2 px-4 border-b text-left">Provider Receives</th>
                              <th className="py-2 px-4 border-b text-left">Platform Fees</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td className="py-2 px-4 border-b">£100</td>
                              <td className="py-2 px-4 border-b">£105</td>
                              <td className="py-2 px-4 border-b">£90</td>
                              <td className="py-2 px-4 border-b">£15</td>
                            </tr>
                            <tr>
                              <td className="py-2 px-4 border-b">£500</td>
                              <td className="py-2 px-4 border-b">£525</td>
                              <td className="py-2 px-4 border-b">£450</td>
                              <td className="py-2 px-4 border-b">£75</td>
                            </tr>
                            <tr>
                              <td className="py-2 px-4 border-b">£1,000</td>
                              <td className="py-2 px-4 border-b">£1,050</td>
                              <td className="py-2 px-4 border-b">£900</td>
                              <td className="py-2 px-4 border-b">£150</td>
                            </tr>
                            <tr>
                              <td className="py-2 px-4 border-b">£5,000</td>
                              <td className="py-2 px-4 border-b">£5,250</td>
                              <td className="py-2 px-4 border-b">£4,500</td>
                              <td className="py-2 px-4 border-b">£750</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>VAT Information</AlertTitle>
                      <AlertDescription>
                        All fees are subject to VAT at the standard rate where applicable. VAT is clearly itemised on all invoices. Educational institutions may be able to reclaim VAT according to HMRC guidelines.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Special Fee Arrangements</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-yellow-800">Volume Discounts</h4>
                          <p className="mt-1 text-gray-600">
                            Schools and MATs with high task volumes may be eligible for reduced service fees:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>25+ tasks per month: 4% service fee</li>
                            <li>50+ tasks per month: 3.5% service fee</li>
                            <li>100+ tasks per month: Custom pricing available</li>
                          </ul>
                          <p className="mt-2 text-gray-600">
                            Contact our sales team to discuss volume pricing for your organization.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Annual Subscriptions</h4>
                          <p className="mt-1 text-gray-600">
                            Schools can opt for an annual subscription plan that provides:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Reduced per-task fees (3% instead of 5%)</li>
                            <li>Priority support</li>
                            <li>Advanced reporting features</li>
                            <li>Dedicated account manager</li>
                          </ul>
                          <p className="mt-2 text-gray-600">
                            Annual subscriptions are priced based on school size and estimated task volume.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h3 className="text-xl font-semibold mb-3 text-purple-800">When Fees Are Charged</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-purple-800">For Schools</h4>
                          <p className="mt-1 text-gray-600">
                            The service fee is charged at the time of payment for the task. For credit/debit card and digital wallet payments, this is when you accept a provider's offer. For BACS and purchase orders, this is when the invoice is generated.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-purple-800">For Service Providers</h4>
                          <p className="mt-1 text-gray-600">
                            The commission is deducted automatically when payment is released to you after the task is completed and approved by the client.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Fee Transparency</h3>
                      <p className="mb-3">
                        ClassTasker is committed to transparent pricing:
                      </p>
                      <ul className="list-disc pl-5 space-y-2 mb-3">
                        <li>All fees are clearly displayed before you confirm any transaction</li>
                        <li>Detailed fee breakdowns are provided on all invoices and payment receipts</li>
                        <li>There are no hidden charges or surprise fees</li>
                        <li>Fee structures are reviewed annually, and any changes are communicated well in advance</li>
                      </ul>
                      <p>
                        If you have any questions about fees or billing, please contact our support team for assistance.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'refund-policy' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      ClassTasker's refund policy is designed to be fair to both schools and service providers while ensuring quality service. This guide explains when and how refunds are processed on the platform.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Refund Eligibility</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-blue-800">Before Work Begins</h4>
                          <p className="mt-1 text-gray-600">
                            If a service provider has not yet started work on a task, schools are eligible for a full refund in the following situations:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>The service provider cancels the task</li>
                            <li>The service provider fails to respond within 48 hours of task acceptance</li>
                            <li>The school cancels the task at least 24 hours before the scheduled start time</li>
                            <li>The task was posted in error (duplicate posting, incorrect details, etc.)</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">After Work Begins</h4>
                          <p className="mt-1 text-gray-600">
                            Once work has started, refund eligibility depends on the circumstances:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>If the provider abandons the task without completion: Full refund</li>
                            <li>If the work is of unacceptable quality: Full or partial refund, depending on assessment</li>
                            <li>If the provider breaches ClassTasker's terms of service: Full refund</li>
                            <li>If the school cancels after work has begun: Partial refund based on work completed</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">After Work Completion</h4>
                          <p className="mt-1 text-gray-600">
                            Once a school has approved a completed task, refunds are generally not available except in these cases:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Hidden defects that were not reasonably discoverable during inspection</li>
                            <li>Work that fails shortly after completion due to poor workmanship</li>
                            <li>Fraudulent representation of qualifications or certifications</li>
                          </ul>
                          <p className="mt-2 text-gray-600">
                            Claims must be made within 14 days of task completion.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Refund Process</h3>
                      <ol className="list-decimal pl-5 space-y-4">
                        <li className="text-gray-800">
                          <span className="font-medium">Submit a refund request</span>
                          <p className="mt-1 text-gray-600">
                            Navigate to the task in your dashboard and click "Request Refund." Provide details about why you're requesting a refund.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Provider notification</span>
                          <p className="mt-1 text-gray-600">
                            The service provider will be notified and given an opportunity to respond to the refund request.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Review and assessment</span>
                          <p className="mt-1 text-gray-600">
                            ClassTasker's support team will review the request, considering both parties' input and any supporting evidence.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Decision</span>
                          <p className="mt-1 text-gray-600">
                            A decision will be made within 5 business days. Both parties will be notified of the outcome.
                          </p>
                        </li>
                        <li className="text-gray-800">
                          <span className="font-medium">Refund processing</span>
                          <p className="mt-1 text-gray-600">
                            If approved, refunds are processed back to the original payment method. Processing times vary by payment method:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Credit/debit cards: 3-5 business days</li>
                            <li>Digital wallets: 1-2 business days</li>
                            <li>BACS transfers: 5-7 business days</li>
                          </ul>
                        </li>
                      </ol>
                    </div>

                    <Alert className="bg-yellow-50 border-yellow-200">
                      <AlertCircle className="h-4 w-4 text-yellow-800" />
                      <AlertTitle className="text-yellow-800">Dispute Resolution</AlertTitle>
                      <AlertDescription className="text-yellow-800">
                        If you disagree with a refund decision, you can appeal within 7 days. Appeals are reviewed by a senior member of our support team who was not involved in the original decision. For complex disputes, we may suggest mediation through our independent dispute resolution service.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h3 className="text-xl font-semibold mb-3 text-purple-800">Refund Alternatives</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-purple-800">Task Rework</h4>
                          <p className="mt-1 text-gray-600">
                            In cases where work doesn't meet requirements but can be corrected, we often recommend giving the provider an opportunity to fix the issues before processing a refund.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-purple-800">Partial Payments</h4>
                          <p className="mt-1 text-gray-600">
                            For tasks that are partially completed or where quality issues affect only part of the work, we may recommend a partial payment that fairly compensates the provider for acceptable work while refunding the remainder.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-purple-800">ClassTasker Credits</h4>
                          <p className="mt-1 text-gray-600">
                            In some cases, we may offer ClassTasker credits instead of a direct refund. These credits can be used for future tasks and are often processed more quickly than refunds to original payment methods.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Preventing Refund Situations</h3>
                      <p className="mb-3">
                        The best way to avoid refund issues is to prevent problems before they occur:
                      </p>
                      <ul className="list-disc pl-5 space-y-2 mb-3">
                        <li>Create detailed, clear task descriptions with specific requirements</li>
                        <li>Review provider profiles, ratings, and qualifications carefully before accepting offers</li>
                        <li>Communicate expectations clearly before work begins</li>
                        <li>Address any concerns promptly during the task execution</li>
                        <li>Inspect work thoroughly before approving completion</li>
                        <li>For larger tasks, consider breaking them into smaller milestones with separate payments</li>
                      </ul>
                      <p>
                        By following these best practices, both schools and service providers can minimize the need for refunds and ensure a positive experience on ClassTasker.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'payment-security' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      ClassTasker takes payment security seriously to protect both schools and service providers. This guide explains the security measures in place to safeguard your financial information and transactions.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Payment Processing Security</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-blue-800">Secure Payment Processing</h4>
                          <p className="mt-1 text-gray-600">
                            ClassTasker uses Stripe, a PCI-DSS Level 1 certified payment processor, to handle all financial transactions. This is the highest level of certification available in the payments industry.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">Data Encryption</h4>
                          <p className="mt-1 text-gray-600">
                            All payment information is encrypted using TLS (Transport Layer Security) with at least 128-bit encryption. This ensures that sensitive data cannot be intercepted during transmission.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-blue-800">No Card Storage</h4>
                          <p className="mt-1 text-gray-600">
                            ClassTasker never stores complete credit card numbers on our servers. When you save a payment method, the information is securely stored with our payment processor, and only a tokenized reference is kept in our system.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">Transaction Security Features</h3>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <Shield className="h-5 w-5 text-green-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Escrow System</span>
                            <p className="text-sm text-gray-600 mt-1">
                              When you pay for a task, ClassTasker holds the funds in a secure escrow account until the work is completed and approved. This protects schools from paying for incomplete work and ensures service providers will be paid for completed tasks.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <AlertCircle className="h-5 w-5 text-green-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Fraud Detection</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Our system uses advanced algorithms to detect suspicious transaction patterns. Unusual activity triggers additional verification steps to prevent fraudulent transactions.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Lock className="h-5 w-5 text-green-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">3D Secure Authentication</span>
                            <p className="text-sm text-gray-600 mt-1">
                              For credit and debit card payments, we support 3D Secure (Verified by Visa, Mastercard SecureCode, etc.), which adds an additional layer of authentication for online transactions.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Eye className="h-5 w-5 text-green-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Transaction Monitoring</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Our security team continuously monitors transactions for suspicious activity. Any unusual patterns are flagged for review to prevent fraud.
                            </p>
                          </div>
                        </li>
                      </ul>
                    </div>

                    <Alert>
                      <Shield className="h-4 w-4" />
                      <AlertTitle>Secure Authentication</AlertTitle>
                      <AlertDescription>
                        ClassTasker uses multi-factor authentication for account access and sensitive operations like changing payment methods or withdrawing funds. We strongly recommend enabling two-factor authentication on your account for maximum security.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Compliance and Certifications</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-yellow-800">PCI Compliance</h4>
                          <p className="mt-1 text-gray-600">
                            ClassTasker and our payment processors maintain PCI DSS (Payment Card Industry Data Security Standard) compliance, ensuring that we follow strict security standards for handling card information.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">GDPR Compliance</h4>
                          <p className="mt-1 text-gray-600">
                            We comply with the General Data Protection Regulation (GDPR), which includes strict requirements for protecting personal and financial data of EU citizens.
                          </p>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Regular Security Audits</h4>
                          <p className="mt-1 text-gray-600">
                            Our systems undergo regular security audits and penetration testing by independent security firms to identify and address potential vulnerabilities.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h3 className="text-xl font-semibold mb-3 text-purple-800">Protecting Your Account</h3>
                      <p className="mb-3 text-gray-700">
                        While ClassTasker implements robust security measures, you can take additional steps to protect your account:
                      </p>
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <Lock className="h-5 w-5 text-purple-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Strong Passwords</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Use a unique, complex password for your ClassTasker account. Include a mix of uppercase and lowercase letters, numbers, and special characters.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <Shield className="h-5 w-5 text-purple-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Enable Two-Factor Authentication</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Add an extra layer of security by enabling 2FA in your account settings. This requires a verification code in addition to your password when logging in.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <EyeOff className="h-5 w-5 text-purple-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Monitor Your Account</span>
                            <p className="text-sm text-gray-600 mt-1">
                              Regularly review your transaction history and account activity. Report any unauthorized transactions immediately.
                            </p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <AlertCircle className="h-5 w-5 text-purple-800 mr-2 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-medium">Beware of Phishing</span>
                            <p className="text-sm text-gray-600 mt-1">
                              ClassTasker will never ask for your password or full payment details via email or phone. Be cautious of unsolicited communications requesting sensitive information.
                            </p>
                          </div>
                        </li>
                      </ul>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Reporting Security Concerns</h3>
                      <p className="mb-3">
                        If you notice suspicious activity or have security concerns:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2 mb-3">
                        <li>Contact ClassTasker support <NAME_EMAIL></li>
                        <li>Change your password immediately if you suspect your account has been compromised</li>
                        <li>Review your recent transactions for any unauthorized activity</li>
                        <li>Check your account settings for any unauthorized changes</li>
                      </ol>
                      <p>
                        Our security team is available 24/7 to address urgent security concerns and will respond promptly to all reports.
                      </p>
                    </div>
                  </div>
                )}

                {activeSection === 'organization-settings' && (
                  <div className="space-y-6">
                    <p className="text-lg">
                      Configuring your organization settings allows you to customize ClassTasker to match your school or Multi-Academy Trust's specific needs. This guide explains the various settings available and how to configure them.
                    </p>

                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-xl font-semibold mb-3 text-blue-800">Accessing Organization Settings</h3>
                      <p className="mb-3 text-gray-700">
                        To access and manage your organization settings:
                      </p>
                      <ol className="list-decimal pl-5 space-y-2">
                        <li>Log in to your ClassTasker account with administrator privileges</li>
                        <li>Navigate to the "Organization" section in the main menu</li>
                        <li>Select "Settings" from the organization dashboard</li>
                      </ol>
                      <p className="mt-3 text-gray-700">
                        Note: Only users with organization administrator permissions can access and modify organization settings.
                      </p>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                      <h3 className="text-xl font-semibold mb-3 text-green-800">General Organization Settings</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-green-800">Organization Profile</h4>
                          <p className="mt-1 text-gray-600">
                            Manage your organization's public profile information:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Organization name and type (school, college, university, MAT)</li>
                            <li>Logo and branding images</li>
                            <li>Contact information (phone, email, website)</li>
                            <li>Physical address and location details</li>
                            <li>Organization description and mission statement</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Communication Preferences</h4>
                          <p className="mt-1 text-gray-600">
                            Configure how ClassTasker communicates with your organization:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Primary contact email addresses</li>
                            <li>Notification preferences for organization-wide alerts</li>
                            <li>Newsletter and update subscriptions</li>
                            <li>Language preferences</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-green-800">Visibility Settings</h4>
                          <p className="mt-1 text-gray-600">
                            Control what information is visible to service providers:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Organization profile visibility (public or limited)</li>
                            <li>Contact information visibility</li>
                            <li>Task history visibility</li>
                            <li>Member list visibility</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-100">
                      <h3 className="text-xl font-semibold mb-3 text-yellow-800">Task Management Settings</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-yellow-800">Task Approval Workflow</h4>
                          <p className="mt-1 text-gray-600">
                            Configure how tasks are approved before posting:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>No approval required (tasks post immediately)</li>
                            <li>Department head approval required</li>
                            <li>Finance approval required for tasks above a certain budget</li>
                            <li>Multi-level approval for high-value tasks</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Task Templates</h4>
                          <p className="mt-1 text-gray-600">
                            Create and manage templates for common maintenance tasks:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Standard task descriptions</li>
                            <li>Default requirements and specifications</li>
                            <li>Recommended budgets</li>
                            <li>Required qualifications for providers</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-yellow-800">Task Categories</h4>
                          <p className="mt-1 text-gray-600">
                            Customize task categories to match your organization's needs:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Create custom categories specific to your organization</li>
                            <li>Assign department responsibilities for each category</li>
                            <li>Set default approval workflows by category</li>
                            <li>Configure budget limits by category</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Task Visibility</AlertTitle>
                      <AlertDescription>
                        By default, tasks are visible only to service providers who meet the qualifications specified. You can adjust task visibility settings to make tasks visible to a wider pool of providers or restrict them to your preferred provider list.
                      </AlertDescription>
                    </Alert>

                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                      <h3 className="text-xl font-semibold mb-3 text-purple-800">User Roles and Permissions</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-purple-800">Default Roles</h4>
                          <p className="mt-1 text-gray-600">
                            ClassTasker provides several predefined roles:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Organization Admin - Full access to all settings and features</li>
                            <li>Finance Manager - Access to billing, invoices, and payment settings</li>
                            <li>Department Head - Can approve tasks within their department</li>
                            <li>Teacher/Staff - Can create and manage their own tasks</li>
                            <li>Viewer - Can view tasks but not create or modify them</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-purple-800">Custom Roles</h4>
                          <p className="mt-1 text-gray-600">
                            Create custom roles with specific permissions:
                          </p>
                          <ol className="list-decimal pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Go to "User Roles" in the Settings section</li>
                            <li>Click "Create New Role"</li>
                            <li>Name the role and provide a description</li>
                            <li>Select the permissions for this role</li>
                            <li>Save the new role</li>
                          </ol>
                        </div>

                        <div>
                          <h4 className="font-medium text-purple-800">Permission Settings</h4>
                          <p className="mt-1 text-gray-600">
                            Configure granular permissions for each role:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Task creation and management permissions</li>
                            <li>Budget approval thresholds</li>
                            <li>Member management permissions</li>
                            <li>Reporting access</li>
                            <li>Settings modification permissions</li>
                            <li>Billing and payment access</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="bg-red-50 p-4 rounded-lg border border-red-100">
                      <h3 className="text-xl font-semibold mb-3 text-red-800">Multi-Academy Trust Settings</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium text-red-800">School Management</h4>
                          <p className="mt-1 text-gray-600">
                            For MATs, configure settings for managing multiple schools:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Add and remove schools from your trust</li>
                            <li>Configure school-specific settings</li>
                            <li>Set permissions for school administrators</li>
                            <li>Manage shared resources across schools</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-red-800">Centralized vs. Decentralized Control</h4>
                          <p className="mt-1 text-gray-600">
                            Choose how much autonomy individual schools have:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Centralized - All tasks require MAT-level approval</li>
                            <li>Balanced - Schools can manage tasks up to certain budgets</li>
                            <li>Decentralized - Schools manage their own tasks with MAT oversight</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium text-red-800">Cross-School Reporting</h4>
                          <p className="mt-1 text-gray-600">
                            Configure reporting settings for trust-wide insights:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Consolidated financial reporting</li>
                            <li>Maintenance spending comparisons across schools</li>
                            <li>Service provider performance across the trust</li>
                            <li>Task completion metrics by school</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <h3 className="text-xl font-semibold mb-3">Integration Settings</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium">Calendar Integration</h4>
                          <p className="mt-1 text-gray-600">
                            Connect ClassTasker with your organization's calendar system:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Google Calendar integration</li>
                            <li>Microsoft Outlook integration</li>
                            <li>iCalendar feed for other calendar systems</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium">Finance System Integration</h4>
                          <p className="mt-1 text-gray-600">
                            Connect with your financial management systems:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Export invoices to your accounting software</li>
                            <li>Import purchase order numbers</li>
                            <li>Sync payment status with your finance system</li>
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium">API Access</h4>
                          <p className="mt-1 text-gray-600">
                            For organizations with technical resources, configure API access:
                          </p>
                          <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-600">
                            <li>Generate API keys for secure access</li>
                            <li>Configure webhook endpoints for real-time updates</li>
                            <li>Set permission scopes for API access</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Support Request Form */}
                {activeSection === 'support-request' && (
                  <div className="space-y-6">
                    <h3 className="text-xl font-semibold mb-3">Contact Support</h3>
                    <p className="text-lg mb-6">
                      Need help with ClassTasker? Our support team is here to assist you. Please fill out the form below and we'll get back to you as soon as possible.
                    </p>
                    <SupportRequestForm />
                  </div>
                )}

                {/* All content sections have been implemented */}
                {activeSection !== 'creating-account' &&
                 activeSection !== 'posting-task' &&
                 activeSection !== 'finding-provider' &&
                 activeSection !== 'payments-work' &&
                 activeSection !== 'task-description' &&
                 activeSection !== 'budget-setting' &&
                 activeSection !== 'provider-communication' &&
                 activeSection !== 'reviewing-work' &&
                 activeSection !== 'profile-creation' &&
                 activeSection !== 'finding-tasks' &&
                 activeSection !== 'positive-reviews' &&
                 activeSection !== 'getting-paid' &&
                 activeSection !== 'profile-management' &&
                 activeSection !== 'privacy-settings' &&
                 activeSection !== 'notification-preferences' &&
                 activeSection !== 'account-details' &&
                 activeSection !== 'organization-setup' &&
                 activeSection !== 'managing-members' &&
                 activeSection !== 'organization-billing' &&
                 activeSection !== 'payment-methods' &&
                 activeSection !== 'transaction-fees' &&
                 activeSection !== 'refund-policy' &&
                 activeSection !== 'payment-security' &&
                 activeSection !== 'organization-settings' &&
                 activeSection !== 'support-request' && (
                  <div className="p-6 text-center">
                    <h3 className="text-xl font-semibold mb-3">Help Section</h3>
                    <p className="text-gray-600">
                      Please select a topic from the menu to view detailed help information.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default Help;
