import { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import MainLayout from '@/components/layout/MainLayout';
import { Search, Wrench, Calendar, Shield, Clock, Award, ArrowRight, CheckCircle, MapPin, PoundSterling, Building, Globe, Users, Download } from 'lucide-react';
import TaskCard, { TaskProps } from '@/components/tasks/TaskCard';
import SuggestionCard from '@/components/tasks/SuggestionCard';
import PWAInstallButton from '@/components/pwa/PWAInstallButton';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

const Index = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('Debug: Index page rendered');
    }
  const [searchQuery, setSearchQuery] = useState('');

  const featureTasks: TaskProps[] = [
    {
      id: '1',
      title: 'School Playground Equipment Repair',
      description: 'Need a skilled handyman to repair several pieces of playground equipment including swings, slides, and climbing frames.',
      location: 'Washington Elementary School',
      dueDate: '2025-05-15',
      budget: 650,
      category: 'Repairs',
      status: 'open',
      offers: 3,
    },
    {
      id: '2',
      title: 'Classroom Painting - 5 Rooms',
      description: 'Looking for a professional painter to refresh 5 classrooms with new paint. Walls need to be prepped and painted with child-friendly paint.',
      location: 'Lincoln Middle School',
      dueDate: '2025-04-25',
      budget: 1200,
      category: 'Painting',
      status: 'open',
      offers: 7,
    },
    {
      id: '3',
      title: 'Landscaping & School Garden Maintenance',
      description: 'Regular maintenance required for our school garden and grounds. Tasks include mowing, pruning, weeding, and seasonal planting.',
      location: 'Jefferson High School',
      dueDate: '2025-05-01',
      budget: 850,
      category: 'Grounds Keeping',
      status: 'open',
      offers: 4,
    },
  ];

  const suggestionCards = [
    {
      id: 's1',
      title: 'Playground Equipment Repair',
      image: 'https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
      category: 'Repairs',
    },
    {
      id: 's2',
      title: 'Classroom Painting',
      image: 'https://images.unsplash.com/photo-1487958449943-2429e8be8625?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
      category: 'Painting',
    },
    {
      id: 's3',
      title: 'School Garden Maintenance',
      image: 'https://images.unsplash.com/photo-1518005020951-eccb494ad742?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
      category: 'Grounds Keeping',
    },
    {
      id: 's4',
      title: 'Sports Field Maintenance',
      image: 'https://images.unsplash.com/photo-1473177104440-ffee2f376098?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
      category: 'Grounds Keeping',
    },
    {
      id: 's5',
      title: 'Electrical System Maintenance',
      image: 'https://images.unsplash.com/photo-1485833077593-4278bba3f11f?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
      category: 'Electrical',
    },
    {
      id: 's6',
      title: 'Plumbing Repairs',
      image: 'https://images.unsplash.com/photo-1433086966358-54859d0ed716?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80',
      category: 'Plumbing',
    },
  ];

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Will implement search functionality later
    if (process.env.NODE_ENV === 'development') {
      console.log(`Searching for: ${searchQuery}`);
      }
  };

  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-20 md:py-28">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-10 left-10 w-64 h-64 bg-classtasker-blue/5 rounded-full"></div>
          <div className="absolute bottom-10 right-10 w-96 h-96 bg-classtasker-purple/5 rounded-full"></div>
          <div className="absolute top-1/3 right-1/4 w-32 h-32 bg-classtasker-orange/5 rounded-full"></div>
          <div className="absolute -bottom-10 -left-10 w-80 h-80 bg-classtasker-green/5 rounded-full"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <div className="inline-block px-3 py-1 bg-classtasker-blue/10 text-classtasker-blue rounded-full text-sm font-medium mb-6">
                Helping schools across the UK with task management
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 mb-6 leading-tight">
                Simplify School <span className="text-classtasker-blue relative">
                  Maintenance
                  <span className="absolute bottom-2 left-0 w-full h-3 bg-classtasker-blue/10 -z-10 rounded"></span>
                </span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Connect with qualified professionals for all your school facility needs. Save time, reduce costs, and ensure compliance.
              </p>
              <form onSubmit={handleSearch} className="flex mb-8 shadow-lg">
                <div className="relative flex-grow">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="What needs to be fixed?"
                    className="pl-12 h-14 rounded-r-none border-r-0 text-base"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Button type="submit" className="h-14 px-8 rounded-l-none bg-classtasker-blue hover:bg-blue-600 text-base font-medium">
                  Find Help
                </Button>
              </form>
              <div className="flex flex-wrap gap-4 items-center">
                <Button variant="outline" size="lg" asChild className="h-12 px-6 font-medium">
                  <Link to="/post-task">Post a Task</Link>
                </Button>
                <Button asChild size="lg" className="h-12 px-6 bg-classtasker-green hover:bg-green-600 text-white font-medium hidden md:inline-flex">
                  <Link to="/register">Become a Supplier</Link>
                </Button>
                <span className="text-sm text-gray-500 ml-2 hidden md:inline-block">No obligation</span>
              </div>

              {/* PWA Install Button */}
              <div className="mt-6 flex items-center">
                <div className="mr-3 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs font-medium">
                  New
                </div>
                <PWAInstallButton />
                <span className="ml-3 text-sm text-gray-500">Install our app for offline access</span>
              </div>
            </div>
            <div className="md:w-1/2 md:pl-10">
              <div className="relative">
                <div className="bg-white p-6 rounded-xl shadow-xl border border-gray-100 max-w-md mx-auto animate-float">
                  <div className="absolute -top-3 -right-3 bg-classtasker-orange text-white text-xs font-bold px-3 py-1 rounded-full">
                    Featured
                  </div>
                  <img
                    src="https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                    alt="School maintenance"
                    className="w-full h-48 object-cover rounded-lg mb-4"
                  />
                  <h3 className="text-lg font-semibold mb-2">Classroom Projector Installation</h3>
                  <p className="text-gray-600 mb-4 line-clamp-2">
                    Need a professional to install 5 new projectors in our classrooms. Must have experience with educational technology.
                  </p>
                  <div className="flex justify-between text-sm text-gray-500 mb-3">
                    <div className="flex items-center">
                      <PoundSterling className="h-4 w-4 mr-1 text-classtasker-green" />
                      <span>Budget: <span className="font-medium">£750</span></span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1 text-classtasker-blue" />
                      <span>Due: <span className="font-medium">14 May</span></span>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm text-gray-500">
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-1 text-classtasker-purple" />
                      <span>Location: <span className="font-medium">Manchester</span></span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-4 w-4 mr-1 text-classtasker-green" />
                      <span>Success Rate: <span className="font-medium">98%</span></span>
                    </div>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-6 -right-6 w-24 h-24 bg-classtasker-orange/20 rounded-full z-0"></div>
                <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-classtasker-purple/20 rounded-full z-0"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Suggested Tasks Carousel */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold">Popular School Tasks</h2>
            <Link to="/tasks" className="text-classtasker-blue hover:underline font-medium flex items-center">
              Browse All <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>

          <div className="relative">
            <Carousel
              opts={{
                align: "start",
                loop: true,
              }}
              className="w-full"
            >
              <CarouselContent className="-ml-4">
                {suggestionCards.map((suggestion) => (
                  <CarouselItem key={suggestion.id} className="pl-4 md:basis-1/2 lg:basis-1/3">
                    <SuggestionCard
                      id={suggestion.id}
                      title={suggestion.title}
                      image={suggestion.image}
                      category={suggestion.category}
                    />
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="-left-4 md:-left-6" />
              <CarouselNext className="-right-4 md:-right-6" />
            </Carousel>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gradient-to-b from-white to-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-block px-3 py-1 bg-classtasker-green/10 text-classtasker-green rounded-full text-sm font-medium mb-4">
              Complete School Management Solution
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">How Classtasker Works</h2>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg">
              Classtasker is a comprehensive platform for schools to manage internal tasks, external contractors, and compliance requirements - all in one place.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 relative">
            {/* Connecting line for desktop */}
            <div className="hidden md:block absolute top-24 left-0 w-full h-1 bg-gray-200 z-0"></div>

            <div className="relative z-10 bg-white rounded-xl shadow-md p-8 border border-gray-100 transform transition-transform hover:-translate-y-2 duration-300">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6 -mt-12 mx-auto border-4 border-white shadow-sm">
                <Wrench className="h-8 w-8 text-classtasker-blue" />
              </div>
              <span className="absolute top-4 right-4 bg-classtasker-blue text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">1</span>
              <h3 className="text-xl font-semibold mb-4 text-center">Create Tasks</h3>
              <p className="text-gray-600 text-center">
                Create maintenance tasks and assign them internally to your staff or externally to qualified suppliers based on your needs.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <div className="flex items-center text-sm text-gray-500">
                  <Building className="h-4 w-4 mr-2 text-classtasker-blue" />
                  <span>Internal & external options</span>
                </div>
              </div>
            </div>

            <div className="relative z-10 bg-white rounded-xl shadow-md p-8 border border-gray-100 transform transition-transform hover:-translate-y-2 duration-300 md:mt-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6 -mt-12 mx-auto border-4 border-white shadow-sm">
                <Calendar className="h-8 w-8 text-classtasker-green" />
              </div>
              <span className="absolute top-4 right-4 bg-classtasker-green text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">2</span>
              <h3 className="text-xl font-semibold mb-4 text-center">Manage Compliance</h3>
              <p className="text-gray-600 text-center">
                Track and manage all your compliance requirements with automated reminders for daily, weekly, monthly, and annual tasks.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <div className="flex items-center text-sm text-gray-500">
                  <Shield className="h-4 w-4 mr-2 text-classtasker-green" />
                  <span>Audit-ready documentation</span>
                </div>
              </div>
            </div>

            <div className="relative z-10 bg-white rounded-xl shadow-md p-8 border border-gray-100 transform transition-transform hover:-translate-y-2 duration-300">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-6 -mt-12 mx-auto border-4 border-white shadow-sm">
                <Users className="h-8 w-8 text-classtasker-purple" />
              </div>
              <span className="absolute top-4 right-4 bg-classtasker-purple text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">3</span>
              <h3 className="text-xl font-semibold mb-4 text-center">Coordinate Teams</h3>
              <p className="text-gray-600 text-center">
                Assign roles to your staff, manage external suppliers, and ensure everyone has the right access to complete their tasks.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <div className="flex items-center text-sm text-gray-500">
                  <Globe className="h-4 w-4 mr-2 text-classtasker-purple" />
                  <span>Role-based permissions</span>
                </div>
              </div>
            </div>

            <div className="relative z-10 bg-white rounded-xl shadow-md p-8 border border-gray-100 transform transition-transform hover:-translate-y-2 duration-300 md:mt-8">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-6 -mt-12 mx-auto border-4 border-white shadow-sm">
                <CheckCircle className="h-8 w-8 text-classtasker-orange" />
              </div>
              <span className="absolute top-4 right-4 bg-classtasker-orange text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">4</span>
              <h3 className="text-xl font-semibold mb-4 text-center">Track & Report</h3>
              <p className="text-gray-600 text-center">
                Monitor progress, generate reports for audits, and maintain a complete history of all maintenance and compliance activities.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <div className="flex items-center text-sm text-gray-500">
                  <Award className="h-4 w-4 mr-2 text-classtasker-orange" />
                  <span>Comprehensive dashboard</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-16 bg-gray-50 rounded-xl p-8 border border-gray-100">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <Building className="h-5 w-5 mr-2 text-classtasker-blue" />
                  Internal Task Management
                </h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 mr-2 text-classtasker-green flex-shrink-0 mt-0.5" />
                    <span>Assign tasks to your maintenance staff, teachers, or support team</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 mr-2 text-classtasker-green flex-shrink-0 mt-0.5" />
                    <span>Track progress and completion of internal maintenance tasks</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 mr-2 text-classtasker-green flex-shrink-0 mt-0.5" />
                    <span>Manage workloads and prioritize urgent maintenance needs</span>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <Shield className="h-5 w-5 mr-2 text-classtasker-green" />
                  Compliance Management
                </h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 mr-2 text-classtasker-green flex-shrink-0 mt-0.5" />
                    <span>Track daily, weekly, monthly, and annual compliance tasks</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 mr-2 text-classtasker-green flex-shrink-0 mt-0.5" />
                    <span>Upload and store documentation for audit purposes</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 mr-2 text-classtasker-green flex-shrink-0 mt-0.5" />
                    <span>Receive automated reminders for upcoming compliance deadlines</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <Button asChild className="bg-classtasker-blue hover:bg-blue-600">
              <Link to="/how-it-works">Learn More About Our Platform</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Featured Tasks Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold">Featured School Tasks</h2>
            <Link to="/tasks" className="text-classtasker-blue hover:underline font-medium flex items-center">
              View All <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featureTasks.map((task) => (
              <TaskCard key={task.id} {...task} />
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Use Classtasker?</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We're specifically designed to address the unique maintenance challenges faced by educational institutions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white p-6 rounded-lg border border-gray-100 shadow-sm">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <Shield className="h-6 w-6 text-classtasker-blue" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Verified Suppliers</h3>
              <p className="text-gray-600">
                All our suppliers are thoroughly vetted for safety and reliability.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-100 shadow-sm">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <Clock className="h-6 w-6 text-classtasker-green" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Time-Saving</h3>
              <p className="text-gray-600">
                Quickly find qualified professionals instead of making multiple calls.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-100 shadow-sm">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                <Award className="h-6 w-6 text-classtasker-orange" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Quality Work</h3>
              <p className="text-gray-600">
                Our review system ensures only the best suppliers remain on our platform.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-100 shadow-sm">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                <Search className="h-6 w-6 text-classtasker-purple" />
              </div>
              <h3 className="text-lg font-semibold mb-2">School-Focused</h3>
              <p className="text-gray-600">
                Specialists who understand the unique needs of educational facilities.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-classtasker-blue to-blue-700 z-0"></div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0">
          <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full"></div>
          <div className="absolute bottom-10 left-10 w-96 h-96 bg-white/5 rounded-full"></div>
          <div className="absolute top-1/3 left-1/4 w-32 h-32 bg-white/5 rounded-full"></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-3xl mx-auto">
            <div className="inline-block px-4 py-1 bg-white/10 text-white rounded-full text-sm font-medium mb-6">
              Simplify Your School Maintenance
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-white">
              Ready to Simplify Your School Maintenance?
            </h2>
            <p className="text-xl mb-10 text-white/90 max-w-2xl mx-auto">
              Use Classtasker to manage your maintenance needs, ensure compliance, and save money.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
              <Button size="lg" asChild className="bg-white text-classtasker-blue hover:bg-gray-100 h-14 px-8 text-lg font-medium">
                <Link to="/post-task">Post Your First Task</Link>
              </Button>
              <Button size="lg" asChild className="bg-classtasker-green text-white hover:bg-green-600 h-14 px-8 text-lg font-medium hidden md:inline-flex">
                <Link to="/register">Become a Supplier</Link>
              </Button>
            </div>
            <p className="mt-6 text-white/70 text-sm">
              No contracts. No commitments. Start for free today.
            </p>
          </div>

          {/* Removed trust indicators section as requested */}
        </div>
      </section>
    </MainLayout>
  );
};

export default Index;
