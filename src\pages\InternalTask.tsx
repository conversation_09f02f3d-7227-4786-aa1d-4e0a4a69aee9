import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { ArrowLeft, Loader2, CheckCircle, ArrowRight, Calendar, Clock, User, Tag, MessageSquare, FileText, ThumbsUp, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import MainLayout from '@/components/layout/MainLayout';
import notificationService from '@/services/notificationService';
import systemMessageService from '@/services/systemMessageService';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import InternalTaskActions from '@/components/tasks/InternalTaskActions';
import { useQueryClient } from '@tanstack/react-query';

interface TaskComment {
  id: string;
  task_id: string;
  user_id: string;
  content: string;
  created_at: string;
  user?: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
  };
}

interface TaskAttachment {
  id: string;
  task_id: string;
  file_name: string;
  file_url: string;
  file_type: string;
  created_at: string;
  user_id: string;
}

interface TaskHistory {
  id: string;
  task_id: string;
  user_id: string;
  action: string;
  details: string;
  created_at: string;
  user?: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
  };
}

const InternalTask = () => {
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  const { user, profile } = useAuth();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [task, setTask] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('details');
  const [comments, setComments] = useState<TaskComment[]>([]);
  const [attachments, setAttachments] = useState<TaskAttachment[]>([]);
  const [history, setHistory] = useState<TaskHistory[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [assignedUser, setAssignedUser] = useState<any>(null);
  const [creatorUser, setCreatorUser] = useState<any>(null);

  // Load task data and related information
  useEffect(() => {
    const loadTaskData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!id) {
          throw new Error('No task ID provided');
        }

        // Get the task
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('*')
          .eq('id', id)
          .single();

        if (taskError) {
          throw new Error(`Error getting task: ${taskError.message}`);
        }

        setTask(taskData);

        // Load comments
        const { data: commentsData, error: commentsError } = await supabase
          .from('task_comments')
          .select(`
            *,
            user:profiles(id, email, first_name, last_name)
          `)
          .eq('task_id', id)
          .order('created_at', { ascending: false });

        if (commentsError) {
          console.error('Error loading comments:', commentsError);
        } else {
          setComments(commentsData || []);
        }

        // Load attachments
        const { data: attachmentsData, error: attachmentsError } = await supabase
          .from('task_attachments')
          .select('*')
          .eq('task_id', id)
          .order('created_at', { ascending: false });

        if (attachmentsError) {
          console.error('Error loading attachments:', attachmentsError);
        } else {
          setAttachments(attachmentsData || []);
        }

        // Load task history
        const { data: historyData, error: historyError } = await supabase
          .from('task_history')
          .select(`
            *,
            user:profiles(id, email, first_name, last_name)
          `)
          .eq('task_id', id)
          .order('created_at', { ascending: false });

        if (historyError) {
          console.error('Error loading task history:', historyError);
        } else {
          setHistory(historyData || []);
        }

        // Load assigned user details
        if (taskData.assigned_to) {
          const { data: assignedUserData, error: assignedUserError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', taskData.assigned_to)
            .single();

          if (assignedUserError) {
            console.error('Error loading assigned user:', assignedUserError);
          } else {
            setAssignedUser(assignedUserData);
          }
        }

        // Load creator user details
        if (taskData.user_id) {
          const { data: creatorUserData, error: creatorUserError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', taskData.user_id)
            .single();

          if (creatorUserError) {
            console.error('Error loading creator user:', creatorUserError);
          } else {
            setCreatorUser(creatorUserData);
          }
        }
      } catch (err: any) {
        console.error('Error loading task:', err);
        setError(err.message || 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    loadTaskData();
  }, [id]);

  // Function to update task status
  const updateTaskStatus = async (newStatus: 'in_progress' | 'completed' | 'closed') => {
    try {
      setIsUpdatingStatus(true);

      // Update the task status
      const { error } = await supabase
        .from('tasks')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        throw error;
      }

      // Create notification for task update
      if (user) {
        // Determine who to notify based on the status change
        let recipientId = '';
        let action: 'created' | 'updated' | 'completed' | 'assigned' | 'offer' = 'updated';

        if (newStatus === 'in_progress') {
          // Notify the task creator that work has started
          recipientId = task.user_id;
          action = 'updated'; // Map 'started' to 'updated' for notification service
        } else if (newStatus === 'completed') {
          // Notify the task creator that work is completed
          recipientId = task.user_id;
          action = 'completed'; // This matches the allowed values
        } else if (newStatus === 'closed') {
          // Notify the assigned staff that task is closed
          recipientId = task.assigned_to || '';
          action = 'completed'; // Map 'closed' to 'completed' for notification service
        }

        if (recipientId && recipientId !== user.id) {
          notificationService.createTaskUpdateNotification(
            recipientId,
            task.id,
            task.title,
            action,
            true // Send email
          );
        }
      }

      // Show success message
      toast({
        title: `Task ${newStatus.replace('_', ' ')}`,
        description: getStatusUpdateMessage(newStatus),
        variant: "default",
      });

      // Send system message to the chat thread
      try {
        // Get the chat thread ID for this task and the assigned staff
        const { data: threadData } = await supabase
          .from('chat_threads')
          .select('id')
          .eq('task_id', task.id)
          .eq('supplier_id', task.assigned_to || '')
          .maybeSingle();

        const threadId = threadData?.id;

        if (threadId) {
          // Create a system message about the status change
          await systemMessageService.createStatusChangeMessage(
            task.id,
            newStatus,
            task.assigned_to,
            threadId,
            true // This is an internal task
          );
          if (process.env.NODE_ENV === 'development') {
    console.log(`System message created for status change to ${newStatus} in thread ${threadId}`);
  }
        } else {
          if (process.env.NODE_ENV === 'development') {
    console.log('No chat thread found for this internal task');
  }
        }
      } catch (error) {
        console.error('Error creating system message for status change:', error);
      }

      // Refresh task data
      const { data: updatedTask, error: refreshError } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', id)
        .single();

      if (refreshError) {
        throw refreshError;
      }

      setTask(updatedTask);
    } catch (error) {
      console.error(`Error updating task status to ${newStatus}:`, error);
      toast({
        title: "Error",
        description: `Failed to update task status. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Helper function to get status update messages
  const getStatusUpdateMessage = (status: string): string => {
    switch (status) {
      case 'in_progress':
        return "You've marked this task as in progress. The task creator will be notified.";
      case 'completed':
        return "You've marked this task as completed. The task creator will be notified to confirm completion.";
      case 'closed':
        return "You've closed this task. Thank you!";
      default:
        return "Task status has been updated.";
    }
  };

  // Function to add a comment
  const addComment = async () => {
    if (!newComment.trim() || !user || !id) return;

    try {
      setIsSubmittingComment(true);

      // Add comment to database
      const { data, error } = await supabase
        .from('task_comments')
        .insert({
          task_id: id,
          user_id: user.id,
          content: newComment.trim(),
          created_at: new Date().toISOString()
        })
        .select(`
          *,
          user:profiles(id, email, first_name, last_name)
        `)
        .single();

      if (error) {
        throw error;
      }

      // Add to task history
      await supabase
        .from('task_history')
        .insert({
          task_id: id,
          user_id: user.id,
          action: 'comment',
          details: 'Added a comment',
          created_at: new Date().toISOString()
        });

      // Update comments list
      setComments([data, ...comments]);
      setNewComment('');

      toast({
        title: "Comment added",
        description: "Your comment has been added to the task.",
        variant: "default",
      });
    } catch (error: any) {
      console.error('Error adding comment:', error);
      toast({
        title: "Error",
        description: "Failed to add comment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmittingComment(false);
    }
  };

  // Function to add task history entry
  const addTaskHistory = async (action: string, details: string) => {
    if (!user || !id) return;

    try {
      await supabase
        .from('task_history')
        .insert({
          task_id: id,
          user_id: user.id,
          action,
          details,
          created_at: new Date().toISOString()
        });

      // Refresh history
      const { data, error } = await supabase
        .from('task_history')
        .select(`
          *,
          user:profiles(id, email, first_name, last_name)
        `)
        .eq('task_id', id)
        .order('created_at', { ascending: false });

      if (!error && data) {
        setHistory(data);
      }
    } catch (error) {
      console.error('Error adding task history:', error);
    }
  };

  // Helper function to get user display name
  const getUserDisplayName = (userData: any) => {
    if (!userData) return 'Unknown User';

    if (userData.first_name && userData.last_name) {
      return `${userData.first_name} ${userData.last_name}`;
    }

    return userData.email || 'Unknown User';
  };

  // Helper function to get avatar initials
  const getAvatarInitials = (userData: any) => {
    if (!userData) return 'U';

    if (userData.first_name && userData.last_name) {
      return `${userData.first_name[0]}${userData.last_name[0]}`.toUpperCase();
    }

    if (userData.email) {
      return userData.email[0].toUpperCase();
    }

    return 'U';
  };

  // Helper function to get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'assigned':
        return 'bg-blue-100 text-blue-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'confirmed':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Determine user roles and permissions
  const isMaintenance = profile?.role === 'maintenance';
  const isTaskOwner = task?.user_id === user?.id;
  const isAssignedStaff = task?.assigned_to === user?.id;

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="mb-6">
            <Link to="/tasks" className="text-gray-600 hover:text-classtasker-blue flex items-center">
              <ArrowLeft className="h-4 w-4 mr-1" /> Back to Tasks
            </Link>
          </div>
          <Card>
            <CardContent className="p-6 flex justify-center items-center">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  if (error || !task) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="mb-6">
            <Link to="/tasks" className="text-gray-600 hover:text-classtasker-blue flex items-center">
              <ArrowLeft className="h-4 w-4 mr-1" /> Back to Tasks
            </Link>
          </div>
          <Card className="bg-red-50 border-red-200">
            <CardContent className="p-6">
              <p className="text-red-600 font-medium">Error: {error || 'Task not found'}</p>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex justify-between items-center">
          <Link to="/tasks" className="text-gray-600 hover:text-classtasker-blue flex items-center">
            <ArrowLeft className="h-4 w-4 mr-1" /> Back to Tasks
          </Link>
          <Link to={`/tasks/enhanced/${id}`} className="text-blue-600 hover:underline">
            View in Regular Task Page
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="mb-6">
              <div className="flex justify-between items-start">
                <h1 className="text-3xl font-bold">{task.title}</h1>
                <Badge
                  variant="outline"
                  className={
                    `${task.status === 'assigned' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                      task.status === 'in_progress' ? 'bg-indigo-100 text-indigo-800 border-indigo-200' :
                      task.status === 'completed' ? 'bg-purple-100 text-purple-800 border-purple-200' :
                      task.status === 'confirmed' ? 'bg-teal-100 text-teal-800 border-teal-200' :
                      'bg-gray-100 text-gray-800 border-gray-200'}`
                  }
                >
                  {task.status === 'in_progress' ? 'In Progress' :
                   task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                </Badge>
              </div>
              <div className="flex items-center text-gray-600 mt-2">
                <Tag size={16} className="mr-1" /> Internal Maintenance Task
              </div>
            </div>

            <Card className="mb-8">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center">
                      <Calendar size={18} className="text-gray-500 mr-2" />
                      <div>
                        <p className="text-sm text-gray-500">Due Date</p>
                        <p className="font-medium">{task.due_date ? format(new Date(task.due_date), 'PP') : 'Not specified'}</p>
                      </div>
                    </div>
                    <Separator orientation="vertical" className="h-10" />
                    <div className="flex items-center">
                      <User size={18} className="text-gray-500 mr-2" />
                      <div>
                        <p className="text-sm text-gray-500">Assigned To</p>
                        <p className="font-medium">
                          {assignedUser ?
                            getUserDisplayName(assignedUser) :
                            (task.assigned_to_name || task.assigned_to || 'Not assigned')}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Clock size={18} className="text-gray-500 mr-2" />
                    <div>
                      <p className="text-sm text-gray-500">Created</p>
                      <p className="font-medium">{format(new Date(task.created_at), 'PP')}</p>
                    </div>
                  </div>
                </div>

                <Separator className="mb-6" />

                <div className="mb-6">
                  <h2 className="text-xl font-semibold mb-3">Description</h2>
                  <div className="text-gray-700 whitespace-pre-line">
                    {task.description}
                  </div>
                </div>

                <div>
                  <h2 className="text-xl font-semibold mb-3">Category</h2>
                  <Badge variant="outline">
                    {task.category || 'Maintenance'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            {/* Task Status Card */}
            <Card className="bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2 text-blue-600" /> Task Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Current Status:</span>
                    <Badge
                      variant="outline"
                      className={
                        `${task.status === 'assigned' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                          task.status === 'in_progress' ? 'bg-indigo-100 text-indigo-800 border-indigo-200' :
                          task.status === 'completed' ? 'bg-purple-100 text-purple-800 border-purple-200' :
                          task.status === 'confirmed' ? 'bg-teal-100 text-teal-800 border-teal-200' :
                          'bg-gray-100 text-gray-800 border-gray-200'}`
                      }
                    >
                      {task.status === 'in_progress' ? 'In Progress' :
                       task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                    </Badge>
                  </div>

                  <Progress
                    value={
                      task.status === 'assigned' ? 25 :
                      task.status === 'in_progress' ? 50 :
                      task.status === 'completed' ? 75 :
                      task.status === 'confirmed' ? 100 : 0
                    }
                    className="h-2"
                  />

                  <div className="grid grid-cols-4 text-center text-xs">
                    <div className={`${task.status === 'assigned' || task.status === 'in_progress' || task.status === 'completed' || task.status === 'confirmed' ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>Assigned</div>
                    <div className={`${task.status === 'in_progress' || task.status === 'completed' || task.status === 'confirmed' ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>In Progress</div>
                    <div className={`${task.status === 'completed' || task.status === 'confirmed' ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>Completed</div>
                    <div className={`${task.status === 'confirmed' ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>Confirmed</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Task Actions Card - Using the InternalTaskActions Component */}
            <Card className="border-2 border-blue-300">
              <CardHeader>
                <CardTitle>Task Actions</CardTitle>
                <CardDescription>
                  Update the status of this internal task
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* This is the actual InternalTaskActions component that should be showing on the main task page */}
                <InternalTaskActions
                  task={task}
                  onTaskUpdated={() => {
                    // Refresh task data
                    queryClient.invalidateQueries({ queryKey: ['task', id] });

                    // Also refresh this page's data
                    const loadTaskData = async () => {
                      try {
                        const { data: taskData, error: taskError } = await supabase
                          .from('tasks')
                          .select('*')
                          .eq('id', id)
                          .single();

                        if (!taskError && taskData) {
                          setTask(taskData);
                        }
                      } catch (err) {
                        console.error('Error refreshing task data:', err);
                      }
                    };

                    loadTaskData();
                  }}
                />

                {/* Fallback buttons in case the component doesn't render */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <h3 className="text-sm font-medium mb-3">Fallback Actions (if component fails)</h3>
                  <div className="space-y-4">
                    {/* Start Work Button */}
                    {(task.status === 'assigned' || task.status.toLowerCase() === 'assigned') && (
                      <Button
                        onClick={() => updateTaskStatus('in_progress')}
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        disabled={isUpdatingStatus}
                      >
                        {isUpdatingStatus ? (
                          <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
                        ) : (
                          <><ArrowRight className="mr-2 h-4 w-4" /> Start Work</>
                        )}
                      </Button>
                    )}

                    {/* Complete Task Button */}
                    {(task.status === 'in_progress' || task.status.toLowerCase() === 'in_progress') && (
                      <Button
                        onClick={() => updateTaskStatus('completed')}
                        className="w-full bg-green-600 hover:bg-green-700"
                        disabled={isUpdatingStatus}
                      >
                        {isUpdatingStatus ? (
                          <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
                        ) : (
                          <><CheckCircle className="mr-2 h-4 w-4" /> Mark as Completed</>
                        )}
                      </Button>
                    )}

                    {/* Close Task Button (for task owners) */}
                    {isTaskOwner && (task.status === 'completed' || task.status.toLowerCase() === 'completed') && (
                      <Button
                        onClick={() => updateTaskStatus('closed')}
                        className="w-full bg-purple-600 hover:bg-purple-700"
                        disabled={isUpdatingStatus}
                      >
                        {isUpdatingStatus ? (
                          <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
                        ) : (
                          <><ThumbsUp className="mr-2 h-4 w-4" /> Close Task</>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Task Details Card */}
            <Card>
              <CardHeader>
                <CardTitle>Task Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Task ID:</span>
                    <span className="text-sm font-medium">{task.id}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Created By:</span>
                    <span className="text-sm font-medium">
                      {creatorUser ?
                        getUserDisplayName(creatorUser) :
                        task.user_id}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Assigned To:</span>
                    <span className="text-sm font-medium">
                      {assignedUser ?
                        getUserDisplayName(assignedUser) :
                        (task.assigned_to_name || task.assigned_to || 'Not assigned')}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Created:</span>
                    <span className="text-sm font-medium">{format(new Date(task.created_at), 'PP')}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Last Updated:</span>
                    <span className="text-sm font-medium">{format(new Date(task.updated_at), 'PP')}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Visibility:</span>
                    <Badge variant="outline" className="bg-gray-100">
                      {task.visibility}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Debug Information Card */}
            <Card className="bg-yellow-50">
              <CardHeader>
                <CardTitle>Debug Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xs space-y-1">
                  <p><strong>Task ID:</strong> {task.id}</p>
                  <p><strong>Status:</strong> {task.status}</p>
                  <p><strong>Visibility:</strong> {task.visibility}</p>
                  <p><strong>Assigned To:</strong> {task.assigned_to}</p>
                  <p><strong>User ID:</strong> {user?.id}</p>
                  <p><strong>User Role:</strong> {profile?.role}</p>
                  <p><strong>Is Maintenance:</strong> {isMaintenance ? 'Yes' : 'No'}</p>
                  <p><strong>Is Task Owner:</strong> {isTaskOwner ? 'Yes' : 'No'}</p>
                  <p><strong>Is Assigned Staff:</strong> {isAssignedStaff ? 'Yes' : 'No'}</p>
                </div>
                <div className="mt-4 pt-3 border-t border-gray-300">
                  <h4 className="font-bold mb-2">Alternative Views:</h4>
                  <div className="space-y-2">
                    <p>
                      <a
                        href={`/tasks/${task.id}`}
                        className="text-blue-600 hover:underline"
                      >
                        Open in Regular Task View
                      </a>
                    </p>
                    <p>
                      <a
                        href={`/emergency/task/${task.id}`}
                        className="text-blue-600 hover:underline"
                      >
                        Open in Emergency Task View
                      </a>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default InternalTask;
