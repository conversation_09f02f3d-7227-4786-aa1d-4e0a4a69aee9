import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import MainLayout from '@/components/layout/MainLayout';
import { Loader2, CheckCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

const InvitationConfirmation = () => {
  const { user, acceptInvitation } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [isProcessing, setIsProcessing] = useState(false);
  const [organizationName, setOrganizationName] = useState<string>('the organization');
  const [isLoading, setIsLoading] = useState(true);
  const [success, setSuccess] = useState<boolean | null>(null);

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    console.log('InvitationConfirmation rendered', {
    user,
    token: localStorage.getItem('[API_KEY_REDACTED]'.replace(/user.*/, 'hasUser: ' + !!user)),
    email: localStorage.getItem('pendingInvitationEmail'),
    location: location.pathname
  });

    }
  // Get invitation details from localStorage
  const token = localStorage.getItem('pendingInvitationToken');
  const invitedEmail = localStorage.getItem('pendingInvitationEmail');

  useEffect(() => {
    const fetchOrganizationName = async () => {
      if (!token) {
        setIsLoading(false);
        return;
      }

      try {
        // Try multiple approaches to get the invitation details
        let invitation;

        // First try with supabaseAdmin
        const { data: adminData, error: adminError } = await supabase
          .from('user_invitations')
          .select('organization_id')
          .eq('token', token)
          .headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          })
          .single();

        if (adminError || !adminData) {
          console.error('Error fetching invitation with regular client:', adminError);

          // Try using the RPC function if available
          try {
            const { data: rpcData, error: rpcError } = await supabase.rpc('get_invitation_by_token', {
              token_param: token
            });

            if (!rpcError && rpcData) {
              invitation = { organization_id: rpcData.organization_id };
            } else {
              console.error('Error fetching invitation with RPC:', rpcError);
            }
          } catch (rpcError) {
            console.error('RPC function not available or error:', rpcError);
          }
        } else {
          invitation = adminData;
        }

        if (invitation?.organization_id) {
          // Get the organization name
          const { data: org } = await supabase
            .from('organizations')
            .select('name')
            .eq('id', invitation.organization_id)
            .single();

          if (org?.name) {
            setOrganizationName(org.name);
            localStorage.setItem('pendingInvitationOrgName', org.name);
          }
        }
      } catch (error) {
        console.error('Error fetching organization details:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganizationName();
  }, [token]);

  // Check if the user is logged in and matches the invited email
  const isCorrectUser = user?.email?.toLowerCase() === invitedEmail?.toLowerCase();

  const handleAcceptInvitation = async () => {
    if (!token) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No invitation found. Please try the invitation link again.',
      });
      return;
    }

    setIsProcessing(true);

    try {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.log('Accepting invitation with token:', token);
          }
      }
      const result = await acceptInvitation(token);
      setSuccess(result);

      if (result) {
        toast({
          title: 'Invitation Accepted',
          description: `You have successfully joined ${organizationName}.`,
        });

        // Clear invitation data
        localStorage.removeItem('pendingInvitationToken');
        localStorage.removeItem('pendingInvitationEmail');
        localStorage.removeItem('pendingInvitationOrgName');
        localStorage.removeItem('newUserWithInvitation');

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          navigate('/dashboard');
        }, 2000);
      } else {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to accept invitation. Please try again.',
        });
      }
    } catch (error) {
      console.error('Error accepting invitation:', error);
      setSuccess(false);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Loading Invitation</CardTitle>
            </CardHeader>
            <CardContent className="p-6 text-center">
              <Loader2 className="h-16 w-16 animate-spin text-primary mx-auto mb-4" />
              <p className="text-gray-600">
                Loading invitation details...
              </p>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  if (!token) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-center">No Invitation Found</CardTitle>
            </CardHeader>
            <CardContent className="p-6 text-center">
              <p className="text-gray-600 mb-4">
                No pending invitation was found. Please check your email for an invitation link.
              </p>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button onClick={() => navigate('/dashboard')}>Go to Dashboard</Button>
            </CardFooter>
          </Card>
        </div>
      </MainLayout>
    );
  }

  if (!user) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Sign In Required</CardTitle>
            </CardHeader>
            <CardContent className="p-6 text-center">
              <p className="text-gray-600 mb-4">
                You need to sign in to accept this invitation.
              </p>
            </CardContent>
            <CardFooter className="flex justify-center space-x-4">
              <Button
                onClick={() => {
                  // Pass the invited email and token to the login page
                  if (invitedEmail) {
                    navigate(`/login?email=${encodeURIComponent(invitedEmail)}&token=${token}`);
                  } else {
                    navigate(`/login?token=${token}`);
                  }
                }}
              >
                Sign In
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  // Pass the invited email and token to the registration page
                  if (invitedEmail) {
                    navigate(`/register?email=${encodeURIComponent(invitedEmail)}&token=${token}`);
                  } else {
                    navigate(`/register?token=${token}`);
                  }
                }}
              >
                Create Account
              </Button>
            </CardFooter>
          </Card>
        </div>
      </MainLayout>
    );
  }

  if (!isCorrectUser) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Wrong Account</CardTitle>
            </CardHeader>
            <CardContent className="p-6 text-center">
              <p className="text-gray-600 mb-4">
                This invitation was sent to {invitedEmail}, but you're signed in as {user.email}.
              </p>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button
                onClick={() => {
                  // Sign out and redirect to login with the invited email
                  supabase.auth.signOut().then(() => {
                    if (invitedEmail) {
                      navigate(`/login?email=${encodeURIComponent(invitedEmail)}&token=${token}`);
                    } else {
                      navigate(`/login?token=${token}`);
                    }
                  });
                }}
              >
                Sign Out and Continue
              </Button>
            </CardFooter>
          </Card>
        </div>
      </MainLayout>
    );
  }

  if (success === true) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Invitation Accepted</CardTitle>
            </CardHeader>
            <CardContent className="p-6 text-center">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">
                You have successfully joined {organizationName}.
              </p>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button onClick={() => navigate('/dashboard')}>Go to Dashboard</Button>
            </CardFooter>
          </Card>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-center">Join Organization</CardTitle>
          </CardHeader>
          <CardContent className="p-6 text-center">
            <p className="text-gray-600 mb-4">
              Your account has been created successfully. Click the button below to join {organizationName}.
            </p>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button
              onClick={handleAcceptInvitation}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                'Accept Invitation'
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </MainLayout>
  );
};

export default InvitationConfirmation;
