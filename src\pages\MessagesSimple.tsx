import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Link } from 'react-router-dom';

// Import the GetStream chat hook
import { useGetStreamChat } from '@/hooks/use-getstream-chat';

interface MessageThread {
  taskId: string;
  taskTitle: string;
  lastMessage: string;
  lastMessageTime: string;
  otherParticipantName: string;
}

const MessagesSimple = () => {
  const [messageThreads, setMessageThreads] = useState<MessageThread[]>([]);
  const [isLoadingThreads, setIsLoadingThreads] = useState(true);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const { user } = useAuth();

  // Use the GetStream chat hook
  const {
    client,
    channel,
    messages,
    isLoading: isLoadingMessages,
    sendMessage
  } = useGetStreamChat({ taskId: selectedTaskId || '' });

  // Fetch all task messages to build the threads list
  useEffect(() => {
    if (!user) {
      setIsLoadingThreads(false);
      return;
    }

    const fetchAllMessages = async () => {
      setIsLoadingThreads(true);

      try {
        if (process.env.NODE_ENV === 'development') {

          console.log('[MessagesSimple] Fetching all task messages');


          }
        // Direct query to get all task messages
        const { data: allMessages, error } = await supabase
          .from('task_messages')
          .select('*, profiles(*), tasks(*)')
          .order('created_at', { ascending: false });

        if (process.env.NODE_ENV === 'development') {


          console.log('[MessagesSimple] All messages result:', { data: allMessages, error });



          }
        if (error) {
          console.error('[MessagesSimple] Error fetching messages:', error);
          setMessageThreads([]);
          setIsLoadingThreads(false);
          return;
        }

        if (!allMessages || allMessages.length === 0) {
          if (process.env.NODE_ENV === 'development') {

            console.log('[MessagesSimple] No messages found');

            }
          setMessageThreads([]);
          setIsLoadingThreads(false);
          return;
        }

        if (process.env.NODE_ENV === 'development') {


          console.log(`[MessagesSimple] Found ${allMessages.length} messages`);



          }
        // Group by task_id to get unique threads
        const threadMap = new Map<string, MessageThread>();

        for (const msg of allMessages) {
          // Only include threads where the user is involved
          if (msg.sender_id === user.id || msg.tasks?.user_id === user.id) {
            if (!threadMap.has(msg.task_id)) {
              // Determine the other participant name
              let otherParticipantName = 'Unknown User';

              if (msg.profiles) {
                if (msg.sender_id === user.id) {
                  // If user is the sender, use the task owner's name or task title
                  otherParticipantName = `Task: ${msg.tasks?.title || 'Untitled Task'}`;
                } else {
                  // If user is not the sender, use the sender's name
                  otherParticipantName = msg.profiles.first_name
                    ? `${msg.profiles.first_name} ${msg.profiles.last_name || ''}`
                    : 'Unknown User';
                }
              }

              threadMap.set(msg.task_id, {
                taskId: msg.task_id,
                taskTitle: msg.tasks?.title || 'Untitled Task',
                lastMessage: msg.content,
                lastMessageTime: msg.created_at,
                otherParticipantName
              });
            }
          }
        }

        // Convert map to array and sort by last message time (newest first)
        const threadsArray = Array.from(threadMap.values())
          .sort((a, b) => {
            return new Date(b.lastMessageTime).getTime() -
                   new Date(a.lastMessageTime).getTime();
          });

        setMessageThreads(threadsArray);

        // Select the first thread by default
        if (threadsArray.length > 0 && !selectedTaskId) {
          setSelectedTaskId(threadsArray[0].taskId);
        }
      } catch (err) {
        console.error('[MessagesSimple] Unexpected error:', err);
        setMessageThreads([]);
      } finally {
        setIsLoadingThreads(false);
      }
    };

    fetchAllMessages();

    // Set up real-time subscription for new messages
    const channel = supabase
      .channel('task_messages_changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'task_messages'
        },
        () => {
          if (process.env.NODE_ENV === 'development') {

            console.log('[MessagesSimple] New message detected, refreshing threads');

            }
          fetchAllMessages();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, selectedTaskId]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !newMessage.trim() || !selectedTaskId) return;

    const messageToSend = newMessage.trim();
    setNewMessage(''); // Clear input field immediately for better UX

    // Use the same sendMessage function from useTaskChat
    await sendMessage(messageToSend);
  };

  const getInitials = (name: string) => {
    if (!name || name === 'Unknown User') return '?';
    return name.split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getTimePassed = (timestamp: string) => {
    const messageTime = new Date(timestamp).getTime();
    const currentTime = new Date().getTime();
    const diffMinutes = Math.floor((currentTime - messageTime) / (60 * 1000));

    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;

    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;

    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  return (
    <MainLayout>
      <div className="container mx-auto py-6 px-4">
        <h1 className="text-2xl font-bold mb-6">Messages</h1>

        {!user ? (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">Please sign in to view your messages</p>
          </div>
        ) : isLoadingThreads ? (
          <div className="flex justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-[70vh]">
            {/* Message Threads List */}
            <div className="md:col-span-1 border rounded-lg overflow-hidden">
              <div className="bg-gray-50 p-4 border-b">
                <h2 className="font-semibold">Recent Conversations</h2>
              </div>

              <div className="overflow-y-auto h-[calc(70vh-64px)]">
                {messageThreads.length === 0 ? (
                  <div className="p-6 text-center text-gray-500">
                    <p>No conversations yet</p>
                    <p className="text-sm mt-1">Start a conversation by messaging a task owner or supplier</p>
                  </div>
                ) : (
                  messageThreads.map((thread) => (
                    <div
                      key={thread.taskId}
                      onClick={() => setSelectedTaskId(thread.taskId)}
                      className={`p-4 border-b hover:bg-gray-50 cursor-pointer ${selectedTaskId === thread.taskId ? 'bg-gray-100' : ''}`}
                    >
                      <div className="flex items-start gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback>{getInitials(thread.otherParticipantName)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between">
                            <h3 className="font-medium truncate">{thread.otherParticipantName}</h3>
                            <div className="text-xs text-gray-500">{getTimePassed(thread.lastMessageTime)}</div>
                          </div>
                          <Link to={`/tasks/${thread.taskId}`} className="text-sm text-classtasker-blue hover:underline truncate block">
                            {thread.taskTitle}
                          </Link>
                          <p className="text-sm truncate">
                            {thread.lastMessage}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Chat Messages */}
            <div className="md:col-span-2 border rounded-lg overflow-hidden flex flex-col">
              {selectedTaskId && messageThreads.length > 0 ? (
                <>
                  <div className="bg-gray-50 p-4 border-b">
                    {messageThreads.find(t => t.taskId === selectedTaskId) && (
                      <>
                        <h2 className="font-semibold">
                          {messageThreads.find(t => t.taskId === selectedTaskId)?.otherParticipantName || 'Conversation'}
                        </h2>
                        <Link
                          to={`/tasks/${selectedTaskId}`}
                          className="text-sm text-classtasker-blue hover:underline"
                        >
                          {messageThreads.find(t => t.taskId === selectedTaskId)?.taskTitle || 'Task'}
                        </Link>
                      </>
                    )}
                  </div>

                  {isLoadingMessages ? (
                    <div className="flex-1 flex justify-center items-center">
                      <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                    </div>
                  ) : (
                    <>
                      <div className="flex-1 overflow-y-auto p-4 space-y-4">
                        {!hasMessages ? (
                          <div className="text-center py-8">
                            <p className="text-gray-500">No messages yet. Start the conversation!</p>
                          </div>
                        ) : (
                          messages.map((msg) => {
                            const isCurrentUser = user?.id === msg.sender_id;
                            return (
                              <div
                                key={msg.id}
                                className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                              >
                                {!isCurrentUser && (
                                  <Avatar className="h-8 w-8 mr-2">
                                    <AvatarFallback>{getInitials(msg.sender_name || '')}</AvatarFallback>
                                  </Avatar>
                                )}
                                <div
                                  className={`max-w-[70%] p-3 rounded-lg ${
                                    isCurrentUser
                                      ? 'bg-classtasker-blue text-white rounded-br-none'
                                      : 'bg-gray-100 rounded-bl-none'
                                  }`}
                                >
                                  {!isCurrentUser && (
                                    <div className="text-xs font-semibold mb-1">{msg.sender_name}</div>
                                  )}
                                  <p>{msg.content}</p>
                                  <div className={`text-xs mt-1 ${isCurrentUser ? 'text-blue-100' : 'text-gray-500'}`}>
                                    {formatTime(msg.created_at)}
                                  </div>
                                </div>
                                {isCurrentUser && (
                                  <Avatar className="h-8 w-8 ml-2">
                                    <AvatarFallback>{getInitials(msg.sender_name || '')}</AvatarFallback>
                                  </Avatar>
                                )}
                              </div>
                            );
                          })
                        )}
                      </div>

                      <div className="p-4 border-t">
                        <form onSubmit={handleSendMessage} className="flex gap-2">
                          <Input
                            value={newMessage}
                            onChange={(e) => setNewMessage(e.target.value)}
                            placeholder="Type a message..."
                            className="flex-1"
                          />
                          <Button type="submit" size="icon" disabled={!newMessage.trim()}>
                            <Send className="h-4 w-4" />
                          </Button>
                        </form>
                      </div>
                    </>
                  )}
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center p-4 text-gray-500">
                  {messageThreads.length === 0 ?
                    'You have no conversations yet' :
                    'Select a conversation to start messaging'}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default MessagesSimple;
