import { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { PoundSterling, MessageSquare, CheckCircle, Bell, Trash2 } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { useNotifications } from '@/contexts/NotificationContext';
import { Link } from 'react-router-dom';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const Notifications = () => {
  const { 
    notifications, 
    unreadCount, 
    markAsRead, 
    markAllAsRead, 
    deleteNotification,
    isLoading 
  } = useNotifications();
  const [activeTab, setActiveTab] = useState<string>('all');

  // Filter notifications based on active tab
  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'all') return true;
    if (activeTab === 'unread') return !notification.read;
    return notification.type === activeTab;
  });

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'offer':
        return <PoundSterling className="h-4 w-4" />;
      case 'message':
        return <MessageSquare className="h-4 w-4" />;
      case 'task_update':
      case 'system':
      default:
        return <CheckCircle className="h-4 w-4" />;
    }
  };

  // Get notification link based on type and relatedId
  const getNotificationLink = (notification: any) => {
    if (!notification.relatedId) return null;

    switch (notification.relatedType) {
      case 'task':
        return `/tasks/${notification.relatedId}`;
      case 'message':
        return `/dashboard?tab=messages`;
      case 'offer':
        return `/tasks/${notification.relatedId}`;
      default:
        return null;
    }
  };

  // Handle notification click
  const handleNotificationClick = async (id: string) => {
    await markAsRead(id);
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold flex items-center">
                  <Bell className="mr-2 h-5 w-5 text-classtasker-blue" />
                  Notifications
                  {unreadCount > 0 && (
                    <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                      {unreadCount} new
                    </span>
                  )}
                </h1>
                {unreadCount > 0 && (
                  <Button 
                    variant="outline" 
                    className="text-classtasker-blue"
                    onClick={() => markAllAsRead()}
                  >
                    Mark All as Read
                  </Button>
                )}
              </div>

              <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-6">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="unread">Unread</TabsTrigger>
                  <TabsTrigger value="offer">Offers</TabsTrigger>
                  <TabsTrigger value="message">Messages</TabsTrigger>
                  <TabsTrigger value="task_update">Tasks</TabsTrigger>
                  <TabsTrigger value="system">System</TabsTrigger>
                </TabsList>

                <TabsContent value={activeTab}>
                  {isLoading ? (
                    <div className="space-y-4">
                      {Array(5).fill(0).map((_, i) => (
                        <div key={i} className="border rounded-lg p-4">
                          <div className="flex items-start gap-3">
                            <Skeleton className="h-8 w-8 rounded-full" />
                            <div className="flex-1">
                              <Skeleton className="h-4 w-3/4 mb-2" />
                              <Skeleton className="h-3 w-1/4" />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : filteredNotifications.length > 0 ? (
                    <div className="space-y-4">
                      {filteredNotifications.map((notification) => {
                        const link = getNotificationLink(notification);
                        const NotificationContent = (
                          <div
                            className={`border rounded-lg p-4 transition-colors ${
                              !notification.read ? 'bg-blue-50 border-blue-100' : ''
                            }`}
                            onClick={() => handleNotificationClick(notification.id)}
                          >
                            <div className="flex items-start gap-3">
                              <div className={`rounded-full p-2 ${
                                notification.type === 'offer' ? 'bg-green-100 text-green-600' :
                                notification.type === 'message' ? 'bg-blue-100 text-blue-600' :
                                notification.type === 'task_update' ? 'bg-orange-100 text-orange-600' :
                                'bg-gray-100 text-gray-600'
                              }`}>
                                {getNotificationIcon(notification.type)}
                              </div>
                              <div className="flex-1">
                                <p className={`${!notification.read ? 'font-medium' : ''}`}>
                                  {notification.message}
                                </p>
                                <div className="flex justify-between items-center mt-1">
                                  <p className="text-sm text-gray-500">{notification.time}</p>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-gray-400 hover:text-red-500"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      e.preventDefault();
                                      deleteNotification(notification.id);
                                    }}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                    <span className="sr-only">Delete</span>
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        );

                        return link ? (
                          <Link key={notification.id} to={link}>
                            {NotificationContent}
                          </Link>
                        ) : (
                          <div key={notification.id}>
                            {NotificationContent}
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Bell className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-gray-500">No notifications found.</p>
                      {activeTab !== 'all' && (
                        <p className="text-gray-400 mt-1">Try selecting a different filter.</p>
                      )}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default Notifications;
