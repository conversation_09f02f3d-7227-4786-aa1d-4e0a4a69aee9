import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// This component redirects from the old /organization/users path to the organization dashboard
const OrganizationUsersRedirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to the organisation dashboard instead
    navigate('/organisation/dashboard', { replace: true });
  }, [navigate]);

  return null; // This component doesn't render anything
};

export default OrganizationUsersRedirect;
