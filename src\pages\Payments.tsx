
import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PoundSterling, Clock, CreditCard } from 'lucide-react';

const Payments = () => {
  return (
    <MainLayout>
      <div className="container mx-auto py-12 px-4">
        <div className="max-w-2xl mx-auto text-center">
          <div className="mb-8">
            <PoundSterling className="h-16 w-16 text-classtasker-blue mx-auto mb-4" />
            <h1 className="text-3xl font-bold mb-4">Payments & Billing</h1>
            <p className="text-xl text-gray-600">Coming Soon</p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-center">
                <Clock className="mr-2 h-5 w-5 text-classtasker-blue" />
                Payment Management
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-lg text-gray-700">
                You'll soon be able to manage all payments here
              </p>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <CreditCard className="h-8 w-8 text-classtasker-blue mx-auto mb-2" />
                  <h3 className="font-semibold mb-1">Payment Methods</h3>
                  <p className="text-sm text-gray-600">Add and manage your payment cards</p>
                </div>

                <div className="p-4 bg-gray-50 rounded-lg">
                  <PoundSterling className="h-8 w-8 text-classtasker-blue mx-auto mb-2" />
                  <h3 className="font-semibold mb-1">Transaction History</h3>
                  <p className="text-sm text-gray-600">View all your payments and earnings</p>
                </div>
              </div>

              <div className="pt-4 border-t">
                <p className="text-sm text-gray-500">
                  This feature is currently in development and will be available in a future update.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default Payments;
