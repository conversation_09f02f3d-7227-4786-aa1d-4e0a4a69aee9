import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// FormLabel is not needed, we're using Label instead
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CalendarIcon, AlertCircle, User, MapPin } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useRolePermissions } from '@/hooks/useRolePermissions';
import notificationService from '@/services/notificationService';
import { useTasks } from '@/hooks/use-tasks';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { supabase } from '@/integrations/supabase/client';
import LocationSearch from '@/components/ui/location-search';
import { getCoordinates } from '@/utils/location-utils';
import { FileUpload } from '@/components/ui/file-upload';
import { CREATION_CATEGORIES } from '@/constants/categories';

const PostTask = () => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState('');
  const [building, setBuilding] = useState('');
  const [room, setRoom] = useState('');
  const [category, setCategory] = useState('');
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  // New state for location data
  const [organizationLocation, setOrganizationLocation] = useState('');
  const [isLoadingOrgLocation, setIsLoadingOrgLocation] = useState(false);
  const [locationCoordinates, setLocationCoordinates] = useState<{lat: number, lng: number} | null>(null);
  const [useOrgLocation, setUseOrgLocation] = useState(true);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);

  const { toast } = useToast();
  const { user, isSchool, profile, organizationId } = useAuth();
  const { hasPermission } = useRolePermissions();
  const { createTask, isCreatingTask } = useTasks();
  const navigate = useNavigate();

  // Fetch organization location when component mounts
  useEffect(() => {
    if (organizationId) {
      fetchOrganizationLocation(organizationId);
    }
  }, [organizationId]);

  // Function to fetch organization location
  const fetchOrganizationLocation = async (orgId: string) => {
    setIsLoadingOrgLocation(true);
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('location_formatted, location_lat, location_lng, address')
        .eq('id', orgId)
        .single();

      if (error) {
        console.error('Error fetching organization location:', error);
        return;
      }

      if (data) {
        // Use formatted location if available, otherwise use address
        const locationText = data.location_formatted || data.address || '';
        setOrganizationLocation(locationText);

        // If using organization location, set it as the task location
        if (useOrgLocation && locationText) {
          setLocation(locationText);

          // Store coordinates if available
          if (data.location_lat && data.location_lng) {
            setLocationCoordinates({
              lat: data.location_lat,
              lng: data.location_lng
            });
          }
        }
      }
    } catch (error) {
      console.error('Error in fetchOrganizationLocation:', error);
    } finally {
      setIsLoadingOrgLocation(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!title || !description || !location || !category || !dueDate) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please fill in all required fields.",
      });
      return;
    }

    // Building and room are optional, but we'll include them if provided

    if (!user) {
      toast({
        variant: "destructive",
        title: "Authentication required",
        description: "Please log in to post a task.",
      });
      return;
    }

    if (!isSchool || !hasPermission('create_tasks')) {
      toast({
        variant: "destructive",
        title: "Permission denied",
        description: "You don't have permission to create tasks.",
      });
      return;
    }

    // Create task in database
    const taskData = {
      title,
      description,
      location,
      building,
      room,
      category,
      budget: 0, // Default budget is 0, will be set by admin when making public
      due_date: dueDate.toISOString(),
      // Add location coordinates if available
      ...(locationCoordinates ? {
        location_lat: locationCoordinates.lat,
        location_lng: locationCoordinates.lng,
        location_formatted: location
      } : {}),
      // Add organization ID for reference
      organization_id: organizationId,
      // Add uploaded images if any
      ...(uploadedImages.length > 0 ? { images: uploadedImages } : {})
    };

    // All tasks start with admin visibility and open status
    // Admin will review and assign them later

    createTask(taskData, {
      onSuccess: (data) => {
        // Create notification for task creation
        if (user && data && data.id) {
          notificationService.createTaskUpdateNotification(
            user.id,
            data.id,
            title,
            'created',
            true // Send email
          );
        } else {
          console.log('Task created but notification could not be sent - missing data:', data);
        }

        // Reset the form
        setTitle('');
        setDescription('');
        setLocation('');
        setBuilding('');
        setRoom('');
        setCategory('');
        setDueDate(undefined);
        setUploadedImages([]);

        // Show success message
        toast({
          title: "Task created successfully",
          description: "Your task has been created and is pending review by an administrator.",
        });

        // Redirect to dashboard instead of tasks page
        // This ensures teachers can see their pending tasks in the dashboard
        navigate('/dashboard?tab=my-tasks');
      }
    });
  };

  // If user is not logged in or is a supplier, show access denied
  if (!user) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Authentication Required</AlertTitle>
            <AlertDescription>
              Please log in to post a task.
            </AlertDescription>
          </Alert>
        </div>
      </MainLayout>
    );
  }

  if (!isSchool || !hasPermission('create_tasks')) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Access Denied</AlertTitle>
            <AlertDescription>
              You don't have permission to create tasks. Only teachers, admins, maintenance, and support staff can create tasks.
            </AlertDescription>
          </Alert>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold mb-2">Post a Task</h1>
            <p className="text-gray-600">
              Describe your task in detail to get the best responses from suppliers.
            </p>
          </div>

          <Card>
            <CardContent className="p-6 md:p-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Task Title <span className="text-red-500">*</span></Label>
                  <Input
                    id="title"
                    placeholder="e.g. School Playground Equipment Repair"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description <span className="text-red-500">*</span></Label>
                  <Textarea
                    id="description"
                    placeholder="Provide a detailed description of the task..."
                    rows={6}
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    required
                  />
                  <p className="text-sm text-gray-500">
                    Include all details suppliers will need to know, such as specific requirements, materials needed, or any special instructions.
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Location Section */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Location Information</h3>

                    {/* Main Location */}
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label htmlFor="location">School Address <span className="text-red-500">*</span></Label>
                        {organizationLocation && (
                          <div className="flex items-center space-x-2">
                            <Switch
                              id="use-org-location"
                              checked={useOrgLocation}
                              onCheckedChange={(checked) => {
                                setUseOrgLocation(checked);
                                if (checked) {
                                  // Use organization location
                                  setLocation(organizationLocation);
                                  // Fetch coordinates if needed
                                  if (!locationCoordinates && organizationLocation) {
                                    getCoordinates(organizationLocation).then(coords => {
                                      if (coords) {
                                        setLocationCoordinates(coords);
                                      }
                                    });
                                  }
                                } else {
                                  // Clear location if unchecked
                                  setLocation('');
                                  setLocationCoordinates(null);
                                }
                              }}
                            />
                            <Label htmlFor="use-org-location" className="text-xs">
                              Use organization address
                            </Label>
                          </div>
                        )}
                      </div>

                      <div className="relative">
                        <LocationSearch
                          value={location}
                          onChange={async (newLocation) => {
                            setLocation(newLocation);
                            setUseOrgLocation(newLocation === organizationLocation);

                            // Get coordinates for the new location
                            if (newLocation && newLocation !== organizationLocation) {
                              try {
                                const coords = await getCoordinates(newLocation);
                                if (coords) {
                                  setLocationCoordinates(coords);
                                }
                              } catch (error) {
                                console.error('Error getting coordinates:', error);
                              }
                            }
                          }}
                          placeholder="Enter school address"
                          label=""
                          className=""
                        />

                        {isLoadingOrgLocation && (
                          <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                            <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-opacity-50 border-t-transparent rounded-full"></div>
                          </div>
                        )}
                      </div>

                      {organizationLocation && (
                        <p className="text-xs text-gray-500">
                          <MapPin className="inline-block h-3 w-3 mr-1" />
                          Organization address: {organizationLocation}
                        </p>
                      )}
                    </div>

                    {/* Building and Room */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="building">Building</Label>
                        <Input
                          id="building"
                          placeholder="e.g. Main Building, Science Block"
                          value={building}
                          onChange={(e) => setBuilding(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="room">Classroom/Room</Label>
                        <Input
                          id="room"
                          placeholder="e.g. Room 101, Science Lab 3"
                          value={room}
                          onChange={(e) => setRoom(e.target.value)}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Category Section */}
                  <div className="space-y-2">
                    <Label htmlFor="category">Category <span className="text-red-500">*</span></Label>
                    <Select value={category} onValueChange={setCategory} required>
                      <SelectTrigger id="category">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {CREATION_CATEGORIES.map((cat) => (
                          <SelectItem key={cat} value={cat}>
                            {cat}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="due-date">Due Date <span className="text-red-500">*</span></Label>
                  <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        id="due-date"
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !dueDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dueDate ? format(dueDate, "PPP") : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={dueDate}
                        onSelect={(date) => {
                          setDueDate(date);
                          setIsDatePickerOpen(false); // Close the popover immediately after selection
                        }}
                        initialFocus
                        disabled={(date) => {
                          const today = new Date();
                          today.setHours(0, 0, 0, 0); // Set to start of today
                          return date < today;
                        }}
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <FileUpload
                    onImagesUploaded={(urls) => {
                      setUploadedImages(prev => [...prev, ...urls]);
                    }}
                    maxFiles={5}
                  />
                  <p className="text-sm text-gray-500">
                    Upload up to 5 images to help describe the task. Images will be visible to administrators and suppliers.
                  </p>
                </div>

                <div className="border-t pt-4 mt-4">
                  <Alert className="mb-4 bg-blue-50 border-blue-200">
                    <AlertTitle className="text-blue-800">Task Review Process</AlertTitle>
                    <AlertDescription className="text-blue-700">
                      Your task will be reviewed by an administrator who will either assign it to internal staff or make it available to external suppliers.
                    </AlertDescription>
                  </Alert>

                  <Button
                    type="submit"
                    className="w-full bg-classtasker-blue hover:bg-blue-600"
                    size="lg"
                    disabled={isCreatingTask}
                  >
                    {isCreatingTask ? "Posting..." : "Post Task for Review"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4">Tips for Getting Great Responses</h2>
            <ul className="space-y-2 list-disc pl-5 text-gray-600">
              <li>Be as specific as possible about your requirements</li>
              <li>Include any relevant measurements or specifications</li>
              <li>Specify the building and room to help suppliers locate the task precisely</li>
              <li>Mention your preferred timeline for completion</li>
              <li>Add photos to help suppliers understand the task better</li>
              <li>Provide clear details to help administrators set an appropriate budget</li>
            </ul>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

// We're using the Label component directly, no need for a custom FormLabel

export default PostTask;
