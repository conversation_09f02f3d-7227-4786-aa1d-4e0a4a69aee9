
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/contexts/AuthContext';
import { useProfile } from '@/hooks/use-profile';
import { useTasks } from '@/hooks/use-tasks';
import { EditProfileForm } from '@/components/profile/EditProfileForm';
import ProfileCard from '@/components/profile/ProfileCard';
import ProfileSkills from '@/components/profile/ProfileSkills';
import ProfileAbout from '@/components/profile/ProfileAbout';
import ProfileBadges from '@/components/profile/ProfileBadges';
import BusinessProfileForm from '@/components/profile/BusinessProfileForm';
import { organizationService } from '@/services/organizationService';
import { Organization } from '@/types/organization';
import { toast } from '@/hooks/use-toast';

const Profile = () => {
  const { id } = useParams<{ id: string }>();
  const { user, isAdmin, organizationId } = useAuth();
  const { profile, isLoading } = useProfile(id);
  const [isEditing, setIsEditing] = useState(false);
  const [isEditingBusiness, setIsEditingBusiness] = useState(false);
  const [activeTab, setActiveTab] = useState('about');

  // Ensure activeTab is valid after removing tabs
  useEffect(() => {
    // Valid tabs: 'about' and 'business' for suppliers
    const validTabs = ['about'];
    if (profile?.account_type === 'supplier') {
      validTabs.push('business');
    }

    if (!validTabs.includes(activeTab)) {
      setActiveTab('about');
    }
  }, [activeTab, profile?.account_type]);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [isLoadingOrg, setIsLoadingOrg] = useState(false);
  const navigate = useNavigate();

  // Get tasks created by this user
  const { getTasksByUserId, isLoadingUserTasks } = useTasks();
  const { data: userTasks } = getTasksByUserId(id);

  const isOwnProfile = user && id === user.id;

  // Fetch organization details if user is part of an organization
  useEffect(() => {
    if (isOwnProfile && user) {
      setIsLoadingOrg(true);

      // Prioritize database values over metadata
      const userRole = profile?.role;
      const hasOrgId = !!profile?.organization_id;

      // Debug information (development only)
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('Profile organization_id:', profile?.organization_id);

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('Profile role:', profile?.role);

          }
      }

      // If profile has organization_id, fetch the actual organization from the database
      if (hasOrgId) {
        // Fetch the actual organization data
        organizationService.getOrganization(profile.organization_id)
          .then(orgData => {
            if (process.env.NODE_ENV === 'development') {
              if (process.env.NODE_ENV === 'development') {

                console.log('Fetched organization data:', orgData);

                }
            }
            setOrganization(orgData);
            setIsLoadingOrg(false);
          })
          .catch(error => {
            console.error('Error fetching organization:', error);
            // If fetching fails, create a mock org as fallback
            const mockOrg = {
              id: profile.organization_id,
              name: 'Your School',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };
            setOrganization(mockOrg);
            setIsLoadingOrg(false);
            toast({
              variant: "destructive",
              title: "Error loading organization",
              description: "Could not load organization details.",
            });
          });
      } else if (userRole) {
        // If user has a role but no organization, create a mock one
        const mockOrg = {
          id: crypto.randomUUID(),
          name: 'Your School',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        setOrganization(mockOrg);
      }

      setIsLoadingOrg(false);
    }
  }, [user, profile, isOwnProfile]);



  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:col-span-1 space-y-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center">
                    <Skeleton className="h-24 w-24 rounded-full" />
                    <Skeleton className="h-6 w-32 mt-4" />
                    <Skeleton className="h-4 w-24 mt-2" />
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="md:col-span-2">
              <Card>
                <CardContent className="p-6">
                  <Skeleton className="h-6 w-32 mb-4" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4" />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!profile) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card>
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold mb-2">User Not Found</h2>
              <p className="text-gray-600">The user profile you're looking for doesn't exist or has been removed.</p>
              <Button
                onClick={() => navigate('/')}
                className="mt-4"
              >
                Return to Home
              </Button>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  const userFullName = profile.first_name && profile.last_name
    ? `${profile.first_name} ${profile.last_name}`
    : profile.first_name || "User";

  const handleEditComplete = () => {
    setIsEditing(false);
  };

  if (isEditing && isOwnProfile) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="mb-6">
            <h1 className="text-2xl font-bold mb-2">Edit Your Profile</h1>
            <p className="text-gray-600">Update your personal information and preferences</p>
          </div>
          <EditProfileForm
            initialData={{
              firstName: profile.first_name || '',
              lastName: profile.last_name || '',
              email: user?.email || '',
              jobTitle: profile.job_title || '',
              location: profile.location || '',
              bio: profile.bio || '',
              avatarUrl: profile.avatar_url || '',
            }}
            onSuccess={handleEditComplete}
            onPhotoUpdated={(url) => {
              // This would typically trigger a profile refresh
              // For now, we'll just show a success message
              toast({
                title: "Profile photo updated",
                description: "Your profile photo has been updated successfully.",
              });
            }}
          />
        </div>
      </MainLayout>
    );
  }

  // Business profile editing view for suppliers
  if (isEditingBusiness && isOwnProfile && profile.account_type === 'supplier') {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="mb-6">
            <h1 className="text-2xl font-bold mb-2">Edit Business Profile</h1>
            <p className="text-gray-600">Update your business information and services</p>
          </div>
          <BusinessProfileForm
            initialData={{
              business_name: profile.business_name || '',
              business_description: profile.business_description || '',
              address: profile.address || '',
              city: profile.city || '',
              postcode: profile.postcode || '',
              phone: profile.phone || '',
              website: profile.website || '',
              services: profile.services || '',
            }}
            onSuccess={() => setIsEditingBusiness(false)}
          />
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Sidebar */}
          <div className="md:col-span-1 space-y-6">
            {/* Profile Card */}
            <ProfileCard
              profile={profile}
              isOwnProfile={isOwnProfile}
              userTasks={userTasks}
              userFullName={userFullName}
              onEditProfile={() => setIsEditing(true)}
            />

            {/* Skills/Expertise */}
            <ProfileSkills profile={profile} />

            {/* Badges & Achievements */}
            <ProfileBadges
              userId={profile.id}
              accountType={profile.account_type || 'school'}
              tasksCreated={userTasks?.length || 0}
              tasksCompleted={userTasks?.filter(task => task.status === 'completed').length || 0}

              memberSince={profile.created_at}
            />

            {/* Organization Info */}
            {isOwnProfile && (organization || profile?.role || profile?.organization_id) && (
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-2">Organization</h3>
                  <p className="text-gray-600 mb-1">{organization?.name || 'Your School'}</p>
                  {organization?.address && (
                    <p className="text-gray-600 text-sm">{organization.address}</p>
                  )}
                  {organization?.city && organization?.state && (
                    <p className="text-gray-600 text-sm">
                      {organization.city}, {organization.state} {organization.zip}
                    </p>
                  )}
                  {profile?.role && (
                    <div className="mt-2 pt-2 border-t">
                      <p className="text-sm">
                        <span className="text-gray-500">Role:</span>{' '}
                        <span className="font-medium capitalize">{profile.role}</span>
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Organization Setup Button */}
            {isOwnProfile && !organization && !profile?.role && !profile?.organization_id && profile?.account_type === 'school' && (
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-2">School Organization</h3>
                  <p className="text-gray-600 mb-4">Set up your school organization to manage users and tasks</p>
                  <Button onClick={() => navigate('/organisation/setup')}>
                    Set Up Organisation
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Main Content */}
          <div className="md:col-span-2">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="about">About</TabsTrigger>
                {profile.account_type === 'supplier' && (
                  <TabsTrigger value="business">Business</TabsTrigger>
                )}
              </TabsList>

              <TabsContent value="about">
                <ProfileAbout profile={profile} userTasks={userTasks} />
              </TabsContent>

              {profile.account_type === 'supplier' && (
                <TabsContent value="business">
                  <Card>
                    <CardContent className="p-6">
                      {profile.business_name ? (
                        <>
                          <div className="flex justify-between items-start mb-4">
                            <h2 className="text-xl font-semibold">{profile.business_name}</h2>
                            {isOwnProfile && (
                              <Button variant="outline" size="sm" onClick={() => setIsEditingBusiness(true)}>
                                Edit Business Profile
                              </Button>
                            )}
                          </div>

                          {profile.business_description && (
                            <div className="mb-4">
                              <h3 className="text-md font-medium mb-2">About the Business</h3>
                              <p className="text-gray-700">{profile.business_description}</p>
                            </div>
                          )}

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            {(profile.address || profile.city || profile.postcode) && (
                              <div>
                                <h3 className="text-md font-medium mb-1">Location</h3>
                                <p className="text-gray-700">
                                  {profile.address && <span className="block">{profile.address}</span>}
                                  {profile.city && profile.postcode && (
                                    <span className="block">{profile.city}, {profile.postcode}</span>
                                  )}
                                </p>
                              </div>
                            )}

                            {profile.phone && (
                              <div>
                                <h3 className="text-md font-medium mb-1">Contact</h3>
                                <p className="text-gray-700">{profile.phone}</p>
                                {profile.website && (
                                  <a href={profile.website} target="_blank" rel="noopener noreferrer"
                                     className="text-classtasker-blue hover:underline">
                                    {profile.website.replace(/^https?:\/\//, '')}
                                  </a>
                                )}
                              </div>
                            )}
                          </div>

                          {profile.services && (
                            <div>
                              <h3 className="text-md font-medium mb-2">Services Offered</h3>
                              <div className="flex flex-wrap gap-2">
                                {profile.services.split(',').map((service, index) => (
                                  <span key={index} className="bg-gray-100 text-gray-800 px-2 py-1 rounded-md text-sm">
                                    {service.trim()}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </>
                      ) : isOwnProfile ? (
                        <div className="text-center py-8">
                          <h3 className="text-lg font-medium mb-2">Complete Your Business Profile</h3>
                          <p className="text-gray-600 mb-4">
                            Add information about your business to help potential clients learn more about your services.
                          </p>
                          <Button onClick={() => setIsEditingBusiness(true)}>
                            Set Up Business Profile
                          </Button>
                        </div>
                      ) : (
                        <p className="text-gray-600 text-center py-6">
                          This user hasn't added any business information yet.
                        </p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              )}
            </Tabs>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Profile;
