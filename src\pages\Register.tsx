
import { useState, useEffect } from "react";
import { Link, Navigate, useSearchParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import MainLayout from "@/components/layout/MainLayout";
import { useAuth } from "@/contexts/AuthContext";
import { Loader2, School, Briefcase, Mail, CheckCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { supabase } from "@/integrations/supabase/client";

const Register = () => {
  const [searchParams] = useSearchParams();
  const emailFromUrl = searchParams.get('email');

  const [name, setName] = useState("");
  const [email, setEmail] = useState(emailFromUrl || "");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [accountType, setAccountType] = useState("school");
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [marketingConsent, setMarketingConsent] = useState(true); // Pre-ticked for GDPR compliance
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [invitationToken, setInvitationToken] = useState<string | null>(null);
  const [showEmailConfirmation, setShowEmailConfirmation] = useState(false);
  const { signUp, user } = useAuth();
  const navigate = useNavigate();

  // Update email if URL parameter changes and check for invitation token
  useEffect(() => {
    if (emailFromUrl) {
      setEmail(emailFromUrl);
    }

    // Check for invitation token in URL
    const token = searchParams.get('token');
    if (token) {
      setInvitationToken(token);
      console.log('Found invitation token in URL:', token);
    }
  }, [emailFromUrl, searchParams]);

  // Redirect if already logged in
  if (user) {
    return <Navigate to="/dashboard" replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Basic validation
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (!agreeTerms) {
      setError("Please agree to the terms and conditions");
      return;
    }

    try {
      setIsSubmitting(true);
      await signUp(email, password, name, accountType, marketingConsent);

      // Show email confirmation message for all users
      setShowEmailConfirmation(true);

      // If we have an invitation token, store it in localStorage for later processing
      if (invitationToken) {
        console.log('Storing invitation token for confirmation page:', invitationToken);
        localStorage.setItem('pendingInvitationToken', invitationToken);
        localStorage.setItem('pendingInvitationEmail', email);
        localStorage.setItem('newUserWithInvitation', 'true');

        // Try to get organization name if possible
        try {
          const { data } = await supabase
            .from('user_invitations')
            .select('organization_id')
            .eq('token', invitationToken)
            .single();

          if (data?.organization_id) {
            const { data: orgData } = await supabase
              .from('organizations')
              .select('name')
              .eq('id', data.organization_id)
              .single();

            if (orgData?.name) {
              localStorage.setItem('pendingInvitationOrgName', orgData.name);
            }
          }
        } catch (error) {
          console.error('Error fetching organization name:', error);
        }
      }
    } catch (error) {
      console.error("Registration error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show email confirmation message after successful registration
  if (showEmailConfirmation) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-lg mx-auto">
            <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
              <div className="mb-6">
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">Account Created Successfully!</h1>
                <p className="text-gray-600">
                  We've sent a confirmation email to <strong>{email}</strong>
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-center mb-2">
                  <Mail className="w-5 h-5 text-blue-600 mr-2" />
                  <h3 className="font-semibold text-blue-900">Check Your Email</h3>
                </div>
                <p className="text-blue-800 text-sm">
                  Please click the confirmation link in your email to activate your account and start using Classtasker.
                </p>
              </div>

              <div className="space-y-3 text-sm text-gray-600">
                <p><strong>What's next?</strong></p>
                <ul className="text-left space-y-1">
                  <li>• Check your email inbox (and spam folder)</li>
                  <li>• Click the confirmation link</li>
                  <li>• Set up your {accountType === 'school' ? 'school' : 'supplier'} profile</li>
                  <li>• Start {accountType === 'school' ? 'posting tasks' : 'finding opportunities'}</li>
                </ul>
              </div>

              <div className="mt-6 pt-6 border-t">
                <p className="text-sm text-gray-500">
                  Didn't receive the email?{" "}
                  <button
                    onClick={() => setShowEmailConfirmation(false)}
                    className="text-classtasker-blue hover:underline font-medium"
                  >
                    Try again
                  </button>
                </p>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-lg mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold">Create Your Account</h1>
            <p className="text-gray-600 mt-2">
              Join Classtasker to start posting or fulfilling tasks
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-8">
            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  type="text"
                  placeholder="John Doe"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  disabled={isSubmitting}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={isSubmitting || !!emailFromUrl}
                />
                {emailFromUrl && (
                  <p className="text-sm text-gray-500 mt-1">
                    This email is from your invitation and cannot be changed.
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm Password</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    placeholder="••••••••"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                    disabled={isSubmitting}
                  />
                </div>
              </div>

              <div className="space-y-3">
                <Label>Account Type</Label>
                <RadioGroup
                  defaultValue="school"
                  value={accountType}
                  onValueChange={setAccountType}
                  className="flex flex-col space-y-3"
                  disabled={isSubmitting}
                >
                  <div className="flex items-center space-x-3 border rounded-md p-3 hover:bg-gray-50 cursor-pointer">
                    <div className={`rounded-full p-2 ${accountType === 'school' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100'}`}>
                      <School size={18} />
                    </div>
                    <div className="flex-grow">
                      <RadioGroupItem value="school" id="school" className="sr-only" />
                      <Label htmlFor="school" className="font-medium block">
                        School Account
                      </Label>
                      <span className="text-sm text-gray-600">For educational institutions to post tasks</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 border rounded-md p-3 hover:bg-gray-50 cursor-pointer">
                    <div className={`rounded-full p-2 ${accountType === 'supplier' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100'}`}>
                      <Briefcase size={18} />
                    </div>
                    <div className="flex-grow">
                      <RadioGroupItem value="supplier" id="supplier" className="sr-only" />
                      <Label htmlFor="supplier" className="font-medium block">
                        Supplier Account
                      </Label>
                      <span className="text-sm text-gray-600">For service providers to complete tasks</span>
                    </div>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="terms"
                    checked={agreeTerms}
                    onCheckedChange={(checked) => setAgreeTerms(checked as boolean)}
                    required
                    disabled={isSubmitting}
                  />
                  <Label
                    htmlFor="terms"
                    className="text-sm font-normal leading-none"
                  >
                    I agree to the{" "}
                    <Link to="/terms" className="text-classtasker-blue hover:underline">
                      Terms of Service
                    </Link>{" "}
                    and{" "}
                    <Link to="/privacy" className="text-classtasker-blue hover:underline">
                      Privacy Policy
                    </Link>
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="marketing"
                    checked={marketingConsent}
                    onCheckedChange={(checked) => setMarketingConsent(checked as boolean)}
                    disabled={isSubmitting}
                  />
                  <Label
                    htmlFor="marketing"
                    className="text-sm font-normal leading-none"
                  >
                    I would like to receive marketing communications, product updates, and special offers from Classtasker. You can unsubscribe at any time.
                  </Label>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full bg-classtasker-blue hover:bg-blue-600"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Account...
                  </>
                ) : (
                  "Create Account"
                )}
              </Button>
            </form>

            <div className="mt-6 text-center text-sm">
              <p className="text-gray-600">
                Already have an account?{" "}
                <Link to="/login" className="text-classtasker-blue hover:underline font-medium">
                  Sign in
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Register;

// SECURITY: Test accounts removed from production code
// Use environment variables or separate test files for test credentials
