import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const ResetPasswordDirect: React.FC = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [initializing, setInitializing] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  // Process the URL parameters on component mount
  useEffect(() => {
    const processResetToken = async () => {
      try {
        if (process.env.NODE_ENV === 'development') {
          console.log('Processing reset password URL:', window.location.href);
        
          }
        // Get URL parameters
        const searchParams = new URLSearchParams(window.location.search);
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        
        // Log all parameters for debugging
        if (process.env.NODE_ENV === 'development') {
          console.log('Search params:', Object.fromEntries(searchParams.entries()));
          }
        if (process.env.NODE_ENV === 'development') {
          console.log('Hash params:', Object.fromEntries(hashParams.entries()));
        
          }
        // Check for token in hash (fragment)
        const accessToken = hashParams.get('access_token');
        const refreshToken = hashParams.get('refresh_token');
        const type = hashParams.get('type');
        
        if (accessToken && refreshToken && type === 'recovery') {
          if (process.env.NODE_ENV === 'development') {
            console.log('Found tokens in URL hash, setting session');
          
            }
          // Set the session from the recovery tokens
          const { error } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          });
          
          if (error) {
            console.error('Error setting session from hash tokens:', error);
            throw error;
          }
          
          if (process.env.NODE_ENV === 'development') {
    console.log('Session set successfully');
  }
          setInitializing(false);
          return;
        }
        
        // Check for token in query parameters
        const token = searchParams.get('token');
        const queryType = searchParams.get('type');
        
        if (token && (queryType === 'recovery' || queryType === 'passwordRecovery')) {
          if (process.env.NODE_ENV === 'development') {
            console.log('Found token in query parameters, verifying OTP');
          
            }
          try {
            // Try to exchange the token for a session
            const { error } = await supabase.auth.verifyOtp({
              token_hash: token,
              type: 'recovery'
            });
            
            if (error) {
              console.error('Error verifying OTP:', error);
              throw error;
            }
            
            if (process.env.NODE_ENV === 'development') {
    console.log('OTP verified successfully');
  }
            setInitializing(false);
            return;
          } catch (verifyError) {
            console.error('Error verifying recovery token:', verifyError);
            throw verifyError;
          }
        }
        
        // Check if we already have a session
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session) {
          if (process.env.NODE_ENV === 'development') {
            console.log('Active session found');
            }
          setInitializing(false);
          return;
        }
        
        // If we reach here, we couldn't process the reset token
        console.error('No valid reset token found in URL');
        throw new Error('Invalid or expired password reset link. Please request a new one.');
      } catch (err: any) {
        console.error('Error processing reset token:', err);
        setError(err.message || 'Invalid or expired password reset link. Please request a new one.');
        setInitializing(false);
      }
    };
    
    processResetToken();
  }, [location]);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!password) {
      setError('Please enter a new password');
      return;
    }
    
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }
    
    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('Updating password...');
      
        }
      // Update the user's password
      const { error } = await supabase.auth.updateUser({
        password: password
      });
      
      if (error) {
        console.error('Error updating password:', error);
        throw error;
      }
      
      if (process.env.NODE_ENV === 'development') {
    console.log('Password updated successfully');
  }
      // Show success message
      setSuccess(true);
      toast({
        title: 'Password updated',
        description: 'Your password has been successfully reset',
      });
      
      // Redirect to login after a short delay
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (err: any) {
      console.error('Error resetting password:', err);
      setError(err.message || 'Failed to reset password. Please try again.');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to reset password. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  if (initializing) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8 text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <p>Processing your password reset request...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-bold tracking-tight text-gray-900">
            Reset your password
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Enter a new password for your account
          </p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Create New Password</CardTitle>
            <CardDescription>
              Your password must be at least 8 characters long
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            {error ? (
              <div className="space-y-4">
                <Alert className="mb-4" variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
                
                <div className="flex justify-center">
                  <Button variant="outline" onClick={() => navigate('/forgot-password')}>
                    Try Again
                  </Button>
                </div>
              </div>
            ) : success ? (
              <Alert className="mb-4 bg-green-50 border-green-200">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertTitle className="text-green-800">Password Updated</AlertTitle>
                <AlertDescription className="text-green-700">
                  Your password has been successfully reset. You will be redirected to the login page.
                </AlertDescription>
              </Alert>
            ) : (
              <form onSubmit={handleResetPassword}>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="password">New Password</Label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      minLength={8}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      minLength={8}
                    />
                  </div>
                  
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={loading}
                  >
                    {loading ? 'Resetting...' : 'Reset Password'}
                  </Button>
                </div>
              </form>
            )}
          </CardContent>
          
          <CardFooter className="flex justify-center">
            {(error || success) && (
              <Button variant="link" onClick={() => navigate('/login')}>
                Return to Login
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default ResetPasswordDirect;
