import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { 
  School, 
  FileText, 
  Users, 
  Shield, 
  CheckCircle, 
  BookOpen, 
  Download,
  ExternalLink,
  Clock,
  PoundSterling
} from 'lucide-react';

const SchoolResources = () => {
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <School className="h-16 w-16 text-classtasker-blue" />
          </div>
          <h1 className="text-4xl font-bold mb-4">School Resources</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Everything you need to get the most out of Classtasker for your educational institution
          </p>
        </div>

        {/* Quick Start Guide */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="h-6 w-6 mr-2 text-green-600" />
              Quick Start Guide
            </CardTitle>
            <CardDescription>
              Get up and running with Classtasker in just a few simple steps
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 font-bold">1</span>
                </div>
                <h3 className="font-semibold mb-2">Create Your Account</h3>
                <p className="text-sm text-gray-600">Sign up and set up your school organization profile</p>
              </div>
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 font-bold">2</span>
                </div>
                <h3 className="font-semibold mb-2">Post Your First Task</h3>
                <p className="text-sm text-gray-600">Create a maintenance task and get quotes from qualified suppliers</p>
              </div>
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 font-bold">3</span>
                </div>
                <h3 className="font-semibold mb-2">Manage & Track</h3>
                <p className="text-sm text-gray-600">Monitor progress and communicate with suppliers through our platform</p>
              </div>
            </div>
            <div className="text-center mt-6">
              <Button asChild>
                <Link to="/register?type=school">Get Started Now</Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Resource Categories */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {/* Best Practices */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                Best Practices
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• How to write effective task descriptions</li>
                <li>• Setting realistic budgets and timelines</li>
                <li>• Communicating with suppliers effectively</li>
                <li>• Managing multiple maintenance projects</li>
                <li>• Quality assurance and sign-off procedures</li>
              </ul>
            </CardContent>
          </Card>

          {/* User Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2 text-blue-600" />
                User Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• Setting up user roles and permissions</li>
                <li>• Inviting staff members to your organization</li>
                <li>• Managing caretaker and maintenance team access</li>
                <li>• Teacher task creation permissions</li>
                <li>• Admin dashboard overview</li>
              </ul>
            </CardContent>
          </Card>

          {/* Compliance & Safety */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2 text-red-600" />
                Compliance & Safety
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• Ensuring supplier qualifications and insurance</li>
                <li>• Health and safety compliance requirements</li>
                <li>• DBS checks and safeguarding procedures</li>
                <li>• Documentation and audit trails</li>
                <li>• Emergency maintenance protocols</li>
              </ul>
            </CardContent>
          </Card>

          {/* Budget Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <PoundSterling className="h-5 w-5 mr-2 text-green-600" />
                Budget Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• Setting up budget categories and limits</li>
                <li>• Tracking maintenance expenditure</li>
                <li>• Comparing quotes and value for money</li>
                <li>• Annual maintenance planning</li>
                <li>• Cost reporting and analytics</li>
              </ul>
            </CardContent>
          </Card>

          {/* Time Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2 text-purple-600" />
                Time Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• Scheduling maintenance during school holidays</li>
                <li>• Emergency vs. routine maintenance priorities</li>
                <li>• Coordinating with school calendar events</li>
                <li>• Minimizing disruption to learning</li>
                <li>• Planned preventive maintenance schedules</li>
              </ul>
            </CardContent>
          </Card>

          {/* Documentation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2 text-orange-600" />
                Documentation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• Maintenance request templates</li>
                <li>• Supplier evaluation forms</li>
                <li>• Health and safety checklists</li>
                <li>• Completion certificates and warranties</li>
                <li>• Incident reporting procedures</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Downloadable Resources */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Download className="h-6 w-6 mr-2 text-blue-600" />
              Downloadable Resources
            </CardTitle>
            <CardDescription>
              Free templates and guides to help you manage school maintenance effectively
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-lg p-4">
                <h4 className="font-semibold mb-2">Maintenance Request Template</h4>
                <p className="text-sm text-gray-600 mb-3">Standardized form for submitting maintenance requests</p>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
              </div>
              <div className="border rounded-lg p-4">
                <h4 className="font-semibold mb-2">Supplier Evaluation Checklist</h4>
                <p className="text-sm text-gray-600 mb-3">Criteria for assessing and selecting maintenance suppliers</p>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
              </div>
              <div className="border rounded-lg p-4">
                <h4 className="font-semibold mb-2">Annual Maintenance Planner</h4>
                <p className="text-sm text-gray-600 mb-3">Template for planning maintenance activities throughout the year</p>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download Excel
                </Button>
              </div>
              <div className="border rounded-lg p-4">
                <h4 className="font-semibold mb-2">Health & Safety Guidelines</h4>
                <p className="text-sm text-gray-600 mb-3">Essential safety requirements for school maintenance work</p>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Support Section */}
        <Card>
          <CardHeader>
            <CardTitle>Need Additional Support?</CardTitle>
            <CardDescription>
              Our team is here to help you make the most of Classtasker
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" asChild>
                <Link to="/help">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Help Center
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link to="/contact">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Contact Support
                </Link>
              </Button>
              <Button asChild>
                <Link to="/post-task">
                  Post Your First Task
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default SchoolResources;
