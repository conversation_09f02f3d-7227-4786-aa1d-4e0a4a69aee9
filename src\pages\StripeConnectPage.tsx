import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Loader2, AlertCircle, RefreshCw, ExternalLink } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { stripeService } from '@/services/stripeService';
import ConnectOnboarding from '@/components/stripe/ConnectOnboarding';

export default function StripeConnectPage() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [accountStatus, setAccountStatus] = useState<any>(null);
  const [payments, setPayments] = useState<any[]>([]);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (user) {
      fetchAccountStatus();
      fetchPayments();
    }
  }, [user]);

  const fetchAccountStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if the user already has a Stripe account (excluding deleted accounts)
      const { data, error } = await supabase
        .from('stripe_accounts')
        .select('*')
        .eq('user_id', user?.id)
        .neq('account_status', 'deleted')
        .maybeSingle();

      if (error) {
        throw error;
      }

      if (data) {
        setAccountStatus(data);

        // If the account exists, refresh the status from Stripe
        if (data.account_id) {
          // Use the account ID from the database
          const status = await stripeService.getAccountStatus(data.account_id);
          if (status) {
            setAccountStatus(prev => ({ ...prev, ...status }));
          }
        }
      }
    } catch (err) {
      console.error('Error fetching account status:', err);
      setError('Failed to fetch account status. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchPayments = async () => {
    try {
      if (!user) return;

      const { data, error } = await supabase
        .from('payments')
        .select(`
          *,
          tasks(*),
          offers(*)
        `)
        .eq('payee_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      setPayments(data || []);
    } catch (err) {
      console.error('Error fetching payments:', err);
    }
  };

  const handleRefreshStatus = () => {
    fetchAccountStatus();
    fetchPayments();
  };

  // handleDashboardAccess function removed - using inline function in the button instead

  const handleDeleteAccount = async () => {
    try {
      if (!accountStatus?.account_id) {
        throw new Error('No Stripe account found');
      }

      // Confirm deletion
      const confirmed = window.confirm(
        'Are you sure you want to delete your Stripe Connect account? This action cannot be undone.'
      );

      if (!confirmed) {
        return;
      }

      setIsDeleting(true);
      setError(null);

      // Call the service to delete the account
      // Use the account ID from the database
      const success = await stripeService.deleteAccount(accountStatus.account_id);

      if (!success) {
        throw new Error('Failed to delete Stripe account');
      }

      // Refresh the account status
      fetchAccountStatus();

      // Show success message
      alert('Your Stripe Connect account has been successfully deleted.');
    } catch (err) {
      console.error('Error deleting account:', err);
      setError('Failed to delete Stripe account. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    }).format(date);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(amount);
  };

  return (
    <div className="container py-8">
      <Helmet>
        <title>Stripe Connect | Class Tasker</title>
      </Helmet>

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Stripe Connect</h1>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshStatus}
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>

          {/* Stripe Dashboard button removed from header - using the one in the account section instead */}
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="account">
        <TabsList className="mb-6">
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <div className="grid gap-6">
            {!accountStatus?.account_id ? (
              <ConnectOnboarding onComplete={fetchAccountStatus} />
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Stripe Account Status</CardTitle>
                  <CardDescription>
                    Your Stripe Connect account details and status
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  {loading ? (
                    <div className="flex justify-center py-4">
                      <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Account Status:</span>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          accountStatus.charges_enabled && accountStatus.payouts_enabled
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {accountStatus.charges_enabled && accountStatus.payouts_enabled
                            ? 'Active'
                            : 'Pending'}
                        </span>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <div className={`w-4 h-4 rounded-full ${
                            accountStatus.charges_enabled ? 'bg-green-500' : 'bg-gray-300'
                          }`} />
                          <span>Payment processing: {accountStatus.charges_enabled ? 'Enabled' : 'Pending'}</span>
                        </div>

                        <div className="flex items-center gap-2">
                          <div className={`w-4 h-4 rounded-full ${
                            accountStatus.payouts_enabled ? 'bg-green-500' : 'bg-gray-300'
                          }`} />
                          <span>Payouts: {accountStatus.payouts_enabled ? 'Enabled' : 'Pending'}</span>
                        </div>
                      </div>

                      <Separator />

                      <div>
                        <h3 className="font-medium mb-2">Account Details</h3>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div className="text-gray-500">Account ID:</div>
                          <div>{accountStatus.account_id}</div>

                          <div className="text-gray-500">Created:</div>
                          <div>{formatDate(accountStatus.created_at)}</div>

                          <div className="text-gray-500">Last Updated:</div>
                          <div>{formatDate(accountStatus.updated_at)}</div>
                        </div>
                      </div>

                      <div className="mt-4 space-y-4">
                        {!accountStatus.charges_enabled && (
                          <div className="space-y-2">
                            <Button
                              onClick={() => {
                                // Generate a new onboarding link via API
                                fetch('http://localhost:3001/api/stripe-connect/onboarding-link', {
                                  method: 'POST',
                                  headers: {
                                    'Content-Type': 'application/json',
                                  },
                                  body: JSON.stringify({ accountId: accountStatus.account_id }),
                                })
                                .then(response => response.json())
                                .then(data => {
                                  if (data.url) {
                                    // Redirect to the onboarding URL
                                    window.location.href = data.url;
                                  }
                                })
                                .catch(err => {
                                  console.error('Error generating onboarding link:', err);
                                  setError('Failed to generate onboarding link. Please try again.');
                                });
                              }}
                              className="w-full"
                            >
                              Continue Onboarding
                            </Button>
                            <p className="text-xs text-gray-500">
                              Note: Click this button to continue the Stripe onboarding process. You'll be redirected to Stripe's
                              secure platform to complete your account setup.
                            </p>
                          </div>
                        )}

                        <div className="space-y-2">
                          <Button
                            onClick={() => {
                              if (!accountStatus.charges_enabled) {
                                alert('Cannot access the dashboard until onboarding is complete. Please complete the onboarding process first.');
                              } else {
                                // Call the server API to generate a dashboard link
                                fetch('http://localhost:3001/api/stripe-connect/dashboard-link', {
                                  method: 'POST',
                                  headers: {
                                    'Content-Type': 'application/json',
                                  },
                                  body: JSON.stringify({ accountId: accountStatus.account_id }),
                                })
                                .then(response => response.json())
                                .then(data => {
                                  if (data.url) {
                                    window.open(data.url, '_blank');
                                  }
                                })
                                .catch(err => {
                                  console.error('Error generating dashboard link:', err);
                                  setError('Failed to generate dashboard link. Please try again.');
                                });
                              }
                            }}
                            variant="outline"
                            className="w-full"
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            Access Stripe Dashboard
                          </Button>
                          {!accountStatus.charges_enabled && (
                            <p className="text-xs text-gray-500">
                              Note: You cannot access the dashboard until onboarding is complete.
                            </p>
                          )}
                        </div>

                        {/* Delete Account Button */}
                        <div className="space-y-2 mt-4">
                          <Button
                            onClick={handleDeleteAccount}
                            variant="destructive"
                            className="w-full"
                            disabled={isDeleting}
                          >
                            {isDeleting ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                Deleting...
                              </>
                            ) : (
                              'Delete Stripe Account'
                            )}
                          </Button>
                          <p className="text-xs text-gray-500">
                            Warning: This will permanently delete your Stripe Connect account. This action cannot be undone.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="payments">
          <Card>
            <CardHeader>
              <CardTitle>Payment History</CardTitle>
              <CardDescription>
                View your payment history and status
              </CardDescription>
            </CardHeader>

            <CardContent>
              {loading ? (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                </div>
              ) : payments.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No payments found
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2 px-2">Date</th>
                        <th className="text-left py-2 px-2">Task</th>
                        <th className="text-left py-2 px-2">Amount</th>
                        <th className="text-left py-2 px-2">Platform Fee</th>
                        <th className="text-left py-2 px-2">Your Earnings</th>
                        <th className="text-left py-2 px-2">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {payments.map((payment) => (
                        <tr key={payment.id} className="border-b hover:bg-gray-50">
                          <td className="py-2 px-2">{formatDate(payment.created_at)}</td>
                          <td className="py-2 px-2">
                            <Button
                              variant="link"
                              className="p-0 h-auto"
                              onClick={() => navigate(`/tasks/${payment.task_id}`)}
                            >
                              {payment.tasks?.title || 'Unknown Task'}
                            </Button>
                          </td>
                          <td className="py-2 px-2">{formatCurrency(payment.amount)}</td>
                          <td className="py-2 px-2">{formatCurrency(payment.platform_fee)}</td>
                          <td className="py-2 px-2">{formatCurrency(payment.supplier_amount)}</td>
                          <td className="py-2 px-2">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              payment.status === 'succeeded' ? 'bg-green-100 text-green-800' :
                              payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              payment.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}