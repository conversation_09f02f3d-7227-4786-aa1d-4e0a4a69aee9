import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { 
  HelpCircle, 
  ChevronDown, 
  ChevronUp,
  Briefcase,
  PoundSterling,
  Shield,
  Clock,
  Users,
  FileText
} from 'lucide-react';

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

const SupplierFAQ = () => {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const faqData: FAQItem[] = [
    {
      category: "Getting Started",
      question: "How do I become a Classtasker supplier?",
      answer: "To become a supplier, you need to register on our platform, complete the verification process (including DBS checks and insurance verification), and set up your profile with your skills and service areas. Once approved, you can start browsing and bidding on available jobs."
    },
    {
      category: "Getting Started",
      question: "What qualifications do I need?",
      answer: "Essential requirements include: Enhanced DBS check, public liability insurance (minimum £2 million), relevant trade qualifications, proof of business registration, and professional references. Additional certifications like Gas Safe or NICEIC are preferred for relevant trades."
    },
    {
      category: "Getting Started",
      question: "How long does the verification process take?",
      answer: "The verification process typically takes 5-10 business days, depending on how quickly you provide the required documentation. DBS checks can take longer if processed through us, so having a current enhanced DBS check speeds up the process."
    },
    {
      category: "Finding Work",
      question: "How do I find jobs on the platform?",
      answer: "Once verified, you can browse available jobs in your area through the 'Find Jobs' section. You can filter by location, trade type, budget, and urgency. Jobs are posted by schools and you can submit quotes for those that match your skills."
    },
    {
      category: "Finding Work",
      question: "How do I submit a competitive quote?",
      answer: "Include detailed breakdown of costs, materials, labour, and timeline. Be specific about what's included, provide clear start and completion dates, and highlight your relevant experience. Schools value transparency and professionalism in quotes."
    },
    {
      category: "Finding Work",
      question: "Can I work in multiple areas or trades?",
      answer: "Yes, you can offer services in multiple trades and cover multiple geographical areas. Make sure you have the appropriate qualifications and insurance for each trade you offer."
    },
    {
      category: "Payments",
      question: "How and when do I get paid?",
      answer: "Payment is processed through our secure system after job completion and school approval. Payments are typically made within 7-14 days of completion. You'll receive payment directly to your registered bank account."
    },
    {
      category: "Payments",
      question: "What are the platform fees?",
      answer: "Classtasker charges a small service fee on completed jobs to maintain the platform and provide support services. The fee structure is transparent and shown before you accept any job."
    },
    {
      category: "Payments",
      question: "What if there's a payment dispute?",
      answer: "We have a dispute resolution process to handle payment issues fairly. Contact our support team immediately if you encounter any payment problems. We work with both parties to resolve disputes quickly and fairly."
    },
    {
      category: "Working with Schools",
      question: "What should I expect when working in schools?",
      answer: "Schools have strict safeguarding and health & safety requirements. Always sign in at reception, wear ID badges, follow school policies, and be mindful of children's safety. Most work is scheduled during holidays or after hours to minimize disruption."
    },
    {
      category: "Working with Schools",
      question: "Do I need to work around school hours?",
      answer: "Many jobs can be scheduled during school holidays, weekends, or after hours. Some routine maintenance can be done during school hours with proper coordination. Schools are usually flexible and will work with you to find suitable times."
    },
    {
      category: "Working with Schools",
      question: "What if I need to access the school outside normal hours?",
      answer: "Schools will provide access arrangements for work outside normal hours. This might include key holder contact details, alarm codes, or arranging for a caretaker to be present. Always follow the school's security procedures."
    },
    {
      category: "Platform Usage",
      question: "How do I communicate with schools?",
      answer: "All communication should go through the platform's messaging system initially. This keeps a record of all discussions and protects both parties. Schools may provide direct contact details for urgent matters during active projects."
    },
    {
      category: "Platform Usage",
      question: "Can I work for schools directly outside the platform?",
      answer: "We encourage all work to go through the platform for protection and quality assurance. However, if schools approach you directly for additional work after a successful project, this is generally acceptable. Check our terms of service for specific guidelines."
    },
    {
      category: "Platform Usage",
      question: "How do I build my reputation on the platform?",
      answer: "Complete jobs on time and to high standards, communicate professionally, be responsive to messages, and go the extra mile for customer service. Schools can leave reviews and ratings that help build your reputation and attract more work."
    },
    {
      category: "Support",
      question: "What support is available if I have problems?",
      answer: "We provide comprehensive support through our help center, email support, and phone support during business hours. We also have resources, guides, and training materials to help you succeed on the platform."
    },
    {
      category: "Support",
      question: "What if I can't complete a job I've accepted?",
      answer: "Contact the school and our support team immediately if you can't complete an accepted job. We'll work to find a solution, which might include finding a replacement supplier. Frequent cancellations can affect your platform standing."
    },
    {
      category: "Support",
      question: "How do I report issues or provide feedback?",
      answer: "Use the contact form on our website, email support directly, or use the feedback options in your supplier dashboard. We value feedback and continuously improve the platform based on supplier and school input."
    }
  ];

  const categories = [...new Set(faqData.map(item => item.category))];

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <HelpCircle className="h-16 w-16 text-classtasker-blue" />
          </div>
          <h1 className="text-4xl font-bold mb-4">Supplier FAQ</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Find answers to common questions about working as a Classtasker supplier
          </p>
        </div>

        {/* Quick Links */}
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-12">
          {categories.map((category) => {
            const icons = {
              "Getting Started": Briefcase,
              "Finding Work": FileText,
              "Payments": PoundSterling,
              "Working with Schools": Shield,
              "Platform Usage": Users,
              "Support": HelpCircle
            };
            const Icon = icons[category as keyof typeof icons] || HelpCircle;
            
            return (
              <Button 
                key={category} 
                variant="outline" 
                className="h-auto p-4 flex flex-col items-center"
                onClick={() => {
                  const element = document.getElementById(category.toLowerCase().replace(/\s+/g, '-'));
                  element?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                <Icon className="h-6 w-6 mb-2" />
                <span className="text-xs text-center">{category}</span>
              </Button>
            );
          })}
        </div>

        {/* FAQ Sections */}
        {categories.map((category) => (
          <Card key={category} className="mb-8" id={category.toLowerCase().replace(/\s+/g, '-')}>
            <CardHeader>
              <CardTitle className="flex items-center">
                {(() => {
                  const icons = {
                    "Getting Started": Briefcase,
                    "Finding Work": FileText,
                    "Payments": PoundSterling,
                    "Working with Schools": Shield,
                    "Platform Usage": Users,
                    "Support": HelpCircle
                  };
                  const Icon = icons[category as keyof typeof icons] || HelpCircle;
                  return <Icon className="h-6 w-6 mr-2 text-classtasker-blue" />;
                })()}
                {category}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {faqData
                  .filter(item => item.category === category)
                  .map((item, index) => {
                    const globalIndex = faqData.indexOf(item);
                    const isOpen = openItems.includes(globalIndex);
                    
                    return (
                      <div key={globalIndex} className="border rounded-lg">
                        <button
                          className="w-full p-4 text-left flex justify-between items-center hover:bg-gray-50"
                          onClick={() => toggleItem(globalIndex)}
                        >
                          <span className="font-medium">{item.question}</span>
                          {isOpen ? (
                            <ChevronUp className="h-5 w-5 text-gray-500" />
                          ) : (
                            <ChevronDown className="h-5 w-5 text-gray-500" />
                          )}
                        </button>
                        {isOpen && (
                          <div className="px-4 pb-4 text-gray-600">
                            {item.answer}
                          </div>
                        )}
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Still Need Help */}
        <Card>
          <CardHeader>
            <CardTitle>Still Need Help?</CardTitle>
            <CardDescription>
              Can't find the answer you're looking for? Our support team is here to help.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" asChild>
                <Link to="/supplier-resources">
                  <FileText className="h-4 w-4 mr-2" />
                  Supplier Resources
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link to="/contact">
                  <HelpCircle className="h-4 w-4 mr-2" />
                  Contact Support
                </Link>
              </Button>
              <Button asChild>
                <Link to="/register?type=supplier">
                  <Briefcase className="h-4 w-4 mr-2" />
                  Become a Supplier
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default SupplierFAQ;
