import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, CheckCircle, AlertCircle, ArrowRight } from 'lucide-react';
import ConnectOnboarding from '@/components/stripe/ConnectOnboarding';
import { supabase } from '@/integrations/supabase/client';

const SupplierOnboarding: React.FC = () => {
  const { user, profile } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('profile');
  const [loading, setLoading] = useState(false);
  const [profileComplete, setProfileComplete] = useState(false);
  const [businessComplete, setBusinessComplete] = useState(false);
  const [paymentComplete, setPaymentComplete] = useState(false);

  // Form states
  const [businessName, setBusinessName] = useState('');
  const [businessDescription, setBusinessDescription] = useState('');
  const [businessAddress, setBusinessAddress] = useState('');
  const [businessCity, setBusinessCity] = useState('');
  const [businessPostcode, setBusinessPostcode] = useState('');
  const [businessPhone, setBusinessPhone] = useState('');
  const [businessWebsite, setBusinessWebsite] = useState('');
  const [services, setServices] = useState('');

  useEffect(() => {
    // Check if user is logged in
    if (!user) {
      navigate('/login');
      return;
    }

    // Check if user is a supplier
    if (profile && profile.account_type !== 'supplier') {
      navigate('/organisation/setup');
      return;
    }

    // Load existing profile data if available
    if (profile) {
      setProfileComplete(!!profile.first_name && !!profile.last_name);
      setBusinessComplete(!!profile.business_name);
      setPaymentComplete(!!profile.stripe_account_id);

      // Populate form fields
      if (profile.business_name) setBusinessName(profile.business_name);
      if (profile.business_description) setBusinessDescription(profile.business_description);
      if (profile.address) setBusinessAddress(profile.address);
      if (profile.city) setBusinessCity(profile.city);
      if (profile.postcode) setBusinessPostcode(profile.postcode);
      if (profile.phone) setBusinessPhone(profile.phone);
      if (profile.website) setBusinessWebsite(profile.website);
      if (profile.services) setServices(profile.services);
    }
  }, [user, profile, navigate]);

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) return;

    setLoading(true);

    try {
      const formData = new FormData(e.target as HTMLFormElement);
      const firstName = formData.get('firstName') as string;
      const lastName = formData.get('lastName') as string;

      // Update profile in Supabase
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: firstName,
          last_name: lastName,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (error) throw error;

      setProfileComplete(true);
      toast({
        title: 'Profile updated',
        description: 'Your profile information has been saved.',
      });

      // Move to next tab
      setActiveTab('business');
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error updating profile',
        description: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBusinessSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) return;

    setLoading(true);

    try {
      // Create a supplier organization using the secure function
      const { data, error } = await supabase.rpc('create_supplier_organization', {
        name_param: businessName,
        user_id_param: user.id,
        address_param: businessAddress,
        city_param: businessCity,
        postcode_param: businessPostcode,
        phone_param: businessPhone,
        website_param: businessWebsite,
        business_description_param: businessDescription,
        services_param: services
      });

      if (error) throw error;

      // Update the business_name in the profile for backward compatibility
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          business_name: businessName,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (profileError) throw profileError;

      setBusinessComplete(true);
      toast({
        title: 'Business information updated',
        description: 'Your business information has been saved.',
      });

      // Move to next tab
      setActiveTab('payment');
    } catch (error: any) {
      console.error('Error creating supplier organization:', error);
      toast({
        variant: 'destructive',
        title: 'Error updating business information',
        description: error.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleComplete = () => {
    toast({
      title: 'Onboarding complete',
      description: 'You can now start bidding on tasks in the marketplace.',
    });
    navigate('/marketplace');
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Supplier Onboarding</CardTitle>
              <CardDescription>
                Complete your profile to start bidding on tasks in the marketplace
              </CardDescription>
            </CardHeader>
          </Card>

          <div className="flex justify-between mb-6">
            <div className={`flex items-center ${profileComplete ? 'text-green-600' : 'text-gray-400'}`}>
              <div className="w-8 h-8 rounded-full flex items-center justify-center border-2 mr-2 bg-white">
                {profileComplete ? <CheckCircle className="w-6 h-6" /> : '1'}
              </div>
              <span>Profile</span>
            </div>
            <div className="border-t-2 flex-1 mx-2 mt-4"></div>
            <div className={`flex items-center ${businessComplete ? 'text-green-600' : 'text-gray-400'}`}>
              <div className="w-8 h-8 rounded-full flex items-center justify-center border-2 mr-2 bg-white">
                {businessComplete ? <CheckCircle className="w-6 h-6" /> : '2'}
              </div>
              <span>Business</span>
            </div>
            <div className="border-t-2 flex-1 mx-2 mt-4"></div>
            <div className={`flex items-center ${paymentComplete ? 'text-green-600' : 'text-gray-400'}`}>
              <div className="w-8 h-8 rounded-full flex items-center justify-center border-2 mr-2 bg-white">
                {paymentComplete ? <CheckCircle className="w-6 h-6" /> : '3'}
              </div>
              <span>Payment</span>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsContent value="profile">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Profile</CardTitle>
                  <CardDescription>
                    Enter your personal information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form id="profileForm" onSubmit={handleProfileSubmit}>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="firstName">First Name</Label>
                        <Input
                          id="firstName"
                          name="firstName"
                          defaultValue={profile?.first_name || ''}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input
                          id="lastName"
                          name="lastName"
                          defaultValue={profile?.last_name || ''}
                          required
                        />
                      </div>
                    </div>
                  </form>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => navigate('/marketplace')}>
                    Skip for now
                  </Button>
                  <Button type="submit" form="profileForm" disabled={loading}>
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                    Continue <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="business">
              <Card>
                <CardHeader>
                  <CardTitle>Business Information</CardTitle>
                  <CardDescription>
                    Tell us about your business
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form id="businessForm" onSubmit={handleBusinessSubmit}>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="businessName">Business Name</Label>
                        <Input
                          id="businessName"
                          value={businessName}
                          onChange={(e) => setBusinessName(e.target.value)}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="businessDescription">Business Description</Label>
                        <Textarea
                          id="businessDescription"
                          value={businessDescription}
                          onChange={(e) => setBusinessDescription(e.target.value)}
                          required
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="businessAddress">Address</Label>
                          <Input
                            id="businessAddress"
                            value={businessAddress}
                            onChange={(e) => setBusinessAddress(e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="businessCity">City</Label>
                          <Input
                            id="businessCity"
                            value={businessCity}
                            onChange={(e) => setBusinessCity(e.target.value)}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="businessPostcode">Postcode</Label>
                          <Input
                            id="businessPostcode"
                            value={businessPostcode}
                            onChange={(e) => setBusinessPostcode(e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="businessPhone">Phone</Label>
                          <Input
                            id="businessPhone"
                            value={businessPhone}
                            onChange={(e) => setBusinessPhone(e.target.value)}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="businessWebsite">Website</Label>
                        <Input
                          id="businessWebsite"
                          value={businessWebsite}
                          onChange={(e) => setBusinessWebsite(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="services">Services Offered</Label>
                        <Textarea
                          id="services"
                          value={services}
                          onChange={(e) => setServices(e.target.value)}
                          placeholder="List the services you offer, separated by commas (e.g., Plumbing, Electrical, Carpentry)"
                          required
                        />
                      </div>
                    </div>
                  </form>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => setActiveTab('profile')}>
                    Back
                  </Button>
                  <Button type="submit" form="businessForm" disabled={loading}>
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                    Continue <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="payment">
              <Card>
                <CardHeader>
                  <CardTitle>Payment Setup</CardTitle>
                  <CardDescription>
                    Set up your payment account to receive payments for completed tasks
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ConnectOnboarding onComplete={() => setPaymentComplete(true)} />
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => setActiveTab('business')}>
                    Back
                  </Button>
                  <Button onClick={handleComplete} disabled={!profileComplete || !businessComplete}>
                    Complete Setup
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </MainLayout>
  );
};

export default SupplierOnboarding;
