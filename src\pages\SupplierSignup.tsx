import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { 
  Briefcase, 
  CheckCircle, 
  PoundSterling, 
  Clock, 
  Shield, 
  Users,
  Star,
  ArrowRight,
  MapPin,
  Phone,
  Mail
} from 'lucide-react';

const SupplierSignup = () => {
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <Briefcase className="h-16 w-16 text-classtasker-blue" />
          </div>
          <h1 className="text-4xl font-bold mb-4">Become a Classtasker Supplier</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Join our network of trusted maintenance professionals and grow your business by connecting with schools across the UK
          </p>
          <Button size="lg" asChild className="bg-classtasker-blue hover:bg-blue-600">
            <Link to="/register?type=supplier">
              Join as a Supplier
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>

        {/* Benefits Section */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-center mb-8">Why Choose Classtasker?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PoundSterling className="h-6 w-6 mr-2 text-green-600" />
                  Steady Income
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Access a consistent stream of maintenance work from schools in your area. Build long-term relationships with educational institutions.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-6 w-6 mr-2 text-blue-600" />
                  Flexible Schedule
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Choose jobs that fit your schedule. Work during school holidays, weekends, or arrange convenient times with schools.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-6 w-6 mr-2 text-red-600" />
                  Trusted Platform
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  All schools are verified, and our secure payment system ensures you get paid promptly for completed work.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-6 w-6 mr-2 text-purple-600" />
                  Professional Network
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Connect with other professionals, share expertise, and build your reputation in the education sector.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Star className="h-6 w-6 mr-2 text-yellow-600" />
                  Build Your Reputation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Earn reviews and ratings from schools to showcase your quality work and attract more opportunities.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="h-6 w-6 mr-2 text-orange-600" />
                  Local Opportunities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Find work close to home. Our platform matches you with schools in your local area to minimize travel time.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* How It Works */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="text-center">How It Works</CardTitle>
            <CardDescription className="text-center">
              Getting started as a Classtasker supplier is simple and straightforward
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold text-xl">1</span>
                </div>
                <h3 className="font-semibold mb-2">Sign Up</h3>
                <p className="text-sm text-gray-600">Create your supplier profile with your skills, qualifications, and service areas</p>
              </div>
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold text-xl">2</span>
                </div>
                <h3 className="font-semibold mb-2">Get Verified</h3>
                <p className="text-sm text-gray-600">Complete our verification process including insurance, qualifications, and DBS checks</p>
              </div>
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold text-xl">3</span>
                </div>
                <h3 className="font-semibold mb-2">Browse Jobs</h3>
                <p className="text-sm text-gray-600">View available maintenance tasks from schools and submit competitive quotes</p>
              </div>
              <div className="text-center">
                <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold text-xl">4</span>
                </div>
                <h3 className="font-semibold mb-2">Get Paid</h3>
                <p className="text-sm text-gray-600">Complete the work, get approval from the school, and receive payment through our secure system</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Requirements */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Supplier Requirements</CardTitle>
            <CardDescription>
              To ensure quality and safety, all suppliers must meet our verification requirements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-3 flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                  Essential Requirements
                </h3>
                <ul className="space-y-2 text-sm">
                  <li>• Valid public liability insurance (minimum £2 million)</li>
                  <li>• Enhanced DBS check for working in schools</li>
                  <li>• Relevant trade qualifications or certifications</li>
                  <li>• Proof of business registration (sole trader or limited company)</li>
                  <li>• Professional references from previous clients</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-3 flex items-center">
                  <Star className="h-5 w-5 mr-2 text-yellow-600" />
                  Preferred Qualifications
                </h3>
                <ul className="space-y-2 text-sm">
                  <li>• Industry-specific certifications (Gas Safe, NICEIC, etc.)</li>
                  <li>• Health and safety training certificates</li>
                  <li>• Previous experience working in educational environments</li>
                  <li>• Professional memberships (trade associations)</li>
                  <li>• Additional insurance coverage (employer's liability)</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Service Categories */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Service Categories</CardTitle>
            <CardDescription>
              We're looking for qualified professionals in these key areas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[
                'Plumbing', 'Electrical', 'Carpentry', 'Painting & Decorating',
                'Heating & Boilers', 'Roofing', 'Glazing', 'Flooring',
                'Cleaning Services', 'Grounds Maintenance', 'Fire Safety', 'Security Systems',
                'Catering Equipment', 'IT & AV Equipment', 'Playground Equipment', 'General Maintenance'
              ].map((category) => (
                <div key={category} className="bg-gray-50 rounded-lg p-3 text-center">
                  <span className="text-sm font-medium">{category}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Contact Section */}
        <Card>
          <CardHeader>
            <CardTitle>Ready to Get Started?</CardTitle>
            <CardDescription>
              Join thousands of suppliers already working with schools through Classtasker
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-4">Start Your Application</h3>
                <p className="text-gray-600 mb-4">
                  Create your supplier account today and start browsing available opportunities in your area.
                </p>
                <Button asChild className="w-full bg-classtasker-blue hover:bg-blue-600">
                  <Link to="/register?type=supplier">
                    Create Supplier Account
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
              <div>
                <h3 className="font-semibold mb-4">Have Questions?</h3>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-2 text-gray-500" />
                    <span className="text-sm">0800 123 4567</span>
                  </div>
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-2 text-gray-500" />
                    <span className="text-sm"><EMAIL></span>
                  </div>
                  <Button variant="outline" asChild className="w-full">
                    <Link to="/supplier-faq">
                      View Supplier FAQ
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default SupplierSignup;
