import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar, MapPin, PoundSterling, Tag, Clock, User, ArrowLeft, Loader2, AlertCircle, MessageSquare } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useTasks } from '@/hooks/use-tasks';
import { useOffers } from '@/hooks/use-offers';
import { useAuth } from '@/contexts/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useQueryClient } from '@tanstack/react-query';
import GetStreamTaskChat from '@/components/tasks/GetStreamTaskChat';
import SupplierActions from '@/components/tasks/SupplierActions';
import TaskOwnerOfferActions from '@/components/tasks/TaskOwnerOfferActions';
import TaskCompletionActions from '@/components/tasks/TaskCompletionActions';
import TaskPaymentActions from '@/components/tasks/TaskPaymentActions';
import InternalTaskActions from '@/components/tasks/InternalTaskActions';
import TestButton from '@/components/tasks/TestButton';
import DirectDatabaseCheck from '@/components/tasks/DirectDatabaseCheck';
import SuperSimpleTest from '@/components/tasks/SuperSimpleTest';
import DebugTaskComponent from '@/components/tasks/DebugTaskComponent';
import TaskRenderingDebug from '@/components/debug/TaskRenderingDebug';
import TaskDetailTimeline from '@/components/tasks/TaskDetailTimeline';
import TaskFlowExplanation from '@/components/tasks/TaskFlowExplanation';
import type { Task as TaskType, Offer } from '@/services/taskService';
import { supabase } from '@/integrations/supabase/client';
import { useChatVisibility } from '@/hooks/use-chat-visibility';

const normalizeStatus = (status: string): string => {
  if (status === 'pending') return 'awaiting';
  return status;
};

const TaskPage = () => {
  const { id } = useParams<{ id: string }>();
  const [offerAmount, setOfferAmount] = useState('');
  const [offerMessage, setOfferMessage] = useState('');
  const { isVisible: showMessages, hasMessages, isChecking, showChat } = useChatVisibility(id);
  const messagesRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user, isSupplier, isSchool, isAdmin, isMaintenance, profile } = useAuth();

  // CRITICAL DEBUG - Log auth state
  if (process.env.NODE_ENV === 'development') {

    console.log('CRITICAL AUTH DEBUG: completed');

    }
  const queryClient = useQueryClient();

  const { data: task, isLoading: isLoadingTask, error: taskError } = useTasks().getTask(id || '');

  // Task status validation for development only
  useEffect(() => {
    if (task && process.env.NODE_ENV === 'development') {
      // Basic validation for development debugging
      if (typeof task.status !== 'string') {
        console.warn('Task status type validation failed');
        task.status = String(task.status) as 'assigned';
      }
    }
  }, [task, user]);

  const { data: offers, isLoading: isLoadingOffers, refetch: refetchOffers } = useOffers().getOffersForTask(id || '');

  // Offers validation for development only
  useEffect(() => {
    if (offers && process.env.NODE_ENV === 'development') {
      const acceptedOffer = offers.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted');
      if (acceptedOffer) {
        if (process.env.NODE_ENV === 'development') {
    console.log('Accepted offer validation passed');
  }
      }
    }
  }, [offers]);

  const isTaskOwner = user && task && user.id === task.user_id;

  const userOffer = offers?.find(offer => user && offer.user_id === user.id);
  const hasSubmittedOffer = !!userOffer;

  const { createOffer, isCreatingOffer } = useOffers();

  const { acceptOffer, isAcceptingOffer, updateOfferStatus, isUpdatingOfferStatus } = useOffers();

  useEffect(() => {
    if (id) {
      if (process.env.NODE_ENV === 'development') {
    console.log("Task page loaded for task ID:", id);
  }
      refetchOffers();
      queryClient.invalidateQueries({ queryKey: ['task', id] });

      // We don't need to check for messages here anymore
      // Our useChatVisibility hook handles this
    }
  }, [id, refetchOffers, queryClient]);

  useEffect(() => {
    if (taskError) {
      console.error("Task error:", taskError);
      toast({
        variant: "destructive",
        title: "Error loading task",
        description: "The task could not be found or you don't have permission to view it.",
      });
      navigate('/tasks');
    }
  }, [taskError, toast, navigate]);

  useEffect(() => {
    if (task) {
      if (process.env.NODE_ENV === 'development') {
    console.log("Task details:", task);
  }
      if (process.env.NODE_ENV === 'development') {
    console.log("Current message state - showMessages:", showMessages, "hasMessages:", hasMessages);
  }
      if (process.env.NODE_ENV === 'development') {

        console.log("User roles:", { isAdmin, isTaskOwner, isSupplier, isSchool });

        }
    }
    if (offers) {
      if (process.env.NODE_ENV === 'development') {
    console.log("Offers for task:", offers);
  }
      offers.forEach(offer => {
        if (process.env.NODE_ENV === 'development') {
    console.log(`Offer ${offer.id} status: ${offer.status}`);
  }
      });

      // Check if there's an accepted offer
      const acceptedOffer = offers.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted');
      if (process.env.NODE_ENV === 'development') {
    console.log("Accepted offer:", acceptedOffer);
  }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [task, offers, showMessages, hasMessages, isAdmin, isTaskOwner, isSupplier, isSchool]);

  const handleRequestInfo = () => {
    // Show chat using our hook
    showChat();

    // Scroll to messages
    setTimeout(() => {
      messagesRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const handleSubmitOffer = (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast({
        variant: "destructive",
        title: "Authentication required",
        description: "Please log in to submit an offer.",
      });
      return;
    }

    if (!isSupplier) {
      toast({
        variant: "destructive",
        title: "Permission denied",
        description: "Only suppliers can submit offers on tasks.",
      });
      return;
    }

    if (!offerAmount || !offerMessage) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please provide both an amount and a message for your offer.",
      });
      return;
    }

    if (!id) return;

    createOffer({
      task_id: id,
      amount: parseFloat(offerAmount),
      message: offerMessage,
    }, {
      onSuccess: () => {
        setOfferAmount('');
        setOfferMessage('');
      }
    });
  };

  const handleAcceptOffer = (offerId: string) => {
    if (!id) {
      console.error("Cannot accept offer - missing task ID");
      return;
    }

    if (!user || !isTaskOwner) {
      console.warn("Permission denied - user is not task owner", {
        userId: user?.id,
        taskOwnerId: task?.user_id,
        isTaskOwner
      });
      toast({
        variant: "destructive",
        title: "Permission denied",
        description: "Only the task creator can accept offers.",
      });
      return;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`Attempting to accept offer: ${offerId} for task: ${id}`);
  }
    if (process.env.NODE_ENV === 'development') {
    console.log("Task details:", {
      taskId: id,
      status: task?.status,
      visibility: task?.visibility
    });
  }
    if (process.env.NODE_ENV === 'development') {

      console.log("User status:", { userId: user?.id, isSchool, isTaskOwner });


      }
    // Show a toast to indicate the process has started
    toast({
      title: "Processing offer acceptance",
      description: "Please wait while we process your request...",
    });

    acceptOffer(
      {
        taskId: id,
        offerId: offerId
      },
      {
        onSuccess: () => {
          if (process.env.NODE_ENV === 'development') {
    console.log("Offer accepted successfully via callback!");
  }
          toast({
            title: "Offer accepted",
            description: "The offer has been accepted successfully. The task has been assigned to the supplier.",
            variant: "default",
          });

          setTimeout(() => {
            if (process.env.NODE_ENV === 'development') {
    console.log("Refreshing data after successful offer acceptance");
  }
            refetchOffers();
            queryClient.invalidateQueries({ queryKey: ['task', id] });
          }, 500);
        },
        onError: (error) => {
          console.error("Error accepting offer via callback:", error);
          toast({
            variant: "destructive",
            title: "Error accepting offer",
            description: "There was a problem accepting this offer. Please try again.",
          });
        }
      }
    );
  };

  const handleRejectOffer = (offerId: string) => {
    if (!id || !user || !isTaskOwner) {
      console.warn("Permission denied for rejecting offer", {
        userId: user?.id,
        taskOwnerId: task?.user_id,
        isTaskOwner
      });
      toast({
        variant: "destructive",
        title: "Permission denied",
        description: "Only the task creator can reject offers.",
      });
      return;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`Attempting to reject offer: ${offerId} for task: ${id}`);
  }
    if (process.env.NODE_ENV === 'development') {
    console.log("Task details:", {
      taskId: id,
      status: task?.status,
      visibility: task?.visibility
    });
  }
    // Show a toast to indicate the process has started
    toast({
      title: "Processing offer rejection",
      description: "Please wait while we process your request...",
    });

    updateOfferStatus(
      offerId,
      'rejected',
      {
        onSuccess: () => {
          if (process.env.NODE_ENV === 'development') {
    console.log("Offer rejected successfully via callback!");
  }
          toast({
            title: "Offer rejected",
            description: "The offer has been rejected successfully."
          });
          setTimeout(() => {
            if (process.env.NODE_ENV === 'development') {
    console.log("Refreshing data after successful offer rejection");
  }
            refetchOffers();
            queryClient.invalidateQueries({ queryKey: ['task', id] });
          }, 500);
        },
        onError: (error) => {
          console.error("Error rejecting offer via callback:", error);
          toast({
            variant: "destructive",
            title: "Error rejecting offer",
            description: "There was a problem rejecting this offer. Please try again.",
          });
        }
      }
    );
  };

  if (isLoadingTask) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="mb-6">
            <Link to="/tasks" className="text-gray-600 hover:text-classtasker-blue flex items-center">
              <ArrowLeft className="h-4 w-4 mr-1" /> Back to Tasks
            </Link>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="mb-6">
                <Skeleton className="h-8 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </div>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Skeleton className="h-16 w-1/3" />
                    <Skeleton className="h-16 w-1/3" />
                  </div>
                  <Separator className="mb-6" />
                  <Skeleton className="h-4 w-1/4 mb-3" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4 mb-6" />
                  <Skeleton className="h-4 w-1/4 mb-3" />
                  <div className="flex flex-wrap gap-2">
                    <Skeleton className="h-6 w-20" />
                    <Skeleton className="h-6 w-20" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card>
                <CardContent className="p-6">
                  <Skeleton className="h-4 w-1/3 mb-4" />
                  <div className="flex items-center">
                    <Skeleton className="h-14 w-14 rounded-full mr-4" />
                    <div>
                      <Skeleton className="h-4 w-32 mb-2" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <Skeleton className="h-4 w-1/3 mb-4" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-10 w-full mb-4" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-24 w-full mb-4" />
                  <Skeleton className="h-10 w-full" />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!task) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-16 text-center">
          <h2 className="text-2xl font-bold mb-4">Task Not Found</h2>
          <p className="text-gray-600 mb-6">The task you're looking for does not exist or has been removed.</p>
          <Button asChild>
            <Link to="/tasks">View All Tasks</Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <SuperSimpleTest />
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link to="/tasks" className="text-gray-600 hover:text-classtasker-blue flex items-center">
            <ArrowLeft className="h-4 w-4 mr-1" /> Back to Tasks
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="mb-6">
              <div className="flex justify-between items-start">
                <h1 className="text-3xl font-bold">{task?.title}</h1>
                <Badge
                  variant="outline"
                  className={
                    `${task?.status === 'open' ? 'bg-green-100 text-green-800 border-green-200' :
                      task?.status === 'interest' ? 'bg-cyan-100 text-cyan-800 border-cyan-200' :
                      task?.status === 'questions' ? 'bg-cyan-100 text-cyan-800 border-cyan-200' :
                      task?.status === 'assigned' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                      task?.status === 'in_progress' ? 'bg-indigo-100 text-indigo-800 border-indigo-200' :
                      task?.status === 'pending_payment' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                      task?.status === 'completed' ? 'bg-purple-100 text-purple-800 border-purple-200' :
                      task?.status === 'closed' ? 'bg-teal-100 text-teal-800 border-teal-200' :
                      'bg-gray-100 text-gray-800 border-gray-200'}`
                  }
                >
                  {task?.status === 'pending_payment' ? 'Payment Required' :
                   task?.status === 'in_progress' ? 'In Progress' :
                   task?.status === 'interest' ? 'Interest Expressed' :
                   task?.status === 'questions' ? 'Discussion Phase' :
                   task?.status?.charAt(0).toUpperCase() + task?.status?.slice(1)}
                </Badge>
              </div>
              <div className="flex items-center text-gray-600 mt-2">
                <MapPin size={16} className="mr-1" /> {task?.location}
              </div>
            </div>

            <Card className="mb-8">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center">
                      <Calendar size={18} className="text-gray-500 mr-2" />
                      <div>
                        <p className="text-sm text-gray-500">Due Date</p>
                        <p className="font-medium">{format(new Date(task?.due_date), 'PP')}</p>
                      </div>
                    </div>
                    <Separator orientation="vertical" className="h-10" />
                    <div className="flex items-center">
                      <PoundSterling size={18} className="text-gray-500 mr-2" />
                      <div>
                        <p className="text-sm text-gray-500">Budget</p>
                        <p className="font-medium">£{Number(task?.budget).toFixed(2)}</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Clock size={18} className="text-gray-500 mr-2" />
                    <div>
                      <p className="text-sm text-gray-500">Posted</p>
                      <p className="font-medium">{format(new Date(task?.created_at), 'PP')}</p>
                    </div>
                  </div>
                </div>

                <Separator className="mb-6" />

                <div className="mb-6">
                  <h2 className="text-xl font-semibold mb-3">Description</h2>
                  <div className="text-gray-700 whitespace-pre-line">
                    {task?.description}
                  </div>
                </div>

                <div>
                  <h2 className="text-xl font-semibold mb-3">Category</h2>
                  <Badge variant="outline">
                    {task?.category}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <div ref={messagesRef}>
              <div className="mb-8">
                {showMessages && (
                  <>
                    {/* Debug Task Component */}
                    {id && (
                      <DebugTaskComponent taskId={id} />
                    )}

                    {/* DIRECT DATABASE CHECK */}
                    {id && (
                      <div className="mb-4">
                        <DirectDatabaseCheck
                          taskId={id}
                        />
                      </div>
                    )}

                    {/* SUPER SIMPLE TEST BUTTON */}
                    {task && user && (
                      <div className="mb-4">
                        <TestButton
                          taskId={task.id}
                          userId={user.id}
                        />
                      </div>
                    )}

                    {/* InternalTaskActions moved to sidebar for better visibility */}

                    <GetStreamTaskChat
                      taskId={id || ''}
                      taskOwnerId={task?.user_id || ''}
                    />
                  </>
                )}
              </div>
            </div>

            {isTaskOwner && offers && offers.length > 0 && (
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-3">Offers ({offers.length})</h2>
                  <div className="space-y-4">
                    {offers.map(offer => {
                      const displayStatus = normalizeStatus(offer.status);

                      return (
                        <Card key={offer.id} className={
                          displayStatus === 'accepted' ? 'border-green-400' :
                          displayStatus === 'rejected' ? 'border-red-300' : ''
                        }>
                          <CardContent className="p-4">
                            <div className="flex justify-between mb-2">
                              <div className="font-medium">£{Number(offer.amount).toFixed(2)}</div>
                              <Badge
                                variant="outline"
                                className={
                                  `${displayStatus === 'awaiting' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                                    displayStatus === 'accepted' ? 'bg-green-100 text-green-800 border-green-200' :
                                    'bg-red-100 text-red-800 border-red-200'}`
                                }
                              >
                                {displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1)}
                              </Badge>
                            </div>
                            <p className="text-gray-700 mb-3">{offer.message}</p>
                            <div className="flex justify-between items-center">
                              <div className="text-sm text-gray-500">
                                {format(new Date(offer.created_at), 'PP')}
                              </div>
                              {/* Allow accepting/rejecting offers for any task when the offer is awaiting and user is task owner */}
                              {console.log('Task.tsx offer conditions:', {
                                displayStatus,
                                isTaskOwner,
                                isSchool,
                                taskStatus: task?.status,
                                taskVisibility: task?.visibility
                              })}
                              {(displayStatus === 'awaiting' && isTaskOwner) && (
                                <div className="flex gap-2">
                                  <Button
                                    variant="default"
                                    size="sm"
                                    className="bg-classtasker-blue hover:bg-blue-600"
                                    onClick={() => handleAcceptOffer(offer.id)}
                                    disabled={isAcceptingOffer || isUpdatingOfferStatus}
                                  >
                                    {isAcceptingOffer ?
                                      <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Accepting...</> :
                                      'Accept Offer'}
                                  </Button>

                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-red-600 hover:bg-red-50"
                                    onClick={() => handleRejectOffer(offer.id)}
                                    disabled={isUpdatingOfferStatus || isAcceptingOffer}
                                  >
                                    {isUpdatingOfferStatus ?
                                      <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Rejecting...</> :
                                      'Reject'}
                                  </Button>

                                  {!showMessages && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={handleRequestInfo}
                                    >
                                      Message Supplier
                                    </Button>
                                  )}
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          <div className="space-y-6">
            {/* Task Status Timeline */}
            <TaskDetailTimeline
              status={task?.status}
              offersCount={offers?.length || 0}
              createdAt={task?.created_at}
              updatedAt={task?.updated_at}
              assignedTo={task?.assigned_to}
              assignedToName={task?.assigned_to ? "Assigned Staff" : undefined}
              visibility={task?.visibility}
            />

            {/* Task Flow Explanation for Suppliers */}
            {isSupplier && (
              <TaskFlowExplanation currentStatus={task?.status} />
            )}

            {/* Add TaskOwnerOfferActions component for task owners */}
            {isTaskOwner && offers && offers.length > 0 && (
              <TaskOwnerOfferActions task={task} offers={offers} />
            )}

            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold mb-4 flex items-center">
                  <User size={16} className="mr-2" /> Posted by
                </h3>
                <div className="flex items-center">
                  <Avatar className="h-14 w-14 mr-4">
                    <AvatarFallback>{task?.user_id.charAt(0).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <div>
                    <Link to={`/profile/${task?.user_id}`} className="font-medium hover:text-classtasker-blue">
                      School Account
                    </Link>
                    <p className="text-sm text-gray-500">School Staff</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Show supplier actions for tasks in any discussion stage or with public visibility */}
            {(() => {
              // Debug logging for SupplierActions rendering
              if (process.env.NODE_ENV === 'development') {

                console.log('SupplierActions rendering condition:', {
                taskId: task?.id,
                taskTitle: task?.title,
                taskStatus: task?.status,
                taskVisibility: task?.visibility,
                isSupplier,
                hasUserOffer: !!userOffer,
                userOfferStatus: userOffer?.status,
                isExternalTask: task?.visibility === 'public',
                shouldRender: (
                  // Only show for external (public.replace(/user.*/, 'hasUser: ' + !!user)) tasks
                  task?.visibility === 'public' &&
                  // And only for suppliers
                  isSupplier &&
                  // And only for relevant task statuses
                  (task?.status === 'open' ||
                   task?.status === 'assigned' ||
                   task?.status === 'in_progress' ||
                   task?.status === 'questions' ||
                   task?.status === 'interest' ||
                   task?.status === ('offer' as any))
                ),
                renderTime: new Date().toISOString()
              });

                }
              return null;
            })()}

            {/* Only show SupplierActions for external (public) tasks and suppliers */}
            {(task?.visibility === 'public' && isSupplier && (
               task?.status === 'open' ||
               task?.status === 'assigned' ||
               task?.status === 'in_progress' ||
               task?.status === 'questions' ||
               task?.status === 'interest' ||
               task?.status === ('offer' as any)
            )) && (
              <SupplierActions
                task={task}
                existingOffer={userOffer}
                showRequestInfo={showMessages}
                onRequestInfo={handleRequestInfo}
              />
            )}

            {task?.status === 'open' && !isSupplier && user && (
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-4">Make an Offer</h3>

                  {!user && (
                    <Alert variant="default" className="mb-4">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Authentication Required</AlertTitle>
                      <AlertDescription>
                        You need to <Button variant="link" className="p-0" onClick={() => navigate('/login')}>sign in</Button> to make an offer.
                      </AlertDescription>
                    </Alert>
                  )}

                  {user && !isSupplier && (
                    <Alert variant="default" className="mb-4">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Suppliers Only</AlertTitle>
                      <AlertDescription>
                        Only supplier accounts can make offers on tasks.
                      </AlertDescription>
                    </Alert>
                  )}

                  {user && isSupplier && !hasSubmittedOffer && (
                    <form onSubmit={handleSubmitOffer}>
                      <div className="mb-4">
                        <Label htmlFor="offerAmount" className="block mb-2 text-sm">
                          Your Offer Amount (£)
                        </Label>
                        <Input
                          id="offerAmount"
                          type="number"
                          placeholder="Enter amount"
                          value={offerAmount}
                          onChange={(e) => setOfferAmount(e.target.value)}
                          min="1"
                          step="0.01"
                        />
                        <p className="text-sm text-gray-500 mt-1">
                          Task budget: £{Number(task?.budget).toFixed(2)}
                        </p>
                      </div>

                      <div className="mb-4">
                        <Label htmlFor="offerMessage" className="block mb-2 text-sm">
                          Message
                        </Label>
                        <Textarea
                          id="offerMessage"
                          placeholder="Introduce yourself and explain why you're perfect for this task..."
                          value={offerMessage}
                          onChange={(e) => setOfferMessage(e.target.value)}
                          rows={5}
                        />
                      </div>

                      <Button
                        type="submit"
                        className="w-full bg-classtasker-blue hover:bg-blue-600"
                        disabled={isCreatingOffer}
                      >
                        {isCreatingOffer ?
                          <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Submitting...</> :
                          "Submit Offer"}
                      </Button>
                    </form>
                  )}

                  {user && isSupplier && hasSubmittedOffer && (
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <p className="font-medium text-blue-600 mb-2">You have already submitted an offer</p>
                      <p className="text-gray-600">Your offer: £{Number(userOffer?.amount).toFixed(2)}</p>
                      <Badge
                        variant="outline"
                        className={
                          `${(normalizeStatus(userOffer?.status || '')) === 'awaiting' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                            (normalizeStatus(userOffer?.status || '')) === 'accepted' ? 'bg-green-100 text-green-800 border-green-200' :
                            'bg-red-100 text-red-800 border-red-200'}`
                        }
                      >
                        {normalizeStatus(userOffer?.status || '').charAt(0).toUpperCase() + normalizeStatus(userOffer?.status || '').slice(1)}
                      </Badge>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* This debug log has been moved above the InternalTaskActions component */}

            {/* Debug Card - Development Only */}
            {process.env.NODE_ENV === 'development' && task?.visibility === 'internal' && (
              <Card className="mb-6 bg-yellow-50">
                <CardContent className="p-4">
                  <h3 className="font-bold mb-2">Debug Information (Development Only)</h3>
                  <div className="text-xs space-y-1">
                    <p>Task ID: {task?.id}</p>
                    <p>Task Status: {task?.status}</p>
                    <p>Task Visibility: {task?.visibility}</p>
                    <p>User Role: {profile?.role}</p>
                    <p>Is Task Owner: {isTaskOwner ? 'Yes' : 'No'}</p>
                    <p>Is Maintenance Role: {profile?.role === 'maintenance' ? 'Yes' : 'No'}</p>
                  </div>

                  <div className="mt-4 pt-3 border-t border-gray-300">
                    <h4 className="font-bold mb-2">Alternative Views:</h4>
                    <div className="space-y-2">
                      <p>
                        <a
                          href={`/internal-task/${task?.id}`}
                          className="text-blue-600 hover:underline"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Open in Internal Task View
                        </a>
                      </p>
                      <p>
                        <a
                          href={`/emergency/task/${task?.id}`}
                          className="text-blue-600 hover:underline"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Open in Emergency Task View
                        </a>
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Debug logging removed for security - use development tools instead */}

            {/* Debug components - hidden in production */}
            {process.env.NODE_ENV === 'development' && (
              <>
                {/* DIRECT DATABASE CHECK */}
                {id && (
                  <DirectDatabaseCheck
                    taskId={id}
                  />
                )}

                {/* SUPER SIMPLE TEST BUTTON */}
                {task && user && (
                  <TestButton
                    taskId={task.id}
                    userId={user.id}
                  />
                )}

                {/* Debug Task Component */}
                {id && (
                  <DebugTaskComponent taskId={id} />
                )}

                {/* Task Rendering Debug */}
                {task && (
                  <TaskRenderingDebug task={task} />
                )}

                {/* Fix System Messages Button */}
                {id && isAdmin && (
                  <Card className="mb-4 bg-yellow-50 border-yellow-200">
                    <CardHeader>
                      <CardTitle className="text-sm">Fix System Messages</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={async () => {
                          try {
                            const response = await fetch('/api/fix-task-messages', {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json',
                              },
                              body: JSON.stringify({ taskId: id }),
                            });

                            const result = await response.json();

                            if (result.success) {
                              toast({
                                title: "Messages Fixed",
                                description: `Cleaned up incorrect system messages for this ${result.isInternalTask ? 'internal' : 'external'} task.`,
                              });

                              // Refresh the page to show the updated messages
                              window.location.reload();
                            } else {
                              toast({
                                title: "Error",
                                description: result.error || "Failed to fix messages",
                                variant: "destructive",
                              });
                            }
                          } catch (error) {
                            console.error('Error fixing messages:', error);
                            toast({
                              title: "Error",
                              description: "An unexpected error occurred",
                              variant: "destructive",
                            });
                          }
                        }}
                      >
                        Fix System Messages
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </>
            )}

            {/* Internal Task Actions - Show for internal tasks OR for maintenance staff assigned to the task */}
            {task && (
              // Show for:
              // 1. Internal tasks viewed by admins OR task owners
              // 2. Internal tasks viewed by assigned maintenance staff
              // 3. Any task assigned to maintenance staff who is viewing it
              (
                (task?.visibility === 'internal' && (isAdmin || isTaskOwner)) ||
                (task?.visibility === 'internal' && isMaintenance && String(task?.assigned_to) === String(user?.id)) ||
                (isMaintenance && String(task?.assigned_to) === String(user?.id))
              )
            ) && (
              <Card className="mb-6 border-2 border-blue-300">
                <CardHeader>
                  <CardTitle>Task Actions</CardTitle>
                  <CardDescription>
                    {task?.visibility === 'internal'
                      ? 'Actions for this internal maintenance task'
                      : 'Maintenance staff actions for this task'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <InternalTaskActions
                    task={task}
                    onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
                  />
                </CardContent>
              </Card>
            )}

            {/* Task Completion Actions - Shown to task owners and admins for assigned tasks with public visibility */}
            {(isTaskOwner || isAdmin) && task?.visibility !== 'internal' && task?.status === 'assigned' && (
              <>

                <TaskCompletionActions
                  task={task}
                  acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
                  onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
                />
              </>
            )}

            {/* Task Payment Actions - Shown to task owners and admins for tasks in pending_payment status */}
            {(isTaskOwner || isAdmin) && task?.visibility !== 'internal' && task?.status === 'pending_payment' && (
              <TaskPaymentActions
                task={task}
                acceptedOffer={offers?.find(o => o.status === 'accepted' || normalizeStatus(o.status) === 'accepted') || null}
                onTaskUpdated={() => queryClient.invalidateQueries({ queryKey: ['task', id] })}
              />
            )}

            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold mb-4">Task Details</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div className="text-gray-600">Category</div>
                    <div className="font-medium flex items-center">
                      <Tag size={14} className="mr-1" />
                      {task?.category}
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="text-gray-600">Status</div>
                    <Badge
                      variant="outline"
                      className={
                        `${task?.status === 'open' ? 'bg-green-100 text-green-800 border-green-200' :
                          task?.status === 'interest' ? 'bg-cyan-100 text-cyan-800 border-cyan-200' :
                          task?.status === 'questions' ? 'bg-cyan-100 text-cyan-800 border-cyan-200' :
                          task?.status === 'assigned' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                          task?.status === 'pending_payment' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                          task?.status === 'completed' ? 'bg-purple-100 text-purple-800 border-purple-200' :
                          'bg-gray-100 text-gray-800 border-gray-200'}`
                      }
                    >
                      {task?.status === 'pending_payment' ? 'Payment Required' :
                       task?.status === 'in_progress' ? 'In Progress' :
                       task?.status === 'interest' ? 'Interest Expressed' :
                       task?.status === 'questions' ? 'Discussion Phase' :
                       task?.status?.charAt(0).toUpperCase() + task?.status?.slice(1)}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="text-gray-600">Offers</div>
                    <div className="font-medium">{task?.offers_count}</div>
                  </div>
                  {task?.payment_status && (
                    <div className="flex justify-between items-center">
                      <div className="text-gray-600">Payment</div>
                      <Badge
                        variant="outline"
                        className={
                          `${task?.payment_status === 'paid' ? 'bg-green-100 text-green-800 border-green-200' :
                            task?.payment_status === 'processing' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                            task?.payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                            task?.payment_status === 'not_required' ? 'bg-gray-100 text-gray-800 border-gray-200' :
                            'bg-gray-100 text-gray-800 border-gray-200'}`
                        }
                      >
                        {task?.payment_status === 'not_required' ? 'Not Required' :
                         task?.payment_status.charAt(0).toUpperCase() + task?.payment_status.slice(1)}
                      </Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Message button for school users (task owners) */}
            {isTaskOwner && task?.status === 'assigned' && !showMessages && !hasMessages && !isChecking && (
              <Button
                className="w-full"
                onClick={handleRequestInfo}
              >
                <MessageSquare className="mr-2 h-4 w-4" /> Message Supplier
              </Button>
            )}

            {/* Message button for suppliers with accepted offers */}
            {isSupplier && task?.status === 'assigned' && !showMessages && !hasMessages && !isChecking &&
              userOffer && userOffer.status === 'accepted' && (
              <Button
                className="w-full"
                onClick={handleRequestInfo}
              >
                <MessageSquare className="mr-2 h-4 w-4" /> Message School
              </Button>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

const Label = ({ children, className = "", ...props }: React.LabelHTMLAttributes<HTMLLabelElement>) => {
  return (
    <label className={`text-gray-700 font-medium ${className}`} {...props}>
      {children}
    </label>
  );
};

export default TaskPage;
