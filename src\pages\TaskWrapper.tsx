import React from 'react';
import FixedTaskPage from './FixedTask';

/**
 * This is a wrapper component that renders the FixedTaskPage component.
 * We're using this approach to maintain compatibility with existing routes.
 *
 * The FixedTaskPage component is the recommended task view as it has:
 * - Better offer handling
 * - More consistent UI
 * - Improved code structure
 * - Better error handling
 */
const TaskWrapper = () => {
  console.log('TaskWrapper: Using FixedTaskPage');
  return <FixedTaskPage />;
};

export default TaskWrapper;
