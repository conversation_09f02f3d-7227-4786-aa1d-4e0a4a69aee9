import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import MainLayout from '@/components/layout/MainLayout';
import InternalTaskActions from '@/components/tasks/InternalTaskActions';
import { useQueryClient } from '@tanstack/react-query';

const TestInternalTaskActions = () => {
  const { id } = useParams<{ id: string }>();
  const { user, profile } = useAuth();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(true);
  const [task, setTask] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [componentRendered, setComponentRendered] = useState(false);

  // Load task data
  useEffect(() => {
    const loadTaskData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!id) {
          throw new Error('No task ID provided');
        }

        // Get the task
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('*')
          .eq('id', id)
          .single();

        if (taskError) {
          throw new Error(`Error getting task: ${taskError.message}`);
        }

        console.log('Task data loaded:', taskData);
        setTask(taskData);
      } catch (err: any) {
        console.error('Error loading task:', err);
        setError(err.message || 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    loadTaskData();
  }, [id]);

  // Check if component rendered
  useEffect(() => {
    // Use a ref to track if the component rendered
    const checkComponentRendered = () => {
      const componentElement = document.getElementById('internal-task-actions-component');
      setComponentRendered(!!componentElement);
    };

    // Check after a short delay to allow rendering
    const timer = setTimeout(checkComponentRendered, 500);

    return () => clearTimeout(timer);
  }, [task]);

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="mb-6">
            <Link to="/tasks" className="text-gray-600 hover:text-classtasker-blue flex items-center">
              <ArrowLeft className="h-4 w-4 mr-1" /> Back to Tasks
            </Link>
          </div>
          <Card>
            <CardContent className="p-6 flex justify-center items-center">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  if (error || !task) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="mb-6">
            <Link to="/tasks" className="text-gray-600 hover:text-classtasker-blue flex items-center">
              <ArrowLeft className="h-4 w-4 mr-1" /> Back to Tasks
            </Link>
          </div>
          <Card className="bg-red-50 border-red-200">
            <CardContent className="p-6">
              <p className="text-red-600 font-medium">Error: {error || 'Task not found'}</p>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex justify-between items-center">
          <Link to="/tasks" className="text-gray-600 hover:text-classtasker-blue flex items-center">
            <ArrowLeft className="h-4 w-4 mr-1" /> Back to Tasks
          </Link>
          <Link to={`/tasks/${id}`} className="text-blue-600 hover:underline">
            View in Regular Task Page
          </Link>
        </div>

        <h1 className="text-2xl font-bold mb-6">Test Internal Task Actions Component</h1>

        <div className="grid grid-cols-1 gap-8">
          {/* Task Info Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Task Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>Task ID:</strong> {task.id}</p>
                <p><strong>Title:</strong> {task.title}</p>
                <p><strong>Status:</strong> {task.status}</p>
                <p><strong>Visibility:</strong> {task.visibility}</p>
                <p><strong>Assigned To:</strong> {task.assigned_to}</p>
              </div>
            </CardContent>
          </Card>

          {/* User Info Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>User Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>User ID:</strong> {user?.id}</p>
                <p><strong>Email:</strong> {user?.email}</p>
                <p><strong>Role:</strong> {profile?.role}</p>
                <p><strong>Is Task Owner:</strong> {task.user_id === user?.id ? 'Yes' : 'No'}</p>
                <p><strong>Is Assigned Staff:</strong> {task.assigned_to === user?.id ? 'Yes' : 'No'}</p>
                <p><strong>Is Maintenance:</strong> {profile?.role === 'maintenance' ? 'Yes' : 'No'}</p>
              </div>
            </CardContent>
          </Card>

          {/* Component Test Card */}
          <Card className="mb-6 border-2 border-blue-300">
            <CardHeader>
              <CardTitle>InternalTaskActions Component</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-50 p-4 mb-4 rounded-md">
                <h3 className="font-bold mb-2">Task Status: <span className="text-blue-700">{task.status}</span></h3>
                <p className="text-sm">
                  Use the buttons below to update the task status. This is the actual InternalTaskActions component
                  that should be showing on the main task page.
                </p>
              </div>

              <div id="internal-task-actions-component" className="border-2 border-dashed border-blue-500 p-6 mb-4 bg-white">
                {/* This is the actual InternalTaskActions component that should be showing on the main task page */}
                <InternalTaskActions
                  task={task}
                  onTaskUpdated={() => {
                    // Refresh task data
                    queryClient.invalidateQueries({ queryKey: ['task', id] });

                    // Also refresh this page's data
                    const loadTaskData = async () => {
                      try {
                        const { data: taskData, error: taskError } = await supabase
                          .from('tasks')
                          .select('*')
                          .eq('id', id)
                          .single();

                        if (!taskError && taskData) {
                          console.log('Task updated:', taskData);
                          setTask(taskData);
                        }
                      } catch (err) {
                        console.error('Error refreshing task data:', err);
                      }
                    };

                    loadTaskData();
                  }}
                />
              </div>

              <div className="bg-yellow-50 p-4 rounded-md">
                <h3 className="font-bold mb-2">Component Rendering Status:</h3>
                <p>
                  {componentRendered
                    ? '✅ Component appears to be rendering'
                    : '❌ Component does not appear to be rendering'}
                </p>
                <p className="text-sm mt-2">
                  If the component is not rendering, check the browser console for errors.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Task Status History Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Task Status Workflow</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${task.status === 'assigned' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>1</div>
                  <div className="flex-1 h-1 bg-gray-200">
                    <div className={`h-full ${task.status === 'assigned' || task.status === 'in_progress' || task.status === 'completed' || task.status === 'confirmed' ? 'bg-blue-500' : 'bg-gray-200'}`} style={{ width: '100%' }}></div>
                  </div>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${task.status === 'in_progress' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>2</div>
                  <div className="flex-1 h-1 bg-gray-200">
                    <div className={`h-full ${task.status === 'in_progress' || task.status === 'completed' || task.status === 'confirmed' ? 'bg-blue-500' : 'bg-gray-200'}`} style={{ width: '100%' }}></div>
                  </div>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${task.status === 'completed' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>3</div>
                  <div className="flex-1 h-1 bg-gray-200">
                    <div className={`h-full ${task.status === 'completed' || task.status === 'confirmed' ? 'bg-blue-500' : 'bg-gray-200'}`} style={{ width: '100%' }}></div>
                  </div>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${task.status === 'confirmed' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>4</div>
                </div>

                <div className="grid grid-cols-4 text-center text-sm">
                  <div className={task.status === 'assigned' ? 'font-bold text-blue-600' : ''}>Assigned</div>
                  <div className={task.status === 'in_progress' ? 'font-bold text-blue-600' : ''}>In Progress</div>
                  <div className={task.status === 'completed' ? 'font-bold text-blue-600' : ''}>Completed</div>
                  <div className={task.status === 'confirmed' ? 'font-bold text-blue-600' : ''}>Confirmed</div>
                </div>

                <div className="bg-blue-50 p-4 rounded-md mt-4">
                  <h3 className="font-bold mb-2">Current Status: <span className="text-blue-700">{task.status}</span></h3>
                  <p className="text-sm mb-3">
                    {task.status === 'assigned' && 'The task is assigned to a maintenance worker. They should click "Start Work" to begin.'}
                    {task.status === 'in_progress' && 'The task is in progress. The maintenance worker should click "Mark as Completed" when done.'}
                    {task.status === 'completed' && 'The task is completed. The task owner should click "Confirm Completion" to finalize.'}
                    {task.status === 'confirmed' && 'The task is confirmed as completed. No further actions are needed.'}
                  </p>

                  {task.status !== 'assigned' && (
                    <button
                      onClick={async () => {
                        try {
                          await supabase
                            .from('tasks')
                            .update({
                              status: 'assigned',
                              updated_at: new Date().toISOString()
                            })
                            .eq('id', id);

                          // Refresh task data
                          const { data: taskData } = await supabase
                            .from('tasks')
                            .select('*')
                            .eq('id', id)
                            .single();

                          if (taskData) {
                            setTask(taskData);
                            queryClient.invalidateQueries({ queryKey: ['task', id] });
                          }
                        } catch (err) {
                          console.error('Error resetting task status:', err);
                        }
                      }}
                      className="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
                    >
                      Reset to "Assigned" Status
                    </button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Debug Information Card */}
          <Card className="bg-gray-50">
            <CardHeader>
              <CardTitle>Debug Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-bold mb-2">Task Properties</h3>
                    <div className="text-sm space-y-1">
                      <p><strong>ID:</strong> {task.id}</p>
                      <p><strong>Title:</strong> {task.title}</p>
                      <p><strong>Status:</strong> {task.status}</p>
                      <p><strong>Visibility:</strong> {task.visibility}</p>
                      <p><strong>Assigned To:</strong> {task.assigned_to}</p>
                      <p><strong>Created By:</strong> {task.user_id}</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-bold mb-2">User Properties</h3>
                    <div className="text-sm space-y-1">
                      <p><strong>ID:</strong> {user?.id}</p>
                      <p><strong>Email:</strong> {user?.email}</p>
                      <p><strong>Role:</strong> {profile?.role}</p>
                      <p><strong>Is Task Owner:</strong> {task.user_id === user?.id ? 'Yes' : 'No'}</p>
                      <p><strong>Is Assigned Staff:</strong> {task.assigned_to === user?.id ? 'Yes' : 'No'}</p>
                      <p><strong>Is Maintenance:</strong> {profile?.role === 'maintenance' ? 'Yes' : 'No'}</p>
                    </div>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => {
                      const detailsElement = document.getElementById('raw-data-details');
                      if (detailsElement) {
                        detailsElement.open = !detailsElement.open;
                      }
                    }}
                    className="text-blue-600 text-sm hover:underline"
                  >
                    Show/Hide Raw Data
                  </button>

                  <details id="raw-data-details" className="mt-2">
                    <summary className="cursor-pointer text-sm font-medium">Raw Data Objects</summary>
                    <div className="text-xs font-mono whitespace-pre-wrap mt-2">
                      <p>Task Object:</p>
                      <pre className="bg-black text-white p-2 rounded overflow-auto max-h-40">
                        {JSON.stringify(task, null, 2)}
                      </pre>

                      <p className="mt-4">User Object:</p>
                      <pre className="bg-black text-white p-2 rounded overflow-auto max-h-40">
                        {JSON.stringify(user, null, 2)}
                      </pre>

                      <p className="mt-4">Profile Object:</p>
                      <pre className="bg-black text-white p-2 rounded overflow-auto max-h-40">
                        {JSON.stringify(profile, null, 2)}
                      </pre>
                    </div>
                  </details>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default TestInternalTaskActions;
