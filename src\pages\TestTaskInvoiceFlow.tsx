import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { stripeService } from '@/services/stripeService';
import { useAuth } from '@/contexts/AuthContext';

const TestTaskInvoiceFlow = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isRunning, setIsRunning] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [results, setResults] = useState<any>({});
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Test data
  const TEST_TASK = {
    title: 'UI Test Task',
    description: 'This is a test task created from the UI to verify the full task-to-invoice flow.',
    budget: 150,
    category: 'Maintenance',
    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
    location: 'Main Building',
    visibility: 'public',
  };

  // Define the steps
  const steps = [
    'Creating a task',
    'Creating an offer',
    'Accepting the offer',
    'Marking task as complete',
    'Creating a payment record',
    'Creating a payment intent',
    'Simulating payment success',
    'Creating an invoice',
    'Verifying the flow',
  ];

  const runTest = async () => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to run this test.',
        variant: 'destructive',
      });
      return;
    }

    setIsRunning(true);
    setCurrentStep(0);
    setResults({});
    setError(null);
    setSuccess(false);

    try {
      // Step 1: Create a task
      setCurrentStep(1);
      const { data: task, error: taskError } = await supabase
        .from('tasks')
        .insert({
          title: TEST_TASK.title,
          description: TEST_TASK.description,
          budget: TEST_TASK.budget,
          category: TEST_TASK.category,
          due_date: TEST_TASK.due_date,
          location: TEST_TASK.location,
          visibility: TEST_TASK.visibility,
          user_id: user.id,
          status: 'open',
        })
        .select()
        .single();

      if (taskError) {
        throw new Error(`Error creating task: ${taskError.message}`);
      }

      setResults(prev => ({ ...prev, task }));

      // Step 2: Create an offer
      setCurrentStep(2);
      const { data: offer, error: offerError } = await supabase
        .from('offers')
        .insert({
          task_id: task.id,
          user_id: user.id, // Using the same user for testing
          amount: TEST_TASK.budget,
          message: `I can complete this task for the listed budget of £${TEST_TASK.budget}.00.`,
          status: 'pending',
        })
        .select()
        .single();

      if (offerError) {
        throw new Error(`Error creating offer: ${offerError.message}`);
      }

      setResults(prev => ({ ...prev, offer }));

      // Step 3: Accept the offer
      setCurrentStep(3);
      const { error: acceptOfferError } = await supabase
        .from('offers')
        .update({ status: 'accepted' })
        .eq('id', offer.id);

      if (acceptOfferError) {
        throw new Error(`Error accepting offer: ${acceptOfferError.message}`);
      }

      // Update task status to assigned
      const { error: assignTaskError } = await supabase
        .from('tasks')
        .update({
          status: 'assigned',
          assigned_to: user.id, // Assign to self for testing
        })
        .eq('id', task.id);

      if (assignTaskError) {
        throw new Error(`Error assigning task: ${assignTaskError.message}`);
      }

      // Step 4: Mark the task as complete
      setCurrentStep(4);
      const { error: completeTaskError } = await supabase
        .from('tasks')
        .update({
          status: 'pending_payment',
          payment_status: 'pending',
        })
        .eq('id', task.id);

      if (completeTaskError) {
        throw new Error(`Error marking task as complete: ${completeTaskError.message}`);
      }

      // Step 5: Create a payment record
      setCurrentStep(5);
      const payment = await stripeService.createPaymentWithDirectTransfer(
        task.id,
        offer.id,
        TEST_TASK.budget
      );

      if (!payment) {
        throw new Error('Failed to create payment record');
      }

      setResults(prev => ({ ...prev, payment }));

      // Step 6: Create a payment intent
      setCurrentStep(6);
      const clientSecret = await stripeService.createPaymentIntent(payment.id);

      if (!clientSecret) {
        throw new Error('Failed to create payment intent');
      }

      setResults(prev => ({ ...prev, clientSecret }));

      // Step 7: Simulate payment success
      setCurrentStep(7);
      // In a real scenario, the user would complete the payment on the frontend
      // For testing, we'll simulate a successful payment by updating the payment status
      const { error: successPaymentError } = await supabase
        .from('payments')
        .update({
          status: 'succeeded',
          updated_at: new Date().toISOString(),
        })
        .eq('id', payment.id);

      if (successPaymentError) {
        throw new Error(`Error updating payment status: ${successPaymentError.message}`);
      }

      // Update the task status
      const { error: paidTaskError } = await supabase
        .from('tasks')
        .update({
          payment_status: 'paid',
          status: 'completed',
        })
        .eq('id', task.id);

      if (paidTaskError) {
        throw new Error(`Error updating task status: ${paidTaskError.message}`);
      }

      // Step 8: Create an invoice
      setCurrentStep(8);
      const invoice = await stripeService.createInvoice(payment.id);

      if (!invoice) {
        throw new Error('Failed to create invoice');
      }

      setResults(prev => ({ ...prev, invoice }));

      // Step 9: Verify the flow
      setCurrentStep(9);
      // Get the task with all related data
      const { data: finalTask, error: finalTaskError } = await supabase
        .from('tasks')
        .select(`
          *,
          offers (*),
          payments (*)
        `)
        .eq('id', task.id)
        .single();

      if (finalTaskError) {
        throw new Error(`Error fetching final task data: ${finalTaskError.message}`);
      }

      // Get the invoice
      const { data: finalInvoice, error: finalInvoiceError } = await supabase
        .from('invoices')
        .select('*')
        .eq('task_id', task.id)
        .single();

      if (finalInvoiceError) {
        throw new Error(`Error fetching final invoice data: ${finalInvoiceError.message}`);
      }

      setResults(prev => ({ ...prev, finalTask, finalInvoice }));
      setSuccess(true);

      toast({
        title: 'Test Completed Successfully',
        description: 'The full task-to-invoice flow has been tested and verified.',
        variant: 'default',
      });
    } catch (err) {
      console.error('Test error:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      
      toast({
        title: 'Test Failed',
        description: err instanceof Error ? err.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <CardTitle>Test Task-to-Invoice Flow</CardTitle>
          <CardDescription>
            This page allows you to test the full flow from creating a task to generating an invoice.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-6">
            <Button
              onClick={runTest}
              disabled={isRunning}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              {isRunning ? (
                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Running Test...</>
              ) : (
                <>Run Full Test</>
              )}
            </Button>
          </div>

          {isRunning && (
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2">Current Progress</h3>
              <div className="space-y-2">
                {steps.map((step, index) => (
                  <div key={index} className="flex items-center">
                    {index + 1 < currentStep ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    ) : index + 1 === currentStep ? (
                      <Loader2 className="h-5 w-5 animate-spin text-blue-500 mr-2" />
                    ) : (
                      <div className="h-5 w-5 rounded-full border border-gray-300 mr-2" />
                    )}
                    <span className={index + 1 <= currentStep ? 'font-medium' : 'text-gray-500'}>
                      {step}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="mb-6 bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <AlertTitle className="text-green-700">Success</AlertTitle>
              <AlertDescription className="text-green-600">
                The full task-to-invoice flow has been tested and verified.
              </AlertDescription>
            </Alert>
          )}

          {Object.keys(results).length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-2">Test Results</h3>
              <div className="bg-gray-50 p-4 rounded-md overflow-auto max-h-96">
                <pre className="text-xs">{JSON.stringify(results, null, 2)}</pre>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TestTaskInvoiceFlow;
