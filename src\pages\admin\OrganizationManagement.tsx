import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Building, Users, Crown, Shield, ArrowLeft, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/hooks/use-toast';
import { Link } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface Organization {
  id: string;
  name: string;
  type: string;
  created_at: string;
  user_count: number;
  membership_tier: string;
  admin_email: string;
}

/**
 * Organization management page for site administrators
 * Shows overview of all organizations with user counts and membership tiers
 */
const OrganizationManagement = () => {
  const { user, isSiteAdmin } = useAuth();
  const navigate = useNavigate();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Redirect if not site admin
    if (user && !isSiteAdmin) {
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to access organization management.',
        variant: 'destructive',
      });
      navigate('/dashboard');
      return;
    }

    // Load organizations data
    const fetchOrganizations = async () => {
      setIsLoading(true);
      try {
        // Get organizations with user counts and admin details
        const { data: orgsData, error: orgsError } = await supabase
          .from('organizations')
          .select(`
            id,
            name,
            type,
            created_at,
            membership_tier
          `)
          .order('created_at', { ascending: false });

        if (orgsError) {
          throw orgsError;
        }

        // Get user counts for each organization
        const organizationsWithCounts = await Promise.all(
          (orgsData || []).map(async (org) => {
            // Get user count for this organization
            const { count: userCount, error: countError } = await supabase
              .from('profiles')
              .select('*', { count: 'exact', head: true })
              .eq('organization_id', org.id);

            // Get admin email for this organization
            const { data: adminData, error: adminError } = await supabase
              .from('profiles')
              .select('email')
              .eq('organization_id', org.id)
              .eq('role', 'admin')
              .limit(1);

            // Extract email from array format
            const adminEmail = adminData && adminData.length > 0 && adminData[0].email
              ? (Array.isArray(adminData[0].email) ? adminData[0].email[0] : adminData[0].email)
              : 'No admin assigned';

            return {
              ...org,
              user_count: userCount || 0,
              admin_email: adminEmail,
              membership_tier: org.membership_tier || 'free'
            };
          })
        );

        setOrganizations(organizationsWithCounts);
      } catch (error) {
        console.error('Error fetching organizations:', error);
        toast({
          title: 'Error',
          description: 'Failed to load organizations data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganizations();
  }, [user, isSiteAdmin, navigate]);

  if (!user || !isSiteAdmin) {
    return null; // Don't render anything if not site admin
  }

  const getTierBadge = (tier: string) => {
    switch (tier.toLowerCase()) {
      case 'premium':
        return <Badge variant="default" className="bg-amber-500"><Crown className="h-3 w-3 mr-1" />Premium</Badge>;
      case 'pro':
        return <Badge variant="secondary"><Shield className="h-3 w-3 mr-1" />Pro</Badge>;
      case 'free':
      default:
        return <Badge variant="outline">Free</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type.toLowerCase()) {
      case 'multi_academy_trust':
        return <Badge variant="default">Multi-Academy Trust</Badge>;
      case 'school':
        return <Badge variant="secondary">School</Badge>;
      case 'supplier':
        return <Badge variant="outline">Supplier</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="sm" asChild className="mr-4">
          <Link to="/admin/site">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Site Admin
          </Link>
        </Button>
        <Building className="h-8 w-8 text-amber-500 mr-2" />
        <h1 className="text-3xl font-bold">Organization Management</h1>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Organizations Overview</CardTitle>
          <CardDescription>
            Manage all organizations on the platform, view membership tiers and user counts
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Organization</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Admin</TableHead>
                  <TableHead>Users</TableHead>
                  <TableHead>Membership Tier</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {organizations.map((org) => (
                  <TableRow key={org.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <Building className="h-4 w-4 mr-2 text-gray-500" />
                        {org.name}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getTypeBadge(org.type)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-gray-600">
                        {org.admin_email}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1 text-gray-500" />
                        {org.user_count}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getTierBadge(org.membership_tier)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-gray-600">
                        {new Date(org.created_at).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm" asChild>
                        <Link to={`/organization/${org.id}/admin-view`}>
                          <ExternalLink className="h-3 w-3 mr-1" />
                          View
                        </Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {!isLoading && organizations.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No organizations found
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Organizations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{organizations.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {organizations.reduce((sum, org) => sum + org.user_count, 0)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Premium Organizations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {organizations.filter(org => org.membership_tier === 'premium').length}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default OrganizationManagement;
