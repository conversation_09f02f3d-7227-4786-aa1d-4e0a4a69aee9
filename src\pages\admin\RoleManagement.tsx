import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/hooks/use-toast';
import { Input } from '@/components/ui/input';
import { Shield, Search } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface UserProfile {
  id: string;
  email: string[];
  first_name: string | null;
  last_name: string | null;
  role: string | null;
  is_site_admin: boolean | null;
  organization_id: string | null;
  created_at: string;
}

/**
 * Role Management page for site administrators to manage user roles
 */
const RoleManagement = () => {
  const navigate = useNavigate();
  const { user, isSiteAdmin } = useAuth();
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Redirect if not site admin
    if (user && !isSiteAdmin) {
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to access role management.',
        variant: 'destructive',
      });
      navigate('/dashboard');
      return;
    }

    // Load users
    const fetchUsers = async () => {
      setIsLoading(true);
      try {
        // Get users from profiles table
        const { data, error } = await supabase
          .from('profiles')
          .select(`
            id,
            email,
            first_name,
            last_name,
            role,
            is_site_admin,
            organization_id,
            created_at
          `)
          .order('created_at', { ascending: false });

        if (error) {
          throw error;
        }

        setUsers(data || []);
      } catch (error) {
        console.error('Error loading users:', error);
        toast({
          title: 'Error',
          description: 'Failed to load users',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, [user, isSiteAdmin, navigate]);

  const updateUserRole = async (userId: string, role: string) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ role })
        .eq('id', userId);

      if (error) throw error;

      toast({
        title: 'Role Updated',
        description: 'User role has been updated successfully',
      });

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? { ...user, profile: user.profile ? { ...user.profile, role } : null } : user
      ));
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update user role',
        variant: 'destructive',
      });
    }
  };

  const toggleSiteAdmin = async (userId: string, isSiteAdmin: boolean) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ is_site_admin: isSiteAdmin })
        .eq('id', userId);

      if (error) throw error;

      toast({
        title: 'Site Admin Status Updated',
        description: `User is ${isSiteAdmin ? 'now' : 'no longer'} a site admin`,
      });

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? { ...user, profile: user.profile ? { ...user.profile, is_site_admin: isSiteAdmin } : null } : user
      ));
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update site admin status',
        variant: 'destructive',
      });
    }
  };

  // Filter users based on search query
  const filteredUsers = searchQuery
    ? users.filter(user => {
        // Handle email as an array
        const emailMatch = user.email && Array.isArray(user.email) &&
          user.email.some(email => email?.toLowerCase().includes(searchQuery.toLowerCase()));

        // Handle name search
        const nameMatch = `${user.first_name || ''} ${user.last_name || ''}`.toLowerCase().includes(searchQuery.toLowerCase());

        return emailMatch || nameMatch;
      })
    : users;

  if (!user || !isSiteAdmin) {
    return null; // Don't render anything if not site admin
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <Shield className="h-8 w-8 text-red-500 mr-2" />
        <h1 className="text-3xl font-bold">Role Management</h1>
      </div>

      <div className="flex justify-between items-center mb-6">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search users..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Site Admin</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  Loading users...
                </TableCell>
              </TableRow>
            ) : filteredUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  No users found
                </TableCell>
              </TableRow>
            ) : (
              filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    {user.first_name} {user.last_name}
                  </TableCell>
                  <TableCell>
                    {user.email && Array.isArray(user.email) ? user.email[0] : ''}
                  </TableCell>
                  <TableCell>
                    <Select
                      value={user.role || ''}
                      onValueChange={(value) => updateUserRole(user.id, value)}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="admin">Organization Admin</SelectItem>
                        <SelectItem value="teacher">Teacher</SelectItem>
                        <SelectItem value="maintenance">Maintenance</SelectItem>
                        <SelectItem value="support">Support</SelectItem>
                        <SelectItem value="supplier">Supplier</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Switch
                      checked={user.is_site_admin || false}
                      onCheckedChange={(checked) => toggleSiteAdmin(user.id, checked)}
                    />
                  </TableCell>
                  <TableCell>
                    <Button variant="outline" size="sm">View Details</Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default RoleManagement;
