import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Shield, Users, Building, Database, Server, FileText, ShoppingBag } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { Link } from 'react-router-dom';

/**
 * Dashboard for site administrators with platform-wide statistics and management tools
 */
const SiteAdminDashboard = () => {
  const { user, isSiteAdmin } = useAuth();
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalOrganizations: 0,
    totalTasks: 0,
    totalSuppliers: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    // Redirect if not site admin
    if (user && !isSiteAdmin) {
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to access the site admin dashboard.',
        variant: 'destructive',
      });
      navigate('/dashboard');
      return;
    }
    
    // Load site stats
    const fetchStats = async () => {
      setIsLoading(true);
      try {
        // Get total users
        const { count: userCount, error: userError } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true });
          
        // Get total organizations
        const { count: orgCount, error: orgError } = await supabase
          .from('organizations')
          .select('*', { count: 'exact', head: true });
          
        // Get total tasks
        const { count: taskCount, error: taskError } = await supabase
          .from('tasks')
          .select('*', { count: 'exact', head: true });
          
        // Get total suppliers
        const { count: supplierCount, error: supplierError } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .eq('account_type', 'supplier');
          
        if (userError || orgError || taskError || supplierError) {
          throw new Error('Error fetching stats');
        }
        
        setStats({
          totalUsers: userCount || 0,
          totalOrganizations: orgCount || 0,
          totalTasks: taskCount || 0,
          totalSuppliers: supplierCount || 0
        });
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to load site statistics',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchStats();
  }, [user, isSiteAdmin, navigate]);
  
  if (!user || !isSiteAdmin) {
    return null; // Don't render anything if not site admin
  }
  
  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <Shield className="h-8 w-8 text-red-500 mr-2" />
        <h1 className="text-3xl font-bold">Site Administration</h1>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Users className="h-5 w-5 text-blue-500 mr-2" />
              <div className="text-2xl font-bold">
                {isLoading ? '...' : stats.totalUsers}
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Organizations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Building className="h-5 w-5 text-amber-500 mr-2" />
              <div className="text-2xl font-bold">
                {isLoading ? '...' : stats.totalOrganizations}
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Tasks</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <FileText className="h-5 w-5 text-green-500 mr-2" />
              <div className="text-2xl font-bold">
                {isLoading ? '...' : stats.totalTasks}
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Suppliers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <ShoppingBag className="h-5 w-5 text-indigo-500 mr-2" />
              <div className="text-2xl font-bold">
                {isLoading ? '...' : stats.totalSuppliers}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Tabs defaultValue="users">
        <TabsList className="mb-6">
          <TabsTrigger value="users">User Management</TabsTrigger>
          <TabsTrigger value="organizations">Organizations</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>
        
        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>
                Manage users and their roles across the platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button asChild>
                  <Link to="/admin/roles">Manage User Roles</Link>
                </Button>
                <Button asChild variant="outline" className="ml-2">
                  <Link to="/admin/users">View All Users</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="organizations">
          <Card>
            <CardHeader>
              <CardTitle>Organization Management</CardTitle>
              <CardDescription>
                Manage organizations and their hierarchies
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button asChild>
                  <Link to="/admin/organizations">Manage Organizations</Link>
                </Button>
                <Button asChild variant="outline" className="ml-2">
                  <Link to="/admin/organization-types">Organization Types</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="system">
          <Card>
            <CardHeader>
              <CardTitle>System Settings</CardTitle>
              <CardDescription>
                Manage system-wide settings and configurations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button asChild>
                  <Link to="/admin/settings">System Settings</Link>
                </Button>
                <Button asChild variant="outline" className="ml-2">
                  <Link to="/admin/logs">System Logs</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SiteAdminDashboard;
