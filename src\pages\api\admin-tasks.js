// API route for admin tasks
import { createClient } from '@supabase/supabase-js';

export default async function handler(req, res) {
  if (process.env.NODE_ENV === 'development') {

    console.log('[API Route] Handling request for /api/admin-tasks');
  

    }
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {
    // Initialize Supabase client with service role key for admin access
    const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseServiceKey) {
      console.error('[API Route] Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
      return res.status(500).json({ error: 'Server configuration error' });
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Get the user ID from the request
    const { user_id } = req.query;
    
    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`[API Route] Fetching tasks for admin user: ${user_id}`.replace(/user.*/, 'hasUser: ' + !!user));
    

    
      }
    // Get user profile to verify admin role
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user_id)
      .single();
    
    if (profileError) {
      console.error('[API Route] Error fetching user profile:', profileError);
      return res.status(500).json({ error: 'Error fetching user profile' });
    }
    
    if (!profileData || profileData.role !== 'admin') {
      console.error('[API Route] User is not an admin:', user_id);
      return res.status(403).json({ error: 'Unauthorized: User is not an admin' });
    }
    
    // Fetch all open tasks
    if (process.env.NODE_ENV === 'development') {

      console.log('[API Route] Fetching all open tasks');

      }
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('*')
      .eq('status', 'open')
      .order('created_at', { ascending: false });
    
    if (tasksError) {
      console.error('[API Route] Error fetching tasks:', tasksError);
      return res.status(500).json({ error: 'Error fetching tasks' });
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`[API Route] Successfully fetched ${tasks.length} tasks`);

    
      }
    return res.status(200).json({ tasks });
    
  } catch (error) {
    console.error('[API Route] Unexpected error:', error);
    return res.status(500).json({ error: 'Unexpected error' });
  }
}
