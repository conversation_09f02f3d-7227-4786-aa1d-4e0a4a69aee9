/**
 * GetStream Channel API Route
 * 
 * This API route creates a channel for GetStream Chat.
 * It's designed to work as a Vercel serverless function.
 */

import { StreamChat } from 'stream-chat';

export default async function handler(req, res) {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { taskId, taskTitle, members } = req.body;

    if (!taskId || !taskTitle) {
      return res.status(400).json({ error: 'Task ID and title are required' });
    }

    // Get API key and secret from environment variables
    const streamApiKey = process.env.GETSTREAM_API_KEY;
    const streamApiSecret = process.env.GETSTREAM_API_SECRET;

    // Check if API key and secret are available
    if (!streamApiKey || !streamApiSecret) {
      console.error('Error: GetStream API key or secret is missing in environment variables.');
      return res.status(500).json({ error: 'Server configuration error' });
    }

    // Initialize GetStream client
    const serverClient = StreamChat.getInstance(streamApiKey, streamApiSecret);

    // Create a channel
    const channelId = `task-${taskId}`;
    
    // Use provided members or create an empty array
    const channelMembers = members && Array.isArray(members) ? members : [];
    
    const channel = serverClient.channel('messaging', channelId, {
      name: taskTitle,
      members: channelMembers,
      task_id: taskId,
    });
    
    await channel.create();
    
    if (process.env.NODE_ENV === 'development') {
    
      console.log('Channel created successfully:', channelId);
    
    
      }
    res.json({
      channelId,
      channel: channel.id,
      members: channel.state.members
    });
  } catch (error) {
    console.error('Error creating channel:', error);
    res.status(500).json({ error: 'Failed to create channel' });
  }
}
