/**
 * GetStream Token API Route
 *
 * This API route generates a token for GetStream Chat authentication.
 * It's designed to work as a Vercel serverless function.
 */

import { StreamChat } from 'stream-chat';

export default async function handler(req, res) {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Get API key and secret from environment variables
    const streamApiKey = process.env.GETSTREAM_API_KEY;
    const streamApiSecret = process.env.GETSTREAM_API_SECRET;

    // Check if API key and secret are available
    if (!streamApiKey || !streamApiSecret) {
      console.error('Error: GetStream API key or secret is missing in environment variables.');
      return res.status(500).json({ error: 'Server configuration error' });
    }

    // Initialize GetStream client
    const serverClient = StreamChat.getInstance(streamApiKey, streamApiSecret);

    // Generate a token for the user
    const token = serverClient.createToken(userId);

    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
        console.log('Token generated successfully for user: completed');
        }
    }

    res.json({ token });
  } catch (error) {
    console.error('Error generating token:', error);
    res.status(500).json({ error: 'Failed to generate token' });
  }
}
