/**
 * GetStream Channels API Route
 * 
 * This API route creates a channel for a task.
 */

export default async function handler(req, res) {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { taskId, taskTitle, members } = req.body;

    if (!taskId || !taskTitle) {
      return res.status(400).json({ error: 'Task ID and title are required' });
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('Creating channel for task: completed');
  }
    // Forward the request to the server
    const response = await fetch('http://localhost:3001/api/getstream/channels', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ taskId, taskTitle, members }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error creating channel:', errorData);
      return res.status(response.status).json(errorData);
    }

    const data = await response.json();
    return res.json(data);
  } catch (error) {
    console.error('Error creating channel:', error);
    return res.status(500).json({ error: 'Failed to create channel' });
  }
}
