/**
 * GetStream Migrate Task Chat API Route
 * 
 * This API route migrates a task's chat to GetStream.
 */

export default async function handler(req, res) {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { taskId, taskTitle } = req.body;

    if (!taskId || !taskTitle) {
      return res.status(400).json({ error: 'Task ID and title are required' });
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('Migrating chat for task: completed');
  }
    // Forward the request to the server
    const response = await fetch('http://localhost:3001/api/getstream/migrate-task-chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ taskId, taskTitle }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error migrating task chat:', errorData);
      return res.status(response.status).json(errorData);
    }

    const data = await response.json();
    return res.json(data);
  } catch (error) {
    console.error('Error migrating task chat:', error);
    return res.status(500).json({ error: 'Failed to migrate task chat' });
  }
}
