/**
 * GetStream Token API Route
 * 
 * This API route generates a token for GetStream Chat authentication.
 */

export default async function handler(req, res) {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    if (process.env.NODE_ENV === 'development') {

      console.log('Generating token for user:', userId.replace(/user.*/, 'hasUser: ' + !!user));


      }
    // Forward the request to the server
    const response = await fetch('http://localhost:3001/api/getstream/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error generating token:', errorData);
      return res.status(response.status).json(errorData);
    }

    const data = await response.json();
    return res.json(data);
  } catch (error) {
    console.error('Error generating token:', error);
    return res.status(500).json({ error: 'Failed to generate token' });
  }
}
