// API route for submitting support requests
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.VITE_SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Create a Supabase client with the service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { name, email, organization, organization_id, support_type, message } = req.body;

    // Validate required fields
    if (!name || !email || !support_type || !message) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Insert the support request into the database
    const { data, error } = await supabase
      .from('support_requests')
      .insert({
        name,
        email,
        organization: organization || null,
        organization_id: organization_id || null,
        support_type,
        message,
        status: 'new'
      })
      .select();

    if (error) {
      console.error('Error inserting support request:', error);
      return res.status(500).json({ error: 'Failed to submit support request' });
    }

    // Log the support request for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log('Support request submitted: completed');

      }
    // Return success response
    return res.status(200).json({ success: true, data });
  } catch (error) {
    console.error('Error processing support request:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
