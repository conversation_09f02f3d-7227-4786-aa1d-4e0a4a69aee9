/**
 * GetStream Chat List Component for Mobile
 *
 * A mobile-optimized implementation of the chat list using GetStream
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  MessageSquare,
  RefreshCw,
  AlertCircle,
  ArrowLeft,
  Clock
} from 'lucide-react';
import { StreamChat } from 'stream-chat';
import { getStreamClient, connectUser } from '@/integrations/getstream/client';

interface ChatChannel {
  id: string;
  name: string;
  taskId?: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
}

const GetStreamChatList: React.FC = () => {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [channels, setChannels] = useState<ChatChannel[]>([]);
  const [streamClient, setStreamClient] = useState<StreamChat | null>(null);

  // Initialize GetStream client
  useEffect(() => {
    if (!user) return;

    const initializeStreamClient = async () => {
      try {
        setLoading(true);

        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        // Connect to GetStream
        const client = await connectUser(
          user.id,
          userName,
          user.id, // This is just a placeholder, the actual token is generated server-side
          profile?.avatar_url || undefined
        );

        setStreamClient(client);

        // Fetch user's channels
        const filter = { type: 'messaging', members: { $in: [user.id] } };
        const sort = { last_message_at: -1 };

        console.log('[GetStreamChatList] Querying channels with filter:', filter);

        // FIXED: Get channel list with minimal state to ensure member filtering works
        const userChannels = await client.queryChannels(filter, sort, {
          limit: 20,           // ✅ Reduced limit for faster loading
          state: true,         // ✅ Need state for member filtering to work properly
          watch: false,        // ✅ No real-time subscriptions for list
          message_limit: 1,    // ✅ Only last message for preview
        });

        console.log('[GetStreamChatList] Raw channels from GetStream:', userChannels.length);
        userChannels.forEach((channel, index) => {
          console.log(`[GetStreamChatList] Channel ${index}:`, {
            id: channel.id,
            taskId: channel.data?.task_id,
            name: channel.data?.name,
            members: Object.keys(channel.state?.members || {}),
            lastMessageAt: channel.data?.last_message_at,
            hasMessages: channel.state?.messages?.length || 0
          });
        });

        // SECURITY: Filter channels based on organization membership (OPTIMIZED)
        const { data: userProfile } = await supabase
          .from('profiles')
          .select('organization_id, account_type')
          .eq('id', user.id)
          .single();

        const secureChannels = [];

        // Separate task and non-task channels
        const taskChannels = userChannels.filter(channel => channel.data?.task_id);
        const nonTaskChannels = userChannels.filter(channel => !channel.data?.task_id);

        console.log('[GetStreamChatList] Task channels found:', taskChannels.length);
        console.log('[GetStreamChatList] Non-task channels found:', nonTaskChannels.length);

        // Add non-task channels immediately
        secureChannels.push(...nonTaskChannels);

        if (taskChannels.length > 0) {
          // Batch query all tasks at once
          const taskIds = taskChannels.map(channel => channel.data.task_id);
          console.log('[GetStreamChatList] Querying tasks for security filtering:', taskIds);

          const { data: tasks, error: tasksError } = await supabase
            .from('tasks')
            .select('id, organization_id, visibility')
            .in('id', taskIds);

          if (tasksError) {
            console.error('[GetStreamChatList] Error fetching tasks for security filtering:', tasksError);
          } else if (tasks) {
            console.log('[GetStreamChatList] Tasks found for security filtering:', tasks);
            const taskMap = new Map(tasks.map(task => [task.id, task]));

            for (const channel of taskChannels) {
              const taskId = channel.data.task_id;
              const task = taskMap.get(taskId);

              if (!task) {
                console.warn('[GetStreamChatList] Task not found for channel:', channel.id, 'taskId:', taskId);
                continue;
              }

              const hasAccess = (
                userProfile?.organization_id === task.organization_id ||
                (userProfile?.account_type === 'supplier' && task.visibility === 'public')
              );

              console.log('[GetStreamChatList] Access check for channel:', {
                channelId: channel.id,
                taskId,
                userOrg: userProfile?.organization_id,
                taskOrg: task.organization_id,
                userAccountType: userProfile?.account_type,
                taskVisibility: task.visibility,
                hasAccess
              });

              if (hasAccess) {
                secureChannels.push(channel);
                console.log('[GetStreamChatList] Added channel to secure list:', channel.id);
              } else {
                console.warn('[GetStreamChatList] Filtering out unauthorized channel:', {
                  channelId: channel.id,
                  taskId,
                  userOrg: userProfile?.organization_id,
                  taskOrg: task.organization_id
                });
              }
            }
          }
        }

        // LIGHTWEIGHT FORMAT: Format channels for display without heavy operations
        const formattedChannels = secureChannels.map(channel => ({
          id: channel.id,
          name: channel.data?.name || 'Chat',
          taskId: channel.data?.task_id,
          lastMessage: channel.state?.messages?.[0]?.text ||
                      channel.data?.last_message_text ||
                      'No messages yet',
          lastMessageTime: channel.data?.last_message_at,
          unreadCount: 0 // Will be calculated when chat is opened
        }));

        console.log('[GetStreamChatList] Final formatted channels for display:', formattedChannels.length);
        formattedChannels.forEach((channel, index) => {
          console.log(`[GetStreamChatList] Display channel ${index}:`, {
            id: channel.id,
            name: channel.name,
            taskId: channel.taskId,
            lastMessage: channel.lastMessage,
            lastMessageTime: channel.lastMessageTime
          });
        });

        setChannels(formattedChannels);
        setLoading(false);
      } catch (error) {
        console.error('[GetStreamChatList] Error initializing Stream client:', error);
        setError('Failed to load chats. Please try again later.');
        setLoading(false);
      }
    };

    initializeStreamClient();

    return () => {
      // Clean up
      if (streamClient) {
        streamClient.disconnectUser();
      }
    };
  }, [user, profile]);

  // Handle refresh
  const handleRefresh = () => {
    if (streamClient) {
      setLoading(true);
      streamClient.disconnectUser().then(() => {
        setStreamClient(null);
        setChannels([]);
        // Re-initialize
        if (user) {
          const userName = profile?.first_name && profile?.last_name
            ? `${profile.first_name} ${profile.last_name}`
            : user.email?.split('@')[0] || 'User';

          connectUser(
            user.id,
            userName,
            user.id,
            profile?.avatar_url || undefined
          ).then(client => {
            setStreamClient(client);

            // Fetch user's channels
            const filter = { type: 'messaging', members: { $in: [user.id] } };
            const sort = { last_message_at: -1 };

            // LIGHTWEIGHT APPROACH: Get channel list without full state (refresh)
            client.queryChannels(filter, sort, {
              limit: 20,           // ✅ Reduced limit for faster refresh
              state: false,        // ✅ No full state loading
              watch: false,        // ✅ No real-time subscriptions for list
              message_limit: 1,    // ✅ Only last message for preview
            }).then(async userChannels => {
              // SECURITY: Filter channels based on organization membership (OPTIMIZED)
              const { data: userProfile } = await supabase
                .from('profiles')
                .select('organization_id, account_type')
                .eq('id', user.id)
                .single();

              const secureChannels = [];

              // Separate task and non-task channels
              const taskChannels = userChannels.filter(channel => channel.data?.task_id);
              const nonTaskChannels = userChannels.filter(channel => !channel.data?.task_id);

              // Add non-task channels immediately
              secureChannels.push(...nonTaskChannels);

              if (taskChannels.length > 0) {
                // Batch query all tasks at once
                const taskIds = taskChannels.map(channel => channel.data.task_id);
                const { data: tasks, error: tasksError } = await supabase
                  .from('tasks')
                  .select('id, organization_id, visibility')
                  .in('id', taskIds);

                if (!tasksError && tasks) {
                  const taskMap = new Map(tasks.map(task => [task.id, task]));

                  for (const channel of taskChannels) {
                    const taskId = channel.data.task_id;
                    const task = taskMap.get(taskId);

                    if (!task) continue;

                    const hasAccess = (
                      userProfile?.organization_id === task.organization_id ||
                      (userProfile?.account_type === 'supplier' && task.visibility === 'public')
                    );

                    if (hasAccess) {
                      secureChannels.push(channel);
                    }
                  }
                }
              }

              // LIGHTWEIGHT FORMAT: Format channels for display without heavy operations (refresh)
              const formattedChannels = secureChannels.map(channel => ({
                id: channel.id,
                name: channel.data?.name || 'Chat',
                taskId: channel.data?.task_id,
                lastMessage: channel.state?.messages?.[0]?.text ||
                            channel.data?.last_message_text ||
                            'No messages yet',
                lastMessageTime: channel.data?.last_message_at,
                unreadCount: 0 // Will be calculated when chat is opened
              }));

              setChannels(formattedChannels);
              setLoading(false);
            }).catch(error => {
              console.error('[GetStreamChatList] Error fetching channels:', error);
              setError('Failed to load chats. Please try again later.');
              setLoading(false);
            });
          }).catch(error => {
            console.error('[GetStreamChatList] Error reconnecting user:', error);
            setError('Failed to reconnect. Please try again later.');
            setLoading(false);
          });
        }
      }).catch(error => {
        console.error('[GetStreamChatList] Error disconnecting user:', error);
        setLoading(false);
      });
    }
  };

  // Get initials for avatar
  const getInitials = (name: string): string => {
    if (!name) return 'C';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Format date
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Handle channel click
  const handleChannelClick = (channel: ChatChannel) => {
    navigate(`/mobile/stream-chat/${channel.id}${channel.taskId ? `?task=${channel.taskId}` : ''}`);
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/dashboard')}
            className="mr-3 p-1 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft size={20} />
          </button>
          <h1 className="text-xl font-semibold">Messages</h1>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Error state */}
      {error && (
        <div className="bg-red-50 border-b border-red-200 px-4 py-2 flex items-center">
          <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
          <span className="text-sm text-red-700">{error}</span>
        </div>
      )}

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {loading ? (
          // Loading state
          <div className="space-y-4">
            {Array(5).fill(0).map((_, i) => (
              <Card key={i} className="mb-2">
                <CardContent className="p-4">
                  <div className="flex">
                    <Skeleton className="h-10 w-10 rounded-full mr-3" />
                    <div className="flex-1">
                      <Skeleton className="h-5 w-32 mb-1" />
                      <Skeleton className="h-4 w-48" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : channels.length === 0 ? (
          // Empty state
          <div className="text-center py-12">
            <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-1">No messages yet</h3>
            <p className="text-gray-500">
              Your messages will appear here when you start chatting with others.
            </p>
          </div>
        ) : (
          // Channels list
          <div className="space-y-2">
            {channels.map((channel) => (
              <Card
                key={channel.id}
                className="cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => handleChannelClick(channel)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <Avatar className="h-10 w-10 mr-3">
                      <AvatarFallback>{getInitials(channel.name)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-start">
                        <h3 className="font-medium text-gray-900 truncate">{channel.name}</h3>
                        <div className="text-xs text-gray-400 flex items-center ml-2 whitespace-nowrap">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatDate(channel.lastMessageTime)}
                        </div>
                      </div>
                      <p className="text-sm text-gray-500 truncate">{channel.lastMessage}</p>
                      {channel.taskId && (
                        <span className="text-xs text-classtasker-blue mt-1 inline-block">
                          View Task
                        </span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default GetStreamChatList;
