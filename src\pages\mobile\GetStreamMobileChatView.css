/**
 * GetStream Mobile Chat View Styles
 */

/* Container */
.mobile-chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f9fafb;
}

/* Header */
.mobile-chat-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-button {
  margin-right: 0.75rem;
  padding: 0.25rem;
  border-radius: 9999px;
}

.back-button:hover {
  background-color: #f3f4f6;
}

.header-info {
  display: flex;
  align-items: center;
}

.header-avatar {
  height: 2rem;
  width: 2rem;
  margin-right: 0.75rem;
  border: 1px solid #e5e7eb;
}

.avatar-fallback {
  background-color: #dbeafe;
  color: #2563eb;
}

.header-title {
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

.header-status {
  font-size: 0.75rem;
  color: #6b7280;
}

.info-button {
  padding: 0.5rem;
  border-radius: 9999px;
}

.info-button:hover {
  background-color: #f3f4f6;
}

/* Offline Banner */
.offline-banner {
  background-color: #fef3c7;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.offline-icon {
  color: #d97706;
  margin-right: 0.5rem;
}

.offline-text {
  font-size: 0.875rem;
  color: #92400e;
}

/* Chat Content */
.mobile-chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

/* Loading Skeletons */
.loading-skeletons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.skeleton-message {
  max-width: 80%;
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.skeleton-message.left {
  align-self: flex-start;
  background-color: white;
}

.skeleton-message.right {
  align-self: flex-end;
  background-color: #dbeafe;
}

/* Custom Message Input */
.mobile-message-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: white;
  border-top: 1px solid #e5e7eb;
  position: sticky;
  bottom: 0;
}

.mobile-message-input input {
  flex: 1;
  padding: 0.75rem;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
  outline: none;
  resize: none;
}

.mobile-message-input input:focus {
  border-color: #93c5fd;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.send-button {
  border-radius: 9999px;
  background-color: #3b82f6;
  color: white;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:hover:not(.disabled) {
  background-color: #2563eb;
}

.send-button.disabled {
  background-color: #d1d5db;
  cursor: not-allowed;
}

/* GetStream Overrides */
.str-chat {
  height: 100%;
  --str-chat__primary-color: #3b82f6;
  --str-chat__active-primary-color: #2563eb;
  --str-chat__surface-color: #ffffff;
  --str-chat__avatar-background-color: #dbeafe;
  --str-chat__avatar-text-color: #2563eb;
}

.str-chat__message-list {
  padding: 0.5rem;
}

.str-chat__message-input {
  padding: 0.5rem;
}

.str-chat__message-input-inner {
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.str-chat__message-input-inner:focus-within {
  border-color: #93c5fd;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.str-chat__message-textarea {
  font-size: 0.875rem;
}

.str-chat__message-textarea::placeholder {
  color: #9ca3af;
}

.str-chat__send-button {
  background-color: #3b82f6;
}

.str-chat__send-button:hover {
  background-color: #2563eb;
}

.str-chat__message--me {
  background-color: #dbeafe;
}

.str-chat__message--other {
  background-color: white;
}

/* Hide some elements on mobile */
.str-chat__header {
  display: none;
}

.str-chat__thread {
  display: none;
}
