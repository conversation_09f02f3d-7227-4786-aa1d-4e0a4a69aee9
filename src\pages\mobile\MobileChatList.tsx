/**
 * Mobile Chat List Component
 *
 * A full-screen mobile-optimized view that displays all chat threads
 * for the current user in a format similar to native chat apps.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { ArrowLeft, MessageSquare } from 'lucide-react';

interface ChatThread {
  id: string;
  task_id: string;
  status: string;
  created_at: string;
  updated_at: string;
  last_message_at: string;
  supplier_id: string;
  admin_id: string;
  has_offer: boolean;
  is_closed?: boolean;
  task: {
    id: string;
    title: string;
    status: string;
  };
  supplier?: {
    id: string;
    first_name?: string;
    last_name?: string;
    email?: string[];
  };
  last_message?: {
    content: string;
    created_at: string;
    sender_id: string;
  };
  unread_count?: number;
}

const MobileChatList: React.FC = () => {
  const [chatThreads, setChatThreads] = useState<ChatThread[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const navigate = useNavigate();

  // State for debug info
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [showDebug, setShowDebug] = useState(false);

  // Function to add debug info
  const addDebugInfo = (info: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(info);
      }
    setDebugInfo(prev => [info, ...prev].slice(0, 20)); // Keep last 20 messages
  };

  useEffect(() => {
    // Create a flag to track if the component is mounted
    let isMounted = true;

    const fetchChatThreads = async () => {
      if (!user) {
        if (isMounted) {
          addDebugInfo('No user found, cannot fetch threads');
          setError('You must be logged in to view messages');
          setLoading(false);
        }
        return;
      }

      try {
        // Don't set loading to true here, as it's already set by the caller
        // Don't clear error here, as it might be set by checkDatabaseAccess

        if (isMounted) {
          addDebugInfo(`Fetching chat threads for user: ${user.id}`);
        }

        // First check if the user has a profile
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('id, first_name, last_name, email, account_type')
          .eq('id', user.id)
          .single();

        // Check if component is still mounted before updating state
        if (!isMounted) return;

        if (profileError) {
          addDebugInfo(`Error fetching profile: ${profileError.message}`);
          setError(`Could not find your user profile: ${profileError.message}`);
          setLoading(false);
          return;
        } else {
          addDebugInfo(`User profile found: ${profileData.first_name || profileData.email}`);
          addDebugInfo(`Account type: ${profileData.account_type || 'unknown'}`);
        }

        // Fetch chat threads where the user is a participant (either as admin or supplier)
        addDebugInfo('Executing chat_threads query...');

        // Use a single query with a timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Query timeout after 10 seconds')), 10000)
        );

        const queryPromise = supabase
          .from('chat_threads')
          .select(`
            id,
            task_id,
            status,
            created_at,
            updated_at,
            last_message_at,
            supplier_id,
            admin_id,
            has_offer,
            is_closed,
            task:tasks(id, title, status),
            supplier:supplier_id(id, first_name, last_name, email)
          `)
          .or(`supplier_id.eq.${user.id},admin_id.eq.${user.id}`)
          .order('last_message_at', { ascending: false });

        // Race the query against the timeout
        const response = await Promise.race([queryPromise, timeoutPromise])
          .catch(error => {
            if (!isMounted) return { data: null, error };
            addDebugInfo(`Query timeout or error: ${error.message}`);
            throw error;
          });

        if (!isMounted) return;

        // Use the response from our race
        const { data, error } = response;

        if (error) {
          if (!isMounted) return;
          addDebugInfo(`Error fetching chat threads: ${error.message}`);
          setError(`Failed to fetch your messages: ${error.message}`);
          setLoading(false);
          return;
        }

        if (!isMounted) return;
        addDebugInfo(`Fetched chat threads: ${data?.length || 0}`);

        // If no threads, return empty array
        if (!data || data.length === 0) {
          addDebugInfo('No chat threads found for this user');

          // Check if there are any threads at all in the system
          const { count, error: countError } = await supabase
            .from('chat_threads')
            .select('*', { count: 'exact', head: true });

          if (countError) {
            addDebugInfo(`Error checking total threads: ${countError.message}`);
          } else {
            addDebugInfo(`Total threads in system: ${count}`);

            if (count > 0) {
              addDebugInfo(`There are ${count} threads in the system, but none for this user`);
            }
          }

          setChatThreads([]);
          setLoading(false);
          return;
        }

        // Fetch the last message for each thread with a timeout
        if (!isMounted) return;
        addDebugInfo(`Processing ${data.length} threads for messages...`);

        // Use a simplified approach to get thread data
        // Instead of fetching details for each thread individually (which can cause timeouts),
        // we'll do a single batch query for all last messages
        try {
          // Get all thread IDs
          const threadIds = data.map(thread => thread.id);

          // Batch fetch the last message for each thread
          const { data: lastMessages, error: batchError } = await supabase
            .from('task_messages')
            .select('thread_id, content, created_at, sender_id')
            .in('thread_id', threadIds)
            .order('created_at', { ascending: false });

          if (!isMounted) return;

          if (batchError) {
            addDebugInfo(`Error batch fetching messages: ${batchError.message}`);
            // Continue with threads but without messages
          } else {
            addDebugInfo(`Fetched ${lastMessages?.length || 0} messages for ${threadIds.length} threads`);
          }

          // Create a map of thread_id to last message
          const lastMessageMap = {};
          if (lastMessages && lastMessages.length > 0) {
            // Group messages by thread_id and take the most recent one
            lastMessages.forEach(msg => {
              if (!lastMessageMap[msg.thread_id] ||
                  new Date(msg.created_at) > new Date(lastMessageMap[msg.thread_id].created_at)) {
                lastMessageMap[msg.thread_id] = msg;
              }
            });
          }

          // Create a simplified unread count (0 or 1) to avoid excessive queries
          // This is a compromise to prevent the app from hanging
          const threadsWithLastMessage = data.map(thread => {
            const lastMessage = lastMessageMap[thread.id] || null;
            const hasUnread = lastMessage && lastMessage.sender_id !== user.id;

            return {
              ...thread,
              last_message: lastMessage,
              unread_count: hasUnread ? 1 : 0
            };
          });

          if (!isMounted) return;

          addDebugInfo(`Processed ${threadsWithLastMessage.length} threads with messages`);
          setChatThreads(threadsWithLastMessage);
        } catch (innerErr) {
          if (!isMounted) return;
          addDebugInfo(`Error processing messages: ${innerErr instanceof Error ? innerErr.message : 'Unknown error'}`);
          // Still set the threads without messages rather than failing completely
          setChatThreads(data.map(thread => ({
            ...thread,
            last_message: null,
            unread_count: 0
          })));
        }
      } catch (err) {
        if (!isMounted) return;
        addDebugInfo(`Error in fetchChatThreads: ${err instanceof Error ? err.message : 'Unknown error'}`);
        setError(`Failed to load chat threads: ${err instanceof Error ? err.message : 'Unknown error'}`);
        setLoading(false);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Return cleanup function to set isMounted to false when component unmounts
    return () => {
      isMounted = false;
    };

    // Function to refresh chat threads
    const refreshChatThreads = async () => {
      addDebugInfo('Manually refreshing chat threads');
      setLoading(true);
      setError(null);
      setDebugInfo([]); // Clear debug info on refresh

      // First check database access
      const dbAccessOk = await checkDatabaseAccess();

      // Only fetch threads if database access is OK
      if (dbAccessOk) {
        fetchChatThreads();
      } else {
        addDebugInfo('Skipping thread fetch due to database access issues');
        // Loading state will be set to false by checkDatabaseAccess
      }
    };

    // Function to toggle debug panel
    const toggleDebugPanel = () => {
      setShowDebug(prev => !prev);
      addDebugInfo('Debug panel toggled');
    };

    // Run initial database check and then fetch chat threads
    const initializeComponent = async () => {
      if (user) {
        await checkDatabaseAccess();
        fetchChatThreads();
      }
    };

    initializeComponent();
  }, [user]);

  // Function to check database access directly
  const checkDatabaseAccess = async () => {
    addDebugInfo('Checking database access...');
    try {
      // Try a simple query to check database access
      const { data, error } = await supabase
        .from('profiles')
        .select('count(*)', { count: 'exact', head: true });

      if (error) {
        addDebugInfo(`Database access error: ${error.message}`);
        setError(`Database connection error: ${error.message}`);
        return false;
      } else {
        addDebugInfo(`Database access successful. Profile count: ${data}`);
      }

      // Check chat_threads table
      const { count, error: threadError } = await supabase
        .from('chat_threads')
        .select('*', { count: 'exact', head: true });

      if (threadError) {
        addDebugInfo(`chat_threads table error: ${threadError.message}`);
        setError(`Cannot access chat_threads table: ${threadError.message}`);
        return false;
      } else {
        addDebugInfo(`chat_threads table accessible. Count: ${count}`);
      }

      // Check task_messages table
      const { count: msgCount, error: msgError } = await supabase
        .from('task_messages')
        .select('*', { count: 'exact', head: true });

      if (msgError) {
        addDebugInfo(`task_messages table error: ${msgError.message}`);
        setError(`Cannot access task_messages table: ${msgError.message}`);
        return false;
      } else {
        addDebugInfo(`task_messages table accessible. Count: ${msgCount}`);
      }

      // Check if the current user has any chat threads
      const { data: userThreads, error: userThreadsError } = await supabase
        .from('chat_threads')
        .select('id')
        .or(`supplier_id.eq.${user.id},admin_id.eq.${user.id}`)
        .limit(1);

      if (userThreadsError) {
        addDebugInfo(`Error checking user threads: ${userThreadsError.message}`);
      } else if (!userThreads || userThreads.length === 0) {
        addDebugInfo(`User has no chat threads`);
      } else {
        addDebugInfo(`User has at least one chat thread: ${userThreads[0].id.substring(0, 8)}...`);
      }

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      addDebugInfo(`General database error: ${errorMessage}`);
      setError(`Database error: ${errorMessage}`);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleChatClick = (thread: ChatThread) => {
    // Log detailed information about the thread being clicked
    if (process.env.NODE_ENV === 'development') {
      console.log('Thread clicked:', thread);

      }
    // Ensure we have both IDs before navigating
    if (!thread.id || !thread.task_id) {
      console.error('Missing thread ID or task ID', thread);
      return;
    }

    // Use the correct URL format that matches the route in App.tsx
    const url = `/mobile/chat/${thread.id}?task=${thread.task_id}`;
    if (process.env.NODE_ENV === 'development') {
      console.log('Navigating to:', url);

      }
    // Navigate to the chat view
    navigate(url);
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return format(date, 'HH:mm'); // Today, show time
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return format(date, 'EEEE'); // Day of week
    } else {
      return format(date, 'dd/MM/yyyy'); // Full date
    }
  };

  // Get initials from task title for avatar fallback
  const getInitials = (title: string) => {
    return title
      .split(' ')
      .map(word => word[0])
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  // Truncate message content
  const truncateMessage = (content: string, maxLength: number = 60) => {
    if (!content) return '';
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/dashboard')}
            className="mr-3 p-1 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft size={20} />
          </button>
          <h1 className="text-xl font-semibold">Messages</h1>
        </div>

        {/* Debug toggle button */}
        <button
          onClick={toggleDebugPanel}
          className="text-xs px-2 py-1 bg-gray-200 rounded-md"
        >
          {showDebug ? 'Hide Debug' : 'Debug'}
        </button>
      </div>

      {/* Debug panel */}
      {showDebug && (
        <div className="bg-gray-100 border-b border-gray-300 p-2 text-xs">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-bold">Debug Information</h3>
            <div className="space-x-2">
              <button
                onClick={checkDatabaseAccess}
                className="px-2 py-1 bg-blue-500 text-white rounded-md"
              >
                Check DB
              </button>
              <button
                onClick={refreshChatThreads}
                className="px-2 py-1 bg-green-500 text-white rounded-md"
              >
                Refresh
              </button>
              <button
                onClick={() => setDebugInfo([])}
                className="px-2 py-1 bg-red-500 text-white rounded-md"
              >
                Clear
              </button>
            </div>
          </div>

          <div className="max-h-40 overflow-y-auto bg-gray-800 text-green-400 p-2 rounded font-mono">
            {debugInfo.length === 0 ? (
              <p>No debug information yet</p>
            ) : (
              debugInfo.map((info, index) => (
                <div key={index} className="mb-1">
                  {index + 1}. {info}
                </div>
              ))
            )}
          </div>

          <div className="mt-2 text-xs">
            <p><strong>User ID:</strong> {user?.id || 'Not logged in'}</p>
            <p><strong>Email:</strong> {user?.email || 'Unknown'}</p>
          </div>
        </div>
      )}

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          // Loading state with more information
          <div className="p-4 space-y-4">
            <div className="text-center mb-4">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2"></div>
              <p className="text-blue-600 font-medium">Loading Chats...</p>
              <p className="text-sm text-gray-500 mt-1">This may take a moment</p>
            </div>

            {/* Loading status info */}
            <div className="bg-blue-50 p-3 rounded-lg text-sm border border-blue-200 mb-4">
              <p className="font-medium text-blue-700 mb-1">Loading Status:</p>
              <ul className="list-disc list-inside text-blue-600 space-y-1">
                <li>Checking user authentication...</li>
                <li>Connecting to database...</li>
                <li>Fetching chat threads...</li>
              </ul>
              <p className="text-xs text-blue-500 mt-2">
                If this takes too long, try the debug options below
              </p>
            </div>

            {/* Debug options during loading */}
            <div className="flex flex-col space-y-2 max-w-xs mx-auto">
              <button
                onClick={toggleDebugPanel}
                className="px-4 py-2 bg-gray-500 text-white rounded-md text-sm"
              >
                {showDebug ? 'Hide Debug Info' : 'Show Debug Info'}
              </button>

              <button
                onClick={checkDatabaseAccess}
                className="px-4 py-2 bg-blue-500 text-white rounded-md text-sm"
              >
                Check Database Connection
              </button>

              <button
                onClick={refreshChatThreads}
                className="px-4 py-2 bg-green-500 text-white rounded-md text-sm"
              >
                Retry Loading
              </button>
            </div>

            {/* Skeleton loaders */}
            <div className="mt-6 space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4 p-3 bg-white rounded-lg">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : error ? (
          // Error state
          <div className="p-8 text-center">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-red-500 font-medium mb-2">Error Loading Messages</p>
            <p className="text-gray-600 text-sm mb-4">{error}</p>

            <div className="flex flex-col space-y-2 mb-4">
              <button
                onClick={refreshChatThreads}
                className="px-4 py-2 bg-blue-500 text-white rounded-md"
              >
                Try Again
              </button>

              <button
                onClick={checkDatabaseAccess}
                className="px-4 py-2 bg-green-500 text-white rounded-md"
              >
                Check Database
              </button>

              <button
                onClick={toggleDebugPanel}
                className="px-4 py-2 bg-gray-500 text-white rounded-md"
              >
                {showDebug ? 'Hide Debug Info' : 'Show Debug Info'}
              </button>
            </div>

            <p className="text-xs text-gray-500 mt-2">
              If the problem persists, try logging out and back in, or contact support.
            </p>
          </div>
        ) : chatThreads.length === 0 ? (
          // Empty state
          <div className="p-8 text-center">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-700 font-medium">No Messages Found</p>
            <p className="text-sm text-gray-500 mt-2 mb-4">
              You don't have any active conversations yet
            </p>

            <div className="bg-blue-50 p-4 rounded-lg text-left mb-6 border border-blue-200">
              <p className="font-medium text-blue-700 mb-2">Why am I seeing this?</p>
              <ul className="list-disc list-inside text-sm text-blue-600 space-y-1">
                <li>You haven't started any conversations</li>
                <li>You haven't been assigned to any tasks</li>
                <li>You haven't made any offers on tasks</li>
                <li>Your account might not have the right permissions</li>
              </ul>
            </div>

            <div className="flex flex-col space-y-2 max-w-xs mx-auto">
              <button
                onClick={() => navigate('/dashboard')}
                className="px-4 py-2 bg-blue-500 text-white rounded-md text-sm"
              >
                Go to Dashboard
              </button>

              <button
                onClick={refreshChatThreads}
                className="px-4 py-2 bg-green-500 text-white rounded-md text-sm"
              >
                Refresh Messages
              </button>

              <button
                onClick={checkDatabaseAccess}
                className="px-4 py-2 bg-gray-500 text-white rounded-md text-sm"
              >
                Check Database Connection
              </button>

              <button
                onClick={toggleDebugPanel}
                className="px-4 py-2 bg-gray-700 text-white rounded-md text-sm"
              >
                {showDebug ? 'Hide Debug Info' : 'Show Debug Info'}
              </button>
            </div>

            <p className="text-xs text-gray-500 mt-4">
              If you believe this is an error, try refreshing or logging out and back in.
            </p>
          </div>
        ) : (
          // Chat threads list
          <div className="divide-y divide-gray-100">
            {chatThreads.map((thread) => (
              <button
                key={thread.id}
                className={`w-full text-left p-4 ${thread.is_closed ? 'bg-gray-50' : 'bg-white'} hover:bg-gray-50 transition-colors flex items-center space-x-3`}
                onClick={() => handleChatClick(thread)}
              >
                <Avatar className="h-12 w-12 border border-gray-200">
                  {thread.supplier?.first_name ? (
                    <AvatarFallback className="bg-green-100 text-green-600">
                      {thread.supplier.first_name.charAt(0)}{thread.supplier.last_name?.charAt(0) || ''}
                    </AvatarFallback>
                  ) : (
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      {getInitials(thread.task.title)}
                    </AvatarFallback>
                  )}
                </Avatar>

                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium text-gray-900 truncate pr-2">
                      {thread.task.title}
                    </h3>
                    <span className="text-xs text-gray-500 whitespace-nowrap">
                      {thread.last_message_at ? formatTimeAgo(thread.last_message_at) :
                       thread.last_message ? formatTimeAgo(thread.last_message.created_at) : ''}
                    </span>
                  </div>

                  <div className="flex justify-between items-center mt-1">
                    <p className={`text-sm truncate ${thread.unread_count ? 'font-medium text-gray-900' : 'text-gray-500'}`}>
                      {thread.supplier && user?.id !== thread.supplier_id ? (
                        <span className="text-green-600 font-medium">
                          {thread.supplier.first_name || thread.supplier.email?.[0]?.split('@')[0] || 'Supplier'}:
                        </span>
                      ) : null}
                      {' '}
                      {thread.last_message
                        ? truncateMessage(thread.last_message.content)
                        : 'No messages yet'}
                    </p>

                    {thread.unread_count > 0 && (
                      <Badge className="ml-2 bg-blue-500 hover:bg-blue-500">
                        {thread.unread_count}
                      </Badge>
                    )}
                  </div>

                  <div className="mt-1 flex items-center space-x-2">
                    <span className={`text-xs px-2 py-0.5 rounded-full ${
                      thread.status === 'interest' ? 'bg-blue-100 text-blue-700' :
                      thread.status === 'questions' ? 'bg-purple-100 text-purple-700' :
                      thread.status === 'offer' ? 'bg-green-100 text-green-700' :
                      thread.status === 'accepted' ? 'bg-teal-100 text-teal-700' :
                      thread.status === 'rejected' ? 'bg-red-100 text-red-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {thread.status.charAt(0).toUpperCase() + thread.status.slice(1)}
                    </span>

                    {thread.has_offer && (
                      <span className="text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-700">
                        Offer
                      </span>
                    )}

                    {thread.is_closed && (
                      <span className="text-xs px-2 py-0.5 rounded-full bg-gray-100 text-gray-700">
                        Closed
                      </span>
                    )}

                    {thread.task.status === 'completed' && (
                      <span className="text-xs px-2 py-0.5 rounded-full bg-teal-100 text-teal-700">
                        Completed
                      </span>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileChatList;
