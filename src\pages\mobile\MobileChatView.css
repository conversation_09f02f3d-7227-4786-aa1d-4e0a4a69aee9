/**
 * Mobile Chat View Styles
 */

/* Container */
.mobile-chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f9fafb;
  position: relative;
}

/* Header */
.mobile-chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid #e5e7eb;
}

.avatar-fallback {
  background-color: #dbeafe;
  color: #2563eb;
  font-weight: 600;
}

.header-title {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.header-status {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

.info-button {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
}

/* Chat Content */
.mobile-chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

/* Loading Skeletons */
.loading-skeletons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
}

.skeleton-message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: white;
}

.skeleton-message.left {
  align-self: flex-start;
  border-bottom-left-radius: 0;
}

.skeleton-message.right {
  align-self: flex-end;
  border-bottom-right-radius: 0;
}

/* Custom Message Input */
.mobile-message-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: white;
  border-top: 1px solid #e5e7eb;
  position: sticky;
  bottom: 0;
}

.mobile-message-input input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 1.5rem;
  font-size: 0.875rem;
  outline: none;
}

.mobile-message-input input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  border: none;
  cursor: pointer;
}

.send-button.disabled {
  background-color: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
}

/* GetStream Overrides */
.str-chat {
  height: 100%;
  --str-chat__primary-color: #3b82f6;
  --str-chat__active-primary-color: #2563eb;
  --str-chat__surface-color: #ffffff;
  --str-chat__avatar-background-color: #dbeafe;
  --str-chat__avatar-text-color: #2563eb;
}

.str-chat__message-list {
  padding: 0.5rem;
}

.str-chat__message-simple {
  margin-bottom: 0.5rem;
}

.str-chat__message-simple__content {
  background-color: #f3f4f6;
  border-radius: 1rem;
  padding: 0.5rem 0.75rem;
}

.str-chat__message-simple--me .str-chat__message-simple__content {
  background-color: #dbeafe;
}

.str-chat__message-simple__actions {
  display: none;
}

.str-chat__message-simple__timestamp {
  font-size: 0.7rem;
  margin-top: 0.25rem;
}

.str-chat__message-simple__avatar {
  width: 2rem;
  height: 2rem;
}

/* System Messages */
.str-chat__message--system {
  font-style: italic;
  font-size: 0.75rem;
  color: #3b82f6;
  text-align: center;
  margin: 0.5rem 0;
}

/* Mobile Specific Adjustments */
@media (max-width: 640px) {
  .str-chat__message-list {
    padding: 0.25rem;
  }
  
  .str-chat__message-simple__content {
    padding: 0.5rem;
  }
  
  .str-chat__message-simple__avatar {
    width: 1.75rem;
    height: 1.75rem;
  }
}
