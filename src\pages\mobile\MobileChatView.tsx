/**
 * Mobile Chat View Component
 *
 * A mobile-optimized view for individual chat conversations
 * that uses the existing useTaskChat hook with improved error handling
 * and performance optimizations for PWA.
 */

import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';
import { ArrowLeft, Send, Info, AlertCircle, Loader2, RefreshCw } from 'lucide-react';
import { useGetStreamChat } from '@/hooks/use-getstream-chat';
import { supabase } from '@/integrations/supabase/client';

const MobileChatView: React.FC = () => {
  const { threadId } = useParams<{ threadId: string }>();
  const [searchParams] = useSearchParams();
  const taskId = searchParams.get('task');

  // Log parameters to help with debugging
  if (process.env.NODE_ENV === 'development') {
    console.log('MobileChatView params:', {
    threadId,
    taskId,
    pathname: window.location.pathname,
    search: window.location.search
  });
    }
  const navigate = useNavigate();
  const { user } = useAuth();

  const [newMessage, setNewMessage] = useState('');
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [showDebug, setShowDebug] = useState(false);
  const [isManuallyRefreshing, setIsManuallyRefreshing] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);
  const [fallbackMessages, setFallbackMessages] = useState<any[]>([]);
  const [useFallback, setUseFallback] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageContainerRef = useRef<HTMLDivElement>(null);

  // Function to add debug info
  const addDebugInfo = (info: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(info);
      }
    setDebugInfo(prev => [info, ...prev].slice(0, 20)); // Keep last 20 messages
  };

  // Use the GetStream chat hook
  const {
    client,
    channel,
    messages,
    isLoading,
    isSending,
    error: streamError,
    sendMessage
  } = useGetStreamChat({
    taskId: taskId || '',
    threadId
  });

  // Add debug information when component mounts and implement fallback mechanism
  useEffect(() => {
    let isMounted = true;

    const initializeComponent = async () => {
      if (!isMounted) return;

      addDebugInfo(`Component mounted with taskId: ${taskId}, threadId: ${threadId}`);
      addDebugInfo(`User: ${user?.id || 'Not logged in'}`);

      if (taskId && threadId) {
        addDebugInfo('Both taskId and threadId are provided');
      } else if (taskId) {
        addDebugInfo('Only taskId is provided, will use first thread');
      } else {
        addDebugInfo('Missing taskId, cannot load chat');
        setLocalError('Missing task information. Please go back and try again.');
        return;
      }

      // Implement a fallback mechanism to fetch messages directly if needed
      try {
        // Fetch messages directly as a backup
        if (taskId && threadId && user) {
          addDebugInfo('Setting up fallback message fetching');

          const { data: directMessages, error: directError } = await supabase
            .from('task_messages')
            .select(`
              id,
              content,
              created_at,
              sender_id,
              is_system_message,
              profiles:sender_id(first_name, last_name)
            `)
            .eq('task_id', taskId)
            .eq('thread_id', threadId)
            .order('created_at', { ascending: true });

          if (!isMounted) return;

          if (directError) {
            addDebugInfo(`Fallback fetch error: ${directError.message}`);
          } else if (directMessages && directMessages.length > 0) {
            addDebugInfo(`Fallback fetched ${directMessages.length} messages`);

            // Format the messages
            const formattedMessages = directMessages.map(msg => ({
              ...msg,
              sender_name: msg.is_system_message
                ? 'System'
                : (msg.profiles?.first_name
                    ? `${msg.profiles.first_name} ${msg.profiles.last_name || ''}`
                    : 'User')
            }));

            setFallbackMessages(formattedMessages);
          } else {
            addDebugInfo('No messages found in fallback fetch');
          }
        }
      } catch (err) {
        if (!isMounted) return;
        addDebugInfo(`Error in fallback fetch: ${err instanceof Error ? err.message : 'Unknown error'}`);
      }
    };

    initializeComponent();

    return () => {
      isMounted = false;
    };
  }, [taskId, threadId, user]);

  // Log when data changes and handle fallback if needed
  useEffect(() => {
    if (messages.length > 0) {
      addDebugInfo(`Messages loaded: ${messages.length}`);
      // If we have messages from the hook, don't use fallback
      setUseFallback(false);
    } else if (fallbackMessages.length > 0 && isLoading === false) {
      // If hook failed to load messages but we have fallback messages, use them
      addDebugInfo(`Using ${fallbackMessages.length} fallback messages`);
      setUseFallback(true);
    }

    if (channel) {
      addDebugInfo(`Channel loaded: ${channel.id}`);
      addDebugInfo(`Channel type: ${channel.type}`);
    }

    addDebugInfo(`Active thread: ${threadId || 'none'}`);

    // If we've been loading for more than 5 seconds with no messages, try fallback
    if (isLoading && messages.length === 0 && fallbackMessages.length > 0) {
      const timer = setTimeout(() => {
        addDebugInfo('Loading timeout, switching to fallback messages');
        setUseFallback(true);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [messages.length, channel, threadId, fallbackMessages.length, isLoading]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Function to manually refresh messages
  const handleRefresh = async () => {
    setIsManuallyRefreshing(true);
    setLocalError(null);

    try {
      addDebugInfo('Manually refreshing messages');

      // Try to fetch messages directly
      if (taskId && threadId && user) {
        const { data: directMessages, error: directError } = await supabase
          .from('task_messages')
          .select(`
            id,
            content,
            created_at,
            sender_id,
            is_system_message,
            profiles:sender_id(first_name, last_name)
          `)
          .eq('task_id', taskId)
          .eq('thread_id', threadId)
          .order('created_at', { ascending: true });

        if (directError) {
          addDebugInfo(`Refresh error: ${directError.message}`);
          setLocalError('Could not refresh messages. Please try again.');
        } else if (directMessages && directMessages.length > 0) {
          addDebugInfo(`Refreshed ${directMessages.length} messages`);

          // Format the messages
          const formattedMessages = directMessages.map(msg => ({
            ...msg,
            sender_name: msg.is_system_message
              ? 'System'
              : (msg.profiles?.first_name
                  ? `${msg.profiles.first_name} ${msg.profiles.last_name || ''}`
                  : 'User')
          }));

          setFallbackMessages(formattedMessages);
          setUseFallback(true);
        } else {
          addDebugInfo('No messages found in refresh');
        }
      }
    } catch (err) {
      addDebugInfo(`Error in refresh: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setLocalError('Failed to refresh messages. Please try again.');
    } finally {
      setIsManuallyRefreshing(false);
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim() || !taskId || !user) {
      addDebugInfo('Cannot send message: missing data');
      return;
    }

    addDebugInfo(`Sending message to task ${taskId}, thread ${threadId || 'none'}`);

    try {
      // If we're using fallback mode, send message directly
      if (useFallback && threadId) {
        addDebugInfo('Sending message directly (fallback mode)');

        const { error: sendError } = await supabase
          .from('task_messages')
          .insert({
            task_id: taskId,
            thread_id: threadId,
            sender_id: user.id,
            content: newMessage.trim()
          });

        if (sendError) {
          addDebugInfo(`Error sending direct message: ${sendError.message}`);
          throw new Error(sendError.message);
        } else {
          addDebugInfo('Message sent successfully via direct insert');
          setNewMessage('');

          // Refresh messages after sending
          handleRefresh();
          return;
        }
      }

      // Otherwise use the hook's sendMessage function
      const result = await sendMessage(newMessage.trim());

      if (result.success) {
        addDebugInfo('Message sent successfully');
        setNewMessage('');
      } else {
        addDebugInfo(`Failed to send message: ${result.reason}`);

        if (result.reason === 'thread_closed') {
          alert('This conversation is closed. You cannot send new messages.');
        } else {
          alert('Failed to send message. Please try again.');
        }
      }
    } catch (err) {
      addDebugInfo(`Error sending message: ${err instanceof Error ? err.message : 'Unknown error'}`);
      alert('Failed to send message. Please try again.');
    }
  };

  const formatMessageTime = (dateString: string) => {
    return format(new Date(dateString), 'HH:mm');
  };

  const formatMessageDate = (dateString: string, index: number) => {
    const currentDate = new Date(dateString);
    const currentDateString = format(currentDate, 'yyyy-MM-dd');

    // Show date for first message
    if (index === 0) {
      return format(currentDate, 'EEEE, d MMMM yyyy');
    }

    // Show date when it changes
    if (index > 0) {
      const prevDate = new Date(messages[index - 1].created_at);
      const prevDateString = format(prevDate, 'yyyy-MM-dd');

      if (currentDateString !== prevDateString) {
        return format(currentDate, 'EEEE, d MMMM yyyy');
      }
    }

    return null;
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/mobile/chats')}
            className="mr-3 p-1 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft size={20} />
          </button>

          <div className="flex items-center">
            <Avatar className="h-8 w-8 mr-3 border border-gray-200">
              <AvatarFallback className="bg-blue-100 text-blue-600">
                {channel?.data?.name?.substring(0, 2).toUpperCase() || 'T'}
              </AvatarFallback>
            </Avatar>

            <div>
              <h1 className="font-medium text-base line-clamp-1">
                {isLoading ? <Skeleton className="h-4 w-32" /> : channel?.data?.name || 'Chat'}
              </h1>
              {channel?.data?.task_id && (
                <p className="text-xs text-gray-500">
                  Task #{channel.data.task_id}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center">
          <button
            onClick={() => setShowDebug(!showDebug)}
            className="p-1 mr-2 text-xs bg-gray-200 rounded"
          >
            {showDebug ? 'Hide' : 'Debug'}
          </button>

          <button
            onClick={() => navigate(`/tasks/${taskId}`)}
            className="p-2 rounded-full hover:bg-gray-100"
            aria-label="Task details"
          >
            <Info size={20} />
          </button>
        </div>
      </div>

      {/* Debug panel */}
      {showDebug && (
        <div className="bg-gray-100 border-b border-gray-300 p-2 text-xs">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-bold">Debug Information</h3>
            <button
              onClick={() => setDebugInfo([])}
              className="px-2 py-1 bg-red-500 text-white rounded-md"
            >
              Clear
            </button>
          </div>

          <div className="max-h-40 overflow-y-auto bg-gray-800 text-green-400 p-2 rounded font-mono">
            {debugInfo.length === 0 ? (
              <p>No debug information yet</p>
            ) : (
              debugInfo.map((info, index) => (
                <div key={index} className="mb-1">
                  {index + 1}. {info}
                </div>
              ))
            )}
          </div>

          <div className="mt-2 text-xs">
            <p><strong>Task ID:</strong> {taskId || 'None'}</p>
            <p><strong>Thread ID:</strong> {threadId || 'None'}</p>
            <p><strong>Channel ID:</strong> {channel?.id || 'None'}</p>
            <p><strong>Messages:</strong> {messages.length}</p>
            <p><strong>Fallback Messages:</strong> {fallbackMessages.length}</p>
            <p><strong>User ID:</strong> {user?.id || 'Not logged in'}</p>
          </div>
        </div>
      )}

      {/* Messages */}
      <div
        ref={messageContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >
        {/* Error state */}
        {localError && (
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <p className="text-red-500 font-medium mb-2">Error</p>
            <p className="text-gray-600 text-sm mb-4">{localError}</p>
            <button
              onClick={handleRefresh}
              disabled={isManuallyRefreshing}
              className="mt-2 px-4 py-2 bg-blue-500 text-white rounded-md flex items-center justify-center mx-auto"
            >
              {isManuallyRefreshing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Refreshing...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </>
              )}
            </button>
          </div>
        )}

        {/* Loading state */}
        {isLoading && !useFallback && !localError ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className={`flex ${i % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                <div className={`max-w-[80%] rounded-lg p-3 ${
                  i % 2 === 0 ? 'bg-white' : 'bg-blue-500'
                }`}>
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-20 mt-2" />
                </div>
              </div>
            ))}
          </div>
        ) : (messages.length === 0 && !useFallback && !localError) ? (
          // Empty state
          <div className="text-center py-8">
            <p className="text-gray-500">No messages yet. Start the conversation!</p>
          </div>
        ) : (
          // Messages list - either from hook or fallback
          <>
            {/* Refresh button for fallback mode */}
            {useFallback && (
              <div className="flex justify-center mb-4">
                <button
                  onClick={handleRefresh}
                  disabled={isManuallyRefreshing}
                  className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs flex items-center"
                >
                  {isManuallyRefreshing ? (
                    <>
                      <Loader2 className="h-3 w-3 animate-spin mr-1" />
                      Refreshing...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Refresh Messages
                    </>
                  )}
                </button>
              </div>
            )}

            {/* Render messages from hook or fallback */}
            {(useFallback ? fallbackMessages : messages).map((message, index) => {
              const messageList = useFallback ? fallbackMessages : messages;
              const isCurrentUser = message.sender_id === user?.id;
              const dateLabel = formatMessageDate(message.created_at, index);

              return (
                <React.Fragment key={message.id}>
                  {dateLabel && (
                    <div className="flex justify-center my-4">
                      <span className="text-xs bg-gray-200 text-gray-600 px-3 py-1 rounded-full">
                        {dateLabel}
                      </span>
                    </div>
                  )}

                  {message.is_system_message ? (
                    <div className="flex justify-center my-2">
                      <span className="text-xs bg-blue-100 text-blue-700 px-3 py-1 rounded-full italic">
                        {message.content}
                      </span>
                    </div>
                  ) : (
                    <div className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
                      <div className={`max-w-[80%] rounded-lg p-3 ${
                        isCurrentUser
                          ? 'bg-blue-500 text-white rounded-tr-none'
                          : 'bg-white text-gray-800 rounded-tl-none'
                      }`}>
                        {!isCurrentUser && message.sender_name && (
                          <div className="text-xs font-medium mb-1">
                            {message.sender_name}
                          </div>
                        )}
                        <p className="whitespace-pre-wrap break-words">{message.content}</p>
                        <div className={`text-xs mt-1 ${isCurrentUser ? 'text-blue-100' : 'text-gray-500'} text-right`}>
                          {formatMessageTime(message.created_at)}
                        </div>
                      </div>
                    </div>
                  )}
                </React.Fragment>
              );
            })}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <form onSubmit={handleSendMessage} className="sticky bottom-0 bg-white border-t border-gray-200 p-3">
        <div className="flex items-end space-x-2">
          <Textarea
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type a message..."
            className="flex-1 resize-none max-h-32"
            rows={1}
            disabled={isSending}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage(e);
              }
            }}
          />

          <Button
            type="submit"
            size="icon"
            disabled={isSending || !newMessage.trim()}
            className={`rounded-full ${!newMessage.trim() ? 'bg-gray-300' : 'bg-blue-500 hover:bg-blue-600'}`}
          >
            <Send size={18} />
          </Button>
        </div>
      </form>
    </div>
  );
};

export default MobileChatView;
