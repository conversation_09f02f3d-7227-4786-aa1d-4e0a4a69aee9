/**
 * Optimized Mobile Chat View Component
 * 
 * A simplified, optimized implementation of the mobile chat view
 * that uses GetStream for chat functionality.
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Send, Info } from 'lucide-react';
import { useGetStreamChat } from '@/hooks/use-getstream-chat';
import {
  Chat,
  Channel,
  MessageInput,
  MessageList,
  Window,
} from 'stream-chat-react';

// Import GetStream CSS
import 'stream-chat-react/dist/css/v2/index.css';
import '@stream-io/stream-chat-css/dist/v2/css/index.css';

// Import custom CSS for mobile chat
import './MobileChatView.css';

const OptimizedChatView: React.FC = () => {
  // Get URL parameters
  const { threadId } = useParams<{ threadId: string }>();
  const [searchParams] = useSearchParams();
  const taskId = searchParams.get('task');
  const navigate = useNavigate();
  const { user } = useAuth();

  // Local state
  const [taskTitle, setTaskTitle] = useState<string>('Chat');
  const [taskStatus, setTaskStatus] = useState<string>('');

  // Use the GetStream chat hook
  const {
    client,
    channel,
    isLoading,
    isSending,
    sendMessage
  } = useGetStreamChat({
    taskId: taskId || '',
    threadId
  });

  // Log parameters for debugging
  useEffect(() => {
    console.log('OptimizedChatView mounted with:', {
      threadId,
      taskId,
      user: user?.id
    });

    // Fetch task details if available
    if (channel) {
      const channelData = channel.data || {};
      if (channelData.name) {
        setTaskTitle(channelData.name);
      }
    }

    return () => {
      console.log('OptimizedChatView unmounting');
    };
  }, [threadId, taskId, user, channel]);

  // Custom message input handler
  const handleSendMessage = async (text: string) => {
    if (!text.trim() || !taskId || !user) {
      console.log('Cannot send message: missing data');
      return;
    }

    try {
      const result = await sendMessage(text.trim());

      if (result.success) {
        console.log('Message sent successfully');
      } else {
        console.log(`Failed to send message: ${result.reason}`);

        if (result.reason === 'thread_closed') {
          alert('This conversation is closed. You cannot send new messages.');
        } else {
          alert('Failed to send message. Please try again.');
        }
      }
    } catch (err) {
      console.error('Error sending message:', err);
      alert('Failed to send message. Please try again.');
    }
  };

  // Custom message input component
  const CustomInput = () => {
    const [text, setText] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (text.trim()) {
        handleSendMessage(text);
        setText('');
      }
    };

    return (
      <form onSubmit={handleSubmit} className="mobile-message-input">
        <input
          type="text"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Type a message..."
          disabled={isSending}
        />
        <Button
          type="submit"
          size="icon"
          disabled={isSending || !text.trim()}
          className={`send-button ${!text.trim() ? 'disabled' : ''}`}
        >
          <Send size={18} />
        </Button>
      </form>
    );
  };

  return (
    <div className="mobile-chat-container">
      {/* Header */}
      <div className="mobile-chat-header">
        <div className="header-left">
          <button
            onClick={() => navigate('/mobile/chats')}
            className="back-button"
          >
            <ArrowLeft size={20} />
          </button>

          <div className="header-info">
            <Avatar className="header-avatar">
              <AvatarFallback className="avatar-fallback">
                {taskTitle.substring(0, 2).toUpperCase() || 'T'}
              </AvatarFallback>
            </Avatar>

            <div>
              <h1 className="header-title">
                {isLoading ? <Skeleton className="h-4 w-32" /> : taskTitle}
              </h1>
              {taskStatus && (
                <p className="header-status">
                  {taskStatus.replace('_', ' ').charAt(0).toUpperCase() + taskStatus.replace('_', ' ').slice(1)}
                </p>
              )}
            </div>
          </div>
        </div>

        <button
          onClick={() => navigate(`/tasks/enhanced/${taskId}`)}
          className="info-button"
          aria-label="Task details"
        >
          <Info size={20} />
        </button>
      </div>

      {/* Chat Content */}
      <div className="mobile-chat-content">
        {isLoading || !client || !channel ? (
          // Loading skeletons
          <div className="loading-skeletons">
            {[...Array(5)].map((_, i) => (
              <div key={i} className={`skeleton-message ${i % 2 === 0 ? 'left' : 'right'}`}>
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-20 mt-2" />
              </div>
            ))}
          </div>
        ) : (
          // GetStream Chat Component
          <Chat client={client} theme="messaging light">
            <Channel channel={channel}>
              <Window>
                <MessageList />
                <CustomInput />
              </Window>
            </Channel>
          </Chat>
        )}
      </div>
    </div>
  );
};

export default OptimizedChatView;
