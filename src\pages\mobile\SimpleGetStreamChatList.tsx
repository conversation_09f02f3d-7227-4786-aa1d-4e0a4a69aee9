/**
 * Simple GetStream Chat List Component
 *
 * A simplified implementation of the mobile chat list that uses GetStream
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { MessageSquare, AlertCircle } from 'lucide-react';

interface ChatThread {
  id: string;
  task_id: string;
  status: string;
  created_at: string;
  updated_at: string;
  last_message_at: string;
  supplier_id: string;
  admin_id: string;
  has_offer: boolean;
  is_closed: boolean;
  task: {
    id: string;
    title: string;
    status: string;
  };
  supplier?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string[] | any;
  } | null;
  last_message?: {
    content: string;
    created_at: string;
    sender_id: string;
  } | null;
  unread_count: number;
}

const SimpleGetStreamChatList: React.FC = () => {
  const [chatThreads, setChatThreads] = useState<ChatThread[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const navigate = useNavigate();

  // Fetch chat threads
  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }

    if (process.env.NODE_ENV === 'development') {


      console.log('SimpleGetStreamChatList: Starting to fetch chat threads completed');



      }
    const fetchChatThreads = async () => {
      try {
        setLoading(true);
        setError(null);

        if (process.env.NODE_ENV === 'development') {


          console.log('Fetching chat threads for user: completed');



          }
        // Fetch chat threads where the user is a participant (either as admin or supplier)
        const { data, error } = await supabase
          .from('chat_threads')
          .select(`
            id,
            task_id,
            status,
            created_at,
            updated_at,
            last_message_at,
            supplier_id,
            admin_id,
            has_offer,
            is_closed,
            task:tasks!inner(id, title, status),
            supplier:supplier_id!inner(id, first_name, last_name, email)
          `)
          .or(`supplier_id.eq.${user.id},admin_id.eq.${user.id}`)
          .order('last_message_at', { ascending: false });

        if (error) {
          console.error('Error fetching chat threads:', error);
          throw new Error(`Failed to fetch threads: ${error.message}`);
        }

        if (process.env.NODE_ENV === 'development') {
    console.log('Fetched chat threads: completed');
  }
        // If no threads, return empty array
        if (!data || data.length === 0) {
          setChatThreads([]);
          setLoading(false);
          return;
        }

        // Fetch the last message for each thread
        const threadsWithLastMessage = await Promise.all(
          data.map(async (thread) => {
            try {
              // Get last message
              const { data: messages, error: messagesError } = await supabase
                .from('task_messages')
                .select('content, created_at, sender_id')
                .eq('thread_id', thread.id)
                .order('created_at', { ascending: false })
                .limit(1);

              if (messagesError) {
                console.error(`Error fetching last message for thread ${thread.id}:`, messagesError);
                return {
                  ...thread,
                  last_message: null,
                  unread_count: 0
                };
              }

              // Count unread messages
              let unreadCount = 0;

              if (messages && messages.length > 0) {
                // Get the timestamp of the user's last message
                const { data: lastUserMessage, error: lastUserError } = await supabase
                  .from('task_messages')
                  .select('created_at')
                  .eq('thread_id', thread.id)
                  .eq('sender_id', user.id)
                  .order('created_at', { ascending: false })
                  .limit(1);

                if (!lastUserError && lastUserMessage && lastUserMessage.length > 0) {
                  // Count messages after the user's last message that weren't sent by the user
                  const { count, error: countError } = await supabase
                    .from('task_messages')
                    .select('id', { count: 'exact', head: true })
                    .eq('thread_id', thread.id)
                    .neq('sender_id', user.id)
                    .gt('created_at', lastUserMessage[0].created_at);

                  if (!countError) {
                    unreadCount = count || 0;
                  }
                } else {
                  // If user has never sent a message, count all messages not from the user
                  const { count, error: countError } = await supabase
                    .from('task_messages')
                    .select('id', { count: 'exact', head: true })
                    .eq('thread_id', thread.id)
                    .neq('sender_id', user.id);

                  if (!countError) {
                    unreadCount = count || 0;
                  }
                }
              }

              // Create a properly formatted thread object
              const formattedThread: ChatThread = {
                id: thread.id,
                task_id: thread.task_id,
                status: thread.status,
                created_at: thread.created_at,
                updated_at: thread.updated_at,
                last_message_at: thread.last_message_at,
                supplier_id: thread.supplier_id,
                admin_id: thread.admin_id,
                has_offer: thread.has_offer,
                is_closed: thread.is_closed,
                task: {
                  id: thread.task.id || '',
                  title: thread.task.title || '',
                  status: thread.task.status || ''
                },
                supplier: thread.supplier,
                last_message: messages?.[0] || null,
                unread_count: unreadCount
              };

              return formattedThread;
            } catch (threadError) {
              console.error(`Error processing thread ${thread.id}:`, threadError);
              // Return a basic thread object
              return {
                id: thread.id,
                task_id: thread.task_id,
                status: thread.status,
                created_at: thread.created_at,
                updated_at: thread.updated_at,
                last_message_at: thread.last_message_at,
                supplier_id: thread.supplier_id,
                admin_id: thread.admin_id,
                has_offer: thread.has_offer,
                is_closed: thread.is_closed,
                task: {
                  id: thread.task.id || '',
                  title: thread.task.title || '',
                  status: thread.task.status || ''
                },
                supplier: thread.supplier,
                last_message: null,
                unread_count: 0
              };
            }
          })
        );

        if (process.env.NODE_ENV === 'development') {
    console.log('Processed threads with messages:', threadsWithLastMessage.length);
  }
        setChatThreads(threadsWithLastMessage as ChatThread[]);
      } catch (err) {
        console.error('Error in fetchChatThreads:', err);
        setError(`Failed to load chat threads: ${err instanceof Error ? err.message : 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    fetchChatThreads();
  }, [user]);

  const handleChatClick = (thread: ChatThread) => {
    // Ensure we have both IDs before navigating
    if (!thread.id || !thread.task_id) {
      console.error('Missing thread ID or task ID', thread);
      return;
    }

    // Navigate to the GetStream chat view
    navigate(`/mobile/stream-chat/${thread.id}?task=${thread.task_id}`);
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return format(date, 'HH:mm'); // Today, show time
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return format(date, 'EEEE'); // Day of week
    } else {
      return format(date, 'dd/MM/yyyy'); // Full date
    }
  };

  // Truncate message content
  const truncateMessage = (content: string, maxLength: number = 60) => {
    if (!content) return '';
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <h1 className="text-xl font-semibold">Messages</h1>
        <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
          GetStream Chat v{new Date().toISOString().substring(0, 10)}
        </div>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          // Loading skeletons
          <div className="p-4 space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 p-3 bg-white rounded-lg">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          // Error state
          <div className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <p className="text-red-500 font-medium mb-2">Error Loading Messages</p>
            <p className="text-gray-600 text-sm mb-4">{error}</p>
            <button
              onClick={() => setLoading(true)}
              className="mt-2 px-4 py-2 bg-blue-500 text-white rounded-md"
            >
              Try Again
            </button>
          </div>
        ) : chatThreads.length === 0 ? (
          // Empty state
          <div className="p-8 text-center">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No messages yet</p>
            <p className="text-sm text-gray-400 mt-2">
              When you have conversations about tasks, they'll appear here
            </p>
          </div>
        ) : (
          // Chat threads list
          <div className="divide-y divide-gray-100">
            {chatThreads.map((thread) => (
              <div
                key={thread.id}
                className={`w-full text-left p-4 ${thread.is_closed ? 'bg-gray-50' : 'bg-white'} hover:bg-gray-50 transition-colors`}
              >
                <div className="flex items-center space-x-3 mb-2">
                  <button
                    className="flex-1 flex items-center space-x-3"
                    onClick={() => handleChatClick(thread)}
                  >
                <Avatar className="h-12 w-12 border border-gray-200">
                  <AvatarFallback className="bg-blue-100 text-blue-600">
                    {thread.task?.title?.substring(0, 2).toUpperCase() || 'T'}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium text-gray-900 truncate pr-2">
                      {thread.task?.title || 'Task'}
                    </h3>
                    <span className="text-xs text-gray-500 whitespace-nowrap">
                      {thread.last_message ? formatTimeAgo(thread.last_message.created_at) : formatTimeAgo(thread.updated_at)}
                    </span>
                  </div>

                  <div className="flex justify-between items-center mt-1">
                    <p className="text-sm truncate text-gray-500">
                      {thread.last_message ? truncateMessage(thread.last_message.content) : 'No messages yet'}
                    </p>

                    {thread.unread_count > 0 && (
                      <Badge variant="destructive" className="ml-2 px-1.5 py-0.5 text-xs">
                        {thread.unread_count}
                      </Badge>
                    )}
                  </div>

                  <div className="mt-1 flex items-center space-x-2">
                    <span className={`text-xs px-2 py-0.5 rounded-full ${
                      thread.status === 'interest' ? 'bg-blue-100 text-blue-700' :
                      thread.status === 'questions' ? 'bg-purple-100 text-purple-700' :
                      thread.status === 'offer' ? 'bg-green-100 text-green-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {thread.status.charAt(0).toUpperCase() + thread.status.slice(1)}
                    </span>

                    {thread.has_offer && (
                      <span className="text-xs px-2 py-0.5 bg-green-100 text-green-700 rounded-full">
                        Offer
                      </span>
                    )}

                    {thread.is_closed && (
                      <span className="text-xs px-2 py-0.5 bg-gray-100 text-gray-700 rounded-full">
                        Closed
                      </span>
                    )}
                  </div>
                  </div>
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleGetStreamChatList;
