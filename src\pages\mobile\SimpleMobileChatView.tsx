/**
 * Simple Mobile Chat View Component
 *
 * A clean, focused implementation of the mobile chat view
 * that uses GetStream for chat functionality.
 */

import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';
import { ArrowLeft, Send, Info, WifiOff } from 'lucide-react';
import { useGetStreamChat } from '@/hooks/use-getstream-chat';
import { isPWA, isOnline, getCachedChatMessages, storeChatMessages, registerConnectivityListeners } from '@/utils/pwa-utils';

const SimpleMobileChatView: React.FC = () => {
  // Get URL parameters
  const { threadId } = useParams<{ threadId: string }>();
  const [searchParams] = useSearchParams();
  const taskId = searchParams.get('task');
  const navigate = useNavigate();
  const { user } = useAuth();

  // Local state
  const [newMessage, setNewMessage] = useState('');
  const [offlineMode, setOfflineMode] = useState(!isOnline());
  const [offlineMessages, setOfflineMessages] = useState<any[]>([]);
  const [taskTitle, setTaskTitle] = useState<string>('Chat');
  const [taskStatus, setTaskStatus] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Log parameters for debugging
  console.log('SimpleMobileChatView mounted with:', {
    threadId,
    taskId,
    user: user?.id,
    isPWA: isPWA(),
    isOnline: isOnline()
  });

  // Use the GetStream chat hook
  const {
    client,
    channel,
    messages,
    isLoading,
    isSending,
    sendMessage
  } = useGetStreamChat({ taskId: taskId || '', threadId });

  // Effect to handle offline mode and cached messages
  useEffect(() => {
    // Check if we're offline
    if (!isOnline() && isPWA() && threadId) {
      setOfflineMode(true);

      // Try to load cached messages
      const cachedMessages = getCachedChatMessages(threadId);
      if (cachedMessages && cachedMessages.length > 0) {
        console.log('SimpleMobileChatView: Using cached messages in offline mode', cachedMessages.length);
        setOfflineMessages(cachedMessages);
      }
    } else {
      setOfflineMode(false);
    }

    // Register listeners for online/offline events
    const cleanup = registerConnectivityListeners(
      // Online callback
      () => {
        console.log('SimpleMobileChatView: Back online');
        setOfflineMode(false);
      },
      // Offline callback
      () => {
        console.log('SimpleMobileChatView: Went offline');
        setOfflineMode(true);
      }
    );

    // Fetch task details if available
    if (channel) {
      const channelData = channel.data || {};
      // Access name property safely with type assertion
      const channelName = (channelData as any).name;
      if (channelName) {
        setTaskTitle(channelName);
      }

      // You could also fetch task status from Supabase here
    }

    return cleanup;
  }, [threadId, channel]);

  // Cache messages when they change and we're in PWA mode
  useEffect(() => {
    if (isPWA() && threadId && messages.length > 0) {
      console.log('SimpleMobileChatView: Caching messages for offline use', messages.length);
      storeChatMessages(threadId, messages);
    }
  }, [messages, threadId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Add cleanup logging
  useEffect(() => {
    return () => {
      console.log('SimpleMobileChatView unmounting, cleaning up resources for:', {
        threadId,
        taskId,
        isPWA: window.matchMedia('(display-mode: standalone)').matches
      });
    };
  }, [threadId, taskId]);

  // Handle sending a message
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim() || !taskId || !user) {
      console.log('Cannot send message: missing data');
      return;
    }

    try {
      const result = await sendMessage(newMessage.trim());

      if (result.success) {
        console.log('Message sent successfully');
        setNewMessage('');
      } else {
        console.log(`Failed to send message: ${result.reason}`);

        if (result.reason === 'thread_closed') {
          alert('This conversation is closed. You cannot send new messages.');
        } else {
          alert('Failed to send message. Please try again.');
        }
      }
    } catch (err) {
      console.error('Error sending message:', err);
      alert('Failed to send message. Please try again.');
    }
  };

  // Format message time
  const formatMessageTime = (dateString: string) => {
    return format(new Date(dateString), 'HH:mm');
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/mobile/chats')}
            className="mr-3 p-1 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft size={20} />
          </button>

          <div className="flex items-center">
            <Avatar className="h-8 w-8 mr-3 border border-gray-200">
              <AvatarFallback className="bg-blue-100 text-blue-600">
                {taskTitle.substring(0, 2).toUpperCase() || 'T'}
              </AvatarFallback>
            </Avatar>

            <div>
              <h1 className="font-medium text-base line-clamp-1">
                {isLoading ? <Skeleton className="h-4 w-32" /> : taskTitle}
              </h1>
              {taskStatus && (
                <p className="text-xs text-gray-500">
                  {taskStatus.replace('_', ' ').charAt(0).toUpperCase() + taskStatus.replace('_', ' ').slice(1)}
                </p>
              )}
            </div>
          </div>
        </div>

        <button
          onClick={() => navigate(`/tasks/${taskId}`)}
          className="p-2 rounded-full hover:bg-gray-100"
          aria-label="Task details"
        >
          <Info size={20} />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {offlineMode && (
          <div className="bg-yellow-50 p-2 rounded-md flex items-center justify-center mb-4">
            <WifiOff size={16} className="text-yellow-600 mr-2" />
            <span className="text-sm text-yellow-700">Offline Mode - Using cached messages</span>
          </div>
        )}

        {isLoading && !offlineMode ? (
          // Loading skeletons
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className={`flex ${i % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                <div className={`max-w-[80%] rounded-lg p-3 ${
                  i % 2 === 0 ? 'bg-white' : 'bg-blue-500'
                }`}>
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-20 mt-2" />
                </div>
              </div>
            ))}
          </div>
        ) : (offlineMode ? offlineMessages : messages).length === 0 ? (
          // Empty state
          <div className="text-center py-8">
            <p className="text-gray-500">No messages yet. Start the conversation!</p>
          </div>
        ) : (
          // Messages list
          <>
            {(offlineMode ? offlineMessages : messages).map((message: any) => {
              // For GetStream messages
              const isCurrentUser = message.user?.id === user?.id;
              const isSystemMessage = message.type === 'system';
              const messageId = message.id || `msg-${Date.now()}-${Math.random()}`;
              const messageText = message.text || message.content || '';
              const senderName = message.user?.name || message.sender_name || 'User';
              const createdAt = message.created_at || message.timestamp || new Date().toISOString();

              return (
                <React.Fragment key={messageId}>
                  {isSystemMessage ? (
                    <div className="flex justify-center my-2">
                      <span className="text-xs bg-blue-100 text-blue-700 px-3 py-1 rounded-full italic">
                        {messageText}
                      </span>
                    </div>
                  ) : (
                    <div className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
                      <div className={`max-w-[80%] rounded-lg p-3 ${
                        isCurrentUser
                          ? 'bg-blue-500 text-white rounded-tr-none'
                          : 'bg-white text-gray-800 rounded-tl-none'
                      }`}>
                        {!isCurrentUser && (
                          <div className="text-xs font-medium mb-1">
                            {senderName}
                          </div>
                        )}
                        <p className="whitespace-pre-wrap break-words">{messageText}</p>
                        <div className={`text-xs mt-1 ${isCurrentUser ? 'text-blue-100' : 'text-gray-500'} text-right`}>
                          {formatMessageTime(createdAt)}
                        </div>
                      </div>
                    </div>
                  )}
                </React.Fragment>
              );
            })}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <form onSubmit={handleSendMessage} className="sticky bottom-0 bg-white border-t border-gray-200 p-3">
        <div className="flex items-end space-x-2">
          <Textarea
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder={offlineMode ? "Can't send messages while offline" : "Type a message..."}
            className="flex-1 resize-none max-h-32"
            rows={1}
            disabled={isSending || offlineMode}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey && !offlineMode) {
                e.preventDefault();
                handleSendMessage(e);
              }
            }}
          />

          <Button
            type="submit"
            size="icon"
            disabled={isSending || !newMessage.trim() || offlineMode}
            className={`rounded-full ${!newMessage.trim() || offlineMode ? 'bg-gray-300' : 'bg-blue-500 hover:bg-blue-600'}`}
            title={offlineMode ? "Can't send messages while offline" : "Send message"}
          >
            <Send size={18} />
          </Button>
        </div>
      </form>
    </div>
  );
};

export default SimpleMobileChatView;
