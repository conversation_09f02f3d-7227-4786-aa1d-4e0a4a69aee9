import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { stripeService } from '@/services/stripeService';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { Download, FileText, Search, Filter, RefreshCw, Mail } from 'lucide-react';

// Define the invoice interface
interface Invoice {
  id: string;
  payment_id: string;
  invoice_number: string;
  invoice_url?: string;
  stripe_invoice_id?: string;
  status: 'pending' | 'paid' | 'void';
  due_date?: string;
  paid_at?: string;
  created_at: string;
  updated_at: string;
  amount: number;
  task_title?: string;
  task_id?: string;
}

// Define the payment interface
interface Payment {
  id: string;
  task_id: string;
  offer_id: string;
  amount: number;
  status: string;
  created_at: string;
  task?: {
    title: string;
  };
}

const OrganizationInvoices = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDownloading, setIsDownloading] = useState<string | null>(null); // Track which invoice is being downloaded
  const [isSendingEmail, setIsSendingEmail] = useState<string | null>(null); // Track which invoice is having email sent
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [organizationId, setOrganizationId] = useState<string | null>(null);

  // Fetch the user's organization ID
  useEffect(() => {
    const fetchOrganizationId = async () => {
      if (!user) return;

      const { data, error } = await supabase
        .from('profiles')
        .select('organization_id')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error fetching organization ID:', error);
        return;
      }

      if (data?.organization_id) {
        setOrganizationId(data.organization_id);
      }
    };

    fetchOrganizationId();
  }, [user]);

  // Fetch invoices for the organization
  useEffect(() => {
    const fetchInvoices = async () => {
      if (!organizationId) return;

      setIsLoading(true);
      try {
        // Get all payments for the organization
        const { data: payments, error: paymentsError } = await supabase
          .from('payments')
          .select(`
            id,
            task_id,
            offer_id,
            amount,
            status,
            created_at,
            tasks (
              title
            )
          `)
          .eq('payer_id', user?.id);

        if (paymentsError) {
          throw paymentsError;
        }

        // Get all invoices for these payments
        const { data: invoicesData, error: invoicesError } = await supabase
          .from('invoices')
          .select('*')
          .in(
            'payment_id',
            payments.map((p) => p.id)
          );

        if (invoicesError) {
          throw invoicesError;
        }

        // Combine payment and invoice data
        const combinedInvoices = invoicesData.map((invoice) => {
          const relatedPayment = payments.find((p) => p.id === invoice.payment_id);
          return {
            ...invoice,
            amount: relatedPayment?.amount || 0,
            task_title: relatedPayment?.tasks?.title,
            task_id: relatedPayment?.task_id,
          };
        });

        setInvoices(combinedInvoices);
        setFilteredInvoices(combinedInvoices);
      } catch (error) {
        console.error('Error fetching invoices:', error);
        toast({
          title: 'Error',
          description: 'Failed to load invoices. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoices();
  }, [organizationId, user?.id, toast]);

  // Filter invoices based on search term and status
  useEffect(() => {
    let filtered = [...invoices];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (invoice) =>
          invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
          invoice.task_title?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter((invoice) => invoice.status === statusFilter);
    }

    setFilteredInvoices(filtered);
  }, [searchTerm, statusFilter, invoices]);

  // Handle invoice download
  const handleDownloadInvoice = async (invoice: Invoice) => {
    try {
      // Set the downloading state for this invoice
      setIsDownloading(invoice.id);

      // Always try to get the latest PDF URL from Stripe if we have a Stripe invoice ID
      if (invoice.stripe_invoice_id) {
        const invoiceUrl = await stripeService.getInvoicePdfUrl(invoice.stripe_invoice_id);

        if (invoiceUrl) {
          // Update the invoice URL in our local state
          setInvoices(prevInvoices =>
            prevInvoices.map(inv =>
              inv.id === invoice.id ? { ...inv, invoice_url: invoiceUrl } : inv
            )
          );

          // Also update filtered invoices
          setFilteredInvoices(prevInvoices =>
            prevInvoices.map(inv =>
              inv.id === invoice.id ? { ...inv, invoice_url: invoiceUrl } : inv
            )
          );

          // Open the PDF in a new tab
          window.open(invoiceUrl, '_blank');
          return;
        }
      }

      // Fall back to the stored URL if we couldn't get a fresh one from Stripe
      if (invoice.invoice_url) {
        window.open(invoice.invoice_url, '_blank');
      } else {
        throw new Error('No invoice URL available');
      }
    } catch (error) {
      console.error('Error downloading invoice:', error);
      toast({
        title: 'Error',
        description: 'Failed to download invoice. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDownloading(null);
    }
  };

  // Handle sending invoice email
  const handleSendInvoiceEmail = async (invoice: Invoice) => {
    try {
      // Set the sending email state for this invoice
      setIsSendingEmail(invoice.id);

      if (!invoice.stripe_invoice_id) {
        throw new Error('No Stripe invoice ID available');
      }

      // Send the invoice email
      const result = await stripeService.sendInvoiceEmail(invoice.stripe_invoice_id);

      if (result && result.sent) {
        toast({
          title: `Email Sent (${result.mode?.toUpperCase() || 'TEST'} MODE)`,
          description: result.message || 'Invoice has been processed successfully.',
          variant: 'default',
        });
      } else {
        throw new Error('Failed to send invoice email');
      }
    } catch (error) {
      console.error('Error sending invoice email:', error);
      toast({
        title: 'Error',
        description: 'Failed to send invoice email. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSendingEmail(null);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    if (organizationId) {
      setIsLoading(true);
      // Re-fetch invoices
      // This will trigger the useEffect that fetches invoices
      setOrganizationId(null);
      setTimeout(() => setOrganizationId(organizationId), 100);
    }
  };

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-500">Paid</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500">Pending</Badge>;
      case 'void':
        return <Badge variant="outline">Void</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-500" />
                  <CardTitle>Organization Invoices</CardTitle>
                </div>
                <CardDescription>
                  View and download invoices for your organization
                </CardDescription>
              </div>
              <Button variant="outline" size="sm" onClick={handleRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search invoices..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="w-full sm:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <div className="flex items-center">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="Filter by status" />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="void">Void</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Invoices Table */}
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <div className="flex flex-col items-center">
                  <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
                  <p className="mt-2 text-sm text-gray-500">Loading invoices...</p>
                </div>
              </div>
            ) : filteredInvoices.length === 0 ? (
              <div className="flex justify-center items-center h-64">
                <div className="flex flex-col items-center">
                  <FileText className="h-12 w-12 text-gray-300" />
                  <p className="mt-4 text-lg font-medium text-gray-500">No invoices found</p>
                  <p className="mt-1 text-sm text-gray-400">
                    {searchTerm || statusFilter !== 'all'
                      ? 'Try adjusting your filters'
                      : 'Invoices will appear here when you make payments'}
                  </p>
                </div>
              </div>
            ) : (
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Invoice #</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Task</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredInvoices.map((invoice) => (
                      <TableRow key={invoice.id}>
                        <TableCell className="font-medium">{invoice.invoice_number}</TableCell>
                        <TableCell>
                          {invoice.created_at
                            ? format(new Date(invoice.created_at), 'dd MMM yyyy')
                            : 'N/A'}
                        </TableCell>
                        <TableCell>
                          {invoice.task_title ? (
                            <span
                              className="cursor-pointer text-blue-500 hover:underline"
                              onClick={() => navigate(`/task/${invoice.task_id}`)}
                            >
                              {invoice.task_title}
                            </span>
                          ) : (
                            'N/A'
                          )}
                        </TableCell>
                        <TableCell>£{invoice.amount.toFixed(2)}</TableCell>
                        <TableCell>{renderStatusBadge(invoice.status)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleSendInvoiceEmail(invoice)}
                              disabled={!invoice.stripe_invoice_id || isDownloading !== null || isSendingEmail !== null}
                              title="Email Invoice"
                            >
                              {isSendingEmail === invoice.id ? (
                                <RefreshCw className="h-4 w-4 animate-spin" />
                              ) : (
                                <Mail className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDownloadInvoice(invoice)}
                              disabled={(!invoice.invoice_url && !invoice.stripe_invoice_id) || isDownloading !== null || isSendingEmail !== null}
                              title="Download Invoice"
                            >
                              {isDownloading === invoice.id ? (
                                <RefreshCw className="h-4 w-4 animate-spin" />
                              ) : (
                                <Download className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default OrganizationInvoices;
