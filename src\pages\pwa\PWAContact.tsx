import React from 'react';
import { useNavigate } from 'react-router-dom';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Mail, HelpCircle, MessageSquare } from 'lucide-react';
import SupportRequestForm from '@/components/support/SupportRequestForm';

const PWAContact: React.FC = () => {
  const navigate = useNavigate();

  return (
    <PWAMobileLayout>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-lg font-semibold ml-2">Contact Support</h1>
        </div>

        {/* Support Request Form */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Get in Touch</CardTitle>
            <CardDescription>
              We're here to help with any questions or feedback you may have.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SupportRequestForm />
          </CardContent>
        </Card>

        {/* Alternative Contact Methods */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Other Ways to Reach Us</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="bg-blue-100 p-2 rounded-full mr-3">
                  <Mail className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-medium">Email Us</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    For general inquiries, please contact us at:
                  </p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-blue-600 hover:text-blue-800 font-medium text-sm mt-1 inline-block"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>

              <div className="flex items-start">
                <div className="bg-purple-100 p-2 rounded-full mr-3">
                  <HelpCircle className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-medium">Help Center</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Find answers to common questions in our help center:
                  </p>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium text-sm mt-1"
                    onClick={() => navigate('/help')}
                  >
                    Visit Help Center
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* About ClassTasker */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">About ClassTasker</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              ClassTasker is a comprehensive task management platform designed specifically for schools and educational institutions.
              Our mission is to simplify maintenance, IT, and administrative tasks, allowing schools to focus on what matters most: education.
            </p>
            <Button
              variant="link"
              className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium text-sm mt-2"
              onClick={() => navigate('/')}
            >
              Learn more about ClassTasker
            </Button>
          </CardContent>
        </Card>
      </div>
    </PWAMobileLayout>
  );
};

export default PWAContact;
