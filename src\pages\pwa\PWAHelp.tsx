import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Separator } from "@/components/ui/separator";
import {
  Search,
  ArrowLeft,
  BookOpen,
  HelpCircle,
  FileText,
  MessageSquare,
  Settings,
  Shield,
  CreditCard
} from 'lucide-react';

const PWAHelp: React.FC = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('faq');

  return (
    <PWAMobileLayout>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-lg font-semibold ml-2">Help Center</h1>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative">
            <Input
              placeholder="Search for help topics..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full mb-4">
            <TabsTrigger value="faq" className="flex-1">FAQ</TabsTrigger>
            <TabsTrigger value="categories" className="flex-1">Categories</TabsTrigger>
          </TabsList>

          {/* FAQ Tab */}
          <TabsContent value="faq">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Frequently Asked Questions</CardTitle>
                <CardDescription>
                  Find quick answers to common questions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="item-1">
                    <AccordionTrigger>What is ClassTasker?</AccordionTrigger>
                    <AccordionContent>
                      <p className="mb-2">
                        ClassTasker is a platform that connects schools and educational institutions with qualified maintenance and service providers.
                      </p>
                      <p>
                        Our platform makes it easy for schools to post tasks, find reliable providers, and manage payments securely. For service providers,
                        it's a great way to find new clients and grow your business.
                      </p>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-2">
                    <AccordionTrigger>How do I create a task?</AccordionTrigger>
                    <AccordionContent>
                      <p>
                        To create a task, navigate to the "Tasks" section and click on the "Create Task" button. Fill in the required details such as title, description, budget, and deadline. You can also add attachments if needed. Once submitted, your task will be visible to potential service providers.
                      </p>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-3">
                    <AccordionTrigger>How do payments work?</AccordionTrigger>
                    <AccordionContent>
                      <p>
                        ClassTasker uses a secure payment system. When you accept a provider's offer, the payment is held in escrow until the task is completed to your satisfaction. This ensures that providers get paid for their work and schools receive the services they've paid for.
                      </p>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-4">
                    <AccordionTrigger>How do I find tasks as a service provider?</AccordionTrigger>
                    <AccordionContent>
                      <p>
                        As a service provider, you can browse available tasks in the Marketplace. You can filter tasks by category, location, and budget to find ones that match your skills and preferences. Once you find a suitable task, you can submit an offer to the school.
                      </p>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-5">
                    <AccordionTrigger>What if I'm not satisfied with the work?</AccordionTrigger>
                    <AccordionContent>
                      <p>
                        If you're not satisfied with the completed work, you can request revisions from the service provider. If issues persist, our support team can help mediate the situation. Our goal is to ensure that all parties are satisfied with the outcome.
                      </p>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
            </Card>

            {/* Contact Support Card */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-lg">Need More Help?</CardTitle>
                <CardDescription>
                  Can't find what you're looking for? Our support team is here to help.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full"
                  onClick={() => navigate('/contact')}
                >
                  Contact Support
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Categories Tab */}
          <TabsContent value="categories">
            <div className="grid gap-4">
              {/* Getting Started */}
              <Card className="border-t-4 border-t-blue-500">
                <CardContent className="p-4">
                  <div className="flex items-center mb-3">
                    <div className="bg-blue-100 p-2 rounded-full mr-3">
                      <BookOpen className="h-5 w-5 text-blue-600" />
                    </div>
                    <h3 className="font-semibold">Getting Started</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Everything you need to know to begin using ClassTasker
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <div className="w-1 h-1 bg-blue-500 rounded-full mr-2"></div>
                      <span>Creating an account</span>
                    </li>
                    <li className="flex items-center">
                      <div className="w-1 h-1 bg-blue-500 rounded-full mr-2"></div>
                      <span>Posting your first task</span>
                    </li>
                    <li className="flex items-center">
                      <div className="w-1 h-1 bg-blue-500 rounded-full mr-2"></div>
                      <span>Finding the right provider</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* Using ClassTasker */}
              <Card className="border-t-4 border-t-green-500">
                <CardContent className="p-4">
                  <div className="flex items-center mb-3">
                    <div className="bg-green-100 p-2 rounded-full mr-3">
                      <FileText className="h-5 w-5 text-green-600" />
                    </div>
                    <h3 className="font-semibold">Using Classtasker</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Learn how to use Classtasker effectively
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <div className="w-1 h-1 bg-green-500 rounded-full mr-2"></div>
                      <span>Writing effective task descriptions</span>
                    </li>
                    <li className="flex items-center">
                      <div className="w-1 h-1 bg-green-500 rounded-full mr-2"></div>
                      <span>Setting appropriate budgets</span>
                    </li>
                    <li className="flex items-center">
                      <div className="w-1 h-1 bg-green-500 rounded-full mr-2"></div>
                      <span>Communicating with providers</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* Account Management */}
              <Card className="border-t-4 border-t-purple-500">
                <CardContent className="p-4">
                  <div className="flex items-center mb-3">
                    <div className="bg-purple-100 p-2 rounded-full mr-3">
                      <Settings className="h-5 w-5 text-purple-600" />
                    </div>
                    <h3 className="font-semibold">Account Management</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Manage your profile and account settings
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <div className="w-1 h-1 bg-purple-500 rounded-full mr-2"></div>
                      <span>Profile management</span>
                    </li>
                    <li className="flex items-center">
                      <div className="w-1 h-1 bg-purple-500 rounded-full mr-2"></div>
                      <span>Privacy settings</span>
                    </li>
                    <li className="flex items-center">
                      <div className="w-1 h-1 bg-purple-500 rounded-full mr-2"></div>
                      <span>Notification preferences</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* Payments & Billing */}
              <Card className="border-t-4 border-t-amber-500">
                <CardContent className="p-4">
                  <div className="flex items-center mb-3">
                    <div className="bg-amber-100 p-2 rounded-full mr-3">
                      <CreditCard className="h-5 w-5 text-amber-600" />
                    </div>
                    <h3 className="font-semibold">Payments & Billing</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Learn about payments, fees, and billing
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <div className="w-1 h-1 bg-amber-500 rounded-full mr-2"></div>
                      <span>Payment methods</span>
                    </li>
                    <li className="flex items-center">
                      <div className="w-1 h-1 bg-amber-500 rounded-full mr-2"></div>
                      <span>Transaction fees</span>
                    </li>
                    <li className="flex items-center">
                      <div className="w-1 h-1 bg-amber-500 rounded-full mr-2"></div>
                      <span>Refund policy</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* Contact Support Card */}
              <Card className="mt-2 border-t-4 border-t-teal-500">
                <CardContent className="p-4">
                  <div className="flex items-center mb-3">
                    <div className="bg-teal-100 p-2 rounded-full mr-3">
                      <MessageSquare className="h-5 w-5 text-teal-600" />
                    </div>
                    <h3 className="font-semibold">Need More Help?</h3>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    Can't find what you're looking for? Our support team is here to help.
                  </p>
                  <Button
                    className="w-full"
                    onClick={() => navigate('/contact')}
                  >
                    Contact Support
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </PWAMobileLayout>
  );
};

export default PWAHelp;
