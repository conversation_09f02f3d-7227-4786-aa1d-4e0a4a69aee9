import React, { useState } from 'react';
import { useN<PERSON><PERSON>, Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useNotifications } from '@/contexts/NotificationContext';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import { 
  ArrowLeft, 
  Bell, 
  Trash2, 
  PoundSterling, 
  MessageSquare, 
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const PWANotifications: React.FC = () => {
  const navigate = useNavigate();
  const { 
    notifications, 
    unreadCount, 
    markAsRead, 
    markAllAsRead, 
    deleteNotification,
    isLoading 
  } = useNotifications();
  const [activeTab, setActiveTab] = useState<string>('all');

  // Filter notifications based on active tab
  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'all') return true;
    if (activeTab === 'unread') return !notification.read;
    return notification.type === activeTab;
  });

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'offer':
        return <PoundSterling className="h-4 w-4" />;
      case 'message':
        return <MessageSquare className="h-4 w-4" />;
      case 'task_update':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  // Get notification link based on type and related data
  const getNotificationLink = (notification: any) => {
    if (notification.relatedType === 'task' && notification.relatedId) {
      return `/tasks/${notification.relatedId}`;
    }
    if (notification.relatedType === 'offer' && notification.relatedId) {
      return `/tasks/${notification.relatedId}`;
    }
    if (notification.relatedType === 'message' && notification.relatedId) {
      return `/messages/${notification.relatedId}`;
    }
    return null;
  };

  // Handle notification click
  const handleNotificationClick = async (id: string) => {
    await markAsRead(id);
  };

  return (
    <PWAMobileLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="bg-white border-b px-4 py-3 flex items-center">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)} className="mr-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex-1">
            <h1 className="text-lg font-semibold flex items-center">
              <Bell className="mr-2 h-5 w-5 text-classtasker-blue" />
              Notifications
              {unreadCount > 0 && (
                <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                  {unreadCount} new
                </span>
              )}
            </h1>
          </div>
          {unreadCount > 0 && (
            <Button 
              variant="ghost" 
              size="sm"
              className="text-classtasker-blue"
              onClick={() => markAllAsRead()}
            >
              Mark All Read
            </Button>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto">
          <div className="p-4">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-4">
                <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
                <TabsTrigger value="unread" className="text-xs">Unread</TabsTrigger>
                <TabsTrigger value="task_update" className="text-xs">Tasks</TabsTrigger>
                <TabsTrigger value="message" className="text-xs">Messages</TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-0">
                {isLoading ? (
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <Card key={i}>
                        <CardContent className="p-4">
                          <div className="flex items-start gap-3">
                            <Skeleton className="h-8 w-8 rounded-full" />
                            <div className="flex-1 space-y-2">
                              <Skeleton className="h-4 w-full" />
                              <Skeleton className="h-3 w-24" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : filteredNotifications.length > 0 ? (
                  <div className="space-y-3">
                    {filteredNotifications.map((notification) => {
                      const link = getNotificationLink(notification);
                      const NotificationContent = (
                        <Card
                          className={`transition-colors ${
                            !notification.read ? 'bg-blue-50 border-blue-100' : ''
                          }`}
                          onClick={() => handleNotificationClick(notification.id)}
                        >
                          <CardContent className="p-4">
                            <div className="flex items-start gap-3">
                              <div className={`rounded-full p-2 flex-shrink-0 ${
                                notification.type === 'offer' ? 'bg-green-100 text-green-600' :
                                notification.type === 'message' ? 'bg-blue-100 text-blue-600' :
                                notification.type === 'task_update' ? 'bg-orange-100 text-orange-600' :
                                'bg-gray-100 text-gray-600'
                              }`}>
                                {getNotificationIcon(notification.type)}
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className={`text-sm ${!notification.read ? 'font-medium' : ''}`}>
                                  {notification.message}
                                </p>
                                <div className="flex justify-between items-center mt-2">
                                  <p className="text-xs text-gray-500 flex items-center">
                                    <Clock className="h-3 w-3 mr-1" />
                                    {notification.time}
                                  </p>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 text-gray-400 hover:text-red-500"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      e.preventDefault();
                                      deleteNotification(notification.id);
                                    }}
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );

                      return link ? (
                        <Link key={notification.id} to={link}>
                          {NotificationContent}
                        </Link>
                      ) : (
                        <div key={notification.id}>
                          {NotificationContent}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Bell className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                      <p className="text-gray-500 mb-1">No notifications found</p>
                      {activeTab !== 'all' && (
                        <p className="text-gray-400 text-sm">Try selecting a different filter</p>
                      )}
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </PWAMobileLayout>
  );
};

export default PWANotifications;
