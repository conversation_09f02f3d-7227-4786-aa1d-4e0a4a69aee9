import React from 'react';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Shield } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const PWAPrivacyPolicy = () => {
  const navigate = useNavigate();

  return (
    <PWAMobileLayout>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center ml-2">
            <Shield className="h-5 w-5 text-classtasker-blue mr-2" />
            <h1 className="text-lg font-semibold">Privacy Policy</h1>
          </div>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-classtasker-blue">Classtasker UK Ltd (GDPR) Privacy Policy</CardTitle>
            <p className="text-sm text-gray-600">Last updated: 14th October, 2024</p>
          </CardHeader>
          <CardContent className="space-y-4 text-sm">
            <section>
              <h3 className="font-semibold mb-2">1. Introduction</h3>
              <p className="text-gray-700 leading-relaxed mb-2">
                This GDPR Privacy Policy describes how Classtasker UK Ltd collects, uses, retains,
                discloses, and deletes your Personal Information on the platform.
              </p>
              <p className="text-gray-700 leading-relaxed">
                By using the platform, you confirm that you have read and understood this Privacy Policy
                and the applicable Terms & Conditions.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">2. Information Collection</h3>

              <h4 className="font-medium mb-1 text-gray-800">Personal Information:</h4>
              <ul className="list-disc pl-4 space-y-1 text-gray-700 mb-3">
                <li>Contact information (name, address, email, phone)</li>
                <li>Billing data (payment card details, security codes)</li>
                <li>Identity information (date of birth, ID numbers)</li>
                <li>Financial information (bank details, tax information)</li>
                <li>Employment and education history</li>
              </ul>

              <h4 className="font-medium mb-1 text-gray-800">Automatic Collection:</h4>
              <ul className="list-disc pl-4 space-y-1 text-gray-700">
                <li>Service use data (pages visited, features used)</li>
                <li>Device data (browser type, operating system, IP address)</li>
                <li>Location data (with your consent when required)</li>
              </ul>
            </section>

            <section>
              <h3 className="font-semibold mb-2">3. How We Use Information</h3>
              <ul className="list-disc pl-4 space-y-1 text-gray-700">
                <li>Operate and make available the platform</li>
                <li>Connect Users for task completion and payment</li>
                <li>Billing and fraud prevention</li>
                <li>Conduct background checks (where permitted)</li>
                <li>Analyze platform usage to improve services</li>
                <li>Send transactional and marketing communications</li>
                <li>Provide customer support</li>
              </ul>
            </section>

            <section>
              <h3 className="font-semibold mb-2">4. Information Sharing</h3>
              <p className="text-gray-700 leading-relaxed mb-2">
                We share information only as described in this policy:
              </p>
              <ul className="list-disc pl-4 space-y-1 text-gray-700">
                <li>Third Party Agents (service providers)</li>
                <li>Other Users (contact info for task facilitation)</li>
                <li>Legal authorities (when required by law)</li>
                <li>Business transfers (mergers, acquisitions)</li>
              </ul>
            </section>

            <section>
              <h3 className="font-semibold mb-2">5. Your Rights & Choices</h3>
              <p className="text-gray-700 leading-relaxed mb-2">
                You can manage your information:
              </p>
              <ul className="list-disc pl-4 space-y-1 text-gray-700">
                <li>Update account settings and profile</li>
                <li>Opt-out of promotional communications</li>
                <li>Access and correct your data</li>
                <li>Request data deletion (subject to legal requirements)</li>
                <li>Control push notifications in device settings</li>
              </ul>
            </section>

            <section>
              <h3 className="font-semibold mb-2">6. Data Security</h3>
              <p className="text-gray-700 leading-relaxed">
                We implement reasonable security safeguards including password protection,
                administrative controls, and technical measures. However, no internet
                transmission is completely secure.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">7. Data Retention</h3>
              <p className="text-gray-700 leading-relaxed">
                We retain personal data while you are a User and for such longer period
                as required for our legitimate interests and legal obligations.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">8. Children's Privacy</h3>
              <p className="text-gray-700 leading-relaxed">
                Our service is not directed at children under 13 (or higher minimum age
                in certain jurisdictions). We do not knowingly collect information from children.
              </p>
            </section>

            <section>
              <h3 className="font-semibold mb-2">9. Contact Information</h3>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-gray-700 text-xs">
                  <strong>Email:</strong> <EMAIL><br />
                  <strong>DPO:</strong> <EMAIL><br />
                  <strong>Company:</strong> Classtasker UK Ltd, Inc.<br />
                  <strong>Address:</strong> United Kingdom
                </p>
              </div>
            </section>

            <div className="pt-4 border-t">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => navigate('/terms')}
              >
                View Terms of Service
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </PWAMobileLayout>
  );
};

export default PWAPrivacyPolicy;
