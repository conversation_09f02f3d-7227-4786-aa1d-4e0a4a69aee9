import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useAuth } from '@/contexts/AuthContext';
import { useProfile } from '@/hooks/use-profile';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import {
  ArrowLeft,
  Camera,
  User,
  Briefcase,
  MapPin,
  FileText,
  Upload,
  X
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Ava<PERSON>, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const profileFormSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().optional(),
  jobTitle: z.string().optional(),
  location: z.string().optional(),
  bio: z.string().optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

const PWAProfileEdit = () => {
  const { user } = useAuth();
  const { profile, isLoading, refetch, updateProfile } = useProfile(user?.id);
  const navigate = useNavigate();
  const [isSaving, setIsSaving] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(profile?.avatar_url || null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      firstName: profile?.first_name || "",
      lastName: profile?.last_name || "",
      jobTitle: profile?.job_title || "",
      location: profile?.location || "",
      bio: profile?.bio || "",
    },
  });

  // Handle file upload for profile photo
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !user) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        variant: 'destructive',
        title: 'Invalid file type',
        description: 'Please upload an image file (JPEG, PNG, etc.)'
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        variant: 'destructive',
        title: 'File too large',
        description: 'Please upload an image smaller than 5MB'
      });
      return;
    }

    setIsUploading(true);

    try {
      // Create a local preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Generate a unique file name that includes the user ID in the path
      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `avatars/${user.id}/${fileName}`;

      // Upload to Supabase Storage
      const { error: uploadError, data } = await supabase.storage
        .from('profile-photos')
        .upload(filePath, file, {
          upsert: true,
          contentType: file.type
        });

      if (uploadError) {
        console.error('Upload error details:', uploadError);
        throw uploadError;
      }

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('profile-photos')
        .getPublicUrl(filePath);

      // Update the user's profile with the new avatar URL using our hook
      const { success, error: updateError } = await updateProfile({
        avatar_url: publicUrl
      });

      if (!success && updateError) throw updateError;

      // Update local state
      setAvatarUrl(publicUrl);

      toast({
        title: 'Photo uploaded',
        description: 'Your profile photo has been updated successfully.'
      });
    } catch (error: any) {
      console.error('Error uploading photo:', error);

      // Provide more specific error messages based on the error type
      let errorMessage = 'Failed to upload profile photo';

      if (error.message) {
        errorMessage = error.message;
      }

      // Handle specific error cases
      if (error.statusCode === 400) {
        errorMessage = 'Storage error: The bucket may not exist or you may not have permission to upload.';
      } else if (error.statusCode === 413) {
        errorMessage = 'The file is too large. Please upload a smaller image.';
      } else if (error.statusCode === 401 || error.statusCode === 403) {
        errorMessage = 'You do not have permission to upload files.';
      }

      toast({
        variant: 'destructive',
        title: 'Upload failed',
        description: errorMessage
      });

      // Reset preview to original photo
      setAvatarUrl(profile?.avatar_url || null);
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemovePhoto = async () => {
    if (!user) return;

    try {
      // Update the user's profile to remove the avatar URL using our hook
      const { success, error } = await updateProfile({
        avatar_url: null
      });

      if (!success && error) throw error;

      // Reset the preview
      setAvatarUrl(null);

      toast({
        title: 'Photo removed',
        description: 'Your profile photo has been removed.'
      });
    } catch (error: any) {
      console.error('Error removing photo:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to remove profile photo'
      });
    }
  };

  async function onSubmit(data: ProfileFormValues) {
    if (!user) return;

    setIsSaving(true);

    try {
      // Use the updateProfile function from the hook instead of direct Supabase call
      const { success, error } = await updateProfile({
        first_name: data.firstName,
        last_name: data.lastName,
        job_title: data.jobTitle,
        location: data.location,
        bio: data.bio
      });

      if (!success && error) throw error;

      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully.",
      });

      // Navigate back to profile page
      navigate('/profile');
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to update profile",
      });
    } finally {
      setIsSaving(false);
    }
  }

  if (isLoading || !profile) {
    return (
      <PWAMobileLayout>
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center mb-4">
            <Button variant="ghost" size="icon" onClick={() => navigate('/profile')}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-semibold ml-2">Edit Profile</h1>
          </div>
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </PWAMobileLayout>
    );
  }

  const userFullName = profile.first_name && profile.last_name
    ? `${profile.first_name} ${profile.last_name}`
    : profile.first_name || "User";

  return (
    <PWAMobileLayout>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate('/profile')}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-lg font-semibold ml-2">Edit Profile</h1>
        </div>

        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col items-center py-4">
              <div className="relative group mb-4">
                <Avatar className="h-24 w-24">
                  {avatarUrl ? (
                    <AvatarImage src={avatarUrl} alt={userFullName} />
                  ) : (
                    <AvatarFallback className="text-2xl bg-classtasker-blue text-white">
                      {userFullName.charAt(0)}
                    </AvatarFallback>
                  )}

                  <div
                    className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Camera className="h-6 w-6 text-white" />
                  </div>
                </Avatar>

                <input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept="image/*"
                  onChange={handleFileChange}
                />
              </div>

              <div className="flex space-x-2 mb-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                >
                  <Upload className="h-4 w-4 mr-1" />
                  {isUploading ? 'Uploading...' : 'Upload Photo'}
                </Button>

                {avatarUrl && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRemovePhoto}
                    disabled={isUploading}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Remove
                  </Button>
                )}
              </div>
              <p className="text-xs text-gray-500 text-center">
                Upload a profile photo to personalize your account
              </p>
            </div>
          </CardContent>
        </Card>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    <User className="h-4 w-4 mr-2 text-gray-500" />
                    First Name
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Your first name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    <User className="h-4 w-4 mr-2 text-gray-500" />
                    Last Name
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Your last name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="jobTitle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    <Briefcase className="h-4 w-4 mr-2 text-gray-500" />
                    Job Title
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Your job title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                    Location
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="City, Country" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    <FileText className="h-4 w-4 mr-2 text-gray-500" />
                    Bio
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Tell us about yourself"
                      className="resize-none min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="pt-4">
              <Button
                type="submit"
                className="w-full"
                disabled={isSaving}
              >
                {isSaving ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </PWAMobileLayout>
  );
};

export default PWAProfileEdit;
