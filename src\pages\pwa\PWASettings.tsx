import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  Bell, 
  MessageSquare, 
  CheckCircle, 
  AlertTriangle,
  Moon,
  Volume2,
  Smartphone,
  Shield,
  Info
} from 'lucide-react';

const PWASettings: React.FC = () => {
  const navigate = useNavigate();
  
  // Notification settings state
  const [pushNotifications, setPushNotifications] = useState(true);
  const [taskNotifications, setTaskNotifications] = useState(true);
  const [messageNotifications, setMessageNotifications] = useState(true);
  const [complianceNotifications, setComplianceNotifications] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [vibrationEnabled, setVibrationEnabled] = useState(true);
  const [darkMode, setDarkMode] = useState(false);

  const handleBack = () => {
    navigate(-1);
  };

  const handleSaveSettings = () => {
    // TODO: Implement settings save functionality
    console.log('Settings saved:', {
      pushNotifications,
      taskNotifications,
      messageNotifications,
      complianceNotifications,
      soundEnabled,
      vibrationEnabled,
      darkMode
    });
    
    // Show success message or navigate back
    navigate(-1);
  };

  return (
    <PWAMobileLayout>
      <div className="container max-w-md mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={handleBack} className="mr-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-semibold">Settings</h1>
        </div>

        <div className="space-y-6">
          {/* Notification Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Bell className="h-5 w-5 mr-2" />
                Notifications
              </CardTitle>
              <CardDescription>
                Manage your notification preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="push-notifications" className="flex items-center">
                  <Smartphone className="h-4 w-4 mr-2" />
                  Push Notifications
                </Label>
                <Switch
                  id="push-notifications"
                  checked={pushNotifications}
                  onCheckedChange={setPushNotifications}
                />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <Label htmlFor="task-notifications" className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Task Updates
                </Label>
                <Switch
                  id="task-notifications"
                  checked={taskNotifications}
                  onCheckedChange={setTaskNotifications}
                  disabled={!pushNotifications}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="message-notifications" className="flex items-center">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  New Messages
                </Label>
                <Switch
                  id="message-notifications"
                  checked={messageNotifications}
                  onCheckedChange={setMessageNotifications}
                  disabled={!pushNotifications}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="compliance-notifications" className="flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Compliance Alerts
                </Label>
                <Switch
                  id="compliance-notifications"
                  checked={complianceNotifications}
                  onCheckedChange={setComplianceNotifications}
                  disabled={!pushNotifications}
                />
              </div>
            </CardContent>
          </Card>

          {/* Sound & Vibration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Volume2 className="h-5 w-5 mr-2" />
                Sound & Vibration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="sound-enabled">Notification Sounds</Label>
                <Switch
                  id="sound-enabled"
                  checked={soundEnabled}
                  onCheckedChange={setSoundEnabled}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="vibration-enabled">Vibration</Label>
                <Switch
                  id="vibration-enabled"
                  checked={vibrationEnabled}
                  onCheckedChange={setVibrationEnabled}
                />
              </div>
            </CardContent>
          </Card>

          {/* Appearance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Moon className="h-5 w-5 mr-2" />
                Appearance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <Label htmlFor="dark-mode">Dark Mode</Label>
                <Switch
                  id="dark-mode"
                  checked={darkMode}
                  onCheckedChange={setDarkMode}
                />
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Coming soon - Dark mode support
              </p>
            </CardContent>
          </Card>

          {/* Privacy & Security */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Privacy & Security
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <Info className="h-4 w-4 mr-2" />
                Privacy Policy
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Info className="h-4 w-4 mr-2" />
                Terms of Service
              </Button>
            </CardContent>
          </Card>

          {/* Save Button */}
          <Button 
            onClick={handleSaveSettings}
            className="w-full bg-classtasker-blue hover:bg-classtasker-blue/90"
          >
            Save Settings
          </Button>
        </div>
      </div>
    </PWAMobileLayout>
  );
};

export default PWASettings;
