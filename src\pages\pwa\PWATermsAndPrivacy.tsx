import React, { useState } from 'react';
import PWAMobileLayout from '@/components/pwa/PWAMobileLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, FileText, Shield } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const PWATermsAndPrivacy = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('terms');

  return (
    <PWAMobileLayout>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-lg font-semibold ml-2">Terms & Privacy</h1>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="terms" className="flex items-center">
              <FileText className="h-4 w-4 mr-1" />
              Terms
            </TabsTrigger>
            <TabsTrigger value="privacy" className="flex items-center">
              <Shield className="h-4 w-4 mr-1" />
              Privacy
            </TabsTrigger>
          </TabsList>

          <TabsContent value="terms">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-classtasker-blue">Classtasker UK Ltd Terms & Conditions</CardTitle>
                <p className="text-sm text-gray-600">Last updated: 12th May, 2024</p>
              </CardHeader>
              <CardContent className="space-y-4 text-sm">
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-3 mb-4">
                  <p className="text-gray-800 font-medium text-xs">
                    BY USING THE PLATFORM, YOU AGREE TO BE BOUND BY THESE TERMS.
                  </p>
                </div>

                <section>
                  <h3 className="font-semibold mb-2">1. The Platform</h3>
                  <p className="text-gray-700 leading-relaxed">
                    Online marketplace connecting Clients (Schools, Colleges, MATs)
                    with Taskers (businesses) for short-term services.
                  </p>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">2. Key Terms</h3>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Provide accurate registration information</li>
                    <li>Maintain account security</li>
                    <li>All fees are non-refundable unless stated</li>
                    <li>Platform content owned by Classtasker UK Ltd</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">3. User Responsibilities</h3>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Provide accurate information</li>
                    <li>Keep account secure</li>
                    <li>Use service lawfully</li>
                    <li>Respect other users</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">4. Payment & Billing</h3>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Fees in British Pounds (£)</li>
                    <li>Non-refundable unless stated</li>
                    <li>30 days notice for price changes</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">5. Limitation of Liability</h3>
                  <p className="text-gray-700 leading-relaxed">
                    ClassTasker is not liable for indirect or consequential damages
                    from your use of the service.
                  </p>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">6. Governing Law</h3>
                  <p className="text-gray-700 leading-relaxed">
                    These terms are governed by English law and subject to
                    English court jurisdiction.
                  </p>
                </section>

                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-gray-700 text-xs">
                    <strong>Contact:</strong> <EMAIL>
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="privacy">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-classtasker-blue">Classtasker UK Ltd (GDPR) Privacy Policy</CardTitle>
                <p className="text-sm text-gray-600">Last updated: 14th October, 2024</p>
              </CardHeader>
              <CardContent className="space-y-4 text-sm">
                <section>
                  <h3 className="font-semibold mb-2">1. Information Collection</h3>
                  <p className="text-gray-700 leading-relaxed mb-2">
                    We collect Personal Information including:
                  </p>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Contact information (name, address, email, phone)</li>
                    <li>Billing data and financial information</li>
                    <li>Identity information and background checks</li>
                    <li>Service use data and device information</li>
                    <li>Location data (with consent when required)</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">2. How We Use Information</h3>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Operate the platform and connect Users</li>
                    <li>Process transactions and prevent fraud</li>
                    <li>Conduct background checks (where permitted)</li>
                    <li>Send communications and updates</li>
                    <li>Analyze usage to improve services</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">3. Information Sharing</h3>
                  <p className="text-gray-700 leading-relaxed mb-2">
                    We share information as described in our policy:
                  </p>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Third Party Agents (service providers)</li>
                    <li>Other Users (for task facilitation)</li>
                    <li>Legal authorities (when required)</li>
                    <li>Business transfers (mergers, acquisitions)</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">4. Your Rights & Choices</h3>
                  <ul className="list-disc pl-4 space-y-1 text-gray-700">
                    <li>Update account settings and profile</li>
                    <li>Opt-out of promotional communications</li>
                    <li>Access and correct your data</li>
                    <li>Request data deletion</li>
                    <li>Control notifications</li>
                  </ul>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">5. Data Security & Retention</h3>
                  <p className="text-gray-700 leading-relaxed">
                    We implement reasonable security safeguards and retain data
                    as long as necessary for our services and legal obligations.
                  </p>
                </section>

                <section>
                  <h3 className="font-semibold mb-2">6. Children's Privacy</h3>
                  <p className="text-gray-700 leading-relaxed">
                    Our service is not directed at children under 13.
                    We do not knowingly collect information from children.
                  </p>
                </section>

                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-gray-700 text-xs">
                    <strong>Email:</strong> <EMAIL><br />
                    <strong>DPO:</strong> <EMAIL><br />
                    <strong>Company:</strong> Classtasker UK Ltd, Inc.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="mt-4 space-y-2">
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigate('/help')}
          >
            Help Center
          </Button>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigate('/contact')}
          >
            Contact Support
          </Button>
        </div>
      </div>
    </PWAMobileLayout>
  );
};

export default PWATermsAndPrivacy;
