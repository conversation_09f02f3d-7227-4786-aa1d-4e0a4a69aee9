-- Function to fetch all messages for a user (sent or received)
CREATE OR REPLACE FUNCTION public.fetch_all_user_messages(user_id_param UUID, limit_param INTEGER DEFAULT 100)
RETURNS TABLE (
  id UUID,
  task_id UUID,
  sender_id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  first_name TEXT,
  last_name TEXT,
  sender_name TEXT,
  task_title TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  WITH user_message_tasks AS (
    -- Get all tasks where the user has sent or received messages
    SELECT DISTINCT tm.task_id
    FROM public.task_messages tm
    LEFT JOIN public.tasks t ON tm.task_id = t.id
    WHERE tm.sender_id = user_id_param OR t.user_id = user_id_param
  ),
  task_info AS (
    -- Get task titles
    SELECT t.id, t.title, t.user_id
    FROM public.tasks t
    JOIN user_message_tasks umt ON t.id = umt.task_id
  ),
  latest_messages AS (
    -- Get the latest message for each task
    SELECT DISTINCT ON (tm.task_id)
      tm.id,
      tm.task_id,
      tm.sender_id,
      tm.content,
      tm.created_at,
      p.first_name,
      p.last_name,
      COALESCE(p.first_name || ' ' || p.last_name, 'User') as sender_name,
      ti.title as task_title
    FROM 
      public.task_messages tm
      JOIN task_info ti ON tm.task_id = ti.id
      JOIN public.profiles p ON tm.sender_id = p.id
    ORDER BY 
      tm.task_id, tm.created_at DESC
  )
  SELECT * FROM latest_messages
  ORDER BY created_at DESC
  LIMIT limit_param;
END;
$$;
