
-- Helper function to get task messages with sender information
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION public.get_task_messages(p_task_id UUID)
RETURNS TABLE (
  id UUID,
  task_id UUID,
  sender_id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  first_name TEXT,
  last_name TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    tm.id,
    tm.task_id,
    tm.sender_id,
    tm.content,
    tm.created_at,
    tm.updated_at,
    p.first_name,
    p.last_name
  FROM
    public.task_messages tm
    JOIN public.profiles p ON p.id = tm.sender_id
  WHERE
    tm.task_id = p_task_id
  ORDER BY
    tm.created_at ASC;
END;
$$;
