
-- Create a table for task-specific messages
CREATE TABLE IF NOT EXISTS public.task_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Enable row level security
ALTER TABLE public.task_messages ENABLE ROW LEVEL SECURITY;

-- Policy to allow task owners and participants to read messages
CREATE POLICY "Task participants can read messages" 
ON public.task_messages
FOR SELECT
USING (
  auth.uid() IN (
    SELECT user_id FROM public.tasks WHERE id = task_id
    UNION
    SELECT user_id FROM public.offers WHERE task_id = task_messages.task_id
  )
);

-- Policy to allow users to send messages if they are task owner or have submitted an offer
CREATE POLICY "Users can send messages for their tasks or offers"
ON public.task_messages
FOR INSERT
WITH CHECK (
  auth.uid() IN (
    SELECT user_id FROM public.tasks WHERE id = task_id
    UNION
    SELECT user_id FROM public.offers WHERE task_id = task_messages.task_id
  )
);

-- Enable Supabase realtime for this table
ALTER PUBLICATION supabase_realtime ADD TABLE public.task_messages;
ALTER TABLE public.task_messages REPLICA IDENTITY FULL;
