
-- Helper function to get task messages with sender information
CREATE OR REPLACE FUNCTION public.get_task_messages_with_sender(task_id_param UUID)
RETURNS TABLE (
  id UUID,
  task_id UUID,
  sender_id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  first_name TEXT,
  last_name TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    tm.id,
    tm.task_id,
    tm.sender_id,
    tm.content,
    tm.created_at,
    tm.updated_at,
    p.first_name,
    p.last_name
  FROM
    public.task_messages tm
    JOIN public.profiles p ON p.id = tm.sender_id
  WHERE
    tm.task_id = task_id_param
  ORDER BY
    tm.created_at ASC;
END;
$$;

-- Function to create a task message with proper permission checks
CREATE OR REPLACE FUNCTION public.create_task_message(
  task_id_param UUID,
  sender_id_param UUID,
  content_param TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_message_id UUID;
BEGIN
  -- Check if user has permission to send message
  IF NOT EXISTS (
    SELECT 1 FROM public.tasks WHERE id = task_id_param AND user_id = sender_id_param
    UNION
    SELECT 1 FROM public.offers WHERE task_id = task_id_param AND user_id = sender_id_param
  ) THEN
    RAISE EXCEPTION 'User does not have permission to send message to this task';
  END IF;

  -- Insert the message
  INSERT INTO public.task_messages (task_id, sender_id, content)
  VALUES (task_id_param, sender_id_param, content_param)
  RETURNING id INTO new_message_id;

  RETURN new_message_id;
END;
$$;

-- Function to fetch recent messages for a user's dashboard
CREATE OR REPLACE FUNCTION public.fetch_recent_messages(user_id_param UUID, limit_param INTEGER DEFAULT 5)
RETURNS TABLE (
  id UUID,
  task_id UUID,
  sender_id UUID,
  content TEXT,
  created_at TIMESTAMPTZ,
  first_name TEXT,
  last_name TEXT,
  sender_name TEXT,
  task_title TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  WITH user_tasks AS (
    -- Tasks owned by user
    SELECT id, title FROM public.tasks WHERE user_id = user_id_param
    UNION
    -- Tasks user has made offers on
    SELECT t.id, t.title 
    FROM public.tasks t
    JOIN public.offers o ON t.id = o.task_id
    WHERE o.user_id = user_id_param
  ),
  latest_messages AS (
    SELECT DISTINCT ON (tm.task_id)
      tm.id,
      tm.task_id,
      tm.sender_id,
      tm.content,
      tm.created_at,
      p.first_name,
      p.last_name,
      COALESCE(p.first_name || ' ' || p.last_name, 'User') as sender_name,
      ut.title as task_title
    FROM 
      public.task_messages tm
      JOIN user_tasks ut ON tm.task_id = ut.id
      JOIN public.profiles p ON tm.sender_id = p.id
    ORDER BY 
      tm.task_id, tm.created_at DESC
  )
  SELECT * FROM latest_messages
  ORDER BY created_at DESC
  LIMIT limit_param;
END;
$$;
