# Database Schema Update

This directory contains scripts to update the database schema for the application.

## Organization Schema Update

The `update-organizations-full-schema.sql` file contains SQL statements to update the organizations table schema to include all fields from the organization setup form:

- name
- organization_type
- parent_organization_id
- address
- city
- state
- zip
- phone
- website
- created_at
- updated_at

It also creates appropriate indexes and constraints for efficient querying and data integrity.

## Running the Schema Update

To update the database schema, run the following command:

```bash
npm run update-db-schema
```

This will execute the SQL statements in the `update-organizations-full-schema.sql` file using the Supabase admin client.

## Verifying the Schema

After running the schema update, the script will verify the schema by listing all columns in the organizations table. Make sure all the expected columns are present.

## Troubleshooting

If you encounter any errors during the schema update, the script will attempt to execute the SQL statements one by one and report any errors. This can help identify which specific statement is causing the issue.

## Manual Execution

If you prefer to run the SQL statements manually, you can copy the contents of the `update-organizations-full-schema.sql` file and execute them in the Supabase SQL Editor.
