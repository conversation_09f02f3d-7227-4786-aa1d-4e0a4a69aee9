# Profile Management Tool

This tool helps you manage user profiles in your Supabase database, specifically for identifying and removing redundant accounts.

## Setup

1. Create a `.env` file in the root directory of your project (copy from `.env.example`)
2. Add your Supabase service role key to the `.env` file:
   ```
   SUPABASE_URL=https://qcnotlojmyvpqbbgoxbc.supabase.co
   SUPABASE_SERVICE_KEY=your_service_role_key_here
   ```
   
   > **Important**: The service role key has admin privileges. Never expose it in client-side code or commit it to your repository.

3. Install the required dependencies:
   ```
   npm install dotenv
   ```

## Running the Tool

Run the tool using the following command:

```
npm run manage-profiles
```

## Features

The tool provides the following features:

1. **List all profiles**: View all user profiles in the database
2. **Find duplicate accounts**: Identify accounts with the same email address
3. **Find orphaned profiles**: Find profiles without corresponding auth users
4. **Delete a specific profile**: Remove a profile by ID
5. **Delete a specific user**: Remove a user and their associated profile

## How to Use

### Finding and Cleaning Up Duplicate Accounts

1. Select option 2 from the main menu
2. The tool will display all email addresses with multiple accounts
3. Choose to clean up duplicates if desired
4. Select which account to keep (the others will be deleted)

### Finding and Removing Orphaned Profiles

1. Select option 3 from the main menu
2. The tool will display all profiles without corresponding auth users
3. Choose to delete orphaned profiles if desired
4. Select which profile to delete or choose to delete all

## Best Practices

1. **Always back up your database** before performing bulk deletions
2. Keep the most recently active account when resolving duplicates
3. Remove test accounts that are no longer needed
4. Run the tool periodically to maintain a clean database

## Troubleshooting

If you encounter any issues:

1. Ensure your Supabase service key is correct
2. Check that you have the necessary permissions
3. Verify your database connection
4. Look for error messages in the console output
