// <PERSON>ript to add email column to profiles table
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addEmailColumn() {
  try {
    if (process.env.NODE_ENV === 'development') {

      console.log('Adding email column to profiles table...');
    

      }
    // Execute raw SQL to add the email column
    const { data, error } = await supabase.rpc('execute_sql', {
      query: "ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS email TEXT;"
    });
    
    if (error) {
      console.error('Error adding email column:', error);
      
      // Try an alternative approach
      if (process.env.NODE_ENV === 'development') {

        console.log('Trying alternative approach...');
      

        }
      // Use the REST API to add the column
      const { error: restError } = await supabase
        .from('profiles')
        .update({ email: '<EMAIL>' })
        .eq('id', 'non-existent-id');
      
      if (restError && restError.code === 'PGRST204') {
        console.error('Email column does not exist. Please add it manually in the Supabase dashboard.');
        if (process.env.NODE_ENV === 'development') {

          console.log('Instructions:');

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('1. Go to the Supabase dashboard');

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('2. Navigate to the "Table Editor"');

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('3. Select the "profiles" table');

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('4. Click "Add column"');

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('5. Enter "email" as the name, select "text" as the type, and click "Save"');

          }
      } else {
        console.error('Unexpected error:', restError);
      }
    } else {
      if (process.env.NODE_ENV === 'development') {

        console.log('Successfully added email column to profiles table');

        }
    }
    
    // Verify the column was added
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (profilesError) {
      console.error('Error verifying profiles table:', profilesError);
    } else {
      if (process.env.NODE_ENV === 'development') {

        console.log('Profiles table exists and is accessible');
      

        }
      // Try to update a profile with an email to see if the column exists
      if (profiles && profiles.length > 0) {
        const profileId = profiles[0].id;
        
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ email: '<EMAIL>' })
          .eq('id', profileId);
        
        if (updateError) {
          console.error('Error updating profile with email:', updateError);
          if (updateError.code === 'PGRST204') {
            if (process.env.NODE_ENV === 'development') {

              console.log('Email column does not exist. Please add it manually.');

              }
          }
        } else {
          if (process.env.NODE_ENV === 'development') {

            console.log('Successfully updated a profile with an email, column exists!');
          

            }
          // Reset the test email
          await supabase
            .from('profiles')
            .update({ email: null })
            .eq('id', profileId);
        }
      }
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

addEmailColumn();
