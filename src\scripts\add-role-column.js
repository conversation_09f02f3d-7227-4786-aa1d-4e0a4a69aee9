// This script adds the role column to the profiles table
import { supabase } from '../integrations/supabase/client';

async function addRoleColumn() {
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS role TEXT;'
    });
    
    if (error) {
      console.error('Error adding role column:', error);
    } else {
      if (process.env.NODE_ENV === 'development') {

        console.log('Role column added successfully');

        }
    }
  } catch (err) {
    console.error('Error:', err);
  }
}

addRoleColumn();
