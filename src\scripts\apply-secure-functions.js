// <PERSON>ript to apply secure database functions
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applySecureFunctions() {
  try {
    console.log('Applying secure database functions...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '..', '..', 'sql', 'secure_database_functions.sql');
    console.log(`Reading SQL file: ${sqlFilePath}`);
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = sql.split(';').filter(stmt => stmt.trim() !== '');
    
    console.log(`Found ${statements.length} SQL statements`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i].trim() + ';';
      console.log(`\nExecuting statement ${i + 1}/${statements.length}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: stmt });
        
        if (error) {
          console.error(`Error executing statement ${i + 1}:`, error);
          console.log('Statement:', stmt);
        } else {
          console.log(`Statement ${i + 1} executed successfully.`);
        }
      } catch (error) {
        console.error(`Error executing statement ${i + 1}:`, error);
        console.log('Statement:', stmt);
      }
    }
    
    console.log('\nVerifying functions...');
    
    // List of functions to verify
    const functions = [
      'get_all_users',
      'delete_user',
      'invite_user_to_organization',
      'get_organization_invitations_secure',
      'update_user_role',
      'create_organization',
      'update_organization',
      'get_trust_schools',
      'create_task_message'
    ];
    
    // Verify each function
    for (const func of functions) {
      try {
        const { data, error } = await supabase.rpc('exec_sql', { 
          sql: `SELECT pg_get_functiondef('public.${func}'::regproc);` 
        });
        
        if (error) {
          console.error(`Error verifying function ${func}:`, error);
        } else {
          console.log(`✅ Function ${func} exists`);
        }
      } catch (error) {
        console.error(`Error verifying function ${func}:`, error);
      }
    }
    
    console.log('\nSecure database functions applied successfully.');
  } catch (error) {
    console.error('Error applying secure database functions:', error);
  }
}

// Run the function
applySecureFunctions();
