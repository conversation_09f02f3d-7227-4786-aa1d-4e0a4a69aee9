// <PERSON><PERSON><PERSON> to apply the task policies SQL to Supabase
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyTaskPolicies() {
  try {
    console.log('Applying task policies to Supabase...');
    
    // Read the SQL file
    const sqlFilePath = path.resolve(process.cwd(), 'sql', 'fix_task_policies.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = sql
      .split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0);
    
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1}/${statements.length}...`);
      
      // Skip comments-only statements
      if (statement.startsWith('--')) {
        console.log('Skipping comment-only statement');
        continue;
      }
      
      const { data, error } = await supabase.rpc('pgaudit.exec_sql', {
        sql_query: statement + ';'
      });
      
      if (error) {
        console.error(`Error executing statement ${i + 1}:`, error);
        console.error('Statement:', statement);
      } else {
        console.log(`Statement ${i + 1} executed successfully`);
      }
    }
    
    console.log('Task policies applied successfully');
    
    // Verify the policies
    console.log('\nVerifying policies...');
    const { data, error } = await supabase.rpc('pgaudit.exec_sql', {
      sql_query: "SELECT schemaname, tablename, policyname, cmd FROM pg_policies WHERE tablename = 'tasks' ORDER BY policyname;"
    });
    
    if (error) {
      console.error('Error verifying policies:', error);
    } else {
      console.log('Current policies on tasks table:');
      console.table(data);
    }
    
  } catch (error) {
    console.error('Error applying task policies:', error);
  }
}

applyTaskPolicies();
