// <PERSON>ript to clear invitation-related data from localStorage
// This script needs to be run in the browser console

// Function to clear invitation data
function clearInvitationData() {
  console.log('Clearing invitation data from localStorage...');
  
  // Check for allInvitations
  const allInvitations = JSON.parse(localStorage.getItem('allInvitations') || '[]');
  console.log(`Found ${allInvitations.length} invitations in localStorage.allInvitations`);
  
  // Clear allInvitations
  localStorage.removeItem('allInvitations');
  console.log('Removed allInvitations from localStorage');
  
  // Check for acceptedInvitations
  const acceptedInvitations = JSON.parse(localStorage.getItem('acceptedInvitations') || '[]');
  console.log(`Found ${acceptedInvitations.length} accepted invitations in localStorage.acceptedInvitations`);
  
  // Clear acceptedInvitations
  localStorage.removeItem('acceptedInvitations');
  console.log('Removed acceptedInvitations from localStorage');
  
  console.log('Invitation data cleared from localStorage');
}

// Execute the function
clearInvitationData();
