// <PERSON>ript to clear invitation-related data from localStorage
// This script needs to be run in the browser console

// Function to clear invitation data
function clearInvitationData() {
  if (process.env.NODE_ENV === 'development') {
    console.log('Clearing invitation data from localStorage...');
  }
  // Check for allInvitations
  const allInvitations = JSON.parse(localStorage.getItem('allInvitations') || '[]');
  if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${allInvitations.length} invitations in localStorage.allInvitations`);
  }
  // Clear allInvitations
  localStorage.removeItem('allInvitations');
  if (process.env.NODE_ENV === 'development') {
    console.log('Removed allInvitations from localStorage');
  }
  // Check for acceptedInvitations
  const acceptedInvitations = JSON.parse(localStorage.getItem('acceptedInvitations') || '[]');
  if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${acceptedInvitations.length} accepted invitations in localStorage.acceptedInvitations`);
  }
  // Clear acceptedInvitations
  localStorage.removeItem('acceptedInvitations');
  if (process.env.NODE_ENV === 'development') {
    console.log('Removed acceptedInvitations from localStorage');
  }
  if (process.env.NODE_ENV === 'development') {
    console.log('Invitation data cleared from localStorage');
  }
}

// Execute the function
clearInvitationData();
