// <PERSON>ript to create a test task with public visibility
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createPublicTask() {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log('Creating a public test task...');
  }
    // Get a user from the database to use as the task creator
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id, role')
      .eq('role', 'teacher')
      .limit(1);
    
    if (usersError) {
      console.error('Error getting users:', usersError);
      return;
    }
    
    if (!users || users.length === 0) {
      if (process.env.NODE_ENV === 'development') {

        console.log('No teacher users found, trying to get any user...');
      

        }
      const { data: anyUsers, error: anyUsersError } = await supabase
        .from('profiles')
        .select('id, role')
        .limit(1);
      
      if (anyUsersError) {
        console.error('Error getting any users:', anyUsersError);
        return;
      }
      
      if (!anyUsers || anyUsers.length === 0) {
        console.error('No users found in the database');
        return;
      }
      
      users[0] = anyUsers[0];
    }
    
    const userId = users[0].id;
    const userRole = users[0].role;
    if (process.env.NODE_ENV === 'development') {

      console.log(`Using user ${userId} with role ${userRole} as the task creator`);
    

      }
    // Create a test task with public visibility
    const testTask = {
      title: 'Public Test Task Created by Script',
      description: 'This is a public test task created by the script',
      location: 'Test Location',
      category: 'Test Category',
      budget: 100,
      due_date: new Date().toISOString(),
      status: 'open',
      visibility: 'public',
      user_id: userId
    };
    
    if (process.env.NODE_ENV === 'development') {
    console.log('Creating task with data: completed');
  }
    const { data, error } = await supabase
      .from('tasks')
      .insert([testTask])
      .select();
    
    if (error) {
      console.error('Error creating test task:', error);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('Successfully created public test task: completed');
  }
  } catch (error) {
    console.error('Error creating public test task:', error);
  }
}

createPublicTask();
