import { supabase } from '../integrations/supabase/client';
import fs from 'fs';
import path from 'path';

async function createSchema() {
  try {
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../../sql/create_multi_tenant_schema.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('Error executing SQL:', error);
    } else {
      if (process.env.NODE_ENV === 'development') {

        console.log('Schema created successfully');

        }
    }
  } catch (err) {
    console.error('Error:', err);
  }
}

createSchema();
