// <PERSON>ript to directly check for tasks in the database
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTasks() {
  try {
    console.log('Checking tasks directly in the database...');
    
    // Get all tasks
    const { data: allTasks, error: allTasksError } = await supabase
      .from('tasks')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (allTasksError) {
      console.error('Error fetching all tasks:', allTasksError);
      return;
    }
    
    console.log(`Found ${allTasks.length} total tasks in the database`);
    
    // Get open tasks
    const { data: openTasks, error: openTasksError } = await supabase
      .from('tasks')
      .select('*')
      .eq('status', 'open')
      .order('created_at', { ascending: false });
    
    if (openTasksError) {
      console.error('Error fetching open tasks:', openTasksError);
      return;
    }
    
    console.log(`Found ${openTasks.length} open tasks in the database`);
    
    // Write tasks to a file for inspection
    const outputFile = 'tasks-dump.json';
    fs.writeFileSync(outputFile, JSON.stringify({
      allTasks,
      openTasks
    }, null, 2));
    
    console.log(`Tasks written to ${outputFile} for inspection`);
    
    // Print a summary of the tasks
    console.log('\nTask Summary:');
    console.log('=============');
    
    if (allTasks.length === 0) {
      console.log('No tasks found in the database');
      return;
    }
    
    // Group tasks by status
    const tasksByStatus = {};
    allTasks.forEach(task => {
      if (!tasksByStatus[task.status]) {
        tasksByStatus[task.status] = [];
      }
      tasksByStatus[task.status].push(task);
    });
    
    // Print task counts by status
    Object.keys(tasksByStatus).forEach(status => {
      console.log(`${status}: ${tasksByStatus[status].length} tasks`);
    });
    
    // Group tasks by visibility
    const tasksByVisibility = {};
    allTasks.forEach(task => {
      if (!tasksByVisibility[task.visibility]) {
        tasksByVisibility[task.visibility] = [];
      }
      tasksByVisibility[task.visibility].push(task);
    });
    
    // Print task counts by visibility
    console.log('\nTasks by visibility:');
    Object.keys(tasksByVisibility).forEach(visibility => {
      console.log(`${visibility}: ${tasksByVisibility[visibility].length} tasks`);
    });
    
    // Print the most recent 5 tasks
    console.log('\nMost recent 5 tasks:');
    allTasks.slice(0, 5).forEach((task, index) => {
      console.log(`\nTask ${index + 1}:`);
      console.log(`ID: ${task.id}`);
      console.log(`Title: ${task.title}`);
      console.log(`Status: ${task.status}`);
      console.log(`Visibility: ${task.visibility}`);
      console.log(`Created by: ${task.user_id}`);
      console.log(`Created at: ${task.created_at}`);
    });
    
  } catch (error) {
    console.error('Error checking tasks:', error);
  }
}

checkTasks();
