// <PERSON>ript to directly check for tasks in the database
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTasks() {
  try {
    if (process.env.NODE_ENV === 'development') {

      console.log('Checking tasks directly in the database...');
    

      }
    // Get all tasks
    const { data: allTasks, error: allTasksError } = await supabase
      .from('tasks')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (allTasksError) {
      console.error('Error fetching all tasks:', allTasksError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`Found ${allTasks.length} total tasks in the database`);
    

    
      }
    // Get open tasks
    const { data: openTasks, error: openTasksError } = await supabase
      .from('tasks')
      .select('*')
      .eq('status', 'open')
      .order('created_at', { ascending: false });
    
    if (openTasksError) {
      console.error('Error fetching open tasks:', openTasksError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`Found ${openTasks.length} open tasks in the database`);
    

    
      }
    // Write tasks to a file for inspection
    const outputFile = 'tasks-dump.json';
    fs.writeFileSync(outputFile, JSON.stringify({
      allTasks,
      openTasks
    }, null, 2));
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`Tasks written to ${outputFile} for inspection`);
    

    
      }
    // Print a summary of the tasks
    if (process.env.NODE_ENV === 'development') {

      console.log('\nTask Summary:');

      }
    if (process.env.NODE_ENV === 'development') {

      console.log('=============');
    

      }
    if (allTasks.length === 0) {
      if (process.env.NODE_ENV === 'development') {

        console.log('No tasks found in the database');

        }
      return;
    }
    
    // Group tasks by status
    const tasksByStatus = {};
    allTasks.forEach(task => {
      if (!tasksByStatus[task.status]) {
        tasksByStatus[task.status] = [];
      }
      tasksByStatus[task.status].push(task);
    });
    
    // Print task counts by status
    Object.keys(tasksByStatus).forEach(status => {
      if (process.env.NODE_ENV === 'development') {

        console.log(`${status}: ${tasksByStatus[status].length} tasks`);

        }
    });
    
    // Group tasks by visibility
    const tasksByVisibility = {};
    allTasks.forEach(task => {
      if (!tasksByVisibility[task.visibility]) {
        tasksByVisibility[task.visibility] = [];
      }
      tasksByVisibility[task.visibility].push(task);
    });
    
    // Print task counts by visibility
    if (process.env.NODE_ENV === 'development') {

      console.log('\nTasks by visibility:');

      }
    Object.keys(tasksByVisibility).forEach(visibility => {
      if (process.env.NODE_ENV === 'development') {

        console.log(`${visibility}: ${tasksByVisibility[visibility].length} tasks`);

        }
    });
    
    // Print the most recent 5 tasks
    if (process.env.NODE_ENV === 'development') {

      console.log('\nMost recent 5 tasks:');

      }
    allTasks.slice(0, 5).forEach((task, index) => {
      if (process.env.NODE_ENV === 'development') {

        console.log(`\nTask ${index + 1}:`);

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`ID: ${task.id}`);

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`Title: ${task.title}`);

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`Status: ${task.status}`);

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`Visibility: ${task.visibility}`);

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`Created by: ${task.user_id}`.replace(/user.*/, 'hasUser: ' + !!user));

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`Created at: ${task.created_at}`);

        }
    });
    
  } catch (error) {
    console.error('Error checking tasks:', error);
  }
}

checkTasks();
