// <PERSON>ript to execute SQL migrations for the multi-academy trust functionality
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Supabase connection details
const supabaseUrl = process.env.SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to execute SQL directly
async function executeSql(sql) {
  try {
    // Using the REST API to execute SQL
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`,
        'Prefer': 'params=single-object'
      },
      body: JSON.stringify({
        query: sql
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`SQL API error: ${response.status} - ${errorText}`);
      return { data: null, error: { message: `SQL API error: ${response.status}` } };
    }

    const result = await response.json();
    return { data: result, error: null };
  } catch (error) {
    console.error('Error executing SQL:', error);
    return { data: null, error };
  }
}

// Function to check if columns exist
async function checkColumnsExist() {
  try {
    console.log('Checking if columns already exist...');

    // Try to select the columns to see if they exist
    const { data, error } = await supabase
      .from('organizations')
      .select('organization_type, parent_organization_id')
      .limit(1);

    if (error) {
      if (error.message.includes('column') && error.message.includes('does not exist')) {
        console.log('Columns do not exist, need to add them.');
        return false;
      } else {
        console.error('Error checking columns:', error);
        throw error;
      }
    }

    console.log('Columns already exist:', data);
    return true;
  } catch (error) {
    console.error('Error checking columns:', error);
    return false;
  }
}

// Function to execute the schema migration
async function executeSchemaUpdate() {
  try {
    // Check if columns already exist
    const columnsExist = await checkColumnsExist();

    if (columnsExist) {
      console.log('Columns already exist, skipping schema update.');
      return;
    }

    console.log('Executing schema update...');

    // Read the schema update SQL file
    const schemaUpdatePath = path.join(__dirname, 'update-organizations-schema.sql');
    const schemaUpdateSql = fs.readFileSync(schemaUpdatePath, 'utf8');

    // Execute the schema update
    const { error } = await executeSql(schemaUpdateSql);

    if (error) {
      console.error('Error executing schema update:', error);
      return;
    }

    console.log('Schema update executed successfully.');

    // Now update the RLS policies
    const rlsUpdatePath = path.join(__dirname, 'update-organizations-rls.sql');
    const rlsUpdateSql = fs.readFileSync(rlsUpdatePath, 'utf8');

    // Execute the RLS update
    const { error: rlsError } = await executeSql(rlsUpdateSql);

    if (rlsError) {
      console.error('Error executing RLS update:', rlsError);
      return;
    }

    console.log('RLS update executed successfully.');

  } catch (error) {
    console.error('Error executing migrations:', error);
  }
}

// Execute the migrations
executeSchemaUpdate();
