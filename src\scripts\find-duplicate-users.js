// <PERSON>ript to find duplicate users with the same email
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function findDuplicateUsers() {
  try {
    if (process.env.NODE_ENV === 'development') {

      console.log('Finding duplicate users...');
    

      }
    // Get all users
    const { data: { users }, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.error('Error fetching users:', usersError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`Found ${users.length} total users`);
    

    
      }
    // Group users by email
    const usersByEmail = {};
    
    for (const user of users) {
      const email = user.email;
      
      if (!usersByEmail[email]) {
        usersByEmail[email] = [];
      }
      
      usersByEmail[email].push(user);
    }
    
    // Find emails with multiple users
    const duplicateEmails = Object.keys(usersByEmail).filter(email => usersByEmail[email].length > 1);
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`Found ${duplicateEmails.length} emails with multiple users`);
    

    
      }
    // Print details for each duplicate
    for (const email of duplicateEmails) {
      const duplicateUsers = usersByEmail[email];
      
      if (process.env.NODE_ENV === 'development') {

      
        console.log(`\n=== Duplicate Users for Email: ${email} ===`);

      
        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`Found ${duplicateUsers.length} users with this email`);
      

        }
      for (let i = 0; i < duplicateUsers.length; i++) {
        const user = duplicateUsers[i];
        
        if (process.env.NODE_ENV === 'development') {

        
          console.log(`\nUser ${i + 1}:`);

        
          }
        if (process.env.NODE_ENV === 'development') {

          console.log(`ID: ${user.id}`);

          }
        if (process.env.NODE_ENV === 'development') {

          console.log(`Created: ${new Date(user.created_at).toLocaleString()}`);

          }
        if (process.env.NODE_ENV === 'development') {

          console.log(`Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('User Metadata:', JSON.stringify(user.user_metadata, null, 2));
        

          }
        // Get profile data
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        
        if (profileError) {
          if (process.env.NODE_ENV === 'development') {

            console.log(`No profile found for user ${user.id}`);

            }
        } else {
          if (process.env.NODE_ENV === 'development') {
    console.log('Profile:');
  }
          if (process.env.NODE_ENV === 'development') {
    console.log(`  First Name: ${profile.first_name || 'Not set'}`);
  }
          if (process.env.NODE_ENV === 'development') {
    console.log(`  Last Name: ${profile.last_name || 'Not set'}`);
  }
          if (process.env.NODE_ENV === 'development') {
    console.log(`  Organization ID: ${profile.organization_id || 'Not set'}`);
  }
          if (process.env.NODE_ENV === 'development') {
    console.log(`  Role: ${profile.role || 'Not set'}`);
  }
          if (profile.organization_id) {
            const { data: org, error: orgError } = await supabase
              .from('organizations')
              .select('name')
              .eq('id', profile.organization_id)
              .single();
            
            if (!orgError && org) {
              if (process.env.NODE_ENV === 'development') {
    console.log(`  Organization: ${org.name}`);
  }
            }
          }
        }
      }
    }
    
    // Check for invitations
    if (process.env.NODE_ENV === 'development') {
    console.log('\n=== Checking Invitations for Duplicate Emails ===');
  }
    for (const email of duplicateEmails) {
      const { data: invitations, error: invitationsError } = await supabase
        .from('user_invitations')
        .select('*')
        .eq('email', email);
      
      if (invitationsError) {
        console.error(`Error fetching invitations for ${email}:`, invitationsError);
        continue;
      }
      
      if (invitations && invitations.length > 0) {
        if (process.env.NODE_ENV === 'development') {
    console.log(`\nFound ${invitations.length} invitations for ${email}:`);
  }
        for (const invitation of invitations) {
          if (process.env.NODE_ENV === 'development') {
    console.log(`\nInvitation ID: ${invitation.id}`);
  }
          if (process.env.NODE_ENV === 'development') {
    console.log(`Status: ${invitation.status}`);
  }
          if (process.env.NODE_ENV === 'development') {
    console.log(`Organization ID: ${invitation.organization_id}`);
  }
          if (process.env.NODE_ENV === 'development') {
    console.log(`Role: ${invitation.role}`);
  }
          if (process.env.NODE_ENV === 'development') {

            console.log(`Created: ${new Date(invitation.created_at).toLocaleString()}`);
          

            }
          const { data: org, error: orgError } = await supabase
            .from('organizations')
            .select('name')
            .eq('id', invitation.organization_id)
            .single();
          
          if (!orgError && org) {
            if (process.env.NODE_ENV === 'development') {
    console.log(`Organization: ${org.name}`);
  }
          }
        }
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log(`\nNo invitations found for ${email}`);
  }
      }
    }
    
  } catch (error) {
    console.error('Error finding duplicate users:', error);
  }
}

findDuplicateUsers();
