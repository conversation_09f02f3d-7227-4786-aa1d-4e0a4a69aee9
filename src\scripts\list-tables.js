// <PERSON>ript to list all tables in the Supabase database
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function listTables() {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log('Listing all tables in the database...');
  }
    // Execute SQL to get all tables
    const { data, error } = await supabase.rpc('pgaudit.exec_sql', {
      sql_query: `
        SELECT 
          table_schema, 
          table_name 
        FROM 
          information_schema.tables 
        WHERE 
          table_schema = 'public' 
        ORDER BY 
          table_schema, 
          table_name;
      `
    });
    
    if (error) {
      console.error('Error listing tables:', error);
      
      // Try an alternative approach
      if (process.env.NODE_ENV === 'development') {
    console.log('Trying alternative approach...');
  }
      const { data: tables, error: tablesError } = await supabase
        .from('pg_tables')
        .select('schemaname, tablename')
        .eq('schemaname', 'public');
      
      if (tablesError) {
        console.error('Error with alternative approach:', tablesError);
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log('Tables in the database:');
  }
        console.table(tables);
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Tables in the database:');
  }
      console.table(data);
    }
    
  } catch (error) {
    console.error('Unexpected error listing tables:', error);
  }
}

listTables();
