// <PERSON>ript to list all tasks in the database
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function listTasks() {
  try {
    if (process.env.NODE_ENV === 'development') {

      console.log('Listing all tasks in the database...');
    

      }
    // Get all tasks
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching tasks:', error);
      return;
    }
    
    if (!data || data.length === 0) {
      if (process.env.NODE_ENV === 'development') {

        console.log('No tasks found in the database');

        }
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`Found ${data.length} tasks:`);

    
      }
    data.forEach((task, index) => {
      if (process.env.NODE_ENV === 'development') {

        console.log(`\nTask ${index + 1}:`);

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`ID: ${task.id}`);

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`Title: ${task.title}`);

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`Status: ${task.status}`);

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`Visibility: ${task.visibility}`);

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`Created by: ${task.user_id}`.replace(/user.*/, 'hasUser: ' + !!user));

        }
      if (process.env.NODE_ENV === 'development') {

        console.log(`Created at: ${task.created_at}`);

        }
    });
    
  } catch (error) {
    console.error('Error listing tasks:', error);
  }
}

listTasks();
