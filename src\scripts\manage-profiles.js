// Script to manage profiles in Supabase
// This script helps identify and remove redundant accounts

import { createClient } from '@supabase/supabase-js';
import readline from 'readline';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with admin key for full access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // This should be the service_role key, not the anon key

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to get all profiles
async function getAllProfiles() {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching profiles:', error);
    return [];
  }
}

// Function to get all auth users
async function getAllUsers() {
  try {
    const { data, error } = await supabase.auth.admin.listUsers();

    if (error) throw error;
    return data.users;
  } catch (error) {
    console.error('Error fetching users:', error);
    return [];
  }
}

// Function to find potential duplicate accounts
function findDuplicates(profiles, users) {
  // Create a map of emails to user IDs
  const emailMap = {};

  // Map users by email
  users.forEach(user => {
    if (!emailMap[user.email]) {
      emailMap[user.email] = [];
    }
    emailMap[user.email].push({
      id: user.id,
      created_at: user.created_at,
      last_sign_in_at: user.last_sign_in_at,
      user_metadata: user.user_metadata
    });
  });

  // Find emails with multiple accounts
  const duplicateEmails = Object.keys(emailMap).filter(email => emailMap[email].length > 1);

  return duplicateEmails.map(email => ({
    email,
    accounts: emailMap[email].sort((a, b) =>
      new Date(b.last_sign_in_at || 0) - new Date(a.last_sign_in_at || 0)
    )
  }));
}

// Function to find profiles without corresponding auth users
function findOrphanedProfiles(profiles, users) {
  const userIds = users.map(user => user.id);
  return profiles.filter(profile => !userIds.includes(profile.id));
}

// Function to delete a profile
async function deleteProfile(profileId) {
  try {
    const { error } = await supabase
      .from('profiles')
      .delete()
      .eq('id', profileId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error deleting profile ${profileId}:`, error);
    return false;
  }
}

// Function to delete a user
async function deleteUser(userId) {
  try {
    const { error } = await supabase.auth.admin.deleteUser(userId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error deleting user ${userId}:`, error);
    return false;
  }
}

// Function to display profile information
function displayProfile(profile, user) {
  const divider = '─'.repeat(50);

  if (user) {
    console.log(divider);
    console.log(`\n📧 EMAIL: ${user.email}`);
  } else {
    console.log(divider);
    console.log(`\n📧 EMAIL: Unknown (No auth user found)`);
  }

  console.log(`🆔 Profile ID: ${profile.id}`);
  console.log(`👤 Name: ${profile.first_name || ''} ${profile.last_name || ''}`.trim());
  console.log(`🏢 Account Type: ${profile.account_type || 'Not set'}`);
  console.log(`🔑 Role: ${profile.role || 'Not set'}`);
  console.log(`📅 Created: ${new Date(profile.created_at).toLocaleString()}`);

  if (user) {
    console.log(`🔒 Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);

    // Display important user metadata in a more readable format
    const metadata = user.user_metadata || {};
    if (Object.keys(metadata).length > 0) {
      console.log('📋 User Metadata:');
      Object.entries(metadata).forEach(([key, value]) => {
        // Format complex objects for better readability
        if (typeof value === 'object' && value !== null) {
          console.log(`  - ${key}: ${JSON.stringify(value)}`);
        } else {
          console.log(`  - ${key}: ${value}`);
        }
      });
    }
  }
}

// Function to prompt user for confirmation
function confirm(message) {
  return new Promise((resolve) => {
    rl.question(`${message} (y/n): `, (answer) => {
      resolve(answer.toLowerCase() === 'y');
    });
  });
}

// Main function
async function main() {
  console.log('Fetching profiles and users...');

  const profiles = await getAllProfiles();
  const users = await getAllUsers();

  console.log(`Found ${profiles.length} profiles and ${users.length} users`);

  // Create a map of profiles by ID for easy lookup
  const profileMap = {};
  profiles.forEach(profile => {
    profileMap[profile.id] = profile;
  });

  // Main menu
  async function showMenu() {
    console.log('\n=== Profile Management Tool ===');
    console.log('1. List all profiles');
    console.log('2. Find duplicate accounts');
    console.log('3. Find orphaned profiles');
    console.log('4. Delete a specific profile');
    console.log('5. Delete a specific user (and associated profile)');
    console.log('6. Exit');

    rl.question('\nSelect an option (1-6): ', async (option) => {
      switch (option) {
        case '1':
          // List all profiles
          console.log('\n=== All Profiles ===');

          // Create a summary table first
          console.log('\nSummary of all profiles:');
          console.log('─'.repeat(100));
          console.log('| ID (truncated)        | Email                           | Name                      | Role      | Last Sign In        |');
          console.log('|' + '─'.repeat(22) + '|' + '─'.repeat(32) + '|' + '─'.repeat(26) + '|' + '─'.repeat(11) + '|' + '─'.repeat(21) + '|');

          // Sort users by email for easier reading
          const sortedUsers = [...users].sort((a, b) => {
            return (a.email || '').localeCompare(b.email || '');
          });

          for (const user of sortedUsers) {
            const profile = profileMap[user.id];
            if (profile) {
              // Format data for the table
              const truncatedId = user.id.substring(0, 20);
              const email = user.email || 'Unknown';
              const name = `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'Not set';
              const role = profile.role || 'Not set';
              const lastSignIn = user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleDateString() : 'Never';

              // Pad strings to align columns
              const idPadded = truncatedId.padEnd(22);
              const emailPadded = email.padEnd(32);
              const namePadded = name.substring(0, 25).padEnd(26);
              const rolePadded = role.substring(0, 10).padEnd(11);
              const lastSignInPadded = lastSignIn.padEnd(21);

              console.log(`| ${idPadded}| ${emailPadded}| ${namePadded}| ${rolePadded}| ${lastSignInPadded}|`);
            }
          }
          console.log('─'.repeat(100));

          // Ask if user wants to see detailed information
          rl.question('\nDo you want to see detailed information for all profiles? (y/n): ', async (answer) => {
            if (answer.toLowerCase() === 'y') {
              for (const user of sortedUsers) {
                const profile = profileMap[user.id];
                if (profile) {
                  displayProfile(profile, user);
                }
              }
            }
            await showMenu();
          });
          break;

        case '2':
          // Find duplicate accounts
          console.log('\n=== Duplicate Accounts ===');
          const duplicates = findDuplicates(profiles, users);

          if (duplicates.length === 0) {
            console.log('No duplicate accounts found.');
          } else {
            console.log(`Found ${duplicates.length} email(s) with multiple accounts:`);

            // Create a summary table of duplicate emails
            console.log('\nSummary of duplicate emails:');
            console.log('─'.repeat(100));
            console.log('| # | Email                           | # of Accounts | Last Active                |');
            console.log('|' + '─'.repeat(3) + '|' + '─'.repeat(32) + '|' + '─'.repeat(14) + '|' + '─'.repeat(28) + '|');

            for (let i = 0; i < duplicates.length; i++) {
              const dup = duplicates[i];
              const numAccounts = dup.accounts.length;

              // Find the most recently active account
              const mostRecentAccount = dup.accounts[0]; // Already sorted by last_sign_in_at
              const lastActive = mostRecentAccount.last_sign_in_at
                ? new Date(mostRecentAccount.last_sign_in_at).toLocaleString()
                : 'Never';

              // Format for table
              const indexPadded = (i + 1).toString().padEnd(3);
              const emailPadded = dup.email.padEnd(32);
              const numAccountsPadded = numAccounts.toString().padEnd(14);
              const lastActivePadded = lastActive.padEnd(28);

              console.log(`| ${indexPadded}| ${emailPadded}| ${numAccountsPadded}| ${lastActivePadded}|`);
            }
            console.log('─'.repeat(100));

            // Detailed view of each duplicate
            for (let i = 0; i < duplicates.length; i++) {
              const dup = duplicates[i];
              console.log('\n' + '─'.repeat(50));
              console.log(`\n📧 DUPLICATE EMAIL #${i+1}: ${dup.email}`);
              console.log(`Found ${dup.accounts.length} accounts with this email:\n`);

              dup.accounts.forEach((account, j) => {
                console.log(`   ${String.fromCharCode(97 + j)}. 🆔 User ID: ${account.id}`);
                console.log(`      📅 Created: ${new Date(account.created_at).toLocaleString()}`);
                console.log(`      🔒 Last Sign In: ${account.last_sign_in_at ? new Date(account.last_sign_in_at).toLocaleString() : 'Never'}`);

                const profile = profileMap[account.id];
                if (profile) {
                  console.log(`      👤 Name: ${profile.first_name || ''} ${profile.last_name || ''}`.trim());
                  console.log(`      🏢 Account Type: ${profile.account_type || 'Not set'}`);
                  console.log(`      🔑 Role: ${profile.role || 'Not set'}`);
                } else {
                  console.log(`      ⚠️ No profile found`);
                }
                console.log(''); // Add a blank line between accounts
              });
            }

            rl.question('\nWould you like to clean up duplicate accounts? (y/n): ', async (answer) => {
              if (answer.toLowerCase() === 'y') {
                rl.question('Enter the number of the email to clean up: ', async (emailIndex) => {
                  const index = parseInt(emailIndex) - 1;
                  if (isNaN(index) || index < 0 || index >= duplicates.length) {
                    console.log('Invalid selection.');
                  } else {
                    const dup = duplicates[index];
                    console.log(`\nFor email ${dup.email}, which accounts would you like to keep?`);
                    console.log('Enter the letter of the account to KEEP (others will be deleted):');

                    rl.question('Account to keep (e.g., a): ', async (keepLetter) => {
                      const keepIndex = keepLetter.charCodeAt(0) - 97;
                      if (isNaN(keepIndex) || keepIndex < 0 || keepIndex >= dup.accounts.length) {
                        console.log('Invalid selection.');
                      } else {
                        const keepAccount = dup.accounts[keepIndex];
                        const accountsToDelete = dup.accounts.filter((_, i) => i !== keepIndex);

                        console.log(`\nKeeping account: ${keepAccount.id}`);
                        console.log(`Accounts to delete: ${accountsToDelete.map(a => a.id).join(', ')}`);

                        const confirmed = await confirm('Are you sure you want to delete these accounts?');
                        if (confirmed) {
                          for (const account of accountsToDelete) {
                            console.log(`Deleting user ${account.id}...`);
                            const success = await deleteUser(account.id);
                            if (success) {
                              console.log(`Successfully deleted user ${account.id}`);
                            }
                          }
                        }
                      }
                      await showMenu();
                    });
                  }
                });
              } else {
                await showMenu();
              }
            });
          }
          break;

        case '3':
          // Find orphaned profiles
          console.log('\n=== Orphaned Profiles ===');
          const orphanedProfiles = findOrphanedProfiles(profiles, users);

          if (orphanedProfiles.length === 0) {
            console.log('No orphaned profiles found.');
          } else {
            console.log(`Found ${orphanedProfiles.length} orphaned profiles:`);

            // Create a summary table of orphaned profiles
            console.log('\nSummary of orphaned profiles:');
            console.log('─'.repeat(100));
            console.log('| # | Profile ID (truncated)   | Name                      | Account Type | Created Date        |');
            console.log('|' + '─'.repeat(3) + '|' + '─'.repeat(25) + '|' + '─'.repeat(26) + '|' + '─'.repeat(14) + '|' + '─'.repeat(21) + '|');

            for (let i = 0; i < orphanedProfiles.length; i++) {
              const profile = orphanedProfiles[i];

              // Format data for the table
              const truncatedId = profile.id.substring(0, 23);
              const name = `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'Not set';
              const accountType = profile.account_type || 'Not set';
              const created = new Date(profile.created_at).toLocaleDateString();

              // Pad strings to align columns
              const indexPadded = (i + 1).toString().padEnd(3);
              const idPadded = truncatedId.padEnd(25);
              const namePadded = name.substring(0, 25).padEnd(26);
              const accountTypePadded = accountType.substring(0, 13).padEnd(14);
              const createdPadded = created.padEnd(21);

              console.log(`| ${indexPadded}| ${idPadded}| ${namePadded}| ${accountTypePadded}| ${createdPadded}|`);
            }
            console.log('─'.repeat(100));

            // Detailed view of each orphaned profile
            console.log('\nDetailed information:');
            orphanedProfiles.forEach((profile, i) => {
              console.log('\n' + '─'.repeat(50));
              console.log(`\n⚠️ ORPHANED PROFILE #${i+1}`);
              console.log(`🆔 Profile ID: ${profile.id}`);
              console.log(`👤 Name: ${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'Not set');
              console.log(`🏢 Account Type: ${profile.account_type || 'Not set'}`);
              console.log(`📅 Created: ${new Date(profile.created_at).toLocaleString()}`);
              console.log(`📧 Email: Unknown (No auth user found)`);
            });

            rl.question('\nWould you like to delete orphaned profiles? (y/n): ', async (answer) => {
              if (answer.toLowerCase() === 'y') {
                rl.question('Enter the number of the profile to delete (or "all" for all): ', async (profileIndex) => {
                  if (profileIndex.toLowerCase() === 'all') {
                    const confirmed = await confirm(`Are you sure you want to delete ALL ${orphanedProfiles.length} orphaned profiles?`);
                    if (confirmed) {
                      for (const profile of orphanedProfiles) {
                        console.log(`Deleting profile ${profile.id}...`);
                        const success = await deleteProfile(profile.id);
                        if (success) {
                          console.log(`Successfully deleted profile ${profile.id}`);
                        }
                      }
                    }
                  } else {
                    const index = parseInt(profileIndex) - 1;
                    if (isNaN(index) || index < 0 || index >= orphanedProfiles.length) {
                      console.log('Invalid selection.');
                    } else {
                      const profile = orphanedProfiles[index];
                      const confirmed = await confirm(`Are you sure you want to delete profile ${profile.id}?`);
                      if (confirmed) {
                        console.log(`Deleting profile ${profile.id}...`);
                        const success = await deleteProfile(profile.id);
                        if (success) {
                          console.log(`Successfully deleted profile ${profile.id}`);
                        }
                      }
                    }
                  }
                  await showMenu();
                });
              } else {
                await showMenu();
              }
            });
          }
          break;

        case '4':
          // Delete a specific profile
          console.log('\n=== Delete a Specific Profile ===');
          console.log('\nYou can delete a profile by ID or by email address.');
          rl.question('\nEnter the ID or email of the profile to delete: ', async (input) => {
            // Check if input is an email address
            if (input.includes('@')) {
              // Find users with this email
              const matchingUsers = users.filter(u => u.email?.toLowerCase() === input.toLowerCase());

              if (matchingUsers.length === 0) {
                console.log(`\n⚠️ No users found with email ${input}.`);
              } else if (matchingUsers.length === 1) {
                // Single match - proceed with deletion
                const user = matchingUsers[0];
                const profile = profileMap[user.id];

                if (profile) {
                  console.log('\nFound profile with this email:');
                  displayProfile(profile, user);

                  const confirmed = await confirm(`Are you sure you want to delete this profile?`);
                  if (confirmed) {
                    console.log(`Deleting profile ${profile.id}...`);
                    const success = await deleteProfile(profile.id);
                    if (success) {
                      console.log(`\n✅ Successfully deleted profile ${profile.id}`);
                    }
                  }
                } else {
                  console.log(`\n⚠️ User found with email ${input}, but no associated profile.`);
                }
              } else {
                // Multiple matches - let user choose
                console.log(`\nFound ${matchingUsers.length} users with email ${input}:`);

                matchingUsers.forEach((user, i) => {
                  const profile = profileMap[user.id];
                  console.log(`\n${i+1}. User ID: ${user.id}`);
                  console.log(`   Email: ${user.email}`);
                  console.log(`   Created: ${new Date(user.created_at).toLocaleString()}`);
                  console.log(`   Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);

                  if (profile) {
                    console.log(`   Name: ${profile.first_name || ''} ${profile.last_name || ''}`.trim());
                    console.log(`   Role: ${profile.role || 'Not set'}`);
                  } else {
                    console.log(`   No profile found`);
                  }
                });

                rl.question('\nEnter the number of the user to delete (or 0 to cancel): ', async (userIndex) => {
                  const index = parseInt(userIndex) - 1;
                  if (isNaN(index) || index < 0 || index >= matchingUsers.length) {
                    console.log('Invalid selection or cancelled.');
                  } else {
                    const user = matchingUsers[index];
                    const profile = profileMap[user.id];

                    if (profile) {
                      const confirmed = await confirm(`Are you sure you want to delete the profile for ${user.email}?`);
                      if (confirmed) {
                        console.log(`Deleting profile ${profile.id}...`);
                        const success = await deleteProfile(profile.id);
                        if (success) {
                          console.log(`\n✅ Successfully deleted profile ${profile.id}`);
                        }
                      }
                    } else {
                      console.log(`No profile found for user ${user.id}.`);
                    }
                  }
                  await showMenu();
                });
                return; // Early return to prevent double menu display
              }
            } else {
              // Assume input is a profile ID
              const profileId = input;
              const profile = profileMap[profileId];

              if (!profile) {
                console.log(`\n⚠️ Profile with ID ${profileId} not found.`);
              } else {
                displayProfile(profile, users.find(u => u.id === profileId));

                const confirmed = await confirm(`Are you sure you want to delete this profile?`);
                if (confirmed) {
                  console.log(`Deleting profile ${profileId}...`);
                  const success = await deleteProfile(profileId);
                  if (success) {
                    console.log(`\n✅ Successfully deleted profile ${profileId}`);
                  }
                }
              }
            }

            await showMenu();
          });
          break;

        case '5':
          // Delete a specific user
          console.log('\n=== Delete a Specific User ===');
          console.log('\nYou can delete a user by ID or by email address.');
          rl.question('\nEnter the ID or email of the user to delete: ', async (input) => {
            // Check if input is an email address
            if (input.includes('@')) {
              // Find users with this email
              const matchingUsers = users.filter(u => u.email?.toLowerCase() === input.toLowerCase());

              if (matchingUsers.length === 0) {
                console.log(`\n⚠️ No users found with email ${input}.`);
              } else if (matchingUsers.length === 1) {
                // Single match - proceed with deletion
                const user = matchingUsers[0];
                const profile = profileMap[user.id];

                console.log('\nFound user with this email:');
                if (profile) {
                  displayProfile(profile, user);
                } else {
                  console.log('─'.repeat(50));
                  console.log(`\n📧 EMAIL: ${user.email}`);
                  console.log(`🆔 User ID: ${user.id}`);
                  console.log(`📅 Created: ${new Date(user.created_at).toLocaleString()}`);
                  console.log(`🔒 Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);
                  console.log(`⚠️ No profile found for this user`);
                }

                const confirmed = await confirm(`Are you sure you want to delete this user and their profile?`);
                if (confirmed) {
                  console.log(`Deleting user ${user.id}...`);
                  const success = await deleteUser(user.id);
                  if (success) {
                    console.log(`\n✅ Successfully deleted user ${user.id}`);
                  }
                }
              } else {
                // Multiple matches - let user choose
                console.log(`\nFound ${matchingUsers.length} users with email ${input}:`);

                matchingUsers.forEach((user, i) => {
                  const profile = profileMap[user.id];
                  console.log(`\n${i+1}. User ID: ${user.id}`);
                  console.log(`   Email: ${user.email}`);
                  console.log(`   Created: ${new Date(user.created_at).toLocaleString()}`);
                  console.log(`   Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);

                  if (profile) {
                    console.log(`   Name: ${profile.first_name || ''} ${profile.last_name || ''}`.trim());
                    console.log(`   Role: ${profile.role || 'Not set'}`);
                  } else {
                    console.log(`   No profile found`);
                  }
                });

                rl.question('\nEnter the number of the user to delete (or 0 to cancel): ', async (userIndex) => {
                  const index = parseInt(userIndex) - 1;
                  if (isNaN(index) || index < 0 || index >= matchingUsers.length) {
                    console.log('Invalid selection or cancelled.');
                  } else {
                    const user = matchingUsers[index];
                    const profile = profileMap[user.id];

                    if (profile) {
                      displayProfile(profile, user);
                    } else {
                      console.log(`\nUser ID: ${user.id}`);
                      console.log(`Email: ${user.email}`);
                      console.log(`Created: ${new Date(user.created_at).toLocaleString()}`);
                      console.log(`Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);
                      console.log(`No profile found for this user`);
                    }

                    const confirmed = await confirm(`Are you sure you want to delete this user and their profile?`);
                    if (confirmed) {
                      console.log(`Deleting user ${user.id}...`);
                      const success = await deleteUser(user.id);
                      if (success) {
                        console.log(`\n✅ Successfully deleted user ${user.id}`);
                      }
                    }
                  }
                  await showMenu();
                });
                return; // Early return to prevent double menu display
              }
            } else {
              // Assume input is a user ID
              const userId = input;
              const user = users.find(u => u.id === userId);

              if (!user) {
                console.log(`\n⚠️ User with ID ${userId} not found.`);
              } else {
                const profile = profileMap[userId];
                if (profile) {
                  displayProfile(profile, user);
                } else {
                  console.log('─'.repeat(50));
                  console.log(`\n📧 EMAIL: ${user.email}`);
                  console.log(`🆔 User ID: ${user.id}`);
                  console.log(`📅 Created: ${new Date(user.created_at).toLocaleString()}`);
                  console.log(`🔒 Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);
                  console.log(`⚠️ No profile found for this user`);
                }

                const confirmed = await confirm(`Are you sure you want to delete this user and their profile?`);
                if (confirmed) {
                  console.log(`Deleting user ${userId}...`);
                  const success = await deleteUser(userId);
                  if (success) {
                    console.log(`\n✅ Successfully deleted user ${userId}`);
                  }
                }
              }
            }

            await showMenu();
          });
          break;

        case '6':
          // Exit
          console.log('Exiting...');
          rl.close();
          process.exit(0);
          break;

        default:
          console.log('Invalid option. Please try again.');
          await showMenu();
          break;
      }
    });
  }

  await showMenu();
}

// Run the main function
main().catch(error => {
  console.error('Error:', error);
  rl.close();
  process.exit(1);
});
