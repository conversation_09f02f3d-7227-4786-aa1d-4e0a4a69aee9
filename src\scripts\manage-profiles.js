// Script to manage profiles in Supabase
// This script helps identify and remove redundant accounts

import { createClient } from '@supabase/supabase-js';
import readline from 'readline';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with admin key for full access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // This should be the service_role key, not the anon key

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to get all profiles
async function getAllProfiles() {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching profiles:', error);
    return [];
  }
}

// Function to get all auth users
async function getAllUsers() {
  try {
    const { data, error } = await supabase.auth.admin.listUsers();

    if (error) throw error;
    return data.users;
  } catch (error) {
    console.error('Error fetching users:', error);
    return [];
  }
}

// Function to find potential duplicate accounts
function findDuplicates(profiles, users) {
  // Create a map of emails to user IDs
  const emailMap = {};

  // Map users by email
  users.forEach(user => {
    if (!emailMap[user.email]) {
      emailMap[user.email] = [];
    }
    emailMap[user.email].push({
      id: user.id,
      created_at: user.created_at,
      last_sign_in_at: user.last_sign_in_at,
      user_metadata: user.user_metadata
    });
  });

  // Find emails with multiple accounts
  const duplicateEmails = Object.keys(emailMap).filter(email => emailMap[email].length > 1);

  return duplicateEmails.map(email => ({
    email,
    accounts: emailMap[email].sort((a, b) =>
      new Date(b.last_sign_in_at || 0) - new Date(a.last_sign_in_at || 0)
    )
  }));
}

// Function to find profiles without corresponding auth users
function findOrphanedProfiles(profiles, users) {
  const userIds = users.map(user => user.id);
  return profiles.filter(profile => !userIds.includes(profile.id));
}

// Function to delete a profile
async function deleteProfile(profileId) {
  try {
    const { error } = await supabase
      .from('profiles')
      .delete()
      .eq('id', profileId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error deleting profile ${profileId}:`, error);
    return false;
  }
}

// Function to delete a user
async function deleteUser(userId) {
  try {
    const { error } = await supabase.auth.admin.deleteUser(userId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error(`Error deleting user ${userId}:`, error);
    return false;
  }
}

// Function to display profile information
function displayProfile(profile, user) {
  const divider = '─'.repeat(50);

  if (user) {
    if (process.env.NODE_ENV === 'development') {
    console.log(divider);
  }
    if (process.env.NODE_ENV === 'development') {

      console.log(`\n📧 EMAIL: ${user.email}`);

      }
  } else {
    if (process.env.NODE_ENV === 'development') {
    console.log(divider);
  }
    if (process.env.NODE_ENV === 'development') {

      console.log(`\n📧 EMAIL: Unknown (No auth user found)`);

      }
  }

  if (process.env.NODE_ENV === 'development') {
    console.log(`🆔 Profile ID: ${profile.id}`);
  }
  if (process.env.NODE_ENV === 'development') {

    console.log(`👤 Name: ${profile.first_name || ''} ${profile.last_name || ''}`.trim());

    }
  if (process.env.NODE_ENV === 'development') {
    console.log(`🏢 Account Type: ${profile.account_type || 'Not set'}`);
  }
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔑 Role: ${profile.role || 'Not set'}`);
  }
  if (process.env.NODE_ENV === 'development') {

    console.log(`📅 Created: ${new Date(profile.created_at).toLocaleString()}`);


    }
  if (user) {
    if (process.env.NODE_ENV === 'development') {

      console.log(`🔒 Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);


      }
    // Display important user metadata in a more readable format
    const metadata = user.user_metadata || {};
    if (Object.keys(metadata).length > 0) {
      if (process.env.NODE_ENV === 'development') {

        console.log('📋 User Metadata:');

        }
      Object.entries(metadata).forEach(([key, value]) => {
        // Format complex objects for better readability
        if (typeof value === 'object' && value !== null) {
          if (process.env.NODE_ENV === 'development') {

            console.log(`  - ${key}: ${JSON.stringify(value)}`);

            }
        } else {
          if (process.env.NODE_ENV === 'development') {
    console.log(`  - ${key}: ${value}`);
  }
        }
      });
    }
  }
}

// Function to prompt user for confirmation
function confirm(message) {
  return new Promise((resolve) => {
    rl.question(`${message} (y/n): `, (answer) => {
      resolve(answer.toLowerCase() === 'y');
    });
  });
}

// Main function
async function main() {
  if (process.env.NODE_ENV === 'development') {

    console.log('Fetching profiles and users...');


    }
  const profiles = await getAllProfiles();
  const users = await getAllUsers();

  if (process.env.NODE_ENV === 'development') {


    console.log(`Found ${profiles.length} profiles and ${users.length} users`);



    }
  // Create a map of profiles by ID for easy lookup
  const profileMap = {};
  profiles.forEach(profile => {
    profileMap[profile.id] = profile;
  });

  // Main menu
  async function showMenu() {
    if (process.env.NODE_ENV === 'development') {
    console.log('\n=== Profile Management Tool ===');
  }
    if (process.env.NODE_ENV === 'development') {
    console.log('1. List all profiles');
  }
    if (process.env.NODE_ENV === 'development') {
    console.log('2. Find duplicate accounts');
  }
    if (process.env.NODE_ENV === 'development') {
    console.log('3. Find orphaned profiles');
  }
    if (process.env.NODE_ENV === 'development') {
    console.log('4. Delete a specific profile');
  }
    if (process.env.NODE_ENV === 'development') {

      console.log('5. Delete a specific user (and associated profile)');

      }
    if (process.env.NODE_ENV === 'development') {
    console.log('6. Exit');
  }
    rl.question('\nSelect an option (1-6): ', async (option) => {
      switch (option) {
        case '1':
          // List all profiles
          if (process.env.NODE_ENV === 'development') {
    console.log('\n=== All Profiles ===');
  }
          // Create a summary table first
          if (process.env.NODE_ENV === 'development') {
    console.log('\nSummary of all profiles:');
  }
          if (process.env.NODE_ENV === 'development') {

            console.log('─'.repeat(100));

            }
          if (process.env.NODE_ENV === 'development') {

            console.log('| ID (truncated)        | Email                           | Name                      | Role      | Last Sign In        |');

            }
          if (process.env.NODE_ENV === 'development') {

            console.log('|' + '─'.repeat(22) + '|' + '─'.repeat(32) + '|' + '─'.repeat(26) + '|' + '─'.repeat(11) + '|' + '─'.repeat(21) + '|');


            }
          // Sort users by email for easier reading
          const sortedUsers = [...users].sort((a, b) => {
            return (a.email || '').localeCompare(b.email || '');
          });

          for (const user of sortedUsers) {
            const profile = profileMap[user.id];
            if (profile) {
              // Format data for the table
              const truncatedId = user.id.substring(0, 20);
              const email = user.email || 'Unknown';
              const name = `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'Not set';
              const role = profile.role || 'Not set';
              const lastSignIn = user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleDateString() : 'Never';

              // Pad strings to align columns
              const idPadded = truncatedId.padEnd(22);
              const emailPadded = email.padEnd(32);
              const namePadded = name.substring(0, 25).padEnd(26);
              const rolePadded = role.substring(0, 10).padEnd(11);
              const lastSignInPadded = lastSignIn.padEnd(21);

              if (process.env.NODE_ENV === 'development') {
    console.log(`| ${idPadded}| ${emailPadded}| ${namePadded}| ${rolePadded}| ${lastSignInPadded}|`);
  }
            }
          }
          if (process.env.NODE_ENV === 'development') {

            console.log('─'.repeat(100));


            }
          // Ask if user wants to see detailed information
          rl.question('\nDo you want to see detailed information for all profiles? (y/n): ', async (answer) => {
            if (answer.toLowerCase() === 'y') {
              for (const user of sortedUsers) {
                const profile = profileMap[user.id];
                if (profile) {
                  displayProfile(profile, user);
                }
              }
            }
            await showMenu();
          });
          break;

        case '2':
          // Find duplicate accounts
          if (process.env.NODE_ENV === 'development') {
    console.log('\n=== Duplicate Accounts ===');
  }
          const duplicates = findDuplicates(profiles, users);

          if (duplicates.length === 0) {
            if (process.env.NODE_ENV === 'development') {
    console.log('No duplicate accounts found.');
  }
          } else {
            if (process.env.NODE_ENV === 'development') {

              console.log(`Found ${duplicates.length} email(s) with multiple accounts:`);


              }
            // Create a summary table of duplicate emails
            if (process.env.NODE_ENV === 'development') {
    console.log('\nSummary of duplicate emails:');
  }
            if (process.env.NODE_ENV === 'development') {

              console.log('─'.repeat(100));

              }
            if (process.env.NODE_ENV === 'development') {
    console.log('| # | Email                           | # of Accounts | Last Active                |');
  }
            if (process.env.NODE_ENV === 'development') {

              console.log('|' + '─'.repeat(3) + '|' + '─'.repeat(32) + '|' + '─'.repeat(14) + '|' + '─'.repeat(28) + '|');


              }
            for (let i = 0; i < duplicates.length; i++) {
              const dup = duplicates[i];
              const numAccounts = dup.accounts.length;

              // Find the most recently active account
              const mostRecentAccount = dup.accounts[0]; // Already sorted by last_sign_in_at
              const lastActive = mostRecentAccount.last_sign_in_at
                ? new Date(mostRecentAccount.last_sign_in_at).toLocaleString()
                : 'Never';

              // Format for table
              const indexPadded = (i + 1).toString().padEnd(3);
              const emailPadded = dup.email.padEnd(32);
              const numAccountsPadded = numAccounts.toString().padEnd(14);
              const lastActivePadded = lastActive.padEnd(28);

              if (process.env.NODE_ENV === 'development') {
    console.log(`| ${indexPadded}| ${emailPadded}| ${numAccountsPadded}| ${lastActivePadded}|`);
  }
            }
            if (process.env.NODE_ENV === 'development') {

              console.log('─'.repeat(100));


              }
            // Detailed view of each duplicate
            for (let i = 0; i < duplicates.length; i++) {
              const dup = duplicates[i];
              if (process.env.NODE_ENV === 'development') {

                console.log('\n' + '─'.repeat(50));

                }
              if (process.env.NODE_ENV === 'development') {
    console.log(`\n📧 DUPLICATE EMAIL #${i+1}: ${dup.email}`);
  }
              if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${dup.accounts.length} accounts with this email:\n`);
  }
              dup.accounts.forEach((account, j) => {
                if (process.env.NODE_ENV === 'development') {

                  console.log(`   ${String.fromCharCode(97 + j)}. 🆔 User ID: ${account.id}`);

                  }
                if (process.env.NODE_ENV === 'development') {

                  console.log(`      📅 Created: ${new Date(account.created_at).toLocaleString()}`);

                  }
                if (process.env.NODE_ENV === 'development') {

                  console.log(`      🔒 Last Sign In: ${account.last_sign_in_at ? new Date(account.last_sign_in_at).toLocaleString() : 'Never'}`);


                  }
                const profile = profileMap[account.id];
                if (profile) {
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`      👤 Name: ${profile.first_name || ''} ${profile.last_name || ''}`.trim());

                    }
                  if (process.env.NODE_ENV === 'development') {
    console.log(`      🏢 Account Type: ${profile.account_type || 'Not set'}`);
  }
                  if (process.env.NODE_ENV === 'development') {
    console.log(`      🔑 Role: ${profile.role || 'Not set'}`);
  }
                } else {
                  if (process.env.NODE_ENV === 'development') {
    console.log(`      ⚠️ No profile found`);
  }
                }
                if (process.env.NODE_ENV === 'development') {

                  console.log(''); // Add a blank line between accounts
              });
            }

            rl.question('\nWould you like to clean up duplicate accounts? (y/n): ', async (answer) => {
              if (answer.toLowerCase() === 'y') {
                rl.question('Enter the number of the email to clean up: ', async (emailIndex) => {
                  const index = parseInt(emailIndex) - 1;
                  if (isNaN(index) || index < 0 || index >= duplicates.length) {
                    if (process.env.NODE_ENV === 'development') {
    console.log('Invalid selection.');
  }
                  } else {
                    const dup = duplicates[index];
                    if (process.env.NODE_ENV === 'development') {
    console.log(`\nFor email ${dup.email}, which accounts would you like to keep?`);
  }
                    if (process.env.NODE_ENV === 'development') {

                      console.log('Enter the letter of the account to KEEP (others will be deleted):');


                      }
                    rl.question('Account to keep (e.g., a): ', async (keepLetter) => {
                      const keepIndex = keepLetter.charCodeAt(0) - 97;
                      if (isNaN(keepIndex) || keepIndex < 0 || keepIndex >= dup.accounts.length) {
                        if (process.env.NODE_ENV === 'development') {
    console.log('Invalid selection.');
  }
                      } else {
                        const keepAccount = dup.accounts[keepIndex];
                        const accountsToDelete = dup.accounts.filter((_, i) => i !== keepIndex);

                        if (process.env.NODE_ENV === 'development') {
    console.log(`\nKeeping account: ${keepAccount.id}`);
  }
                        if (process.env.NODE_ENV === 'development') {

                          console.log(`Accounts to delete: ${accountsToDelete.map(a => a.id).join(', ')}`);


                          }
                        const confirmed = await confirm('Are you sure you want to delete these accounts?');
                        if (confirmed) {
                          for (const account of accountsToDelete) {
                            if (process.env.NODE_ENV === 'development') {

                              console.log(`Deleting user ${account.id}...`);

                              }
                            const success = await deleteUser(account.id);
                            if (success) {
                              if (process.env.NODE_ENV === 'development') {

                                console.log(`Successfully deleted user ${account.id}`);

                                }
                            }
                          }
                        }
                      }
                      await showMenu();
                    });
                  }
                });
              } else {
                await showMenu();
              }
            });
          }
          break;

        case '3':
          // Find orphaned profiles
          if (process.env.NODE_ENV === 'development') {
    console.log('\n=== Orphaned Profiles ===');
  }
          const orphanedProfiles = findOrphanedProfiles(profiles, users);

          if (orphanedProfiles.length === 0) {
            if (process.env.NODE_ENV === 'development') {
    console.log('No orphaned profiles found.');
  }
          } else {
            if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${orphanedProfiles.length} orphaned profiles:`);
  }
            // Create a summary table of orphaned profiles
            if (process.env.NODE_ENV === 'development') {
    console.log('\nSummary of orphaned profiles:');
  }
            if (process.env.NODE_ENV === 'development') {

              console.log('─'.repeat(100));

              }
            if (process.env.NODE_ENV === 'development') {

              console.log('| # | Profile ID (truncated)   | Name                      | Account Type | Created Date        |');

              }
            if (process.env.NODE_ENV === 'development') {

              console.log('|' + '─'.repeat(3) + '|' + '─'.repeat(25) + '|' + '─'.repeat(26) + '|' + '─'.repeat(14) + '|' + '─'.repeat(21) + '|');


              }
            for (let i = 0; i < orphanedProfiles.length; i++) {
              const profile = orphanedProfiles[i];

              // Format data for the table
              const truncatedId = profile.id.substring(0, 23);
              const name = `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'Not set';
              const accountType = profile.account_type || 'Not set';
              const created = new Date(profile.created_at).toLocaleDateString();

              // Pad strings to align columns
              const indexPadded = (i + 1).toString().padEnd(3);
              const idPadded = truncatedId.padEnd(25);
              const namePadded = name.substring(0, 25).padEnd(26);
              const accountTypePadded = accountType.substring(0, 13).padEnd(14);
              const createdPadded = created.padEnd(21);

              if (process.env.NODE_ENV === 'development') {
    console.log(`| ${indexPadded}| ${idPadded}| ${namePadded}| ${accountTypePadded}| ${createdPadded}|`);
  }
            }
            if (process.env.NODE_ENV === 'development') {

              console.log('─'.repeat(100));


              }
            // Detailed view of each orphaned profile
            if (process.env.NODE_ENV === 'development') {
    console.log('\nDetailed information:');
  }
            orphanedProfiles.forEach((profile, i) => {
              if (process.env.NODE_ENV === 'development') {

                console.log('\n' + '─'.repeat(50));

                }
              if (process.env.NODE_ENV === 'development') {
    console.log(`\n⚠️ ORPHANED PROFILE #${i+1}`);
  }
              if (process.env.NODE_ENV === 'development') {
    console.log(`🆔 Profile ID: ${profile.id}`);
  }
              if (process.env.NODE_ENV === 'development') {

                console.log(`👤 Name: ${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'Not set');

                }
              if (process.env.NODE_ENV === 'development') {
    console.log(`🏢 Account Type: ${profile.account_type || 'Not set'}`);
  }
              if (process.env.NODE_ENV === 'development') {

                console.log(`📅 Created: ${new Date(profile.created_at).toLocaleString()}`);

                }
              if (process.env.NODE_ENV === 'development') {

                console.log(`📧 Email: Unknown (No auth user found)`);

                }
            });

            rl.question('\nWould you like to delete orphaned profiles? (y/n): ', async (answer) => {
              if (answer.toLowerCase() === 'y') {
                rl.question('Enter the number of the profile to delete (or "all" for all): ', async (profileIndex) => {
                  if (profileIndex.toLowerCase() === 'all') {
                    const confirmed = await confirm(`Are you sure you want to delete ALL ${orphanedProfiles.length} orphaned profiles?`);
                    if (confirmed) {
                      for (const profile of orphanedProfiles) {
                        if (process.env.NODE_ENV === 'development') {
    console.log(`Deleting profile ${profile.id}...`);
  }
                        const success = await deleteProfile(profile.id);
                        if (success) {
                          if (process.env.NODE_ENV === 'development') {
    console.log(`Successfully deleted profile ${profile.id}`);
  }
                        }
                      }
                    }
                  } else {
                    const index = parseInt(profileIndex) - 1;
                    if (isNaN(index) || index < 0 || index >= orphanedProfiles.length) {
                      if (process.env.NODE_ENV === 'development') {
    console.log('Invalid selection.');
  }
                    } else {
                      const profile = orphanedProfiles[index];
                      const confirmed = await confirm(`Are you sure you want to delete profile ${profile.id}?`);
                      if (confirmed) {
                        if (process.env.NODE_ENV === 'development') {
    console.log(`Deleting profile ${profile.id}...`);
  }
                        const success = await deleteProfile(profile.id);
                        if (success) {
                          if (process.env.NODE_ENV === 'development') {
    console.log(`Successfully deleted profile ${profile.id}`);
  }
                        }
                      }
                    }
                  }
                  await showMenu();
                });
              } else {
                await showMenu();
              }
            });
          }
          break;

        case '4':
          // Delete a specific profile
          if (process.env.NODE_ENV === 'development') {
    console.log('\n=== Delete a Specific Profile ===');
  }
          if (process.env.NODE_ENV === 'development') {
    console.log('\nYou can delete a profile by ID or by email address.');
  }
          rl.question('\nEnter the ID or email of the profile to delete: ', async (input) => {
            // Check if input is an email address
            if (input.includes('@')) {
              // Find users with this email
              const matchingUsers = users.filter(u => u.email?.toLowerCase() === input.toLowerCase());

              if (matchingUsers.length === 0) {
                if (process.env.NODE_ENV === 'development') {

                  console.log(`\n⚠️ No users found with email ${input}.`);

                  }
              } else if (matchingUsers.length === 1) {
                // Single match - proceed with deletion
                const user = matchingUsers[0];
                const profile = profileMap[user.id];

                if (profile) {
                  if (process.env.NODE_ENV === 'development') {
    console.log('\nFound profile with this email:');
  }
                  displayProfile(profile, user);

                  const confirmed = await confirm(`Are you sure you want to delete this profile?`);
                  if (confirmed) {
                    if (process.env.NODE_ENV === 'development') {
    console.log(`Deleting profile ${profile.id}...`);
  }
                    const success = await deleteProfile(profile.id);
                    if (success) {
                      if (process.env.NODE_ENV === 'development') {
    console.log(`\n✅ Successfully deleted profile ${profile.id}`);
  }
                    }
                  }
                } else {
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`\n⚠️ User found with email ${input}, but no associated profile.`);

                    }
                }
              } else {
                // Multiple matches - let user choose
                if (process.env.NODE_ENV === 'development') {

                  console.log(`\nFound ${matchingUsers.length} users with email ${input}:`);


                  }
                matchingUsers.forEach((user, i) => {
                  const profile = profileMap[user.id];
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`\n${i+1}. User ID: ${user.id}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`   Email: ${user.email}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`   Created: ${new Date(user.created_at).toLocaleString()}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`   Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);


                    }
                  if (profile) {
                    if (process.env.NODE_ENV === 'development') {

                      console.log(`   Name: ${profile.first_name || ''} ${profile.last_name || ''}`.trim());

                      }
                    if (process.env.NODE_ENV === 'development') {
    console.log(`   Role: ${profile.role || 'Not set'}`);
  }
                  } else {
                    if (process.env.NODE_ENV === 'development') {
    console.log(`   No profile found`);
  }
                  }
                });

                rl.question('\nEnter the number of the user to delete (or 0 to cancel): ', async (userIndex) => {
                  const index = parseInt(userIndex) - 1;
                  if (isNaN(index) || index < 0 || index >= matchingUsers.length) {
                    if (process.env.NODE_ENV === 'development') {
    console.log('Invalid selection or cancelled.');
  }
                  } else {
                    const user = matchingUsers[index];
                    const profile = profileMap[user.id];

                    if (profile) {
                      const confirmed = await confirm(`Are you sure you want to delete the profile for ${user.email}?`);
                      if (confirmed) {
                        if (process.env.NODE_ENV === 'development') {
    console.log(`Deleting profile ${profile.id}...`);
  }
                        const success = await deleteProfile(profile.id);
                        if (success) {
                          if (process.env.NODE_ENV === 'development') {
    console.log(`\n✅ Successfully deleted profile ${profile.id}`);
  }
                        }
                      }
                    } else {
                      if (process.env.NODE_ENV === 'development') {

                        console.log(`No profile found for user ${user.id}.`);

                        }
                    }
                  }
                  await showMenu();
                });
                return; // Early return to prevent double menu display
              }
            } else {
              // Assume input is a profile ID
              const profileId = input;
              const profile = profileMap[profileId];

              if (!profile) {
                if (process.env.NODE_ENV === 'development') {
    console.log(`\n⚠️ Profile with ID ${profileId} not found.`);
  }
              } else {
                displayProfile(profile, users.find(u => u.id === profileId));

                const confirmed = await confirm(`Are you sure you want to delete this profile?`);
                if (confirmed) {
                  if (process.env.NODE_ENV === 'development') {
    console.log(`Deleting profile ${profileId}...`);
  }
                  const success = await deleteProfile(profileId);
                  if (success) {
                    if (process.env.NODE_ENV === 'development') {
    console.log(`\n✅ Successfully deleted profile ${profileId}`);
  }
                  }
                }
              }
            }

            await showMenu();
          });
          break;

        case '5':
          // Delete a specific user
          if (process.env.NODE_ENV === 'development') {

            console.log('\n=== Delete a Specific User ===');

            }
          if (process.env.NODE_ENV === 'development') {

            console.log('\nYou can delete a user by ID or by email address.');

            }
          rl.question('\nEnter the ID or email of the user to delete: ', async (input) => {
            // Check if input is an email address
            if (input.includes('@')) {
              // Find users with this email
              const matchingUsers = users.filter(u => u.email?.toLowerCase() === input.toLowerCase());

              if (matchingUsers.length === 0) {
                if (process.env.NODE_ENV === 'development') {

                  console.log(`\n⚠️ No users found with email ${input}.`);

                  }
              } else if (matchingUsers.length === 1) {
                // Single match - proceed with deletion
                const user = matchingUsers[0];
                const profile = profileMap[user.id];

                if (process.env.NODE_ENV === 'development') {


                  console.log('\nFound user with this email:');


                  }
                if (profile) {
                  displayProfile(profile, user);
                } else {
                  if (process.env.NODE_ENV === 'development') {

                    console.log('─'.repeat(50));

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`\n📧 EMAIL: ${user.email}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`🆔 User ID: ${user.id}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`📅 Created: ${new Date(user.created_at).toLocaleString()}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`🔒 Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`⚠️ No profile found for this user`);

                    }
                }

                const confirmed = await confirm(`Are you sure you want to delete this user and their profile?`);
                if (confirmed) {
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`Deleting user ${user.id}...`);

                    }
                  const success = await deleteUser(user.id);
                  if (success) {
                    if (process.env.NODE_ENV === 'development') {

                      console.log(`\n✅ Successfully deleted user ${user.id}`);

                      }
                  }
                }
              } else {
                // Multiple matches - let user choose
                if (process.env.NODE_ENV === 'development') {

                  console.log(`\nFound ${matchingUsers.length} users with email ${input}:`);


                  }
                matchingUsers.forEach((user, i) => {
                  const profile = profileMap[user.id];
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`\n${i+1}. User ID: ${user.id}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`   Email: ${user.email}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`   Created: ${new Date(user.created_at).toLocaleString()}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`   Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);


                    }
                  if (profile) {
                    if (process.env.NODE_ENV === 'development') {

                      console.log(`   Name: ${profile.first_name || ''} ${profile.last_name || ''}`.trim());

                      }
                    if (process.env.NODE_ENV === 'development') {
    console.log(`   Role: ${profile.role || 'Not set'}`);
  }
                  } else {
                    if (process.env.NODE_ENV === 'development') {
    console.log(`   No profile found`);
  }
                  }
                });

                rl.question('\nEnter the number of the user to delete (or 0 to cancel): ', async (userIndex) => {
                  const index = parseInt(userIndex) - 1;
                  if (isNaN(index) || index < 0 || index >= matchingUsers.length) {
                    if (process.env.NODE_ENV === 'development') {
    console.log('Invalid selection or cancelled.');
  }
                  } else {
                    const user = matchingUsers[index];
                    const profile = profileMap[user.id];

                    if (profile) {
                      displayProfile(profile, user);
                    } else {
                      if (process.env.NODE_ENV === 'development') {

                        console.log(`\nUser ID: ${user.id}`);

                        }
                      if (process.env.NODE_ENV === 'development') {

                        console.log(`Email: ${user.email}`);

                        }
                      if (process.env.NODE_ENV === 'development') {

                        console.log(`Created: ${new Date(user.created_at).toLocaleString()}`);

                        }
                      if (process.env.NODE_ENV === 'development') {

                        console.log(`Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);

                        }
                      if (process.env.NODE_ENV === 'development') {

                        console.log(`No profile found for this user`);

                        }
                    }

                    const confirmed = await confirm(`Are you sure you want to delete this user and their profile?`);
                    if (confirmed) {
                      if (process.env.NODE_ENV === 'development') {

                        console.log(`Deleting user ${user.id}...`);

                        }
                      const success = await deleteUser(user.id);
                      if (success) {
                        if (process.env.NODE_ENV === 'development') {

                          console.log(`\n✅ Successfully deleted user ${user.id}`);

                          }
                      }
                    }
                  }
                  await showMenu();
                });
                return; // Early return to prevent double menu display
              }
            } else {
              // Assume input is a user ID
              const userId = input;
              const user = users.find(u => u.id === userId);

              if (!user) {
                if (process.env.NODE_ENV === 'development') {

                  console.log(`\n⚠️ User with ID ${userId} not found.`);

                  }
              } else {
                const profile = profileMap[userId];
                if (profile) {
                  displayProfile(profile, user);
                } else {
                  if (process.env.NODE_ENV === 'development') {

                    console.log('─'.repeat(50));

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`\n📧 EMAIL: ${user.email}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`🆔 User ID: ${user.id}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`📅 Created: ${new Date(user.created_at).toLocaleString()}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`🔒 Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}`);

                    }
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`⚠️ No profile found for this user`);

                    }
                }

                const confirmed = await confirm(`Are you sure you want to delete this user and their profile?`);
                if (confirmed) {
                  if (process.env.NODE_ENV === 'development') {

                    console.log(`Deleting user ${userId}...`);

                    }
                  const success = await deleteUser(userId);
                  if (success) {
                    if (process.env.NODE_ENV === 'development') {

                      console.log(`\n✅ Successfully deleted user ${userId}`);

                      }
                  }
                }
              }
            }

            await showMenu();
          });
          break;

        case '6':
          // Exit
          if (process.env.NODE_ENV === 'development') {
    console.log('Exiting...');
  }
          rl.close();
          process.exit(0);
          break;

        default:
          if (process.env.NODE_ENV === 'development') {
    console.log('Invalid option. Please try again.');
  }
          await showMenu();
          break;
      }
    });
  }

  await showMenu();
}

// Run the main function
main().catch(error => {
  console.error('Error:', error);
  rl.close();
  process.exit(1);
});
