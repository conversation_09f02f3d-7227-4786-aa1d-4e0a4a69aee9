// Script to migrate existing organizations from user metadata to the organizations table
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function migrateExistingOrganizations() {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log('Starting migration of existing organizations...');
  }
    // Step 1: Get all users with their metadata
    if (process.env.NODE_ENV === 'development') {

      console.log('\nFetching all users...');

      }
    const { data: { users }, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.error('Error fetching users:', usersError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`Found ${users.length} users`);
    

    
      }
    // Step 2: Extract unique organizations from user metadata
    if (process.env.NODE_ENV === 'development') {

      console.log('\nExtracting organizations from user metadata...');

      }
    const organizationsMap = new Map();
    
    for (const user of users) {
      const metadata = user.user_metadata || {};
      const organization = metadata.organization;
      
      if (organization && organization.id && organization.name) {
        if (process.env.NODE_ENV === 'development') {

          console.log(`Found organization in user ${user.email}: ${organization.name} (${organization.id}.replace(/user.*/, 'hasUser: ' + !!user))`);
        

          }
        // Use the organization ID as the key to avoid duplicates
        organizationsMap.set(organization.id, {
          id: organization.id,
          name: organization.name,
          created_at: organization.created_at || new Date().toISOString(),
          updated_at: organization.updated_at || new Date().toISOString()
        });
      }
    }
    
    const uniqueOrganizations = Array.from(organizationsMap.values());
    if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${uniqueOrganizations.length} unique organizations`);
  }
    if (uniqueOrganizations.length === 0) {
      if (process.env.NODE_ENV === 'development') {

        console.log('No organizations found in user metadata. Nothing to migrate.');

        }
      return;
    }
    
    // Step 3: Create organizations in the organizations table
    if (process.env.NODE_ENV === 'development') {
    console.log('\nCreating organizations in the database...');
  }
    for (const org of uniqueOrganizations) {
      if (process.env.NODE_ENV === 'development') {

        console.log(`Processing organization: ${org.name} (${org.id})`);
      

        }
      // Check if the organization already exists
      const { data: existingOrg, error: checkError } = await supabase
        .from('organizations')
        .select('id')
        .eq('id', org.id)
        .single();
      
      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found"
        console.error(`Error checking if organization ${org.id} exists:`, checkError);
        continue;
      }
      
      if (existingOrg) {
        if (process.env.NODE_ENV === 'development') {

          console.log(`Organization ${org.name} (${org.id}) already exists in the database`);

          }
        continue;
      }
      
      // Create the organization
      const { data: newOrg, error: createError } = await supabase
        .from('organizations')
        .insert({
          id: org.id,
          name: org.name,
          created_at: org.created_at,
          updated_at: org.updated_at
        })
        .select()
        .single();
      
      if (createError) {
        console.error(`Error creating organization ${org.name}:`, createError);
      } else {
        if (process.env.NODE_ENV === 'development') {

          console.log(`Created organization: ${newOrg.name} (${newOrg.id})`);

          }
      }
    }
    
    // Step 4: Update profiles with organization IDs
    if (process.env.NODE_ENV === 'development') {
    console.log('\nUpdating profiles with organization IDs...');
  }
    // Get all profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*');
    
    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${profiles.length} profiles to process`);
  }
    let updatedCount = 0;
    
    for (const profile of profiles) {
      // Find the corresponding user
      const user = users.find(u => u.id === profile.id);
      
      if (!user) {
        if (process.env.NODE_ENV === 'development') {

          console.log(`No user found for profile ${profile.id}, skipping`);

          }
        continue;
      }
      
      const metadata = user.user_metadata || {};
      const organization = metadata.organization;
      
      if (organization && organization.id) {
        // Check if the profile already has an organization_id
        if (profile.organization_id === organization.id) {
          if (process.env.NODE_ENV === 'development') {
    console.log(`Profile ${profile.id} already has the correct organization_id, skipping`);
  }
          continue;
        }
        
        // Update the profile
        const { data: updatedProfile, error: updateError } = await supabase
          .from('profiles')
          .update({
            organization_id: organization.id,
            role: metadata.role || profile.role || 'teacher' // Use existing role or default to 'teacher'
          })
          .eq('id', profile.id)
          .select()
          .single();
        
        if (updateError) {
          console.error(`Error updating profile ${profile.id}:`, updateError);
        } else {
          if (process.env.NODE_ENV === 'development') {
    console.log(`Updated profile ${profile.id} with organization_id ${updatedProfile.organization_id} and role ${updatedProfile.role}`);
  }
          updatedCount++;
        }
      } else {
        if (process.env.NODE_ENV === 'development') {

          console.log(`No organization found in metadata for user ${user.email}, skipping`);

          }
      }
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log(`\nMigration complete. Updated ${updatedCount} profiles.`);
  }
  } catch (error) {
    console.error('Error migrating organizations:', error);
  }
}

migrateExistingOrganizations();
