/**
 * GetStream Migration Script
 *
 * This script migrates all tasks to use GetStream for chat functionality.
 * It performs the following operations:
 * 1. Finds all tasks that haven't been migrated to GetStream
 * 2. Creates a GetStream channel for each task
 * 3. Updates the task record with the GetStream channel ID
 * 4. Sets the chat_migrated_to_stream flag to true
 *
 * Run this script with:
 * npm run migrate-to-getstream
 */

import { createClient } from '@supabase/supabase-js';
import { StreamChat } from 'stream-chat';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Use service role key for admin operations
if (process.env.NODE_ENV === 'development') {
    console.log('Supabase URL:', supabaseUrl);
  }
if (process.env.NODE_ENV === 'development') {
    console.log('Supabase Key available:', !!supabaseKey);
  }
if (!supabaseKey) {
  console.error('Error: Supabase key is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize GetStream client
const streamApiKey = process.env.GETSTREAM_API_KEY; // SECURITY: Never use VITE_ prefix for secrets
const streamApiSecret = process.env.GETSTREAM_API_SECRET;
if (process.env.NODE_ENV === 'development') {
    console.log('GetStream API Key:', streamApiKey);
  }
if (process.env.NODE_ENV === 'development') {
    console.log('GetStream API Secret available:', !!streamApiSecret);
  }
// Configuration
const BATCH_SIZE = 10; // Use a smaller batch size to avoid timeouts

// Main migration function
async function migrateToGetStream() {
  if (process.env.NODE_ENV === 'development') {
    console.log('Starting migration to GetStream...');
  }
  if (process.env.NODE_ENV === 'development') {
    console.log('Using Supabase URL:', supabaseUrl);
  }
  if (process.env.NODE_ENV === 'development') {
    console.log('Using GetStream API Key:', streamApiKey);
  }
  if (!streamApiSecret) {
    console.error('Error: GetStream API Secret is required for server-side operations');
    if (process.env.NODE_ENV === 'development') {
    console.log('Please set the GETSTREAM_API_SECRET environment variable');
  }
    process.exit(1);
  }

  let client;

  try {
    // Initialize the GetStream client with server-side capabilities
    client = StreamChat.getInstance(streamApiKey, streamApiSecret, {
      allowServerSideConnect: true // Add this option to disable the warning
    });
    if (process.env.NODE_ENV === 'development') {
    console.log('Initialized GetStream client with server capabilities');
  }
    // No need to connect a user when using server-side client
    if (process.env.NODE_ENV === 'development') {
    console.log('Using server-side client with API secret');
  }
    // Get all tasks that haven't been migrated to GetStream yet
    // For this final run, we'll check all tasks to make sure they're properly migrated
    const { data: tasks, error } = await supabase
      .from('tasks')
      .select('id, title, user_id, assigned_to, chat_migrated_to_stream, getstream_channel_id')
      .is('getstream_channel_id', null) // Tasks without a GetStream channel ID
      .order('created_at', { ascending: false })
      .limit(BATCH_SIZE);

    if (error) {
      throw new Error(`Error fetching tasks: ${error.message}`);
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${tasks?.length || 0} tasks to migrate`);
  }
    // Process each task
    for (const task of tasks || []) {
      try {
        if (process.env.NODE_ENV === 'development') {
    console.log(`Processing task ${task.id}: ${task.title}`);
  }
        // Determine channel members
        const members = [task.user_id];
        if (task.assigned_to && !members.includes(task.assigned_to)) {
          members.push(task.assigned_to);
        }

        // Create users first if they don't exist
        for (const memberId of members) {
          try {
            // Get user profile from Supabase
            const { data: profile } = await supabase
              .from('profiles')
              .select('first_name, last_name, email, avatar_url')
              .eq('id', memberId)
              .maybeSingle();

            // Create a user name
            const userName = profile
              ? `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || profile.email?.split('@')[0] || 'User'
              : 'User';

            // Create or update the user in GetStream
            await client.upsertUser({
              id: memberId,
              name: userName,
              image: profile?.avatar_url,
            });

            if (process.env.NODE_ENV === 'development') {


              console.log(`Created/updated user ${memberId} (${userName}) in GetStream`);


              }
          } catch (userError) {
            console.error(`Error creating user ${memberId} in GetStream:`, userError);
          }
        }

        // Create a channel ID
        const channelId = `task-${task.id}`;

        // Create the channel using server-side client
        const channel = client.channel('messaging', channelId, {
          name: task.title,
          members,
          task_id: task.id,
          created_by: { id: 'system', name: 'System' },
        });

        await channel.create();
        if (process.env.NODE_ENV === 'development') {
    console.log(`Created GetStream channel ${channelId} for task ${task.id}`);
  }
        // Add a system message
        await channel.sendMessage({
          text: 'This chat has been migrated to GetStream.',
          user: { id: 'system', name: 'System' },
          type: 'system',
        });

        // Update the task record
        const { error: updateError } = await supabase
          .from('tasks')
          .update({
            chat_migrated_to_stream: true,
            getstream_channel_id: channelId,
            updated_at: new Date().toISOString(),
          })
          .eq('id', task.id);

        if (updateError) {
          throw new Error(`Error updating task: ${updateError.message}`);
        }

        if (process.env.NODE_ENV === 'development') {
    console.log(`Successfully migrated task ${task.id} to GetStream`);
  }
        // Migrate existing messages if any
        await migrateExistingMessages(task.id, channelId, client);

      } catch (taskError) {
        console.error(`Error migrating task ${task.id}:`, taskError);
      }
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('Migration completed successfully');
  }
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    // No need to disconnect when using server-side client
    if (process.env.NODE_ENV === 'development') {
    console.log('Migration process completed');
  }
  }
}

// Helper function to migrate existing messages
async function migrateExistingMessages(taskId, channelId, client) {
  try {
    // Get the channel
    const channel = client.channel('messaging', channelId);

    // Get all messages for this task
    const { data: messages, error } = await supabase
      .from('task_messages')
      .select('id, sender_id, content, created_at')
      .eq('task_id', taskId)
      .order('created_at', { ascending: true });

    if (error) {
      throw new Error(`Error fetching messages: ${error.message}`);
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${messages?.length || 0} messages to migrate for task ${taskId}`);
  }
    // Process each message
    for (const message of messages || []) {
      try {
        // Get sender profile
        const { data: sender } = await supabase
          .from('profiles')
          .select('first_name, last_name, email')
          .eq('id', message.sender_id)
          .maybeSingle();

        // Determine sender name
        const senderName = sender
          ? `${sender.first_name || ''} ${sender.last_name || ''}`.trim() || sender.email?.split('@')[0] || 'User'
          : 'User';

        // Send message to GetStream with server-side client
        // Note: We can't set created_at directly as it's a reserved field
        const response = await channel.sendMessage({
          text: message.content,
          user: {
            id: message.sender_id,
            name: senderName,
          },
          silent: true, // Don't trigger notifications for historical messages
        });

        // Update the message record with the Stream message ID
        if (response.message?.id) {
          await supabase
            .from('task_messages')
            .update({
              stream_message_id: response.message.id,
            })
            .eq('id', message.id);
        }

      } catch (messageError) {
        console.error(`Error migrating message ${message.id}:`, messageError);
      }
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`Successfully migrated ${messages?.length || 0} messages for task ${taskId}`);
  }
  } catch (error) {
    console.error(`Error migrating messages for task ${taskId}:`, error);
  }
}

// Run the migration
migrateToGetStream();
