/**
 * GetStream Migration Script
 * 
 * This script migrates all tasks to use GetStream for chat functionality.
 * It performs the following operations:
 * 1. Finds all tasks that haven't been migrated to GetStream
 * 2. Creates a GetStream channel for each task
 * 3. Updates the task record with the GetStream channel ID
 * 4. Sets the chat_migrated_to_stream flag to true
 * 
 * Run this script with:
 * npm run migrate-to-getstream
 */

import { supabase } from '@/integrations/supabase/client';
import { 
  getStreamClient, 
  connectUser, 
  createTaskChannel 
} from '@/integrations/getstream/client';
import { StreamChat } from 'stream-chat';

// Configuration
const BATCH_SIZE = 50;
const ADMIN_USER_ID = 'system-migration';
const ADMIN_USER_NAME = 'System Migration';

// Main migration function
async function migrateToGetStream() {
  if (process.env.NODE_ENV === 'development') {
    console.log('Starting migration to GetStream...');
  
    }
  try {
    // Initialize the GetStream client
    const client = getStreamClient();
    
    // Connect as the system migration user
    await connectUser(ADMIN_USER_ID, ADMIN_USER_NAME, ADMIN_USER_ID);
    if (process.env.NODE_ENV === 'development') {
      console.log('Connected to GetStream as system migration user');
    
      }
    // Get all tasks that haven't been migrated to GetStream yet
    const { data: tasks, error } = await supabase
      .from('tasks')
      .select('id, title, user_id, assigned_to, chat_migrated_to_stream, getstream_channel_id')
      .is('chat_migrated_to_stream', null)
      .order('created_at', { ascending: false })
      .limit(BATCH_SIZE);
      
    if (error) {
      throw new Error(`Error fetching tasks: ${error.message}`);
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${tasks?.length || 0} tasks to migrate`);
  }
    // Process each task
    for (const task of tasks || []) {
      try {
        if (process.env.NODE_ENV === 'development') {
          console.log(`Processing task ${task.id}: ${task.title}`);
        
          }
        // Determine channel members
        const members = [task.user_id];
        if (task.assigned_to && !members.includes(task.assigned_to)) {
          members.push(task.assigned_to);
        }
        
        // Create a channel ID
        const channelId = `task-${task.id}`;
        
        // Create the channel
        const channel = client.channel('messaging', channelId, {
          name: task.title,
          members,
          task_id: task.id,
          created_by_id: ADMIN_USER_ID,
        });
        
        await channel.create();
        if (process.env.NODE_ENV === 'development') {
          console.log(`Created GetStream channel ${channelId} for task ${task.id}`);
        
          }
        // Add a system message
        await channel.sendMessage({
          text: 'This chat has been migrated to GetStream.',
          user_id: ADMIN_USER_ID,
          type: 'system',
        });
        
        // Update the task record
        const { error: updateError } = await supabase
          .from('tasks')
          .update({
            chat_migrated_to_stream: true,
            getstream_channel_id: channelId,
            updated_at: new Date().toISOString(),
          })
          .eq('id', task.id);
          
        if (updateError) {
          throw new Error(`Error updating task: ${updateError.message}`);
        }
        
        if (process.env.NODE_ENV === 'development') {
    console.log(`Successfully migrated task ${task.id} to GetStream`);
  }
        // Migrate existing messages if any
        await migrateExistingMessages(task.id, channelId, client);
        
      } catch (taskError) {
        console.error(`Error migrating task ${task.id}:`, taskError);
      }
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('Migration completed successfully');
  }
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    // Disconnect from GetStream
    try {
      const client = getStreamClient();
      await client.disconnectUser();
      if (process.env.NODE_ENV === 'development') {
        console.log('Disconnected from GetStream');
        }
    } catch (error) {
      console.error('Error disconnecting from GetStream:', error);
    }
  }
}

// Helper function to migrate existing messages
async function migrateExistingMessages(taskId: string, channelId: string, client: StreamChat) {
  try {
    // Get the channel
    const channel = client.channel('messaging', channelId);
    
    // Get all messages for this task
    const { data: messages, error } = await supabase
      .from('task_messages')
      .select('id, sender_id, content, created_at')
      .eq('task_id', taskId)
      .order('created_at', { ascending: true });
      
    if (error) {
      throw new Error(`Error fetching messages: ${error.message}`);
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${messages?.length || 0} messages to migrate for task ${taskId}`);
  }
    // Process each message
    for (const message of messages || []) {
      try {
        // Get sender profile
        const { data: sender } = await supabase
          .from('profiles')
          .select('first_name, last_name, email')
          .eq('id', message.sender_id)
          .maybeSingle();
          
        // Determine sender name
        const senderName = sender 
          ? `${sender.first_name || ''} ${sender.last_name || ''}`.trim() || sender.email?.split('@')[0] || 'User'
          : 'User';
        
        // Send message to GetStream
        const response = await channel.sendMessage({
          text: message.content,
          user: {
            id: message.sender_id,
            name: senderName,
          },
          created_at: message.created_at,
        });
        
        // Update the message record with the Stream message ID
        if (response.message?.id) {
          await supabase
            .from('task_messages')
            .update({
              stream_message_id: response.message.id,
            })
            .eq('id', message.id);
        }
        
      } catch (messageError) {
        console.error(`Error migrating message ${message.id}:`, messageError);
      }
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log(`Successfully migrated ${messages?.length || 0} messages for task ${taskId}`);
  }
  } catch (error) {
    console.error(`Error migrating messages for task ${taskId}:`, error);
  }
}

// Run the migration
migrateToGetStream();
