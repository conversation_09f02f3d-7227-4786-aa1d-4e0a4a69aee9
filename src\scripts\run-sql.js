// <PERSON>ript to run SQL commands
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runSQL(filePath) {
  try {
    // Read the SQL file
    const sqlPath = path.resolve(__dirname, '../../', filePath);
    if (process.env.NODE_ENV === 'development') {
    console.log(`Reading SQL file: ${sqlPath}`);
  }
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = sql.split(';').filter(stmt => stmt.trim() !== '');
    
    if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${statements.length} SQL statements`);
  }
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i].trim() + ';';
      if (process.env.NODE_ENV === 'development') {
    console.log(`\nExecuting statement ${i + 1}/${statements.length}:`);
  }
      if (process.env.NODE_ENV === 'development') {
    console.log(stmt);
  }
      try {
        const { data, error } = await supabase.rpc('exec_sql', { sql: stmt });
        
        if (error) {
          console.error(`Error executing statement ${i + 1}:`, error);
          
          // Try a different approach for this statement
          if (process.env.NODE_ENV === 'development') {
    console.log('Trying a different approach...');
  }
          if (stmt.toLowerCase().startsWith('create table') || 
              stmt.toLowerCase().startsWith('alter table') ||
              stmt.toLowerCase().startsWith('create or replace function')) {
            // For DDL statements, try using the REST API
            const { error: restError } = await supabase.from('_exec_sql').select('*').eq('sql', stmt);
            
            if (restError) {
              console.error('REST API approach also failed:', restError);
            } else {
              if (process.env.NODE_ENV === 'development') {
    console.log('Statement executed successfully via REST API');
  }
            }
          } else if (stmt.toLowerCase().startsWith('insert into') || 
                     stmt.toLowerCase().startsWith('update')) {
            // For DML statements, try using the appropriate API
            if (stmt.toLowerCase().includes('organizations')) {
              if (process.env.NODE_ENV === 'development') {
    console.log('Trying to insert/update organization via API...');
  }
              if (stmt.toLowerCase().startsWith('insert into')) {
                const { data, error } = await supabase
                  .from('organizations')
                  .insert({ name: 'Test Organization' })
                  .select();
                
                if (error) {
                  console.error('API insert failed:', error);
                } else {
                  if (process.env.NODE_ENV === 'development') {
    console.log('Organization inserted successfully: completed');
  }
                }
              } else if (stmt.toLowerCase().startsWith('update')) {
                // Handle update statements
                if (process.env.NODE_ENV === 'development') {
    console.log('Update statements not implemented via API');
  }
              }
            } else if (stmt.toLowerCase().includes('profiles')) {
              if (process.env.NODE_ENV === 'development') {
    console.log('Trying to update profile via API...');
  }
              // Get the organization ID
              const { data: orgs, error: orgError } = await supabase
                .from('organizations')
                .select('id')
                .eq('name', 'Test Organization')
                .limit(1);
              
              if (orgError || !orgs || orgs.length === 0) {
                console.error('Could not find organization:', orgError);
              } else {
                const orgId = orgs[0].id;
                
                // Update the profile
                const { data, error } = await supabase
                  .from('profiles')
                  .update({ 
                    organization_id: orgId,
                    role: 'teacher'
                  })
                  .eq('id', 'e0eb9971-8690-4b51-b0d6-04805f2955ab')
                  .select();
                
                if (error) {
                  console.error('API update failed:', error);
                } else {
                  if (process.env.NODE_ENV === 'development') {
    console.log('Profile updated successfully: completed');
  }
                }
              }
            }
          }
        } else {
          if (process.env.NODE_ENV === 'development') {
    console.log('Statement executed successfully');
  }
        }
      } catch (stmtError) {
        console.error(`Exception executing statement ${i + 1}:`, stmtError);
      }
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('\nSQL execution complete');
  }
  } catch (error) {
    console.error('Error running SQL:', error);
  }
}

// Check if file path was provided as a command line argument
const filePath = process.argv[2];
if (!filePath) {
  console.error('Please provide a SQL file path as a command line argument');
  if (process.env.NODE_ENV === 'development') {
    console.log('Example: node run-sql.js sql/setup_database.sql');
  }
  process.exit(1);
}

runSQL(filePath);
