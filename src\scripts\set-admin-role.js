// This script sets the current user's role to admin
import { supabase } from '../integrations/supabase/client';

async function setAdminRole() {
  try {
    // Get current user
    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError) {
      console.error('Error getting user:', userError);
      return;
    }
    
    if (!userData.user) {
      console.error('No user logged in');
      return;
    }
    
    // Update user metadata to set role to admin
    const { error: updateError } = await supabase.auth.updateUser({
      data: {
        role: 'admin'
      }
    });
    
    if (updateError) {
      console.error('Error updating user role:', updateError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log('User role set to admin successfully');
    

    
      }
    // Refresh session to get updated metadata
    const { data: sessionData, error: sessionError } = await supabase.auth.refreshSession();
    if (sessionError) {
      console.error('Error refreshing session:', sessionError);
    } else {
      if (process.env.NODE_ENV === 'development') {

        console.log('Session refreshed, new metadata: completed');

        }
    }
  } catch (err) {
    console.error('Error:', err);
  }
}

setAdminRole();
