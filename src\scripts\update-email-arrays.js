// Script to update profile emails as arrays
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateEmailArrays() {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log('Starting to update email arrays in profiles table...');
  }
    // Get all users from auth.users
    const { data: authData, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('Error fetching auth users:', authError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`Found ${authData.users.length} total auth users`);
    

    
      }
    // Create a map of user IDs to emails
    const userEmailMap = {};
    authData.users.forEach(user => {
      userEmailMap[user.id] = user.email;
    });
    
    // Get all profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*');
    
    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${profiles.length} total profiles`);
  }
    // Update each profile with the corresponding email as an array
    let updatedCount = 0;
    let errorCount = 0;
    
    for (const profile of profiles) {
      const authEmail = userEmailMap[profile.id];
      
      if (authEmail) {
        if (process.env.NODE_ENV === 'development') {
    console.log(`Updating profile ${profile.id} with email array [${authEmail}]`);
  }
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ email: [authEmail] })
          .eq('id', profile.id);
        
        if (updateError) {
          console.error(`Error updating profile ${profile.id}:`, updateError);
          errorCount++;
        } else {
          if (process.env.NODE_ENV === 'development') {
    console.log(`Successfully updated profile ${profile.id}`);
  }
          updatedCount++;
        }
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log(`No email found for profile ${profile.id}`);
  }
      }
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log(`\nSummary:`);
  }
    if (process.env.NODE_ENV === 'development') {
    console.log(`Updated ${updatedCount} profiles with email arrays`);
  }
    if (process.env.NODE_ENV === 'development') {
    console.log(`Encountered ${errorCount} errors`);
  }
    // Final verification
    const { data: updatedProfiles, error: verificationError } = await supabase
      .from('profiles')
      .select('id, email, organization_id, role');
    
    if (verificationError) {
      console.error('Error fetching updated profiles:', verificationError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('\nVerification of updated profiles:');
  }
      updatedProfiles.forEach(profile => {
        const emailDisplay = profile.email ? `[${profile.email.join(', ')}]` : 'NULL';
        if (process.env.NODE_ENV === 'development') {
    console.log(`ID: ${profile.id}, Email: ${emailDisplay}, Organization: ${profile.organization_id}, Role: ${profile.role}`);
  }
      });
      
      // Count profiles with and without emails
      const withEmail = updatedProfiles.filter(p => p.email && p.email.length > 0).length;
      const withoutEmail = updatedProfiles.filter(p => !p.email || p.email.length === 0).length;
      if (process.env.NODE_ENV === 'development') {
    console.log(`\nProfiles with email: ${withEmail}`);
  }
      if (process.env.NODE_ENV === 'development') {
    console.log(`Profiles without email: ${withoutEmail}`);
  }
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('\nEmail array update completed.');
  }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

updateEmailArrays();
