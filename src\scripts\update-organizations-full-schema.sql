-- SQL migration script to update the organizations table schema
-- This script ensures all fields from the organization form are included in the database

-- Add all required columns if they don't exist
ALTER TABLE public.organizations 
ADD COLUMN IF NOT EXISTS name TEXT NOT NULL DEFAULT 'Unnamed Organization',
ADD COLUMN IF NOT EXISTS organization_type VARCHAR(50) NOT NULL DEFAULT 'school',
ADD COLUMN IF NOT EXISTS parent_organization_id UUID REFERENCES organizations(id),
ADD COLUMN IF NOT EXISTS address TEXT,
ADD COLUMN IF NOT EXISTS city TEXT,
ADD COLUMN IF NOT EXISTS state TEXT,
ADD COLUMN IF NOT EXISTS zip TEXT,
ADD COLUMN IF NOT EXISTS phone TEXT,
ADD COLUMN IF NOT EXISTS website TEXT,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT now();

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_organizations_type ON organizations(organization_type);
CREATE INDEX IF NOT EXISTS idx_organizations_parent ON organizations(parent_organization_id);
CREATE INDEX IF NOT EXISTS idx_organizations_name ON organizations(name);

-- Add constraint to ensure a trust cannot have a parent (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'check_trust_no_parent'
    ) THEN
        ALTER TABLE organizations 
        ADD CONSTRAINT check_trust_no_parent 
        CHECK (NOT (organization_type = 'trust' AND parent_organization_id IS NOT NULL));
    END IF;
END
$$;

-- Update RLS policies to ensure proper access control
-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Organizations are viewable by authenticated users" ON organizations;
DROP POLICY IF EXISTS "Organizations are editable by admins" ON organizations;

-- Create new policies that account for hierarchical structure
CREATE POLICY "Organizations are viewable by authenticated users" 
ON organizations FOR SELECT 
TO authenticated
USING (
  -- Users can view their own organization
  id IN (
    SELECT organization_id FROM profiles WHERE id = auth.uid()
  )
  -- Trust admins can view all schools in their trust
  OR (
    -- User is a trust admin
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
      AND organization_id IN (
        SELECT id FROM organizations WHERE organization_type = 'trust'
      )
    )
    -- And this is a school in their trust
    AND (
      parent_organization_id IN (
        SELECT organization_id FROM profiles 
        WHERE id = auth.uid()
      )
    )
  )
);

CREATE POLICY "Organizations are editable by admins" 
ON organizations FOR ALL 
TO authenticated
USING (
  -- Organization admins can edit their own organization
  (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
      AND organization_id = organizations.id
    )
  )
  -- Trust admins can edit schools in their trust
  OR (
    -- User is a trust admin
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
      AND organization_id IN (
        SELECT id FROM organizations WHERE organization_type = 'trust'
      )
    )
    -- And this is a school in their trust
    AND (
      parent_organization_id IN (
        SELECT organization_id FROM profiles 
        WHERE id = auth.uid()
      )
    )
  )
);

-- Make sure profiles table has organization_id column and role column
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES public.organizations(id),
ADD COLUMN IF NOT EXISTS role TEXT;

-- Create index on profiles for organization_id for faster queries
CREATE INDEX IF NOT EXISTS idx_profiles_organization_id ON profiles(organization_id);
