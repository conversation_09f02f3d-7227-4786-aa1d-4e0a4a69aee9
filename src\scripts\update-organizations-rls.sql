-- Update RLS policies for organizations table to account for hierarchical structure

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Organizations are viewable by authenticated users" ON organizations;
DROP POLICY IF EXISTS "Organizations are editable by admins" ON organizations;

-- Create new policies that account for hierarchical structure
CREATE POLICY "Organizations are viewable by authenticated users" 
ON organizations FOR SELECT 
TO authenticated
USING (
  -- Users can view their own organization
  id IN (
    SELECT organization_id FROM profiles WHERE id = auth.uid()
  )
  -- Trust admins can view all schools in their trust
  OR (
    -- User is a trust admin
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
      AND organization_id IN (
        SELECT id FROM organizations WHERE organization_type = 'trust'
      )
    )
    -- And this is a school in their trust
    AND (
      parent_organization_id IN (
        SELECT organization_id FROM profiles 
        WHERE id = auth.uid()
      )
    )
  )
);

CREATE POLICY "Organizations are editable by admins" 
ON organizations FOR ALL 
TO authenticated
USING (
  -- Organization admins can edit their own organization
  (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
      AND organization_id = organizations.id
    )
  )
  -- Trust admins can edit schools in their trust
  OR (
    -- User is a trust admin
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
      AND organization_id IN (
        SELECT id FROM organizations WHERE organization_type = 'trust'
      )
    )
    -- And this is a school in their trust
    AND (
      parent_organization_id IN (
        SELECT organization_id FROM profiles 
        WHERE id = auth.uid()
      )
    )
  )
);
