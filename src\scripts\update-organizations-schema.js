// Script to update the organizations table schema
import { supabase } from '../integrations/supabase/client';
import { supabaseAdmin } from '../services/supabaseAdmin';
import fs from 'fs';
import path from 'path';

async function updateOrganizationsSchema() {
  try {
    console.log('Updating organizations table schema...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'update-organizations-full-schema.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL using supabaseAdmin to bypass RLS
    const { error } = await supabaseAdmin.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('Error executing SQL:', error);
      
      // Try executing the SQL statements one by one
      console.log('Trying to execute SQL statements one by one...');
      
      const statements = sql.split(';').filter(stmt => stmt.trim() !== '');
      
      for (let i = 0; i < statements.length; i++) {
        const stmt = statements[i];
        console.log(`Executing statement ${i + 1}/${statements.length}...`);
        
        const { error: stmtError } = await supabaseAdmin.rpc('exec_sql', { sql: stmt + ';' });
        
        if (stmtError) {
          console.error(`Error executing statement ${i + 1}:`, stmtError);
          console.log('Statement:', stmt);
        } else {
          console.log(`Statement ${i + 1} executed successfully.`);
        }
      }
    } else {
      console.log('SQL executed successfully.');
    }
    
    // Verify the schema
    console.log('\nVerifying organizations table schema...');
    
    const { data: columns, error: columnsError } = await supabaseAdmin
      .rpc('get_table_columns', { table_name: 'organizations' });
    
    if (columnsError) {
      console.error('Error getting table columns:', columnsError);
    } else {
      console.log('Organizations table columns:');
      columns.forEach(col => {
        console.log(`- ${col.column_name} (${col.data_type}${col.is_nullable === 'NO' ? ', NOT NULL' : ''})`);
      });
    }
    
    console.log('\nSchema update completed.');
  } catch (error) {
    console.error('Error updating schema:', error);
  }
}

// Run the function
updateOrganizationsSchema();
