-- Add organization_type and parent_organization_id columns
ALTER TABLE organizations 
ADD COLUMN IF NOT EXISTS organization_type VARCHAR(50) NOT NULL DEFAULT 'school',
ADD COLUMN IF NOT EXISTS parent_organization_id UUID REFERENCES organizations(id);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_organizations_type ON organizations(organization_type);
CREATE INDEX IF NOT EXISTS idx_organizations_parent ON organizations(parent_organization_id);

-- Add constraint to ensure a trust cannot have a parent (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'check_trust_no_parent'
    ) THEN
        ALTER TABLE organizations 
        ADD CONSTRAINT check_trust_no_parent 
        CHECK (NOT (organization_type = 'trust' AND parent_organization_id IS NOT NULL));
    END IF;
END
$$;
