import { supabase } from '../integrations/supabase/client';
import fs from 'fs';
import path from 'path';

async function updateSupplierOrganizationSchema() {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('Updating supplier organization schema...');

      }
    // Read the SQL file
    const sqlPath = path.resolve('sql/update_supplier_organization_schema.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');

    // Execute the SQL using the exec_sql RPC function
    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error('Error executing SQL:', error);
      return;
    }

    if (process.env.NODE_ENV === 'development') {

      console.log('Successfully updated supplier organization schema');


      }
    // Verify the function was created
    const { data, error: verifyError } = await supabase.rpc('exec_sql', {
      sql: "SELECT proname FROM pg_proc WHERE proname = 'create_supplier_organization';"
    });

    if (verifyError) {
      console.error('Error verifying function creation:', verifyError);
      return;
    }

    if (data && data.length > 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Verified create_supplier_organization function exists');
        }
    } else {
      console.error('Function create_supplier_organization was not created');
    }

  } catch (err) {
    console.error('Error:', err);
  }
}

updateSupplierOrganizationSchema();
