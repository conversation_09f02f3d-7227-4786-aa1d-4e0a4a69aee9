// <PERSON>ript to update the task schema for the new workflow
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateTaskSchema() {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log('Adding new columns to tasks table...');
  }
    // Add assigned_to column
    const { error: assignedToError } = await supabase.rpc('alter_table_add_column', {
      table_name: 'tasks',
      column_name: 'assigned_to',
      column_type: 'uuid references auth.users(id)'
    });
    
    if (assignedToError) {
      console.error('Error adding assigned_to column:', assignedToError);
      // Continue anyway, column might already exist
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Added assigned_to column successfully');
  }
    }
    
    // Add visibility column
    const { error: visibilityError } = await supabase.rpc('alter_table_add_column', {
      table_name: 'tasks',
      column_name: 'visibility',
      column_type: 'text not null default \'admin\''
    });
    
    if (visibilityError) {
      console.error('Error adding visibility column:', visibilityError);
      // Continue anyway, column might already exist
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Added visibility column successfully');
  }
    }
    
    // Update existing tasks to have public visibility (for backward compatibility)
    const { error: updateError } = await supabase
      .from('tasks')
      .update({ visibility: 'public' })
      .eq('visibility', 'admin');
    
    if (updateError) {
      console.error('Error updating existing tasks:', updateError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Updated existing tasks to have public visibility');
  }
    }
    
    // Drop existing policies
    if (process.env.NODE_ENV === 'development') {
    console.log('Dropping existing policies...');
  }
    const { error: dropPolicyError } = await supabase.rpc('drop_policy_if_exists', {
      policy_name: 'Organization members can view their organization\'s tasks',
      table_name: 'tasks'
    });
    
    if (dropPolicyError) {
      console.error('Error dropping existing policy:', dropPolicyError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Dropped existing policy successfully');
  }
    }
    
    // Create new policies
    if (process.env.NODE_ENV === 'development') {
    console.log('Creating new policies...');
  }
    // Admin view policy
    const { error: adminViewError } = await supabase.rpc('create_policy', {
      policy_name: 'Admins can view all organization tasks',
      table_name: 'tasks',
      operation: 'SELECT',
      using_expression: `
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'admin'
          AND organization_id = (
            SELECT organization_id FROM public.profiles 
            WHERE id = (SELECT user_id FROM public.tasks WHERE id = public.tasks.id)
          )
        )
      `
    });
    
    if (adminViewError) {
      console.error('Error creating admin view policy:', adminViewError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Created admin view policy successfully');
  }
    }
    
    // Teacher view policy
    const { error: teacherViewError } = await supabase.rpc('create_policy', {
      policy_name: 'Teachers can view their own tasks',
      table_name: 'tasks',
      operation: 'SELECT',
      using_expression: 'user_id = auth.uid()'
    });
    
    if (teacherViewError) {
      console.error('Error creating teacher view policy:', teacherViewError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Created teacher view policy successfully');
  }
    }
    
    // Maintenance/support view policy
    const { error: maintenanceViewError } = await supabase.rpc('create_policy', {
      policy_name: 'Maintenance and support can view assigned tasks',
      table_name: 'tasks',
      operation: 'SELECT',
      using_expression: '(visibility = \'internal\' OR visibility = \'public\') AND assigned_to = auth.uid()'
    });
    
    if (maintenanceViewError) {
      console.error('Error creating maintenance view policy:', maintenanceViewError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Created maintenance view policy successfully');
  }
    }
    
    // Supplier view policy
    const { error: supplierViewError } = await supabase.rpc('create_policy', {
      policy_name: 'Suppliers can view public tasks',
      table_name: 'tasks',
      operation: 'SELECT',
      using_expression: `
        visibility = 'public'
        AND EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND account_type = 'supplier'
        )
      `
    });
    
    if (supplierViewError) {
      console.error('Error creating supplier view policy:', supplierViewError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Created supplier view policy successfully');
  }
    }
    
    // Task creation policy
    const { error: createPolicyError } = await supabase.rpc('create_policy', {
      policy_name: 'Teachers and admins can create tasks',
      table_name: 'tasks',
      operation: 'INSERT',
      check_expression: `
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND (role = 'teacher' OR role = 'admin')
        )
      `
    });
    
    if (createPolicyError) {
      console.error('Error creating task creation policy:', createPolicyError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Created task creation policy successfully');
  }
    }
    
    // Admin update policy
    const { error: adminUpdateError } = await supabase.rpc('create_policy', {
      policy_name: 'Admins can update any task',
      table_name: 'tasks',
      operation: 'UPDATE',
      using_expression: `
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'admin'
          AND organization_id = (
            SELECT organization_id FROM public.profiles 
            WHERE id = (SELECT user_id FROM public.tasks WHERE id = public.tasks.id)
          )
        )
      `
    });
    
    if (adminUpdateError) {
      console.error('Error creating admin update policy:', adminUpdateError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Created admin update policy successfully');
  }
    }
    
    // Owner update policy
    const { error: ownerUpdateError } = await supabase.rpc('create_policy', {
      policy_name: 'Task owners can update their own tasks',
      table_name: 'tasks',
      operation: 'UPDATE',
      using_expression: 'user_id = auth.uid()'
    });
    
    if (ownerUpdateError) {
      console.error('Error creating owner update policy:', ownerUpdateError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Created owner update policy successfully');
  }
    }
    
    // Admin delete policy
    const { error: adminDeleteError } = await supabase.rpc('create_policy', {
      policy_name: 'Admins can delete any task',
      table_name: 'tasks',
      operation: 'DELETE',
      using_expression: `
        EXISTS (
          SELECT 1 FROM public.profiles 
          WHERE id = auth.uid() 
          AND role = 'admin'
          AND organization_id = (
            SELECT organization_id FROM public.profiles 
            WHERE id = (SELECT user_id FROM public.tasks WHERE id = public.tasks.id)
          )
        )
      `
    });
    
    if (adminDeleteError) {
      console.error('Error creating admin delete policy:', adminDeleteError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Created admin delete policy successfully');
  }
    }
    
    // Owner delete policy
    const { error: ownerDeleteError } = await supabase.rpc('create_policy', {
      policy_name: 'Task owners can delete their own tasks',
      table_name: 'tasks',
      operation: 'DELETE',
      using_expression: 'user_id = auth.uid()'
    });
    
    if (ownerDeleteError) {
      console.error('Error creating owner delete policy:', ownerDeleteError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Created owner delete policy successfully');
  }
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('Task schema update completed successfully!');
  }
  } catch (error) {
    console.error('Error updating task schema:', error);
  }
}

// Run the update function
updateTaskSchema();
