// <PERSON><PERSON>t to update the tasks table schema using direct SQL
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateTasksTable() {
  try {
    console.log('Updating tasks table...');
    
    // Add assigned_to column
    console.log('Adding assigned_to column...');
    const { error: assignedToError } = await supabase.rpc('execute_sql', {
      sql: `ALTER TABLE public.tasks ADD COLUMN IF NOT EXISTS assigned_to UUID REFERENCES auth.users(id);`
    });
    
    if (assignedToError) {
      console.error('Error adding assigned_to column:', assignedToError);
    } else {
      console.log('Successfully added assigned_to column');
    }
    
    // Add visibility column
    console.log('Adding visibility column...');
    const { error: visibilityError } = await supabase.rpc('execute_sql', {
      sql: `ALTER TABLE public.tasks ADD COLUMN IF NOT EXISTS visibility TEXT NOT NULL DEFAULT 'public';`
    });
    
    if (visibilityError) {
      console.error('Error adding visibility column:', visibilityError);
    } else {
      console.log('Successfully added visibility column');
    }
    
    // Update existing tasks to have public visibility
    console.log('Updating existing tasks...');
    const { error: updateError } = await supabase.rpc('execute_sql', {
      sql: `UPDATE public.tasks SET visibility = 'public' WHERE visibility IS NULL;`
    });
    
    if (updateError) {
      console.error('Error updating existing tasks:', updateError);
    } else {
      console.log('Successfully updated existing tasks');
    }
    
    console.log('Tasks table update completed');
    
  } catch (error) {
    console.error('Error updating tasks table:', error);
  }
}

updateTasksTable();
