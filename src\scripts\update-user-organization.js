// <PERSON>ript to manually update a user's organization
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import readline from 'readline';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt user for confirmation
function confirm(message) {
  return new Promise((resolve) => {
    rl.question(`${message} (y/n): `, (answer) => {
      resolve(answer.toLowerCase() === 'y');
    });
  });
}

async function updateUserOrganization(email, organizationId, role = 'teacher') {
  try {
    if (process.env.NODE_ENV === 'development') {

      console.log(`Updating organization for user: ${email}`);

      }
    if (process.env.NODE_ENV === 'development') {
    console.log(`Organization ID: ${organizationId}`);
  }
    if (process.env.NODE_ENV === 'development') {
    console.log(`Role: ${role}`);
  }
    // Get user from auth.users
    const { data: userData, error: userError } = await supabase.auth.admin.listUsers();

    if (userError) {
      console.error('Error fetching users:', userError);
      return false;
    }

    // Find the user with the specified email
    const user = userData.users.find(u => u.email === email);

    if (!user) {
      if (process.env.NODE_ENV === 'development') {

        console.log(`User with email ${email} not found in auth.users`);

        }
      return false;
    }

    if (process.env.NODE_ENV === 'development') {


      console.log(`Found user: ${user.id} (${user.email}.replace(/user.*/, 'hasUser: ' + !!user))`);



      }
    // Check if the organization exists
    const { data: orgData, error: orgError } = await supabase
      .from('organizations')
      .select('*')
      .eq('id', organizationId)
      .single();

    if (orgError) {
      console.error('Error fetching organization:', orgError);

      // If the organization doesn't exist, ask if we should create it
      if (orgError.code === 'PGRST116') {
        const createOrg = await confirm(`Organization with ID ${organizationId} does not exist. Would you like to create it?`);

        if (createOrg) {
          const orgName = await new Promise((resolve) => {
            rl.question('Enter organization name: ', (answer) => {
              resolve(answer);
            });
          });

          // Create the organization
          const { data: newOrg, error: createError } = await supabase
            .from('organizations')
            .insert({
              id: organizationId,
              name: orgName
            })
            .select()
            .single();

          if (createError) {
            console.error('Error creating organization:', createError);
            return false;
          }

          if (process.env.NODE_ENV === 'development') {


            console.log(`Created organization: ${newOrg.id} (${newOrg.name})`);


            }
        } else {
          return false;
        }
      } else {
        return false;
      }
    } else {
      if (process.env.NODE_ENV === 'development') {

        console.log(`Found organization: ${orgData.id} (${orgData.name})`);

        }
    }

    // Update the user's profile
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .update({
        organization_id: organizationId,
        role: role
      })
      .eq('id', user.id)
      .select()
      .single();

    if (profileError) {
      console.error('Error updating profile:', profileError);
      return false;
    }

    if (process.env.NODE_ENV === 'development') {


      console.log(`Successfully updated profile for user ${user.email}`);


      }
    if (process.env.NODE_ENV === 'development') {
    console.log('Updated profile: completed');
  }
    // Also update the user metadata for backward compatibility
    const { error: updateError } = await supabase.auth.admin.updateUserById(
      user.id,
      {
        user_metadata: {
          ...user.user_metadata,
          role: role,
          organization: {
            id: organizationId,
            name: orgData?.name || 'Organization',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        }
      }
    );

    if (updateError) {
      console.error('Error updating user metadata:', updateError);
      if (process.env.NODE_ENV === 'development') {
    console.log('Profile was updated successfully, but metadata update failed');
  }
      return true; // Still return true because the profile was updated
    }

    if (process.env.NODE_ENV === 'development') {


      console.log(`Successfully updated metadata for user ${user.email}`);


      }
    return true;
  } catch (error) {
    console.error('Error updating user organization:', error);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Get email from command line arguments or prompt
    let email = process.argv[2];
    if (!email) {
      email = await new Promise((resolve) => {
        rl.question('Enter user email: ', (answer) => {
          resolve(answer);
        });
      });
    }

    // Get organization ID
    const organizationId = await new Promise((resolve) => {
      rl.question('Enter organization ID (or "new" to create a new one): ', (answer) => {
        resolve(answer);
      });
    });

    let finalOrgId = organizationId;

    // Create a new organization if requested
    if (organizationId.toLowerCase() === 'new') {
      const orgName = await new Promise((resolve) => {
        rl.question('Enter organization name: ', (answer) => {
          resolve(answer);
        });
      });

      // Create the organization with a specific UUID
      const newOrgId = crypto.randomUUID();
      if (process.env.NODE_ENV === 'development') {
    console.log(`Generated new organization ID: ${newOrgId}`);
  }
      const { data: newOrg, error: createError } = await supabase
        .from('organizations')
        .insert({
          id: newOrgId,
          name: orgName
        })
        .select()
        .single();

      if (createError) {
        console.error('Error creating organization:', createError);
        rl.close();
        return;
      }

      if (process.env.NODE_ENV === 'development') {


        console.log(`Created organization: ${newOrg.id} (${newOrg.name})`);


        }
      finalOrgId = newOrg.id;
    }

    // Get role
    const role = await new Promise((resolve) => {
      rl.question('Enter user role (default: teacher): ', (answer) => {
        resolve(answer || 'teacher');
      });
    });

    // Confirm the update
    const confirmed = await confirm(`Are you sure you want to update the organization for ${email} to ${finalOrgId} with role ${role}?`);

    if (confirmed) {
      const success = await updateUserOrganization(email, finalOrgId, role);

      if (success) {
        if (process.env.NODE_ENV === 'development') {

          console.log('User organization updated successfully');

          }
      } else {
        if (process.env.NODE_ENV === 'development') {

          console.log('Failed to update user organization');

          }
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Operation cancelled');
  }
    }
  } catch (error) {
    console.error('Error in main function:', error);
  } finally {
    rl.close();
  }
}

// Run the main function
main();
