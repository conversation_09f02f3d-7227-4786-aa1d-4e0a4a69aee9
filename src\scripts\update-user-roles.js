// <PERSON>ript to update user roles in the profiles table
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Organization ID to update
const organizationId = 'b8db712b-7f48-44be-9269-be2cbbb2d213';

// User roles to set
const userRoles = [
  {
    email: '<EMAIL>',
    role: 'maintenance'
  },
  {
    email: '<EMAIL>',
    role: 'support'
  },
  {
    email: '<EMAIL>',
    role: 'teacher'
  },
  {
    email: '<EMAIL>',
    role: 'admin'
  }
];

async function updateUserRoles() {
  try {
    console.log('Starting to update user roles...');
    
    // Get all profiles for the organization
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .eq('organization_id', organizationId);
    
    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      return;
    }
    
    console.log(`Found ${profiles.length} profiles for organization ${organizationId}`);
    
    // Get all users to match with profiles
    const { data: authData, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('Error fetching auth users:', authError);
      return;
    }
    
    console.log(`Found ${authData.users.length} total auth users`);
    
    // Create a map of user IDs to emails
    const userMap = {};
    authData.users.forEach(user => {
      userMap[user.id] = user.email;
    });
    
    // Create a map of emails to user IDs
    const emailMap = {};
    authData.users.forEach(user => {
      emailMap[user.email] = user.id;
    });
    
    // Update roles for each user
    let updatedCount = 0;
    
    for (const userRole of userRoles) {
      const userId = emailMap[userRole.email];
      
      if (!userId) {
        console.log(`No user found with email ${userRole.email}`);
        continue;
      }
      
      // Find the profile for this user
      const profile = profiles.find(p => p.id === userId);
      
      if (!profile) {
        console.log(`No profile found for user ${userRole.email} (${userId})`);
        
        // Create a profile for this user
        console.log(`Creating profile for user ${userRole.email} with role ${userRole.role}`);
        
        const { error: insertError } = await supabase
          .from('profiles')
          .insert([{
            id: userId,
            email: [userRole.email],
            role: userRole.role,
            organization_id: organizationId
          }]);
        
        if (insertError) {
          console.error(`Error creating profile for ${userRole.email}:`, insertError);
        } else {
          console.log(`Successfully created profile for ${userRole.email} with role ${userRole.role}`);
          updatedCount++;
        }
      } else {
        // Update the existing profile
        console.log(`Updating role for ${userRole.email} to ${userRole.role}`);
        
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ role: userRole.role })
          .eq('id', userId);
        
        if (updateError) {
          console.error(`Error updating role for ${userRole.email}:`, updateError);
        } else {
          console.log(`Successfully updated role for ${userRole.email} to ${userRole.role}`);
          updatedCount++;
        }
      }
    }
    
    console.log(`\nSummary: Updated ${updatedCount} user roles`);
    
    // Verify the updates
    const { data: updatedProfiles, error: verificationError } = await supabase
      .from('profiles')
      .select('id, email, role, organization_id')
      .eq('organization_id', organizationId);
    
    if (verificationError) {
      console.error('Error fetching updated profiles:', verificationError);
    } else {
      console.log('\nVerification of updated profiles:');
      
      for (const profile of updatedProfiles) {
        const email = userMap[profile.id] || (profile.email && profile.email[0]) || 'unknown';
        console.log(`ID: ${profile.id}, Email: ${email}, Role: ${profile.role}`);
      }
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

updateUserRoles();
