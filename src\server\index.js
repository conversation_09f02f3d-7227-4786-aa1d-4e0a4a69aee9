// Simple Express server for handling admin API requests
import express from 'express';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import cors from 'cors';
import bodyParser from 'body-parser';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import nodemailer from 'nodemailer';

// Stripe Connect router is handled by the Stripe Connect server

// Handle Windows paths correctly in ESM
let __filename;
let __dirname;

try {
  __filename = fileURLToPath(import.meta.url);
  __dirname = dirname(__filename);
} catch (e) {
  // Fallback for Windows paths
  __dirname = process.cwd();
}

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.ADMIN_API_PORT || 3002;

// Middleware
app.use(cors({
  origin: ['http://localhost:8080', 'http://localhost:8081', 'http://localhost:8082', 'http://127.0.0.1:8082'],
  credentials: true
}));
app.use(bodyParser.json());

// Root route for health check
app.get('/', (req, res) => {
  res.json({ status: 'Admin API server is running' });
});

// Stripe Connect router is handled by the Stripe Connect server

// Initialize Supabase with service role key for admin access
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co',
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Admin users API endpoint - GET method for testing
app.get('/api/admin-users', (req, res) => {
  if (process.env.NODE_ENV === 'development') {

    console.log('GET request to admin-users endpoint'.replace(/user.*/, 'hasUser: ' + !!user));

    }
  res.json({ status: 'Admin API is working', message: 'Use POST for actual operations' });
});

// Email test endpoint
app.post('/api/test-email', async (req, res) => {
  if (process.env.NODE_ENV === 'development') {

    console.log('Test email API route called', req.body);


    }
  try {
    const { config, testEmail } = req.body;

    if (!config || !testEmail) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters'
      });
    }

    // Validate email configuration
    if (config.provider === 'smtp') {
      if (!config.smtpHost || !config.smtpPort || !config.smtpUsername || !config.smtpPassword) {
        return res.status(400).json({
          success: false,
          message: 'Missing SMTP configuration. Please provide host, port, username, and password.'
        });
      }
    } else {
      return res.status(400).json({
        success: false,
        message: `Unsupported email provider: ${config.provider}`
      });
    }

    if (process.env.NODE_ENV === 'development') {


      console.log('Sending test email to:', testEmail);


      }
    if (process.env.NODE_ENV === 'development') {

      console.log('With configuration:', {
      provider: config.provider,
      host: config.smtpHost,
      port: config.smtpPort,
      secure: config.smtpSecure,
      username: config.smtpUsername,
      // Password is masked for security
    }.replace(/user.*/, 'hasUser: ' + !!user));


      }
    try {
      // Create a test transporter
      const transporter = nodemailer.createTransport({
        host: config.smtpHost,
        port: config.smtpPort,
        secure: config.smtpSecure || false,
        auth: {
          user: config.smtpUsername,
          pass: config.smtpPassword,
        },
        debug: true, // Enable debug output
      });

      // Verify connection configuration
      try {
        await transporter.verify();
        if (process.env.NODE_ENV === 'development') {

          console.log('SMTP connection verified successfully');

          }
      } catch (verifyError) {
        console.error('SMTP connection verification failed:', verifyError);
        return res.status(400).json({
          success: false,
          message: `SMTP connection failed: ${verifyError.message}`
        });
      }

      // Create email content
      const timestamp = new Date().toISOString();
      const subject = `Classtasker Email Test - ${timestamp}`;
      const body = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Classtasker Email Test</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4f46e5; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
            .content { padding: 20px; border: 1px solid #e0e0e0; border-top: none; border-radius: 0 0 5px 5px; }
            .footer { margin-top: 20px; font-size: 12px; color: #666; text-align: center; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Classtasker Email Test</h1>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>This is a test email from Classtasker to verify your email configuration.</p>
            <p>If you're seeing this, your email configuration is working correctly!</p>
            <p><strong>Test timestamp:</strong> ${timestamp}</p>
            <p>Configuration details:</p>
            <ul>
              <li>Provider: ${config.provider}</li>
              <li>From: ${config.fromEmail}</li>
              <li>SMTP Host: ${config.smtpHost}</li>
              <li>SMTP Port: ${config.smtpPort}</li>
              <li>Secure: ${config.smtpSecure ? 'Yes' : 'No'}</li>
              <li>To: ${testEmail}</li>
            </ul>
          </div>
          <div class="footer">
            <p>This is an automated message from Classtasker. Please do not reply to this email.</p>
            <p>&copy; ${new Date().getFullYear()} Classtasker. All rights reserved.</p>
          </div>
        </body>
        </html>
      `;

      // Send test email
      const info = await transporter.sendMail({
        from: `"${config.fromName || 'Classtasker'}" <${config.fromEmail}>`,
        to: testEmail,
        subject: subject,
        text: 'This is a test email from Classtasker to verify your SMTP configuration.',
        html: body
      });

      if (process.env.NODE_ENV === 'development') {


        console.log('Email sent successfully:', info);



        }
      // Return success
      return res.status(200).json({
        success: true,
        message: `Test email sent to ${testEmail} using ${config.provider} provider. Message ID: ${info.messageId}`
      });
    } catch (emailError) {
      console.error('Error sending email:', emailError);
      return res.status(500).json({
        success: false,
        message: `Error sending email: ${emailError.message}`
      });
    }
  } catch (error) {
    console.error('Error testing email:', error);
    return res.status(500).json({
      success: false,
      message: `Server error: ${error.message}`
    });
  }
});

// Admin users API endpoint - POST method for operations
app.post('/api/admin-users', async (req, res) => {
  if (process.env.NODE_ENV === 'development') {

    console.log('Admin users API route called', req.body.replace(/user.*/, 'hasUser: ' + !!user));

    }
  if (process.env.NODE_ENV === 'development') {

    console.log('Headers:', req.headers);


    }
  try {
    const { action, userId, page, pageSize } = req.body;

    // Verify the requesting user is an admin
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);

    if (authError || !user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || profile?.role !== 'admin') {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    // Handle different actions
    switch (action) {
      case 'listUsers':
        const { data, error } = await supabaseAdmin.auth.admin.listUsers({
          page: page || 1,
          perPage: pageSize || 20,
        });

        if (error) throw error;
        return res.status(200).json(data);

      case 'deleteUser':
        if (!userId) {
          return res.status(400).json({ error: 'User ID is required' });
        }

        const { error: deleteError } = await supabaseAdmin.auth.admin.deleteUser(userId);

        if (deleteError) throw deleteError;
        return res.status(200).json({ success: true });

      default:
        return res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    console.error('Admin API error:', error);
    return res.status(500).json({ error: error.message });
  }
});

// Start server
app.listen(PORT, () => {
  if (process.env.NODE_ENV === 'development') {

    console.log(`Admin API server running on port ${PORT}`);

    }
});

export default app;
