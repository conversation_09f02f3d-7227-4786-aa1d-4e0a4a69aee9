# Stripe Integration Documentation

## Overview

This documentation explains how the frontend integrates with the Stripe API server for payment processing and Stripe Connect functionality.

## Stripe Service

The `stripeService.ts` file provides a set of functions for interacting with the Stripe API server:

### Stripe Connect Functions

- `getAccountStatus(accountId)` - Fetches the status of a Stripe Connect account from the database and Stripe
- `createAccount(userId)` - Creates a new Stripe Connect account for a user
- `getOnboardingLink(accountId)` - Generates an onboarding link for a Stripe Connect account
- `getDashboardLink(accountId)` - Generates a dashboard link for a Stripe Connect account
- `deleteAccount(accountId)` - Deletes a Stripe Connect account

### Payment Functions

- `createPaymentIntent(amount, currency, metadata)` - Creates a payment intent for direct payments
- `confirmPaymentIntent(paymentIntentId, paymentMethodId)` - Confirms a payment intent

## Implementation Details

### Database Integration

The frontend integrates with the Supabase database to:

1. Fetch Stripe account IDs from the database
2. Use those IDs for all Stripe API calls
3. Handle cases where users don't have Stripe accounts yet

### Security Measures

The frontend includes several security measures:

1. **No Hardcoded Values** - No Stripe account IDs are hardcoded in the frontend code
2. **Error Handling** - Provides appropriate error messages to users
3. **Validation** - Validates user input before making API calls

## Usage Examples

### Fetching Account Status

```typescript
// Fetch the user's Stripe account from the database
const { data, error } = await supabase
  .from('stripe_accounts')
  .select('*')
  .eq('user_id', user.id)
  .single();

if (data && data.account_id) {
  // Use the account ID from the database
  const status = await stripeService.getAccountStatus(data.account_id);
  if (status) {
    setAccountStatus(prev => ({ ...prev, ...status }));
  }
}
```

### Creating a Dashboard Link

```typescript
// Use the account ID from the database
const dashboardLink = await stripeService.getDashboardLink(accountStatus.account_id);
if (dashboardLink && dashboardLink.url) {
  window.open(dashboardLink.url, '_blank');
}
```

## Important Notes

1. **No Hardcoded Values** - The frontend does not use any hardcoded Stripe account IDs. All account IDs are fetched from the database.
2. **Error Handling** - The frontend includes robust error handling to ensure a smooth user experience even when errors occur.
3. **Direct Payments** - The frontend is configured for direct payments through the platform, not Stripe Connect payments.
