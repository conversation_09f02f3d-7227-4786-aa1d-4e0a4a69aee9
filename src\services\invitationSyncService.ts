import { supabase } from "@/integrations/supabase/client";
import { supabaseAdmin } from "@/services/supabaseAdmin";
import { toast } from "@/hooks/use-toast";

/**
 * InvitationSyncService - Simplified version that handles invitation acceptance
 * without background processing or localStorage dependencies
 */
const InvitationSyncService = {
  /**
   * Initialize the sync service
   * This is kept for backward compatibility with existing code
   */
  init: () => {
    console.log('InvitationSyncService.init called (compatibility mode)');

    // Process any pending invitations immediately
    InvitationSyncService.processPendingInvitations();

    // Return a no-op cleanup function for backward compatibility
    return () => {
      console.log('InvitationSyncService cleanup called (compatibility mode)');
    };
  },

  /**
   * Accept an invitation with the given token
   * @param token The invitation token
   * @param userId The user ID to associate with the invitation
   * @returns Promise<boolean> True if successful, false otherwise
   */
  acceptInvitation: async (token: string, userId: string): Promise<boolean> => {
    try {
      // Call the accept_invitation function
      const { error } = await supabaseAdmin.rpc('accept_invitation', {
        token_param: token,
        user_id_param: userId
      });

      if (error) {
        console.error('Error accepting invitation:', error);
        return false;
      }

      console.log('Successfully accepted invitation');
      return true;
    } catch (error) {
      console.error('Error accepting invitation:', error);
      return false;
    }
  },

  /**
   * Get the status of an invitation
   * @param token The invitation token
   * @param userId The user ID to check
   * @returns Status object with isPending, isProcessing, isError, and retryCount
   */
  getInvitationStatus: (token: string, userId: string) => {
    // This is a simplified version that always returns not pending/processing
    // since we're no longer using the background sync process
    return {
      isPending: false,
      isProcessing: false,
      isError: false,
      retryCount: 0
    };
  },

  /**
   * Process a pending invitation from localStorage
   * This is kept for backward compatibility but should be phased out
   */
  processPendingInvitations: async (): Promise<void> => {
    // Skip if offline
    if (!navigator.onLine) {
      return;
    }

    const token = localStorage.getItem('pendingInvitationToken');
    const email = localStorage.getItem('pendingInvitationEmail');

    if (!token || !email) {
      return;
    }

    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();

      if (!user || user.email !== email) {
        return;
      }

      const success = await InvitationSyncService.acceptInvitation(token, user.id);

      if (success) {
        // Clear localStorage
        localStorage.removeItem('pendingInvitationToken');
        localStorage.removeItem('pendingInvitationEmail');
        localStorage.removeItem('pendingInvitationOrgName');
        localStorage.removeItem('newUserWithInvitation');

        // Show success toast
        toast({
          title: "Invitation Accepted",
          description: "You have successfully joined the organization.",
        });

        // Refresh the page to show updated organization
        window.location.reload();
      }
    } catch (error) {
      console.error('Error processing invitation:', error);
    }
  }
};

export default InvitationSyncService;
