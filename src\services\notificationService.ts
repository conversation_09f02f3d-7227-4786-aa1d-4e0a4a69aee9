import { supabase } from '@/integrations/supabase/client';
// Email service removed - now using Resend directly
import { NotificationType, RelatedType } from '@/types/notifications';

interface CreateNotificationParams {
  userId: string;
  type: NotificationType;
  message: string;
  relatedId?: string;
  relatedType?: RelatedType;
  sendEmail?: boolean;
}

export const notificationService = {
  /**
   * Create a new notification
   */
  async createNotification({
    userId,
    type,
    message,
    relatedId = null,
    relatedType = null,
    sendEmail = false
  }: CreateNotificationParams): Promise<boolean> {
    try {
      if (process.env.NODE_ENV === 'development') {

        console.log('Creating notification:', {
        userId,
        type,
        message,
        relatedId,
        relatedType,
        sendEmail
      }.replace(/user.*/, 'hasUser: ' + !!user));


        }
      // Insert notification into database
      const { data, error } = await supabase
        .from('notifications')
        .insert({
          user_id: userId,
          type,
          message,
          related_id: relatedId,
          related_type: relatedType,
          read: false,
          email_sent: false
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating notification:', error);
        throw error;
      }

      if (process.env.NODE_ENV === 'development') {


        console.log('Notification created successfully:', data);



        }
      // Send email notification if requested
      if (sendEmail) {
        // Get user email
        const { data: userData, error: userError } = await supabase
          .from('profiles')
          .select('email')
          .eq('id', userId)
          .single();

        if (userError) {
          console.error('Error fetching user email:', userError);
        } else if (userData?.email) {
          // Send email notification
          const emailSent = await this.sendEmailNotification(
            userData.email[0], // Assuming email is stored as an array
            type,
            message,
            relatedId,
            relatedType
          );

          // Update notification to mark email as sent
          if (emailSent) {
            await supabase
              .from('notifications')
              .update({ email_sent: true })
              .eq('id', data.id);
          }
        }
      }

      return true;
    } catch (error) {
      console.error('Error creating notification:', error);
      return false;
    }
  },

  /**
   * Send an email notification
   */
  async sendEmailNotification(
    email: string,
    type: NotificationType,
    message: string,
    relatedId?: string,
    relatedType?: RelatedType
  ): Promise<boolean> {
    try {
      // Create email subject based on notification type
      let subject = 'Classtasker Notification';
      switch (type) {
        case 'offer':
          subject = 'New Offer on Classtasker';
          break;
        case 'message':
          subject = 'New Message on Classtasker';
          break;
        case 'task_update':
          subject = 'Task Update on Classtasker';
          break;
        case 'system':
          subject = 'Important Classtasker Update';
          break;
      }

      // Create action URL if there's a related item
      let actionUrl = '';
      if (relatedId) {
        switch (relatedType) {
          case 'task':
            actionUrl = `${window.location.origin}/tasks/${relatedId}`;
            break;
          case 'message':
            actionUrl = `${window.location.origin}/dashboard?tab=messages`;
            break;
          case 'offer':
            actionUrl = `${window.location.origin}/tasks/${relatedId}`;
            break;
        }
      }

      // Create email body
      const body = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${subject}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4f46e5; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
            .content { padding: 20px; border: 1px solid #e0e0e0; border-top: none; border-radius: 0 0 5px 5px; }
            .button { display: inline-block; background-color: #4f46e5; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin: 20px 0; }
            .footer { margin-top: 20px; font-size: 12px; color: #666; text-align: center; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${subject}</h1>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>${message}</p>
            ${actionUrl ? `
            <p>To view more details, please click the button below:</p>
            <p style="text-align: center;">
              <a href="${actionUrl}" class="button">View Details</a>
            </p>
            <p>Or copy and paste this URL into your browser:</p>
            <p style="word-break: break-all;">${actionUrl}</p>
            ` : ''}
          </div>
          <div class="footer">
            <p>This is an automated message from Classtasker. Please do not reply to this email.</p>
            <p>&copy; ${new Date().getFullYear()} Classtasker. All rights reserved.</p>
          </div>
        </body>
        </html>
      `;

      // Send the email using Resend Edge Function
      try {
        const { data, error } = await supabase.functions.invoke('send-notification-email', {
          body: {
            to: email,
            subject,
            html: body
          }
        });

        if (error) {
          console.error('Error sending notification email:', error);
          return false;
        }

        return true;
      } catch (error) {
        console.error('Error calling email function:', error);
        return false;
      }
    } catch (error) {
      console.error('Error sending email notification:', error);
      return false;
    }
  },

  /**
   * Create a task update notification
   */
  async createTaskUpdateNotification(
    userId: string,
    taskId: string,
    taskTitle: string,
    updateType: 'created' | 'updated' | 'completed' | 'assigned' | 'offer',
    sendEmail: boolean = false
  ): Promise<boolean> {
    let message = '';

    switch (updateType) {
      case 'created':
        message = `Your task "${taskTitle}" has been created successfully.`;
        break;
      case 'updated':
        message = `Your task "${taskTitle}" has been updated.`;
        break;
      case 'completed':
        message = `Your task "${taskTitle}" has been marked as completed.`;
        break;
      case 'assigned':
        message = `Your task "${taskTitle}" has been assigned to a service provider.`;
        break;
      case 'offer':
        message = `You have received a new offer on your task "${taskTitle}".`;
        break;
    }

    return await this.createNotification({
      userId,
      type: 'task_update',
      message,
      relatedId: taskId,
      relatedType: 'task',
      sendEmail
    });
  },

  /**
   * Create a message notification
   */
  async createMessageNotification(
    userId: string,
    senderName: string,
    taskId: string,
    taskTitle: string,
    sendEmail: boolean = false
  ): Promise<boolean> {
    const message = `You have received a new message from ${senderName} regarding "${taskTitle}".`;

    return await this.createNotification({
      userId,
      type: 'message',
      message,
      relatedId: taskId,
      relatedType: 'message',
      sendEmail
    });
  },

  /**
   * Create an offer notification
   */
  async createOfferNotification(
    userId: string,
    providerName: string,
    taskId: string,
    taskTitle: string,
    offerAmount: number,
    sendEmail: boolean = false
  ): Promise<boolean> {
    const message = `${providerName} has made an offer of £${offerAmount.toFixed(2)} on your task "${taskTitle}".`;

    return await this.createNotification({
      userId,
      type: 'offer',
      message,
      relatedId: taskId,
      relatedType: 'offer',
      sendEmail
    });
  },

  /**
   * Create a system notification
   */
  async createSystemNotification(
    userId: string,
    message: string,
    sendEmail: boolean = false
  ): Promise<boolean> {
    return await this.createNotification({
      userId,
      type: 'system',
      message,
      sendEmail
    });
  }
};

export default notificationService;
