import { supabase } from "@/integrations/supabase/client";
import { supabaseAdmin } from "@/services/supabaseAdmin";
import { Organization, OrganizationUser, UserInvitation, InviteUserRequest } from "@/types/organization";

export const organizationService = {
  // Create a new organization using secure database function
  async createOrganization(name: string, details?: Partial<Organization>): Promise<Organization> {
    try {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('Creating organization:', { name, details });
  }
      }

      // Use the secure database function to create the organization
      const { data: orgId, error: orgError } = await supabase.rpc('create_organization', {
        name_param: name,
        organization_type_param: details?.organization_type || 'school',
        parent_organization_id_param: details?.parent_organization_id || null,
        address_param: details?.address || null,
        city_param: details?.city || null,
        state_param: details?.state || null,
        zip_param: details?.zip || null,
        phone_param: details?.phone || null,
        website_param: details?.website || null
      });

      if (orgError) {
        console.error('Error creating organization:', orgError);
        throw orgError;
      }

      if (process.env.NODE_ENV === 'development') {
    console.log('Organization created with ID:', orgId);
  }
      // Fetch the created organization
      const { data: orgData, error: fetchError } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', orgId)
        .single();

      if (fetchError) {
        console.error('Error fetching created organization:', fetchError);
        throw fetchError;
      }

      // Store organization data in user's metadata for backward compatibility
      const { error: updateError } = await supabase.auth.updateUser({
        data: {
          organization: orgData
        }
      });

      if (updateError) {
        console.error('Error updating user metadata:', updateError);
        // Continue anyway since the organization was created successfully
      }

      // Refresh the user session to get the updated metadata
      const { data: sessionData, error: sessionError } = await supabase.auth.refreshSession();
      if (sessionError) {
        console.error('Error refreshing session:', sessionError);
      } else {
        if (process.env.NODE_ENV === 'development') {

          console.log('Session refreshed, new metadata: completed');

          }
      }

      // Get the current user ID
      const { data: userData } = await supabase.auth.getUser();
      const userId = userData.user?.id;

      if (userId) {
        // Fetch the updated profile to ensure we have the latest role information
        if (process.env.NODE_ENV === 'development') {
    console.log('Fetching updated profile after organization creation');
  }
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single();

        if (profileError) {
          console.error('Error fetching updated profile:', profileError);
        } else {
          if (process.env.NODE_ENV === 'development') {
    console.log('Updated profile fetched successfully: completed');
  }
          // Verify that the role is set to admin
          if (profileData.role !== 'admin') {
            console.warn('Profile role is not set to admin after organization creation. Manually updating...');

            // Manually update the profile role if needed
            const { error: roleUpdateError } = await supabase
              .from('profiles')
              .update({ role: 'admin' })
              .eq('id', userId);

            if (roleUpdateError) {
              console.error('Error manually updating role to admin:', roleUpdateError);
            } else {
              if (process.env.NODE_ENV === 'development') {
    console.log('Successfully manually updated role to admin');
  }
            }
          }
        }
      }

      return orgData as Organization;
    } catch (error) {
      console.error('Error creating organization:', error);
      throw error;
    }
  },

  // Get organization details from database (prioritizing database over metadata)
  async getOrganization(id: string): Promise<Organization> {
    try {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('Getting organization with ID:', id);
  }
      }

      // First try to get the organization from the database
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', id)
        .single();

      if (!orgError && orgData) {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
    console.log('Found organization in database: completed');
  }
        }
        return orgData as Organization;
      }

      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('Organization not found in database or error occurred: completed');
  }
      }
      throw new Error('Organization not found');
    } catch (error) {
      console.error('Error getting organization:', error);
      throw error;
    }
  },

  // Update organization details - temporary implementation
  async updateOrganization(id: string, updates: Partial<Organization>): Promise<Organization> {
    try {
      // Get current user and organization
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      const organization = userData.user?.user_metadata?.organization;

      if (!organization) {
        throw new Error('Organization not found');
      }

      // Update organization
      const updatedOrg = {
        ...organization,
        ...updates,
        updated_at: new Date().toISOString()
      };

      // Store updated organization in user metadata
      const { error: updateError } = await supabase.auth.updateUser({
        data: {
          organization: updatedOrg
        }
      });

      if (updateError) throw updateError;

      return updatedOrg as Organization;
    } catch (error) {
      console.error('Error updating organization:', error);
      throw error;
    }
  },

  // Get all users in an organization - client-side implementation
  async getOrganizationUsers(organizationId: string): Promise<OrganizationUser[]> {
    try {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Getting users for organization: completed');

          }
      }

      // Get current user to check organization
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) {
        console.error('DEBUG: Error fetching current user:', userError);
        throw userError;
      }

      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Current user: completed');

          }
      }

      // Get all profiles for this organization
      const { data: orgProfiles, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .eq('organization_id', organizationId);

      if (profilesError) {
        console.error('DEBUG: Error fetching profiles:', profilesError);
      }

      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Found ${orgProfiles?.length || 0} profiles for organization ${organizationId}`);
  }
      }

      // Initialize organization users array
      const organizationUsers: OrganizationUser[] = [];

      // Add the current user if they belong to this organization based on profile data
      const currentUserProfile = orgProfiles?.find(p => p.id === userData.user?.id);
      if (currentUserProfile && currentUserProfile.organization_id === organizationId) {

        organizationUsers.push({
          user_id: userData.user.id,
          email: userData.user.email || '<EMAIL>',
          first_name: currentUserProfile?.first_name ||
                     userData.user.user_metadata?.first_name ||
                     userData.user.user_metadata?.name?.split(' ')[0] ||
                     'Unknown',
          last_name: currentUserProfile?.last_name ||
                    userData.user.user_metadata?.last_name ||
                    (userData.user.user_metadata?.name?.split(' ').slice(1).join(' ')) ||
                    'User',
          role: currentUserProfile?.role ||
                userData.user.user_metadata?.role ||
                'admin',
          created_at: currentUserProfile?.created_at || new Date().toISOString()
        });

        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {

            console.log('DEBUG: Added current user to organization users');

            }
        }
      }

      // We've removed hardcoded users in favor of fetching real users from the database
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: No longer using hardcoded users');

          }
      }

      // Include profiles with matching organization_id
      if (orgProfiles && orgProfiles.length > 0) {
        // Get the current user's email
        const currentUserEmail = userData.user?.email || '';

        // Now add all profiles to the organization users list
        for (const profile of orgProfiles) {
          // Skip if this user is already in the list
          if (organizationUsers.some(user => user.user_id === profile.id)) {
            continue;
          }

          // Determine the email to use
          let email = '<EMAIL>';

          // First try to get the email from the profile
          if (profile.email && Array.isArray(profile.email) && profile.email.length > 0) {
            email = profile.email[0];
          }
          // If this is the current user, use their email
          else if (profile.id === userData.user?.id) {
            email = currentUserEmail;
          }
          // Otherwise, use a placeholder with the user ID
          else {
            email = `user-${profile.id.substring(0, 8)}@example.com`;
          }

          organizationUsers.push({
            user_id: profile.id,
            email: email,
            first_name: profile.first_name || 'Unknown',
            last_name: profile.last_name || 'User',
            role: profile.role || 'teacher',
            created_at: profile.created_at || new Date().toISOString()
          });
        }

        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {

            console.log(`DEBUG: Added ${orgProfiles.length} profiles to organization users`);

            }
        }
      }

      // We've removed the test user code
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: No longer using test users');

          }
      }

      // For backward compatibility, also include accepted invitation users
      const acceptedInvitations = JSON.parse(localStorage.getItem('acceptedInvitations') || '[]');
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Accepted invitations in getOrganizationUsers: completed');

          }
      }

      // Get all invitations from user metadata
      const allInvitations = userData.user?.user_metadata?.invitations || [];

      // Find invitations that have been accepted
      const acceptedInvitationObjects = allInvitations.filter((inv: any) =>
        acceptedInvitations.includes(inv.token)
      );

      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Accepted invitation objects:', acceptedInvitationObjects);
  }
      }

      // Get stored roles for accepted users
      const acceptedUserRoles = JSON.parse(localStorage.getItem('acceptedUserRoles') || '{}');

      // Add each accepted invitation as a user if not already included
      for (const invitation of acceptedInvitationObjects) {
        // Check if this user is already included (by email)
        const userExists = organizationUsers.some(user => user.email === invitation.email);

        if (!userExists) {
          organizationUsers.push({
            user_id: `accepted-${invitation.email}`,
            email: invitation.email,
            first_name: 'Invited',
            last_name: 'User',
            role: acceptedUserRoles[invitation.email] || invitation.role || 'teacher',
            created_at: invitation.createdAt || new Date().toISOString()
          });
        }
      }

      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log(`DEBUG: Final result - Found ${organizationUsers.length} users for organization ${organizationId}`);

          }
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: User list: completed');

          }
      }
      return organizationUsers;
    } catch (error) {
      console.error('Error getting organization users:', error);
      return [];
    }
  },

  // Invite a user to join the organization using secure database function
  async inviteUser(organizationId: string, inviteData: InviteUserRequest): Promise<string> {
    try {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Inviting user to organization: completed');

          }
      }

      // Get current user
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) {
        console.error('DEBUG: Error getting current user:', userError);
        throw userError;
      }

      // Get organization name
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('name')
        .eq('id', organizationId)
        .single();

      if (orgError) {
        console.error('DEBUG: Error fetching organization:', orgError);
        throw orgError;
      }

      const organizationName = orgData?.name || 'Classtasker Organization';
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Organization name:', organizationName);
  }
      }

      // Use the secure database function to create the invitation
      const { data: invitationId, error: inviteError } = await supabase.rpc('invite_user_to_organization', {
        email_param: inviteData.email,
        organization_id_param: organizationId,
        role_param: inviteData.role
      });

      if (inviteError) {
        console.error('DEBUG: Error creating invitation:', inviteError);
        throw inviteError;
      }

      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Invitation created with ID:', invitationId);
  }
      }

      // Fetch the created invitation to get the token
      const { data: invitation, error: fetchError } = await supabase
        .from('user_invitations')
        .select('*')
        .eq('id', invitationId)
        .single();

      if (fetchError) {
        console.error('DEBUG: Error fetching invitation:', fetchError);
        throw fetchError;
      }

      const token = invitation.token;
      // SECURITY: Don't log actual tokens in production
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Invitation token:', token);
  }
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Invitation token present:', !!token);
  }
      }

      // Create invitation object for metadata (for backward compatibility)
      const invitationObj = {
        token,
        email: inviteData.email,
        role: inviteData.role,
        organizationId,
        organizationName,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
      };

      // We no longer need to store invitations in user metadata or localStorage
      // as we're using the user_invitations table as the single source of truth

      // Send invitation email using dedicated send-invitation-email Edge Function
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Sending invitation email to:', inviteData.email);
  }
      }

      const { data, error } = await supabase.functions.invoke('send-invitation-email', {
        body: {
          to: inviteData.email,
          token,
          organizationName,
          role: inviteData.role
        }
      });

      const emailSent = !error;
      if (error) {
        console.error('Error sending invitation email:', error);
      }

      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Invitation ${emailSent ? 'sent' : 'not sent'} to ${inviteData.email} for role ${inviteData.role}`);
  }
      }

      return token;
    } catch (error) {
      console.error('Error inviting user:', error);
      throw error;
    }
  },

  // Get all invitations for an organization using secure database function
  async getOrganizationInvitations(organizationId: string): Promise<UserInvitation[]> {
    try {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Getting invitations for organization:', organizationId);
  }
      }

      // Use the secure database function to get invitations
      const { data: dbInvitations, error: dbError } = await supabase.rpc('get_organization_invitations_secure', {
        org_id: organizationId
      });

      if (dbError) {
        console.error('DEBUG: Error fetching invitations from database:', dbError);
        throw dbError;
      }

      if (!dbInvitations || dbInvitations.length === 0) {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: No invitations found in database');
  }
        }
        return [];
      }

      // SECURITY: Don't log invitation details with tokens in production
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Successfully fetched invitations from database: completed');
  }
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Successfully fetched invitations count:', dbInvitations.length);
  }
      }

      // Process all invitations without filtering by token
      // The token field is not returned by the get_organization_invitations_secure function
      // for security reasons, but we still need to display the invitations
      const processedInvitations = dbInvitations.map(inv => ({
        ...inv,
        // Add a placeholder token if needed for UI compatibility
        token: inv.token || inv.id
      }));

      // SECURITY: Don't log processed invitation details with tokens in production
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Processed invitations:', processedInvitations);
  }
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Processed invitations count:', processedInvitations.length);
  }
      }
      return processedInvitations as UserInvitation[];
    } catch (error) {
      console.error('Error getting organization invitations:', error);
      return [];
    }
  },

  // Cancel an invitation - completely rewritten implementation
  async cancelInvitation(invitationId: string): Promise<void> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Cancelling invitation with token/id ${invitationId}`);
  }
      // Validate invitationId
      if (!invitationId) {
        console.error('DEBUG: Invalid invitation ID: undefined or empty');
        throw new Error('Cannot cancel invitation: invalid identifier');
      }

      // DIRECT APPROACH: Try to delete by token first
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Directly deleting invitation by token:', invitationId);
  }
      const { data: deleteByTokenData, error: deleteByTokenError } = await supabase
        .from('user_invitations')
        .delete()
        .eq('token', invitationId)
        .select();

      if (!deleteByTokenError && deleteByTokenData && deleteByTokenData.length > 0) {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Successfully deleted invitation by token. Deleted data: completed');
  }
        // Update metadata for backward compatibility
        await this.updateInvitationMetadata(invitationId);
        return; // Success! We're done.
      }

      // If that didn't work, try to delete by ID
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Token deletion failed or no results. Trying by ID:', invitationId);
  }
      const { data: deleteByIdData, error: deleteByIdError } = await supabase
        .from('user_invitations')
        .delete()
        .eq('id', invitationId)
        .select();

      if (!deleteByIdError && deleteByIdData && deleteByIdData.length > 0) {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Successfully deleted invitation by ID. Deleted data: completed');
  }
        // Update metadata for backward compatibility
        await this.updateInvitationMetadata(invitationId);
        return; // Success! We're done.
      }

      // If we're still here, we need to find the invitation first
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Direct deletion failed. Searching for invitation to get its ID...');
  }
      // Try to find by token
      const { data: findByTokenData, error: findByTokenError } = await supabase
        .from('user_invitations')
        .select('*')
        .eq('token', invitationId)
        .maybeSingle();

      if (findByTokenError) {
        console.error('DEBUG: Error finding invitation by token:', findByTokenError);
      } else if (findByTokenData) {
        // SECURITY: Don't log invitation objects with tokens in production
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Found invitation by token:', findByTokenData);
  }
        } else {
          if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Found invitation by token with ID:', findByTokenData.id);
  }
        }

        // Delete using the found ID
        const { data: finalDeleteData, error: finalDeleteError } = await supabase
          .from('user_invitations')
          .delete()
          .eq('id', findByTokenData.id)
          .select();

        if (finalDeleteError) {
          console.error('DEBUG: Final error deleting invitation by ID:', finalDeleteError);
          throw new Error(`Failed to delete invitation: ${finalDeleteError.message}`);
        } else {
          if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Successfully deleted invitation in final attempt. Deleted data: completed');
  }
          // Update metadata for backward compatibility
          await this.updateInvitationMetadata(invitationId);
          return; // Success!
        }
      }

      // If we're still here, we couldn't find or delete the invitation
      console.error('DEBUG: All deletion attempts failed for invitation with token/id:', invitationId);
      throw new Error(`Failed to delete invitation with token/id ${invitationId}`);
    } catch (error) {
      console.error('DEBUG: Exception in cancelInvitation:', error);
      throw error;
    }

  },

  // Clean up all invitations in metadata
  async cleanupAllMetadataInvitations(): Promise<void> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Cleaning up all invitations in metadata');
  }
      // Get current user
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      // Update user metadata to clear all invitations
      const { error: updateError } = await supabase.auth.updateUser({
        data: {
          invitations: []
        }
      });

      if (updateError) {
        console.error('DEBUG: Error clearing invitations in user metadata:', updateError);
      } else {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Successfully cleared all invitations in user metadata');

          }
      }

      // Also clear localStorage
      localStorage.setItem('allInvitations', '[]');
      localStorage.setItem('acceptedInvitations', '[]');

      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Successfully cleaned up all invitation metadata');
  }
    } catch (error) {
      console.error('Error cleaning up all invitation metadata:', error);
      // Don't throw here, as this is just a cleanup operation
    }
  },

  // Clean up user metadata for backward compatibility
  async updateInvitationMetadata(invitationId: string): Promise<void> {
    try {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Cleaning up invitation in user metadata');


        }
      // Get current user
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      // Get current invitations
      const currentInvitations = userData.user?.user_metadata?.invitations || [];
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Current invitations in metadata before cleanup: completed');
  }
      // Filter out the invitation to cancel
      const updatedInvitations = currentInvitations.filter((inv: any) => inv.token !== invitationId);
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Updated invitations after cleanup:', updatedInvitations);
  }
      // Update user metadata
      const { error: updateError } = await supabase.auth.updateUser({
        data: {
          invitations: updatedInvitations
        }
      });

      if (updateError) {
        console.error('DEBUG: Error updating user metadata:', updateError);
        // Continue anyway since we've already cancelled it in the database
      } else {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Successfully updated user metadata');

          }
      }

      // Also update localStorage
      const allInvitations = JSON.parse(localStorage.getItem('allInvitations') || '[]');
      const updatedLocalInvitations = allInvitations.filter((inv: any) => inv.token !== invitationId);
      localStorage.setItem('allInvitations', JSON.stringify(updatedLocalInvitations));

      // Also update acceptedInvitations in localStorage
      const acceptedInvitations = JSON.parse(localStorage.getItem('acceptedInvitations') || '[]');
      const updatedAcceptedInvitations = acceptedInvitations.filter((token: string) => token !== invitationId);
      localStorage.setItem('acceptedInvitations', JSON.stringify(updatedAcceptedInvitations));

      if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Successfully cleaned up metadata for invitation ${invitationId}`);
  }
    } catch (error) {
      console.error('Error cleaning up invitation metadata:', error);
      // Don't throw here, as this is just a cleanup operation
      if (process.env.NODE_ENV === 'development') {
    console.log('Continuing despite metadata cleanup error');
  }
    }
  },

  // Update a user's role in the organization using secure database function
  async updateUserRole(userId: string, role: string): Promise<void> {
    try {
      if (process.env.NODE_ENV === 'development') {

        console.log(`DEBUG: Updating role for user ${userId} to ${role}`);


        }
      // Handle different types of users

      // Case 1: Regular user (with UUID)
      if (userId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Updating user role using secure database function');


          }
        // Use the secure database function to update the role
        const { data: success, error } = await supabase.rpc('update_user_role', {
          user_id_param: userId,
          role_param: role
        });

        if (error) {
          console.error('DEBUG: Error updating user role:', error);
          throw error;
        }

        if (process.env.NODE_ENV === 'development') {


          console.log('DEBUG: Successfully updated user role');


          }
        return;
      }

      // Case 2: Accepted invitation user (temporary UI representation)
      if (userId.startsWith('accepted-')) {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Updating accepted invitation user role');


          }
        // Extract email from userId
        const email = userId.replace('accepted-', '');

        // Store the updated role in localStorage for UI purposes only
        // This is temporary until the user creates an account
        const acceptedUserRoles = JSON.parse(localStorage.getItem('acceptedUserRoles') || '{}');
        acceptedUserRoles[email] = role;
        localStorage.setItem('acceptedUserRoles', JSON.stringify(acceptedUserRoles));

        if (process.env.NODE_ENV === 'development') {


          console.log('DEBUG: Successfully updated accepted user role in localStorage');


          }
        return;
      }

      if (process.env.NODE_ENV === 'development') {


        console.log(`DEBUG: Unknown user type ${userId}, cannot update role`);


        }
    } catch (error) {
      console.error('Error updating user role:', error);
      throw error;
    }
  },

  // Remove a user from the organization
  async removeUserFromOrganization(userId: string): Promise<void> {
    try {
      if (process.env.NODE_ENV === 'development') {

        console.log(`DEBUG: Removing user ${userId} from organization`);


        }
      // Handle accepted invitation user
      if (userId.startsWith('accepted-')) {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Removing accepted invitation user');


          }
        // Extract email from userId
        const email = userId.replace('accepted-', '');

        // Get current user
        const { data: userData, error: userError } = await supabase.auth.getUser();
        if (userError) throw userError;

        // Get accepted invitations
        const acceptedInvitations = JSON.parse(localStorage.getItem('acceptedInvitations') || '[]');

        // Get all invitations from user metadata
        const allInvitations = userData.user?.user_metadata?.invitations || [];

        // Find the invitation for this user
        const invitation = allInvitations.find((inv: any) =>
          inv.email === email && acceptedInvitations.includes(inv.token)
        );

        if (invitation) {
          if (process.env.NODE_ENV === 'development') {

            console.log('DEBUG: Found invitation for accepted user: completed');


            }
          // Remove from accepted invitations
          const updatedAcceptedInvitations = acceptedInvitations.filter(
            (token: string) => token !== invitation.token
          );
          localStorage.setItem('acceptedInvitations', JSON.stringify(updatedAcceptedInvitations));

          // Remove from accepted user roles
          const acceptedUserRoles = JSON.parse(localStorage.getItem('acceptedUserRoles') || '{}');
          if (acceptedUserRoles[email]) {
            delete acceptedUserRoles[email];
            localStorage.setItem('acceptedUserRoles', JSON.stringify(acceptedUserRoles));
          }

          if (process.env.NODE_ENV === 'development') {


            console.log('DEBUG: Successfully removed accepted user from organization');


            }
        } else {
          console.error('DEBUG: Could not find invitation for accepted user');
        }

        return;
      }

      // Get current user
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      // Check if the current user is an admin
      // Always use the profiles table as the source of truth
      let isAdmin = false;

      // Get the user's role from the profiles table
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userData.user?.id)
        .single();

      if (!profileError && profileData) {
        isAdmin = profileData.role === 'admin';
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {

            console.log('DEBUG: User role from profile: completed');

            }
        }
      }

      if (process.env.NODE_ENV === 'development') {


        console.log('DEBUG: Current user role check - isAdmin: completed');



        }
      // If the user is trying to remove themselves
      if (userData.user?.id === userId) {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: User is removing themselves from organization');


          }
        // Update user metadata to remove organization and role
        const { error: updateError } = await supabase.auth.updateUser({
          data: {
            organization: null,
            role: null
          }
        });

        if (updateError) {
          console.error('DEBUG: Error updating user metadata:', updateError);
          throw updateError;
        }

        if (process.env.NODE_ENV === 'development') {


          console.log('DEBUG: Successfully removed user from organization');


          }
        return;
      }

      // Check if the current user is a teacher
      // We already have the profile data from above, so just check if the role is 'teacher'
      let isTeacher = profileData?.role === 'teacher';
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: User role from profile for teacher check: completed');

          }
      }

      // If the current user is an admin or teacher and is removing another user
      if (isAdmin || isTeacher) {
        if (process.env.NODE_ENV === 'development') {

          console.log(`DEBUG: Admin is removing user ${userId} from organization`);


          }
        // Update the profile in the database to remove organization_id
        const { error: profileError } = await supabase
          .from('profiles')
          .update({ organization_id: null, role: null })
          .eq('id', userId);

        if (profileError) {
          console.error('DEBUG: Error updating profile:', profileError);
          throw profileError;
        }

        if (process.env.NODE_ENV === 'development') {


          console.log('DEBUG: Successfully removed user from organization in database');


          }
        return;
      }

      // If we get here, the user doesn't have permission to remove the target user
      console.error(`DEBUG: User doesn't have permission to remove user ${userId}`);
      throw new Error('You do not have permission to remove this user');
    } catch (error) {
      console.error('Error removing user from organization:', error);
      throw error;
    }
  }
};

export default organizationService;
