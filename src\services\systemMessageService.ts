import { supabase } from '@/integrations/supabase/client';

/**
 * Service for creating system messages in task chats
 */
const systemMessageService = {
  /**
   * Cleans up incorrect system messages for a task
   * @param taskId The ID of the task
   * @param isInternalTask Whether this is an internal task
   */
  async cleanupIncorrectSystemMessages(taskId: string, isInternalTask: boolean): Promise<void> {
    try {
      const SYSTEM_USER_ID = '00000000-0000-0000-0000-000000000000';

      // Define messages that should be removed based on task type
      let messagesToRemove = [];

      if (isInternalTask) {
        // For internal tasks, remove supplier-related messages
        messagesToRemove = [
          'Task has been assigned to a supplier.',
          'Task is now public and visible to suppliers.',
          'Task created and is now open for offers.',
          'A supplier has expressed interest in this task.',
          'Discussion phase has started. Please discuss requirements before submitting formal offers.',
          'Supplier has marked this task as completed.',
          'Task is awaiting payment.'
        ];
      } else {
        // For external tasks, remove internal-related messages
        messagesToRemove = [
          'Task has been assigned to an internal staff member.',
          'Internal task has been assigned to maintenance staff.',
          'Maintenance work has started on this task.',
          'Maintenance staff has marked this task as completed.'
        ];
      }

      console.log(`Removing ${messagesToRemove.length} incorrect messages for ${isInternalTask ? 'internal' : 'external'} task ${taskId}:`, messagesToRemove);

      // Delete the incorrect messages
      if (messagesToRemove.length > 0) {
        const { error } = await supabase
          .from('task_messages')
          .delete()
          .eq('task_id', taskId)
          .eq('sender_id', SYSTEM_USER_ID)
          .in('content', messagesToRemove);

        if (error) {
          console.error('Error cleaning up incorrect system messages:', error);
        } else {
          console.log(`Cleaned up incorrect system messages for task ${taskId}`);
        }
      }
    } catch (error) {
      console.error('Error in cleanupIncorrectSystemMessages:', error);
    }
  },
  /**
   * Creates a system message for a task status change using GetStream
   * @param taskId The ID of the task
   * @param status The new status of the task
   * @param assignedTo Optional ID of the user the task is assigned to
   * @param threadId Optional thread ID (deprecated, kept for backward compatibility)
   * @param isInternalTask Optional flag to indicate if this is an internal task
   */
  async createStatusChangeMessage(
    taskId: string,
    status: string,
    assignedTo?: string,
    threadId?: string,
    isInternalTask?: boolean
  ): Promise<boolean> {
    try {
      let message = '';

      // First, check if we need to determine if this is an internal task
      if (isInternalTask === undefined) {
        // Get the task to determine if it's internal
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('visibility')
          .eq('id', taskId)
          .single();

        if (!taskError && taskData) {
          isInternalTask = taskData.visibility === 'internal';
        }
      }

      // Generate appropriate message based on status and task type
      if (isInternalTask) {
        // Messages for internal tasks
        switch (status) {
          case 'assigned':
            message = 'Internal task has been assigned to maintenance staff.';
            break;
          case 'in_progress':
            message = 'Maintenance work has started on this task.';
            break;
          case 'completed':
            message = 'Maintenance staff has marked this task as completed.';
            break;
          case 'closed':
            message = 'Task has been closed by the administrator.';
            break;
          default:
            message = `Task status changed to: ${status}`;
        }
      } else {
        // Messages for external tasks
        switch (status) {
          case 'open':
            message = 'Task created and is now open for offers.';
            break;
          case 'interest':
            message = 'A supplier has expressed interest in this task.';
            break;
          case 'questions':
            message = 'Discussion phase has started. Please discuss requirements before submitting formal offers.';
            break;
          case 'assigned':
            message = 'Task has been assigned to a supplier.';
            break;
          case 'in_progress':
            message = 'Task is now in progress. Work has started.';
            break;
          case 'completed':
            message = 'Supplier has marked this task as completed.';
            break;
          case 'closed':
            message = 'Task has been closed by the administrator.';
            break;
          case 'pending_payment':
            message = 'Task is awaiting payment.';
            break;
          default:
            message = `Task status changed to: ${status}`;
        }
      }

      // If we have assignedTo information, add it to the message
      if (assignedTo && (status === 'assigned' || status === 'in_progress')) {
        // Get the user's name
        const { data: userData, error: userError } = await supabase
          .from('profiles')
          .select('first_name, last_name, role')
          .eq('id', assignedTo)
          .single();

        if (!userError && userData) {
          // Use different wording based on user role
          const isMaintenanceStaff = userData.role === 'maintenance';

          const userName = userData.first_name && userData.last_name
            ? `${userData.first_name} ${userData.last_name}`
            : isMaintenanceStaff ? 'maintenance staff' : 'a supplier';

          message += ` The task has been assigned to ${userName}.`;
        }
      }

      // Use the system user ID for system messages
      const SYSTEM_USER_ID = '00000000-0000-0000-0000-000000000000';

      // Import the necessary functions
      const { getTaskChannel, createOrUpdateTaskChannel } = await import('@/integrations/getstream/client');

      try {
        // Try to get the existing channel
        let channel;

        try {
          channel = await getTaskChannel(taskId);
          console.log('Found existing GetStream channel for task:', taskId);
        } catch (error) {
          console.log('No existing GetStream channel found, creating a new one');

          // Get the task title
          const { data: taskData } = await supabase
            .from('tasks')
            .select('title, user_id')
            .eq('id', taskId)
            .single();

          const taskTitle = taskData?.title || 'Task';

          // Create a new channel with the task creator and assignee if available
          const members = [taskData?.user_id];
          if (assignedTo) {
            members.push(assignedTo);
          }

          // Create the channel
          channel = await createOrUpdateTaskChannel(
            taskId,
            taskTitle,
            members.filter(Boolean), // Remove any undefined values
            true
          );
        }

        if (channel) {
          // Send the status change message as a system message
          await channel.sendMessage({
            text: message,
            user_id: SYSTEM_USER_ID,
            type: 'system',
          });

          if (process.env.NODE_ENV === 'development') {
            console.log('Successfully sent status change message via GetStream');
          }
          return true;
        } else {
          console.error('No GetStream channel found or created for task:', taskId);
          return false;
        }
      } catch (error) {
        console.error('Error sending status change message via GetStream:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error creating system message:', error);
      return false;
    }
  },

  /**
   * Creates a system message for a file upload using GetStream
   * @param taskId The ID of the task
   * @param userId The ID of the user who uploaded the file
   * @param fileType The type of file uploaded
   */
  async createFileUploadMessage(taskId: string, userId: string, fileType: string): Promise<boolean> {
    try {
      // Get the user's name
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('first_name, last_name')
        .eq('id', userId)
        .single();

      let userName = 'A user';
      if (!userError && userData) {
        userName = userData.first_name && userData.last_name
          ? `${userData.first_name} ${userData.last_name}`
          : 'A user';
      }

      const message = `${userName} uploaded ${fileType === 'image' ? 'an image' : 'a file'}.`;

      // Use the system user ID for system messages
      const SYSTEM_USER_ID = '00000000-0000-0000-0000-000000000000';

      // Import the necessary functions
      const { getTaskChannel } = await import('@/integrations/getstream/client');

      try {
        // Try to get the existing channel
        const channel = await getTaskChannel(taskId);

        if (channel) {
          // Send the file upload message as a system message
          await channel.sendMessage({
            text: message,
            user_id: SYSTEM_USER_ID,
            type: 'system',
          });

          console.log('Successfully sent file upload message via GetStream');
          return true;
        } else {
          console.error('No GetStream channel found for task:', taskId);
          return false;
        }
      } catch (error) {
        console.error('Error getting GetStream channel for file upload message:', error);
        return false;
      }
    } catch (error) {
      console.error('Error creating file upload message:', error);
      return false;
    }
  }
};

export default systemMessageService;
