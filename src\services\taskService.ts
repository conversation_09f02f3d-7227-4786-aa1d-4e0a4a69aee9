
import { supabase } from "@/integrations/supabase/client";
import systemMessageService from "@/services/systemMessageService";
import { getStreamApiUrl } from "@/utils/apiConfig";
import { ensureTaskChatExists, updateTaskChatMembership } from "@/utils/chatMembershipUtils";

export interface Task {
  id: string;
  title: string;
  description: string;
  location: string;
  category: string;
  budget: number;
  due_date: string;
  status: 'open' | 'interest' | 'questions' | 'offer' | 'assigned' | 'in_progress' | 'completed' | 'closed' | 'pending_payment';
  created_at: string;
  updated_at: string;
  user_id: string;
  offers_count: number;
  assigned_to?: string;
  visibility: 'admin' | 'internal' | 'public';
  payment_status?: 'unpaid' | 'pending' | 'processing' | 'paid' | 'not_required';
  // New location fields
  location_formatted?: string;
  location_lat?: number;
  location_lng?: number;
  location_place_id?: string;
  // Building and room fields
  building?: string;
  room?: string;
  // Organization reference
  organization_id?: string;
}

export interface Offer {
  id: string;
  task_id: string;
  user_id: string;
  amount: number;
  message: string;
  status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
  updated_at: string;
}

export type NewTask = Omit<Task, 'id' | 'created_at' | 'updated_at' | 'user_id' | 'offers_count' | 'status' | 'visibility'> & {
  status?: 'open' | 'interest' | 'questions' | 'offer' | 'assigned' | 'in_progress' | 'completed' | 'closed' | 'pending_payment';
  visibility?: 'admin' | 'internal' | 'public';
  assigned_to?: string;
  payment_status?: 'unpaid' | 'pending' | 'processing' | 'paid' | 'not_required';
  // Optional location fields
  location_formatted?: string;
  location_lat?: number;
  location_lng?: number;
  location_place_id?: string;
  // Optional organization reference
  organization_id?: string;
};

export type NewOffer = Omit<Offer, 'id' | 'created_at' | 'updated_at' | 'user_id' | 'status'> & {
  status?: 'pending' | 'accepted' | 'rejected';
};

export const taskService = {
  async createTask(task: NewTask): Promise<Task | null> {
    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: createTask called with: completed');
  }
    const { data: userData } = await supabase.auth.getUser();
    const user = userData?.user;

    if (!user) {
      console.error('DEBUG: No authenticated user found');
      return null;
    }

    if (process.env.NODE_ENV === 'development') {


      console.log('DEBUG: User authenticated: completed');



      }
    // Get user role and organization to determine default visibility and ensure organization_id
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('role, account_type, organization_id')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('DEBUG: Error fetching user profile:', profileError);
      return null;
    }

    const userRole = profileData?.role;
    const accountType = profileData?.account_type;
    const userOrganizationId = profileData?.organization_id;
    if (process.env.NODE_ENV === 'development') {

      console.log('DEBUG: Creating task with user role: completed');

      }
    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Task data received from frontend: completed');
  }
    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Frontend organization_id: completed');
  }
    if (process.env.NODE_ENV === 'development') {

      console.log('DEBUG: User profile organization_id: completed');


      }
    const taskWithUserId = {
      ...task,
      user_id: user.id,
      // Ensure organization_id is always set from user's profile if not provided
      organization_id: task.organization_id || userOrganizationId,
      status: task.status || 'open',
      // Use provided visibility or default to 'admin' for new tasks
      visibility: task.visibility || 'admin',
      // Include assigned_to if provided
      ...(task.assigned_to ? { assigned_to: task.assigned_to } : {}),
      // Make sure building and room are included
      building: task.building || null,
      room: task.room || null,
    };

    // Validate that organization_id is set (required for RLS policy)
    if (!taskWithUserId.organization_id) {
      console.error('DEBUG: Cannot create task - organization_id is required but not set');
      console.error('DEBUG: User organization_id:', userOrganizationId);
      console.error('DEBUG: Task organization_id:', task.organization_id);
      return null;
    }

    if (process.env.NODE_ENV === 'development') {


      console.log('DEBUG: Final task data to insert: completed');


      }
    if (process.env.NODE_ENV === 'development') {

      console.log('DEBUG: Final organization_id value: completed');


      }
    try {
      const { data, error } = await supabase
        .from('tasks')
        .insert([taskWithUserId])
        .select();

      if (error) {
        console.error('DEBUG: Error creating task:', error);
        throw error;
      }

      if (!data || data.length === 0) {
        console.error('DEBUG: No data returned after task creation');
        return null;
      }

      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Task created successfully: completed');
  }
      // Ensure chat is created with proper membership
      const createdTask = data[0] as Task;
      try {
        await ensureTaskChatExists(createdTask.id, createdTask.title);
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Chat created for task: completed');
  }
      } catch (error) {
        console.error('DEBUG: Error creating chat for task:', error);
        // Don't fail task creation if chat creation fails
      }

      return createdTask;
    } catch (error) {
      console.error('DEBUG: Exception during task creation:', error);
      return null;
    }
  },

  async getTasks(): Promise<Task[]> {
    // Get current user and their role
    const { data: userData } = await supabase.auth.getUser();
    const user = userData?.user;

    if (!user) {
      console.error('No authenticated user found');
      return [];
    }

    // Get user profile to determine role
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('role, account_type, organization_id')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return [];
    }

    const userRole = profileData?.role;
    const accountType = profileData?.account_type;
    const organizationId = profileData?.organization_id;

    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log('Fetching tasks for user with role: completed');

        }
    }

    let query = supabase
      .from('tasks')
      .select('*')
      .order('created_at', { ascending: false });

    // Apply strict filters based on user role and permissions
    if (accountType === 'supplier') {
      // Suppliers can only see public tasks in early stages (open, interest, questions, offer)
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('Supplier: Filtering to show only public tasks in early stages');
  }
      }
      query = query
        .eq('visibility', 'public')
        .in('status', ['open', 'interest', 'questions', 'offer']) // Only include tasks in early stages
        .not('status', 'in', ['assigned', 'in_progress', 'pending_payment', 'completed', 'confirmed', 'cancelled', 'approved']); // Explicitly exclude later stages
    } else if (userRole === 'maintenance' || userRole === 'support') {
      // Internal staff can see tasks assigned to them OR tasks they created within their organization
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('Internal staff: Filtering to show assigned tasks and tasks they created in their organization');
  }
      }
      query = query
        .or(`assigned_to.eq.${user.id},user_id.eq.${user.id}`)
        .eq('organization_id', organizationId); // Ensure organization isolation
    } else if (userRole === 'teacher') {
      // Teachers can see their own tasks and public tasks in their organization
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('Teacher: Filtering to show own tasks and public tasks in their organization');
  }
      }
      query = query
        .or(`user_id.eq.${user.id},visibility.eq.public`)
        .eq('organization_id', organizationId); // Ensure organization isolation
    } else if (userRole === 'admin') {
      // Admins can see all tasks in their organization
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('Admin: Can see all tasks in their organization');
  }
      }
      query = query.eq('organization_id', organizationId); // Ensure organization isolation
    } else {
      // Unknown role - return empty array for safety
      console.error('Unknown user role or account type:', userRole, accountType);
      return [];
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching tasks:', error);
      return [];
    }

    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log(`Retrieved ${data.length} tasks for user with role ${userRole}`);


        }
      // Log the visibility of retrieved tasks for debugging
      const visibilityCounts = data.reduce((counts, task) => {
        counts[task.visibility] = (counts[task.visibility] || 0) + 1;
        return counts;
      }, {});
      if (process.env.NODE_ENV === 'development') {
    console.log('Task visibility breakdown: completed');
  }
    }

    return data as Task[];
  },

  async getTaskById(id: string): Promise<Task | null> {
    // Get current user and their role
    const { data: userData } = await supabase.auth.getUser();
    const user = userData?.user;

    if (!user) {
      console.error('No authenticated user found');
      return null;
    }

    // Get user profile to determine role
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('role, account_type, organization_id')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return null;
    }

    const userRole = profileData?.role;
    const accountType = profileData?.account_type;

    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log(`Fetching task ${id} for user with role: ${userRole}, account type: ${accountType}`);

        }
    }

    // Fetch the task
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('id', id)
      .maybeSingle();

    if (error) {
      console.error('Error fetching task:', error);
      return null;
    }

    if (!data) {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log(`Task ${id} not found`);
  }
      }
      return null;
    }

    // Apply visibility rules
    const task = data as Task;
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Task ${id} has visibility: ${task.visibility}`);
  }
    }

    // Check if the user has permission to view this task
    let hasPermission = false;
    const userOrganizationId = profileData?.organization_id;

    // First check organization membership (except for suppliers who can see public tasks across organizations)
    if (accountType !== 'supplier' && task.organization_id && userOrganizationId !== task.organization_id) {
      if (process.env.NODE_ENV === 'development') {

        console.log(`User organization ${userOrganizationId} does not match task organization ${task.organization_id}`);

        }
      hasPermission = false;
    } else if (accountType === 'supplier') {
      // Suppliers can only see public tasks in early stages
      const earlyStages = ['open', 'interest', 'questions', 'offer'];
      const laterStages = ['assigned', 'in_progress', 'pending_payment', 'completed', 'confirmed', 'cancelled', 'approved'];
      hasPermission = task.visibility === 'public' &&
                     earlyStages.includes(task.status) &&
                     !laterStages.includes(task.status);
    } else if (userRole === 'maintenance' || userRole === 'support') {
      // Internal staff can see tasks assigned to them OR tasks they created within their organization
      hasPermission = (task.assigned_to === user.id || task.user_id === user.id) &&
                     task.organization_id === userOrganizationId;
    } else if (userRole === 'teacher') {
      // Teachers can see their own tasks (regardless of visibility) or public tasks within their organization
      hasPermission = (task.user_id === user.id || task.visibility === 'public') &&
                     task.organization_id === userOrganizationId;

      // Log teacher permission check for debugging (development only)
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('Teacher permission check: completed');

          }
      }
    } else if (userRole === 'admin') {
      // Admins can see all tasks within their organization
      hasPermission = task.organization_id === userOrganizationId;
    }

    if (!hasPermission) {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log(`User ${user.id} does not have permission to view task ${id}`);

          }
      }
      return null;
    }

    return task;
  },

  async getUserTasks(userId: string): Promise<Task[]> {
    // Get user profile to determine role
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Error fetching user profile for task permissions:', profileError);
      return [];
    }

    const userRole = profileData?.role;
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log(`Fetching tasks for user ${userId} with role ${userRole}`);

        }
    }

    // Fetch tasks created by the user OR assigned to the user
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .or(`user_id.eq.${userId},assigned_to.eq.${userId}`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user tasks:', error);
      return [];
    }

    // Log the tasks found for debugging (development only)
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log(`Found ${data?.length || 0} tasks for user ${userId}`);

        }
    }

    return data as Task[];
  },

  async getAdminReviewTasks(): Promise<Task[]> {
    // This function gets tasks that need admin review
    // Only admins should call this function (RLS will enforce this)
    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: getAdminReviewTasks called');
  }
    // Get current user to verify admin role
    const { data: userData } = await supabase.auth.getUser();
    const user = userData?.user;

    if (!user) {
      console.error('DEBUG: No authenticated user found');
      return [];
    }

    // Get user profile to verify admin role
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('role, organization_id')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('DEBUG: Error fetching user profile:', profileError);
      return [];
    }

    const userRole = profileData?.role;
    const organizationId = profileData?.organization_id;
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: User role: completed');

        }
    }

    if (userRole !== 'admin') {
      if (process.env.NODE_ENV === 'development') {
        console.error('DEBUG: User is not an admin');
      }
      return [];
    }

    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Fetching tasks for admin review');
  }
    }
    // Get only tasks that need admin review (open status AND admin visibility) within the admin's organization
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('status', 'open') // Only get open tasks
      .eq('visibility', 'admin') // Only get tasks with admin visibility
      .eq('organization_id', organizationId) // Only get tasks from admin's organization
      .order('created_at', { ascending: false });

    if (error) {
      console.error('DEBUG: Error fetching admin review tasks:', error);
      return [];
    }

    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Retrieved ${data.length} tasks needing admin review`);
  }
      // Log task details for debugging
      if (data && data.length > 0) {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Admin review tasks details:');
  }
        data.forEach(task => {
          if (process.env.NODE_ENV === 'development') {
    console.log(`Task ID: ${task.id}, Title: ${task.title}, Status: ${task.status}, Visibility: ${task.visibility}`);
  }
        });
      }
    }

    return data as Task[];
  },

  async getAdminAssignedTasks(): Promise<Task[]> {
    // This function gets tasks that have been assigned (for admin view)
    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: getAdminAssignedTasks called');
  }
    // Get current user to verify admin role
    const { data: userData } = await supabase.auth.getUser();
    const user = userData?.user;

    if (!user) {
      console.error('DEBUG: No authenticated user found');
      return [];
    }

    // Get user profile to verify admin role
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('role, organization_id')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('DEBUG: Error fetching user profile:', profileError);
      return [];
    }

    const userRole = profileData?.role;
    const organizationId = profileData?.organization_id;
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: User role: completed');

        }
    }

    if (userRole !== 'admin') {
      if (process.env.NODE_ENV === 'development') {
        console.error('DEBUG: User is not an admin');
      }
      return [];
    }

    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Fetching all assigned tasks');
  }
    }
    // Get all tasks that have been assigned or made public within the admin's organization
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .or('status.eq.assigned,visibility.eq.public,visibility.eq.internal') // Get assigned tasks or public/internal tasks
      .eq('organization_id', organizationId) // Only get tasks from admin's organization
      .order('created_at', { ascending: false });

    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Query for assigned tasks: completed');
  }
    }

    if (error) {
      console.error('DEBUG: Error fetching admin assigned tasks:', error);
      return [];
    }

    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Retrieved ${data.length} assigned tasks`);
  }
      // Log task details for debugging
      if (data && data.length > 0) {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Admin assigned tasks details:');
  }
        data.forEach(task => {
          if (process.env.NODE_ENV === 'development') {
    console.log(`Task ID: ${task.id}, Title: ${task.title}, Status: ${task.status}, Visibility: ${task.visibility}`);
  }
        });
      }
    }

    return data as Task[];
  },

  async getAssignedTasks(userId: string): Promise<Task[]> {
    // This function gets tasks assigned to a specific user

    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('assigned_to', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching assigned tasks:', error);
      return [];
    }

    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log(`Retrieved ${data.length} tasks assigned to user ${userId}`);

        }
    }
    return data as Task[];
  },

  async updateTask(id: string, updates: Partial<Task>): Promise<Task | null> {
    const { data, error } = await supabase
      .from('tasks')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating task:', error);
      return null;
    }

    return data as Task;
  },

  async assignTask(taskId: string, assignedTo: string, visibility: 'internal' | 'public', budget?: number): Promise<Task | null> {
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log(`Assigning task ${taskId} to user ${assignedTo || 'public'} with visibility ${visibility}`);

        }
    }

    // Get current user to validate organization membership
    const { data: userData } = await supabase.auth.getUser();
    const user = userData?.user;

    if (!user) {
      console.error('No authenticated user found for task assignment');
      return null;
    }

    // Get the task details including organization
    const { data: task, error: taskError } = await supabase
      .from('tasks')
      .select('id, organization_id, user_id')
      .eq('id', taskId)
      .single();

    if (taskError || !task) {
      console.error('Task not found for assignment:', taskError);
      return null;
    }

    // Get the current user's profile to check organization
    const { data: userProfile, error: userError } = await supabase
      .from('profiles')
      .select('organization_id, role, is_site_admin')
      .eq('id', user.id)
      .single();

    if (userError || !userProfile) {
      console.error('User profile not found for assignment:', userError);
      return null;
    }

    // Validate organization access (unless user is site admin)
    if (!userProfile.is_site_admin && task.organization_id && userProfile.organization_id !== task.organization_id) {
      console.error('Assignment denied: User not in task organization', {
        userOrg: userProfile.organization_id,
        taskOrg: task.organization_id,
        userId: user.id,
        taskId
      });
      return null;
    }

    // If assigning to a specific user (internal assignment), validate they're in the same organization
    if (visibility === 'internal' && assignedTo) {
      const { data: assigneeProfile, error: assigneeError } = await supabase
        .from('profiles')
        .select('organization_id, role')
        .eq('id', assignedTo)
        .single();

      if (assigneeError || !assigneeProfile) {
        console.error('Assignee profile not found:', assigneeError);
        return null;
      }

      // Validate assignee is in the same organization as the task
      if (task.organization_id && assigneeProfile.organization_id !== task.organization_id) {
        console.error('Assignment denied: Assignee not in task organization', {
          assigneeOrg: assigneeProfile.organization_id,
          taskOrg: task.organization_id,
          assigneeId: assignedTo,
          taskId
        });
        return null;
      }
    }

    // First, clean up any incorrect system messages
    const isInternalTask = visibility === 'internal';
    try {
      if (process.env.NODE_ENV === 'development') {

        console.log(`Cleaning up incorrect system messages for task ${taskId} (isInternalTask: ${isInternalTask})`);

        }
      await systemMessageService.cleanupIncorrectSystemMessages(taskId, isInternalTask);
    } catch (error) {
      console.error('Error cleaning up system messages:', error);
      // Continue anyway, the task update is more important
    }

    // For public assignments, we don't set assigned_to
    const updateData = visibility === 'public'
      ? {
          visibility: visibility,
          // For public visibility, we keep it as 'open' until a supplier offer is accepted
          status: 'open',
          // Clear any existing assignment
          assigned_to: null,
          // Update budget if provided
          ...(budget !== undefined ? { budget } : {}),
          // Ensure updated_at is set
          updated_at: new Date().toISOString()
        }
      : {
          assigned_to: assignedTo,
          visibility: visibility,
          // For internal assignments, set status to assigned
          status: 'assigned',
          // For internal assignments, set payment_status to not_required
          payment_status: 'not_required',
          // Ensure updated_at is set
          updated_at: new Date().toISOString()
        };

    if (process.env.NODE_ENV === 'development') {
    console.log('Update data: completed');
  }
    const { data, error } = await supabase
      .from('tasks')
      .update(updateData)
      .eq('id', taskId)
      .select();

    if (error) {
      console.error('Error assigning task:', error);
      return null;
    }

    // Create a system message about the visibility change
    try {
      // Use the system user ID for system messages
      const SYSTEM_USER_ID = '00000000-0000-0000-0000-000000000000';

      // Create an appropriate message based on the visibility
      const message = visibility === 'public'
        ? 'Task is now public and visible to suppliers.'
        : `Task has been assigned to an internal staff member.`;

      // Check if a similar system message already exists to prevent duplicates
      const { data: existingMessages } = await supabase
        .from('task_messages')
        .select('*')
        .eq('task_id', taskId)
        .eq('sender_id', SYSTEM_USER_ID)
        .eq('content', message)
        .is('thread_id', null);

      // Only insert if no duplicate exists
      if (!existingMessages || existingMessages.length === 0) {
        // Insert the system message
        await supabase
          .from('task_messages')
          .insert({
            task_id: taskId,
            sender_id: SYSTEM_USER_ID,
            content: message,
            thread_id: null // No thread for system messages about visibility changes
          });

        if (process.env.NODE_ENV === 'development') {
    console.log(`Created system message for task ${taskId} visibility change to ${visibility}`);
  }
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log(`System message already exists for task ${taskId} visibility change to ${visibility}`);
  }
      }
    } catch (error) {
      console.error('Error creating system message for visibility change:', error);
      // Continue anyway, the task update was successful
    }

    // For internal assignments, create a GetStream channel between the admin and the assigned staff
    if (visibility === 'internal' && assignedTo) {
      try {
        // Get the current user (admin) who is assigning the task
        const { data: userData } = await supabase.auth.getUser();
        const adminId = userData?.user?.id;

        if (adminId) {
          if (process.env.NODE_ENV === 'development') {
    console.log(`Creating GetStream channel for internal task ${taskId} between admin ${adminId} and staff ${assignedTo}`);
  }
          // Import the necessary functions
          const { createOrUpdateTaskChannel } = await import('@/integrations/getstream/client');

          // Get the task title
          const { data: taskData } = await supabase
            .from('tasks')
            .select('title')
            .eq('id', taskId)
            .single();

          const taskTitle = taskData?.title || 'Internal Task';

          // Create or update the GetStream channel
          const channel = await createOrUpdateTaskChannel(
            taskId,
            taskTitle,
            [adminId, assignedTo],
            true
          );

          // Add a system message to the channel
          await channel.sendMessage({
            text: 'This internal maintenance task has been assigned to you. You can use this chat to communicate with the administrator.',
            user_id: adminId,
            type: 'system',
          });

          if (process.env.NODE_ENV === 'development') {
    console.log('GetStream channel created successfully for internal task: completed');
  }
        }
      } catch (error) {
        console.error('Error creating chat thread for internal task:', error);
        // Continue anyway, the task assignment was successful
      }
    }

    // Update chat membership after assignment
    try {
      const assigneeId = visibility === 'public' ? null : assignedTo;
      // Pass the current user as the assigner since they're performing the assignment
      await updateTaskChatMembership(taskId, assigneeId, user.id);
      if (process.env.NODE_ENV === 'development') {
    console.log('Chat membership updated for task assignment: completed');
  }
    } catch (error) {
      console.error('Error updating chat membership:', error);
      // Don't fail the assignment if chat update fails
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('Task assigned successfully: completed');
  }
    return data[0] as Task;
  },

  async deleteTask(id: string): Promise<boolean> {
    try {
      // First, get the task to retrieve its GetStream channel ID
      const { data: task, error: fetchError } = await supabase
        .from('tasks')
        .select('getstream_channel_id, chat_migrated_to_stream')
        .eq('id', id)
        .maybeSingle();

      if (fetchError) {
        console.error('Error fetching task before deletion:', fetchError);
        return false;
      }

      // Delete the task from the database
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting task:', error);
        return false;
      }

      // If the task had a GetStream channel, delete it
      if (task?.chat_migrated_to_stream && task?.getstream_channel_id) {
        try {
          if (process.env.NODE_ENV === 'development') {
    console.log(`Deleting GetStream channel for task ${id}: ${task.getstream_channel_id}`);
  }
          // Use the GetStream API to delete the channel
          // Use the getStreamApiUrl utility to ensure the correct URL is used in all environments
          const apiUrl = getStreamApiUrl(`/channels/${task.getstream_channel_id}`);
          if (process.env.NODE_ENV === 'development') {
    console.log(`Calling GetStream API to delete channel at: ${apiUrl}`);
  }
          const response = await fetch(apiUrl, {
            method: 'DELETE',
          });

          if (!response.ok) {
            console.error(`Failed to delete GetStream channel: ${response.status} ${response.statusText}`);
            // We don't return false here because the task was successfully deleted
            // This is just cleanup of the chat data
          } else {
            if (process.env.NODE_ENV === 'development') {
    console.log(`Successfully deleted GetStream channel for task ${id}`);
  }
          }
        } catch (channelError) {
          console.error('Error deleting GetStream channel:', channelError);
          // We don't return false here because the task was successfully deleted
        }
      }

      return true;
    } catch (error) {
      console.error('Unexpected error in deleteTask:', error);
      return false;
    }
  },

  async createOffer(offer: NewOffer): Promise<Offer | null> {
    if (process.env.NODE_ENV === 'development') {
    console.log("createOffer called with:", offer);
  }
    const { data: userData } = await supabase.auth.getUser();
    const user = userData?.user;

    if (!user) {
      if (process.env.NODE_ENV === 'development') {

        console.log("createOffer: No authenticated user found");

        }
      return null;
    }

    const offerWithUserId = {
      ...offer,
      user_id: user.id,
      status: offer.status || 'awaiting',
    };

    if (process.env.NODE_ENV === 'development') {


      console.log("createOffer: Submitting offer to database:", offerWithUserId);



      }
    const { data, error } = await supabase
      .from('offers')
      .insert([offerWithUserId])
      .select();

    if (error) {
      console.error('Error creating offer:', error);
      return null;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log("createOffer: Successfully created offer:", data[0]);
  }
    return data[0] as Offer;
  },

  async getOffersByTaskId(taskId: string): Promise<Offer[]> {
    if (process.env.NODE_ENV === 'development') {
    console.log("getOffersByTaskId called for taskId:", taskId);
  }
    const { data, error } = await supabase
      .from('offers')
      .select('*')
      .eq('task_id', taskId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching offers:', error);
      return [];
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`getOffersByTaskId: Retrieved ${data.length} offers for task ${taskId}`);
  }
    return data as Offer[];
  },

  async getUserOffers(userId: string): Promise<Offer[]> {
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log("getUserOffers called for userId:", userId);

        }
    }

    const { data, error } = await supabase
      .from('offers')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user offers:', error);
      return [];
    }

    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log(`getUserOffers: Retrieved ${data.length} offers for user ${userId}`);

        }
    }
    return data as Offer[];
  },

  async updateOfferStatus(id: string, status: 'awaiting' | 'accepted' | 'rejected'): Promise<Offer | null> {
    if (process.env.NODE_ENV === 'development') {
    console.log(`updateOfferStatus: Updating offer ${id} to status '${status}'`);
  }
    try {
      // First check if the offer exists
      const { data: existingOffer, error: checkError } = await supabase
        .from('offers')
        .select('*')
        .eq('id', id)
        .maybeSingle();

      if (checkError) {
        console.error('Error checking offer existence:', checkError);
        return null;
      }

      if (!existingOffer) {
        console.error('No offer found with ID:', id);
        return null;
      }

      // If the offer already has the requested status, return it without making changes
      if (existingOffer.status === status) {
        if (process.env.NODE_ENV === 'development') {
    console.log(`Offer ${id} already has status '${status}'`);
  }
        return existingOffer as Offer;
      }

      // If the offer exists, update its status - using proper case for status value
      // Supabase sometimes has issues with status values, ensure it's a valid string
      const statusValue = status === 'awaiting' ? 'awaiting' :
                          status === 'accepted' ? 'accepted' : 'rejected';

      if (process.env.NODE_ENV === 'development') {
    console.log(`Updating offer ${id} status to '${statusValue}'`);
  }
      const { data, error } = await supabase
        .from('offers')
        .update({ status: statusValue })
        .eq('id', id)
        .select();

      if (error) {
        console.error('Error updating offer status:', error);
        return null;
      }

      if (!data || data.length === 0) {
        console.error('Offer update returned no data for ID:', id);
        // In this case, let's fetch the offer again to verify if the update worked
        const { data: verifyData, error: verifyError } = await supabase
          .from('offers')
          .select('*')
          .eq('id', id)
          .maybeSingle();

        if (verifyError || !verifyData) {
          console.error('Error verifying offer update:', verifyError);
          return null;
        }

        if (process.env.NODE_ENV === 'development') {
    console.log(`Verified offer status after update:`, verifyData);
  }
        return verifyData as Offer;
      }

      if (process.env.NODE_ENV === 'development') {
    console.log(`updateOfferStatus: Successfully updated offer ${id} to status '${status}'`, data[0]);
  }
      return data[0] as Offer;
    } catch (error) {
      console.error('Exception in updateOfferStatus:', error);
      return null;
    }
  },

  async deleteOffer(id: string): Promise<boolean> {
    if (process.env.NODE_ENV === 'development') {
    console.log(`deleteOffer: Deleting offer ${id}`);
  }
    const { error } = await supabase
      .from('offers')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting offer:', error);
      return false;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`deleteOffer: Successfully deleted offer ${id}`);
  }
    return true;
  },

  async acceptOffer(taskId: string, offerId: string): Promise<boolean> {
    if (process.env.NODE_ENV === 'development') {
    console.log(`Starting acceptOffer operation - Task ID: ${taskId}, Offer ID: ${offerId}`);
  }
    try {
      // First check if the offer exists and belongs to the task
      const { data: existingOffer, error: checkOfferError } = await supabase
        .from('offers')
        .select('*')
        .eq('id', offerId)
        .eq('task_id', taskId) // Make sure the offer belongs to this task
        .maybeSingle();

      if (checkOfferError) {
        console.error('Error checking offer existence:', checkOfferError);
        return false;
      }

      if (!existingOffer) {
        console.error('No offer found with ID:', offerId, 'for task:', taskId);
        return false;
      }

      // Check if the task exists and is in 'open' status
      const { data: existingTask, error: checkTaskError } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .maybeSingle();

      if (checkTaskError) {
        console.error('Error checking task existence:', checkTaskError);
        return false;
      }

      if (!existingTask) {
        console.error('No task found with ID:', taskId);
        return false;
      }

      // Check if the task is in the correct state
      if (existingTask.status !== 'open') {
        console.error(`Task is not in 'open' status. Current status: ${existingTask.status}`);
        return false;
      }

      // First update the offer to accepted status
      if (process.env.NODE_ENV === 'development') {
    console.log(`acceptOffer: Updating offer ${offerId} status to 'accepted'`);
  }
      const { data: offerData, error: offerError } = await supabase
        .from('offers')
        .update({ status: 'accepted' })
        .eq('id', offerId)
        .select()
        .single(); // Use single() instead of getting an array

      if (offerError || !offerData) {
        console.error('Error updating offer status:', offerError);
        if (process.env.NODE_ENV === 'development') {
    console.log('Received data: completed');
  }
        return false;
      }

      if (process.env.NODE_ENV === 'development') {
    console.log('Offer updated successfully:', offerData);
  }
      // Then update the task to assigned status and set the assigned_to field
      if (process.env.NODE_ENV === 'development') {

        console.log(`acceptOffer: Updating task ${taskId} status to 'assigned' and assigning to supplier ${existingOffer.user_id}`);

        }
      const { data: taskData, error: taskError } = await supabase
        .from('tasks')
        .update({
          status: 'assigned',
          assigned_to: existingOffer.user_id,
          // Ensure visibility is public since this is a supplier assignment
          visibility: 'public'
        })
        .eq('id', taskId)
        .select()
        .single(); // Use single() instead of getting an array

      if (taskError || !taskData) {
        console.error('Error updating task status:', taskError);
        if (process.env.NODE_ENV === 'development') {
    console.log('Received data: completed');
  }
        // Try to revert the offer status change
        await supabase
          .from('offers')
          .update({ status: 'awaiting' })
          .eq('id', offerId);

        return false;
      }

      if (process.env.NODE_ENV === 'development') {
    console.log('Task status updated to assigned: completed');
  }
      // Update chat membership to include the supplier
      try {
        await updateTaskChatMembership(taskId, existingOffer.user_id);
        if (process.env.NODE_ENV === 'development') {
    console.log('Chat membership updated for offer acceptance: completed');
  }
      } catch (error) {
        console.error('Error updating chat membership:', error);
        // Don't fail the offer acceptance if chat update fails
      }

      // Finally reject all other offers for this task
      if (process.env.NODE_ENV === 'development') {
    console.log(`acceptOffer: Rejecting all other offers for task ${taskId}`);
  }
      const { data: rejectedOffers, error: rejectError } = await supabase
        .from('offers')
        .update({ status: 'rejected' })
        .eq('task_id', taskId)
        .neq('id', offerId)
        .select();

      if (rejectError) {
        console.error('Warning - error rejecting other offers:', rejectError);
        if (process.env.NODE_ENV === 'development') {
    console.log('Received data: completed');
  }
        // We continue despite this error as the main operation succeeded
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log(`All other offers rejected: ${rejectedOffers?.length || 0} offers`);
  }
      }

      return true;
    } catch (error) {
      console.error('Exception in acceptOffer:', error);
      return false;
    }
  }
};
