/**
 * GetStream Chat CSS
 *
 * Custom styles for the GetStream chat component.
 */

/* Basic GetStream styles */
.str-chat {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.str-chat__container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.str-chat__main-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.str-chat__message-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.str-chat__message {
  margin-bottom: 10px;
}

.str-chat__input {
  padding: 10px;
  border-top: 1px solid #f0f0f0;
}

/* System message styles */
.str-chat__message--system {
  margin: 10px auto;
  padding: 8px 16px;
  background-color: rgba(0, 119, 255, 0.05);
  border-radius: 16px;
  max-width: 80%;
  text-align: center;
}

.str-chat__message--system .str-chat__message-text {
  font-size: 0.85em;
  color: #0077ff;
  font-style: italic;
}

.str-chat__message-text-inner {
  max-width: 100%;
  word-break: break-word;
}

/* Message input styles */
.str-chat__messaging-input {
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 10px;
}

.str-chat__messaging-input .str-chat__input-flat {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.str-chat__messaging-input .str-chat__input-flat:focus-within {
  border-color: #0077ff;
}

.str-chat__messaging-input .str-chat__send-button {
  background-color: #0077ff;
  color: white;
  border-radius: 8px;
}

/* Channel header styles */
.str-chat__header-livestream {
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 10px 20px;
}

.str-chat__header-livestream .str-chat__header-livestream-left {
  display: flex;
  align-items: center;
}

.str-chat__header-livestream .str-chat__header-livestream-title {
  font-weight: 600;
  font-size: 16px;
  margin-left: 10px;
}

/* Thread styles */
.str-chat__thread {
  background-color: #f9f9f9;
  border-left: 1px solid #f0f0f0;
}

.str-chat__thread .str-chat__thread-header {
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 10px 20px;
}

/* Image upload button styles */
.str-chat__input-flat-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.str-chat__input-flat--textarea-wrapper {
  flex: 1;
  margin-right: 8px;
}

.str-chat__image-upload-button {
  color: #0077ff;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
}

.str-chat__image-upload-button:hover {
  background-color: rgba(0, 119, 255, 0.1);
}

/* Fix for MessageInput component */
.str-chat__input-flat .str-chat__textarea {
  width: 100%;
  padding: 10px;
  border: none;
  background: transparent;
}

.str-chat__input-container {
  width: 100%;
  padding: 0;
}

/* Image attachment styles */
.str-chat__attachment {
  max-width: 100%;
  margin: 8px 0;
}

.str-chat__attachment--image {
  border-radius: 8px;
  overflow: hidden;
  max-width: 300px;
  cursor: pointer;
}

.str-chat__attachment--image img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 8px;
}

.str-chat__attachment--image:hover {
  opacity: 0.9;
  transform: scale(1.02);
  transition: all 0.2s ease;
}

/* File upload area styles */
.str-chat__file-upload-button {
  color: #0077ff;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
}

.str-chat__file-upload-button:hover {
  background-color: rgba(0, 119, 255, 0.1);
}

/* Drag and drop styles */
.str-chat__dropzone-container {
  position: relative;
}

.str-chat__dropzone {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 119, 255, 0.1);
  border: 2px dashed #0077ff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.str-chat__dropzone-text {
  color: #0077ff;
  font-weight: 600;
  text-align: center;
}

/* Mobile styles */
@media (max-width: 768px) {
  .str-chat__container {
    height: calc(100vh - 120px);
  }

  .str-chat__messaging-input {
    padding: 5px;
  }

  .str-chat__header-livestream {
    padding: 5px 10px;
  }

  .str-chat__image-upload-button {
    padding: 6px;
  }

  .str-chat__attachment--image {
    max-width: 250px;
  }
}
