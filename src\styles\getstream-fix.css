/**
 * GetStream Chat Message Width Fix
 *
 * This CSS fixes the issue with message bubbles being squashed and wrapping
 * even though there's plenty of space.
 */

/* Remove the surrounding blue backgrounds */
.str-chat__li {
  background: transparent !important;
}

.str-chat__message-simple {
  background: transparent !important;
}

/* Remove the message bubble background */
.str-chat__message-simple-text-inner {
  background: transparent !important;
  border: none !important;
  max-width: 100% !important;
  padding: 0 !important;
}

/* Remove the message bubble background for own messages */
.str-chat__message-simple--me .str-chat__message-simple-text-inner {
  background: transparent !important;
  border: none !important;
}

/* Increase the maximum width of message attachments */
.str-chat__attachment {
  --str-chat__attachment-max-width: 100% !important;
  max-width: 100% !important;
}

/* Ensure message text doesn't wrap unnecessarily */
.str-chat__message-text {
  white-space: normal;
  word-break: break-word;
}

/* Ensure the message container has enough space */
.str-chat__message-simple {
  max-width: 100%;
  padding: 8px 0;
}

/* Ensure the message list has proper spacing */
.str-chat__list {
  padding: 20px;
  background: white !important;
}

/* Remove any additional backgrounds */
.str-chat__message-inner {
  background: transparent !important;
}

/* Fix the message bubble alignment */
.str-chat__message-bubble {
  max-width: 100% !important;
}
