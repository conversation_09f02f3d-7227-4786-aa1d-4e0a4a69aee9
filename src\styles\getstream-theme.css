/**
 * GetStream Chat Theme - Elegant Edition
 *
 * A sophisticated, elegant theme for GetStream chat components
 * Based on GetStream documentation: https://getstream.io/chat/docs/sdk/react/customization/css_and_theming/
 */

:root {
  --primary-color: #4f46e5;
  --primary-light: #818cf8;
  --primary-dark: #3730a3;
  --secondary-color: #f3f4f6;
  --text-color: #1f2937;
  --text-light: #6b7280;
  --text-lighter: #9ca3af;
  --text-white: #ffffff;
  --border-color: #e5e7eb;
  --border-radius: 0.5rem;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Container styles */
.getstream-chat-container {
  height: 600px;
  max-height: 75vh;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  background-color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  font-family: var(--font-sans);
  transition: box-shadow 0.3s ease;
}

.getstream-chat-container:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

/* Chat container */
.str-chat {
  height: 100%;
  background-color: white;
}

.str-chat__container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Channel header */
.str-chat__header {
  background-color: white;
  border-bottom: 1px solid #e2e8f0;
  height: 70px;
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
}

.str-chat__header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    rgba(99, 102, 241, 0) 0%,
    rgba(99, 102, 241, 0.3) 50%,
    rgba(99, 102, 241, 0) 100%
  );
}

.str-chat__header-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 1.125rem;
  letter-spacing: -0.01em;
}

.str-chat__header-subtitle {
  color: #64748b;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
}

.str-chat__header-subtitle::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #10b981;
  border-radius: 50%;
  margin-right: 6px;
}

/* Message list */
.str-chat__list {
  background-color: #f8fafc;
  background-image:
    radial-gradient(#e2e8f0 1px, transparent 1px),
    radial-gradient(#e2e8f0 1px, transparent 1px);
  background-size: 40px 40px;
  background-position: 0 0, 20px 20px;
  background-attachment: fixed;
  padding: 1.5rem;
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.5) transparent;
}

.str-chat__list::-webkit-scrollbar {
  width: 6px;
}

.str-chat__list::-webkit-scrollbar-track {
  background: transparent;
}

.str-chat__list::-webkit-scrollbar-thumb {
  background-color: rgba(148, 163, 184, 0.5);
  border-radius: 20px;
}

.str-chat__list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(148, 163, 184, 0.8);
}

/* Message bubbles */
.str-chat__message-simple {
  margin-bottom: 1.25rem;
  display: flex;
  align-items: flex-end;
  position: relative;
}

.str-chat__message-simple-text-inner {
  background-color: #f0f2f5;
  border-radius: 18px;
  padding: 0.75rem 1rem;
  color: var(--text-color);
  font-size: 0.9375rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  line-height: 1.5;
  max-width: 80%;
  position: relative;
  transition: all var(--transition-fast);
  border-bottom-left-radius: 4px;
}

.str-chat__message-simple-text-inner:hover {
  background-color: #e8eaed;
}

.str-chat__message-simple--me {
  justify-content: flex-end;
}

.str-chat__message-simple--me .str-chat__message-simple-text-inner {
  background-color: #5046e5;
  background-image: linear-gradient(135deg, #5046e5 0%, #6366f1 100%);
  color: var(--text-white);
  border: none;
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 4px;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
}

.str-chat__message-simple--me .str-chat__message-simple-text-inner:hover {
  background-image: linear-gradient(135deg, #4338ca 0%, #5046e5 100%);
  box-shadow: 0 3px 6px rgba(79, 70, 229, 0.3);
}

/* User avatar */
.str-chat__avatar {
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0.75rem;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.str-chat__avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.str-chat__message-simple--me .str-chat__avatar {
  margin-right: 0;
  margin-left: 0.75rem;
}

/* Avatar fallback styling */
.str-chat__avatar-fallback {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  font-size: 14px;
}

/* Message metadata */
.str-chat__message-data {
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}

.str-chat__message-simple-name {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.875rem;
  margin-right: 0.5rem;
}

.str-chat__message-simple-timestamp {
  color: var(--text-lighter);
  font-size: 0.75rem;
}

/* Message actions */
.str-chat__message-simple-actions {
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.str-chat__message-simple:hover .str-chat__message-simple-actions {
  opacity: 1;
}

.str-chat__message-actions-box {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
}

/* Message input */
.str-chat__input {
  background-color: white;
  border-top: 1px solid var(--border-color);
  padding: 1rem 1.5rem;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
  position: relative;
}

.str-chat__input-wrapper {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 24px;
  padding: 0.5rem 0.75rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.str-chat__input-wrapper:focus-within {
  border-color: #818cf8;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
  background-color: white;
}

.str-chat__textarea {
  color: #334155;
  font-size: 0.9375rem;
  padding: 0.625rem 0.75rem;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  font-family: var(--font-sans);
  line-height: 1.5;
  flex: 1;
  min-height: 24px;
  max-height: 120px;
}

.str-chat__textarea::placeholder {
  color: #94a3b8;
  font-style: italic;
}

.str-chat__input-emojiselect {
  color: #94a3b8;
  transition: all 0.2s ease;
  margin: 0 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.str-chat__input-emojiselect:hover {
  color: #6366f1;
  background-color: rgba(99, 102, 241, 0.1);
}

.str-chat__send-button {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  border-radius: 50%;
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
  margin-left: 0.5rem;
  transform: translateY(0);
}

.str-chat__send-button:hover {
  background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
  box-shadow: 0 4px 6px rgba(79, 70, 229, 0.3);
  transform: translateY(-1px);
}

.str-chat__send-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(79, 70, 229, 0.3);
}

/* Thread */
.str-chat__thread {
  background-color: white;
  border-left: 1px solid var(--border-color);
  width: 300px;
}

.str-chat__thread-header {
  background-color: white;
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 1.5rem;
  box-shadow: var(--shadow-sm);
}

.str-chat__thread-header-title {
  font-weight: 600;
  color: var(--text-color);
  font-size: 1rem;
}

.str-chat__thread-header-close {
  color: var(--text-light);
  transition: color var(--transition-fast);
}

.str-chat__thread-header-close:hover {
  color: var(--text-color);
}

/* System messages */
.str-chat__message--system {
  color: #64748b;
  font-style: italic;
  font-size: 0.8125rem;
  text-align: center;
  margin: 1.25rem auto;
  padding: 0.5rem 1.25rem;
  background-color: rgba(241, 245, 249, 0.8);
  border-radius: 12px;
  display: inline-block;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(226, 232, 240, 0.8);
  max-width: 80%;
}

.str-chat__message--system::before,
.str-chat__message--system::after {
  content: '';
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg,
    rgba(203, 213, 225, 0) 0%,
    rgba(203, 213, 225, 0.8) 50%,
    rgba(203, 213, 225, 0) 100%
  );
  width: 100px;
  top: 50%;
}

.str-chat__message--system::before {
  right: 100%;
  margin-right: 15px;
}

.str-chat__message--system::after {
  left: 100%;
  margin-left: 15px;
}

/* Date separators */
.str-chat__date-separator {
  margin: 2rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.str-chat__date-separator-line {
  background: linear-gradient(90deg,
    rgba(203, 213, 225, 0) 0%,
    rgba(203, 213, 225, 0.8) 50%,
    rgba(203, 213, 225, 0) 100%
  );
  height: 1px;
  width: 100%;
  position: absolute;
  z-index: 1;
}

.str-chat__date-separator-date {
  background-color: white;
  color: #64748b;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.875rem;
  border-radius: 12px;
  position: relative;
  z-index: 2;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  letter-spacing: 0.025em;
}

/* Typing indicator */
.str-chat__typing-indicator {
  color: #64748b;
  font-size: 0.8125rem;
  font-style: italic;
  padding: 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.str-chat__typing-indicator__dot {
  background-color: #6366f1;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  animation: typing-dot 1.4s infinite ease-in-out both;
}

.str-chat__typing-indicator__dot:nth-child(1) {
  animation-delay: -0.32s;
}

.str-chat__typing-indicator__dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing-dot {
  0%, 80%, 100% { transform: scale(0.6); opacity: 0.6; }
  40% { transform: scale(1); opacity: 1; }
}

/* Emoji picker */
.emoji-mart {
  border-radius: 12px;
  border-color: #e2e8f0;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.emoji-mart-category-label {
  font-size: 0.875rem;
  color: #334155;
  font-weight: 600;
  padding: 0.5rem 0;
}

.emoji-mart-search {
  margin: 0.5rem;
}

.emoji-mart-search input {
  border-radius: 8px;
  border-color: #e2e8f0;
  padding: 0.5rem 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.emoji-mart-search input:focus {
  border-color: #818cf8;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
  outline: none;
}

/* Reactions */
.str-chat__reaction-list {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  padding: 0.25rem;
  overflow: hidden;
  transform-origin: top center;
  animation: reaction-list-appear 0.2s ease-out forwards;
}

@keyframes reaction-list-appear {
  from { transform: translateY(-8px) scale(0.95); opacity: 0; }
  to { transform: translateY(0) scale(1); opacity: 1; }
}

.str-chat__reaction-list--reverse {
  background-color: white;
  transform-origin: bottom center;
}

.str-chat__reaction {
  border-radius: 8px;
  padding: 0.25rem 0.5rem;
  margin: 0.125rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.str-chat__reaction:hover {
  background-color: #f1f5f9;
  transform: scale(1.05);
}

.str-chat__reaction--selected {
  background-color: rgba(99, 102, 241, 0.1);
}

.str-chat__reaction--selected:hover {
  background-color: rgba(99, 102, 241, 0.15);
}

.str-chat__reaction-count {
  color: #334155;
  font-weight: 600;
  font-size: 0.75rem;
  min-width: 16px;
  text-align: center;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .getstream-chat-container {
    height: 500px;
    max-height: 70vh;
  }

  .str-chat__header {
    height: 60px;
    padding: 0 1rem;
  }

  .str-chat__list {
    padding: 1rem;
  }

  .str-chat__input {
    padding: 0.75rem 1rem;
  }

  .str-chat__thread {
    width: 100%;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
  }
}

@media (max-width: 640px) {
  .getstream-chat-container {
    height: 450px;
    max-height: 65vh;
  }

  .str-chat__header {
    height: 50px;
  }

  .str-chat__message-simple-text-inner {
    font-size: 0.875rem;
  }
}
