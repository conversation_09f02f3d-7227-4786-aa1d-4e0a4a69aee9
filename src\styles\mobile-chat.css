/**
 * Mobile Chat Styles
 * 
 * Custom styles for the mobile chat interface using GetStream
 */

/* Fix for mobile height issues */
.str-chat {
  height: 100%;
  max-height: 100%;
}

/* Message list container */
.str-chat__list {
  padding: 10px;
}

/* Message bubbles */
.str-chat__message-simple {
  margin-bottom: 8px;
}

.str-chat__message-simple__content {
  border-radius: 18px;
  padding: 8px 12px;
  max-width: 80%;
}

.str-chat__message-simple--me .str-chat__message-simple__content {
  background-color: #3b82f6;
  color: white;
}

.str-chat__message-simple:not(.str-chat__message-simple--me) .str-chat__message-simple__content {
  background-color: #f3f4f6;
  color: #1f2937;
}

/* System messages */
.str-chat__message--system {
  margin: 8px 0;
  text-align: center;
}

.str-chat__message--system .str-chat__message-text-inner {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
  background-color: #f9fafb;
  padding: 4px 8px;
  border-radius: 12px;
  display: inline-block;
}

/* Message input */
.mobile-message-input {
  background-color: white;
  border-top: 1px solid #e5e7eb;
  padding: 8px;
}

.mobile-message-input form {
  display: flex;
  align-items: center;
}

.mobile-message-input input {
  flex: 1;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  padding: 8px 12px;
  font-size: 14px;
  outline: none;
}

.mobile-message-input input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Message timestamps */
.str-chat__message-simple-timestamp {
  font-size: 10px;
  color: #9ca3af;
  margin-top: 2px;
}

/* Avatar styles */
.str-chat__avatar {
  width: 32px;
  height: 32px;
}

/* Loading indicator */
.str-chat__loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* Channel header */
.str-chat__header {
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
  background-color: white;
}

.str-chat__header-title {
  font-size: 16px;
  font-weight: 600;
}

/* Typing indicator */
.str-chat__typing-indicator {
  font-size: 12px;
  color: #6b7280;
  padding: 4px 8px;
}

/* Message actions */
.str-chat__message-simple__actions {
  right: 0;
  top: 0;
}

/* Emoji picker */
.str-chat__emoji-picker {
  position: absolute;
  bottom: 60px;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: white;
  border-top: 1px solid #e5e7eb;
  max-height: 200px;
  overflow-y: auto;
}

/* Thread */
.str-chat__thread {
  background-color: white;
  border-left: 1px solid #e5e7eb;
}

/* Offline indicator */
.offline-indicator {
  background-color: #fef3c7;
  color: #92400e;
  padding: 4px 8px;
  font-size: 12px;
  text-align: center;
  border-bottom: 1px solid #fcd34d;
}

/* Error indicator */
.error-indicator {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 4px 8px;
  font-size: 12px;
  text-align: center;
  border-bottom: 1px solid #fca5a5;
}

/* Responsive adjustments for small screens */
@media (max-width: 640px) {
  .str-chat__message-simple__content {
    max-width: 85%;
  }
  
  .str-chat__list {
    padding: 8px;
  }
  
  .str-chat__avatar {
    width: 28px;
    height: 28px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .str-chat__message-simple:not(.str-chat__message-simple--me) .str-chat__message-simple__content {
    background-color: #374151;
    color: #f3f4f6;
  }
  
  .str-chat__message--system .str-chat__message-text-inner {
    background-color: #1f2937;
    color: #9ca3af;
  }
  
  .str-chat__header,
  .mobile-message-input {
    background-color: #111827;
    border-color: #374151;
  }
  
  .mobile-message-input input {
    background-color: #1f2937;
    border-color: #374151;
    color: #f3f4f6;
  }
  
  .str-chat__header-title {
    color: #f3f4f6;
  }
}
