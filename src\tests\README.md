# GetStream Chat Testing Suite

This directory contains a comprehensive testing suite for the GetStream chat implementation in ClassTasker Connect. The tests cover both the main application and the PWA implementation.

## Test Scripts

### 1. Comprehensive Test Suite
Tests all aspects of the GetStream implementation:
- Token generation
- User creation
- Channel creation
- Message sending
- System messages
- Channel queries

```bash
node src/tests/getstream-comprehensive-test.js
```

### 2. PWA Implementation Test
Tests the PWA-specific implementation of GetStream chat:
- Simplified chat component
- PWA-specific hooks and utilities
- Error handling and fallback mechanisms
- Offline behavior

```bash
node src/tests/getstream-pwa-test.js
```

### 3. API Endpoints Test
Tests the GetStream API endpoints:
- `/api/getstream/token`
- `/api/getstream/channels`
- `/api/getstream/system-message`

```bash
node src/tests/getstream-api-test.js
```

### 4. PWA UI Component Test
Tests the PWA GetStream chat UI components:
- `PWASimplifiedChat`
- `PWAGetStreamChatView`
- Error boundaries and fallbacks

```bash
npx vitest src/tests/getstream-pwa-ui-test.jsx
```

## Running the Vercel MCP Server

The Vercel MCP (Multi-Cloud Platform) server is used for local development and testing of the GetStream chat implementation. It provides a local environment that closely matches the production environment on Vercel.

### Prerequisites

1. Install the Vercel CLI:
```bash
npm install -g vercel
```

2. Link your project to Vercel:
```bash
vercel link
```

3. Pull environment variables:
```bash
vercel env pull
```

### Starting the Server

1. Start the Vercel MCP server:
```bash
npm run vercel-dev
```

2. The server will be available at `http://localhost:3000`.

## Testing with Chrome DevTools and USB Debugging

To test the PWA implementation on a mobile device:

1. Enable USB debugging on your Android device:
   - Go to **Settings** > **About phone**
   - Tap on **Build number** 7 times to enable Developer options
   - Go back to **Settings** > **System** > **Developer options**
   - Enable **USB debugging**
   - Connect your device to your computer with a USB cable
   - Accept the USB debugging permission prompt on your device

2. Connect Chrome DevTools to your device:
   - Open Chrome on your computer
   - Navigate to `chrome://inspect/#devices` in the address bar
   - Make sure "Discover USB devices" is checked
   - Your connected device should appear in the list

3. Test the PWA:
   - Open the ClassTasker PWA on your device
   - In Chrome on your computer, click on **inspect** next to the PWA
   - This will open Chrome DevTools connected to your PWA

4. What to look for:
   - Check console logs for any errors or warnings
   - Monitor network requests to the GetStream API
   - Analyze performance using the Performance tab
   - Check the Application tab for service worker status and cache storage

## Troubleshooting

### Common Issues

1. **Token Generation Fails**:
   - Check that the GetStream API key and secret are correctly configured in the environment variables
   - Verify that the user ID is being passed correctly

2. **Channel Creation Fails**:
   - Ensure that the task ID and members are being passed correctly
   - Check that the users exist in GetStream

3. **PWA Shows Blank Screen**:
   - Check the console for errors
   - Verify that the service worker is correctly registered
   - Clear the cache and reload the PWA

4. **Messages Not Loading**:
   - Check the network requests to the GetStream API
   - Verify that the channel ID is correct
   - Check that the user has permission to access the channel

### Debugging Tips

1. Use the `console.log` statements in the PWA components to debug issues
2. Check the network requests in Chrome DevTools
3. Use the Application tab to inspect the service worker and cache storage
4. Clear the cache and reload the PWA if you encounter issues

## Environment Variables

The following environment variables are required for the GetStream chat implementation:

```
# GetStream Configuration (Client-side)
VITE_GETSTREAM_API_KEY=your-api-key

# GetStream Configuration (Server-side)
GETSTREAM_API_KEY=your-api-key
GETSTREAM_API_SECRET=your-api-secret
```

These should be configured in both the `.env.local` file for local development and in the Vercel dashboard for production.
