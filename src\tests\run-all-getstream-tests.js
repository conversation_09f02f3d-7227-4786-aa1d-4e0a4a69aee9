/**
 * Run All GetStream Tests
 * 
 * This script runs all the GetStream tests in sequence.
 * 
 * Run with: node src/tests/run-all-getstream-tests.js
 */

const { spawn } = require('child_process');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Test scripts to run
const tests = [
  {
    name: 'Comprehensive Test Suite',
    command: 'node',
    args: ['src/tests/getstream-comprehensive-test.js'],
  },
  {
    name: 'PWA Implementation Test',
    command: 'node',
    args: ['src/tests/getstream-pwa-test.js'],
  },
  {
    name: 'API Endpoints Test',
    command: 'node',
    args: ['src/tests/getstream-api-test.js'],
  },
];

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  total: tests.length,
};

// Helper function to run a command
function runCommand(command, args) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });
    
    child.on('error', (error) => {
      reject(error);
    });
  });
}

// Run all tests in sequence
async function runAllTests() {
  if (process.env.NODE_ENV === 'development') {
    console.log(`${colors.bright}${colors.magenta}=== Running All GetStream Tests ===${colors.reset}`);
    }
  if (process.env.NODE_ENV === 'development') {
    console.log(`Total tests: ${tests.length}`);
    }
  if (process.env.NODE_ENV === 'development') {
    console.log(`${colors.magenta}======================================${colors.reset}\n`);
  
    }
  for (let i = 0; i < tests.length; i++) {
    const test = tests[i];
    
    if (process.env.NODE_ENV === 'development') {
    
      console.log(`${colors.bright}${colors.cyan}=== Running Test ${i + 1}/${tests.length}: ${test.name} ===${colors.reset}`);
    
    
      }
    try {
      await runCommand(test.command, test.args);
      if (process.env.NODE_ENV === 'development') {
        console.log(`${colors.green}✓ PASS${colors.reset} ${test.name}`);
        }
      testResults.passed++;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`${colors.red}✗ FAIL${colors.reset} ${test.name}`);
        }
      if (process.env.NODE_ENV === 'development') {
        console.log(`  ${colors.red}${error.message}${colors.reset}`);
        }
      testResults.failed++;
    }
    
    if (process.env.NODE_ENV === 'development') {
    
      console.log();
    
      }
  }
  
  // Print summary
  if (process.env.NODE_ENV === 'development') {
    console.log(`${colors.bright}${colors.cyan}=== Test Summary ===${colors.reset}`);
    }
  if (process.env.NODE_ENV === 'development') {
    console.log(`Total tests: ${testResults.total}`);
    }
  if (process.env.NODE_ENV === 'development') {
    console.log(`${colors.green}Passed: ${testResults.passed}${colors.reset}`);
    }
  if (process.env.NODE_ENV === 'development') {
    console.log(`${colors.red}Failed: ${testResults.failed}${colors.reset}`);
  
    }
  if (testResults.failed === 0) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`\n${colors.green}${colors.bright}All tests passed!${colors.reset}`);
      }
    process.exit(0);
  } else {
    if (process.env.NODE_ENV === 'development') {
      console.log(`\n${colors.red}${colors.bright}Some tests failed!${colors.reset}`);
      }
    process.exit(1);
  }
}

// Run the tests
runAllTests().catch((error) => {
  console.error(`${colors.red}Error running tests:${colors.reset}`, error);
  process.exit(1);
});
