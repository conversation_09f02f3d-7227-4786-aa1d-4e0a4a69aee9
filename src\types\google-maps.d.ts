// Type definitions for Google Maps JavaScript API
declare namespace google.maps {
  class Map {
    constructor(mapDiv: Element, opts?: MapOptions);
    setCenter(latLng: LatLng | LatLngLiteral): void;
    setZoom(zoom: number): void;
    setOptions(options: MapOptions): void;
    panTo(latLng: LatLng | LatLngLiteral): void;
    fitBounds(bounds: LatLngBounds | LatLngBoundsLiteral): void;
  }

  interface MapOptions {
    center?: LatLng | LatLngLiteral;
    zoom?: number;
    mapTypeId?: string;
    mapTypeControl?: boolean;
    streetViewControl?: boolean;
    fullscreenControl?: boolean;
    zoomControl?: boolean;
    styles?: any[];
  }

  class LatLng {
    constructor(lat: number, lng: number);
    lat(): number;
    lng(): number;
    toString(): string;
  }

  interface LatLngLiteral {
    lat: number;
    lng: number;
  }

  class LatLngBounds {
    constructor(sw?: LatLng, ne?: LatLng);
    extend(latLng: LatLng | LatLngLiteral): LatLngBounds;
    getCenter(): LatLng;
    toString(): string;
  }

  interface LatLngBoundsLiteral {
    east: number;
    north: number;
    south: number;
    west: number;
  }

  class Marker {
    constructor(opts?: MarkerOptions);
    setMap(map: Map | null): void;
    setPosition(latLng: LatLng | LatLngLiteral): void;
    setTitle(title: string): void;
    setIcon(icon: string | Icon | Symbol): void;
    getPosition(): LatLng | null;
    addListener(event: string, handler: Function): MapsEventListener;
  }

  interface MarkerOptions {
    position: LatLng | LatLngLiteral;
    map?: Map;
    title?: string;
    icon?: string | Icon | Symbol;
    label?: string | MarkerLabel;
    draggable?: boolean;
    animation?: Animation;
  }

  interface MarkerLabel {
    color: string;
    fontFamily: string;
    fontSize: string;
    fontWeight: string;
    text: string;
  }

  interface Icon {
    url: string;
    size?: Size;
    scaledSize?: Size;
    origin?: Point;
    anchor?: Point;
  }

  class Size {
    constructor(width: number, height: number);
    width: number;
    height: number;
  }

  class Point {
    constructor(x: number, y: number);
    x: number;
    y: number;
  }

  class Symbol {
    constructor(opts: SymbolOptions);
  }

  interface SymbolOptions {
    path: SymbolPath | string;
    fillColor?: string;
    fillOpacity?: number;
    scale?: number;
    strokeColor?: string;
    strokeOpacity?: number;
    strokeWeight?: number;
  }

  enum SymbolPath {
    BACKWARD_CLOSED_ARROW,
    BACKWARD_OPEN_ARROW,
    CIRCLE,
    FORWARD_CLOSED_ARROW,
    FORWARD_OPEN_ARROW
  }

  enum Animation {
    BOUNCE,
    DROP
  }

  class InfoWindow {
    constructor(opts?: InfoWindowOptions);
    open(map?: Map, anchor?: MVCObject): void;
    close(): void;
    setContent(content: string | Node): void;
    setPosition(latLng: LatLng | LatLngLiteral): void;
  }

  interface InfoWindowOptions {
    content?: string | Node;
    position?: LatLng | LatLngLiteral;
    maxWidth?: number;
  }

  class Circle {
    constructor(opts?: CircleOptions);
    setMap(map: Map | null): void;
    setCenter(latLng: LatLng | LatLngLiteral): void;
    setRadius(radius: number): void;
    getCenter(): LatLng;
    getRadius(): number;
  }

  interface CircleOptions {
    center?: LatLng | LatLngLiteral;
    radius?: number;
    map?: Map;
    strokeColor?: string;
    strokeOpacity?: number;
    strokeWeight?: number;
    fillColor?: string;
    fillOpacity?: number;
  }

  class MVCObject {
    addListener(eventName: string, handler: Function): MapsEventListener;
  }

  interface MapsEventListener {
    remove(): void;
  }

  namespace places {
    class AutocompleteService {
      getPlacePredictions(
        request: AutocompletionRequest,
        callback: (predictions: AutocompletePrediction[], status: PlacesServiceStatus) => void
      ): void;
    }

    interface AutocompletionRequest {
      input: string;
      bounds?: LatLngBounds | LatLngBoundsLiteral;
      componentRestrictions?: ComponentRestrictions;
      location?: LatLng;
      offset?: number;
      radius?: number;
      sessionToken?: AutocompleteSessionToken;
      types?: string[];
    }

    interface ComponentRestrictions {
      country: string | string[];
    }

    class AutocompleteSessionToken {}

    interface AutocompletePrediction {
      description: string;
      matched_substrings: PredictionSubstring[];
      place_id: string;
      structured_formatting: StructuredFormatting;
      terms: PredictionTerm[];
      types: string[];
    }

    interface PredictionSubstring {
      length: number;
      offset: number;
    }

    interface PredictionTerm {
      offset: number;
      value: string;
    }

    interface StructuredFormatting {
      main_text: string;
      main_text_matched_substrings: PredictionSubstring[];
      secondary_text: string;
    }

    class PlacesService {
      constructor(attrContainer: HTMLElement);
      getDetails(
        request: PlaceDetailsRequest,
        callback: (result: PlaceResult, status: PlacesServiceStatus) => void
      ): void;
    }

    interface PlaceDetailsRequest {
      placeId: string;
      fields?: string[];
      sessionToken?: AutocompleteSessionToken;
    }

    interface PlaceResult {
      address_components?: AddressComponent[];
      formatted_address?: string;
      geometry?: PlaceGeometry;
      name?: string;
      place_id: string;
    }

    interface AddressComponent {
      long_name: string;
      short_name: string;
      types: string[];
    }

    interface PlaceGeometry {
      location: LatLng;
      viewport: LatLngBounds;
    }

    type PlacesServiceStatus =
      'OK' |
      'ZERO_RESULTS' |
      'OVER_QUERY_LIMIT' |
      'REQUEST_DENIED' |
      'INVALID_REQUEST' |
      'UNKNOWN_ERROR';
  }
}

declare global {
  interface Window {
    google?: typeof google;
  }
}

export {};
