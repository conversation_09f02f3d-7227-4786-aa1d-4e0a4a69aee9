export type UserRole = 'admin' | 'teacher' | 'support' | 'supplier' | 'maintenance';

export type OrganizationType = 'school' | 'trust' | 'supplier';

export interface Organization {
  id: string;
  name: string;
  organization_type: OrganizationType;
  parent_organization_id?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  phone?: string;
  website?: string;
  created_at: string;
  updated_at: string;
}

export interface OrganizationUser {
  user_id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  role: UserRole;
  created_at: string;
}

export interface UserInvitation {
  id: string;
  email: string;
  organization_id: string;
  role: UserRole;
  invited_by: string;
  status: 'pending' | 'accepted' | 'expired';
  token: string;
  created_at: string;
  expires_at: string;
}

export interface InviteUserRequest {
  email: string;
  role: UserRole;
}

export interface AcceptInvitationRequest {
  token: string;
}
