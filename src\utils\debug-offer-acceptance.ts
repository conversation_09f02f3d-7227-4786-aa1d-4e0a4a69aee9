import { supabase } from '@/integrations/supabase/client';

/**
 * Debug utility to test offer acceptance directly
 */
export const debugAcceptOffer = async (taskId: string, offerId: string) => {
  if (process.env.NODE_ENV === 'development') {

    console.log(`Debug: Testing offer acceptance - Task ID: ${taskId}, Offer ID: ${offerId}`);
  

    }
  try {
    // Step 1: Check if the offer exists and get its current status
    if (process.env.NODE_ENV === 'development') {

      console.log("Step 1: Checking offer status");

      }
    const { data: offerData, error: offerError } = await supabase
      .from('offers')
      .select('*')
      .eq('id', offerId)
      .single();
    
    if (offerError) {
      console.error('Error checking offer:', offerError);
      return {
        success: false,
        step: 1,
        error: offerError,
        message: 'Failed to check offer status'
      };
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log("Current offer data:", offerData);
    

    
      }
    // Step 2: Check if the task exists and get its current status
    if (process.env.NODE_ENV === 'development') {

      console.log("Step 2: Checking task status");

      }
    const { data: taskData, error: taskError } = await supabase
      .from('tasks')
      .select('*')
      .eq('id', taskId)
      .single();
    
    if (taskError) {
      console.error('Error checking task:', taskError);
      return {
        success: false,
        step: 2,
        error: taskError,
        message: 'Failed to check task status'
      };
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log("Current task data:", taskData);
    

    
      }
    // Step 3: Try to update the offer status to 'accepted'
    if (process.env.NODE_ENV === 'development') {

      console.log("Step 3: Updating offer status to 'accepted'");

      }
    const { data: updatedOffer, error: updateOfferError } = await supabase
      .from('offers')
      .update({ status: 'accepted' })
      .eq('id', offerId)
      .select()
      .single();
    
    if (updateOfferError) {
      console.error('Error updating offer status:', updateOfferError);
      return {
        success: false,
        step: 3,
        error: updateOfferError,
        message: 'Failed to update offer status'
      };
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log("Updated offer data:", updatedOffer);
    

    
      }
    // Step 4: Try to update the task status to 'assigned'
    if (process.env.NODE_ENV === 'development') {

      console.log("Step 4: Updating task status to 'assigned'");

      }
    const { data: updatedTask, error: updateTaskError } = await supabase
      .from('tasks')
      .update({ status: 'assigned' })
      .eq('id', taskId)
      .select()
      .single();
    
    if (updateTaskError) {
      console.error('Error updating task status:', updateTaskError);
      return {
        success: false,
        step: 4,
        error: updateTaskError,
        message: 'Failed to update task status'
      };
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log("Updated task data:", updatedTask);
    

    
      }
    // Step 5: Try to reject all other offers for this task
    if (process.env.NODE_ENV === 'development') {

      console.log("Step 5: Rejecting all other offers for this task");

      }
    const { data: rejectedOffers, error: rejectError } = await supabase
      .from('offers')
      .update({ status: 'rejected' })
      .eq('task_id', taskId)
      .neq('id', offerId)
      .select();
    
    if (rejectError) {
      console.error('Warning - error rejecting other offers:', rejectError);
      // We continue despite this error as the main operation succeeded
    } else {
      if (process.env.NODE_ENV === 'development') {

        console.log(`All other offers rejected: ${rejectedOffers?.length || 0} offers`);

        }
    }
    
    return {
      success: true,
      updatedOffer,
      updatedTask,
      rejectedOffers
    };
  } catch (error) {
    console.error('Exception in debugAcceptOffer:', error);
    return {
      success: false,
      error,
      message: 'Exception occurred during offer acceptance'
    };
  }
};
