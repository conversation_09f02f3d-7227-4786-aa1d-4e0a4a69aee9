/**
 * Debug utility functions
 */

// Set to true to enable debug logging
const DEBUG_ENABLED = process.env.NODE_ENV === 'development';

/**
 * Log debug messages to the console
 * @param component - The component or module name
 * @param message - The debug message
 * @param data - Optional data to log
 */
export const debugLog = (component: string, message: string, data?: any) => {
  if (DEBUG_ENABLED) {
    console.log(`[DEBUG][${component}] ${message}`, data || '');
  }
};

/**
 * Log performance timing
 * @param label - Label for the performance measurement
 * @returns Function to call when the operation is complete
 */
export const debugTime = (label: string) => {
  if (!DEBUG_ENABLED) return () => {};

  const start = performance.now();
  return () => {
    const end = performance.now();
    console.log(`[PERF] ${label}: ${(end - start).toFixed(2)}ms`);
  };
};

export default {
  log: debugLog,
  time: debugTime
};
