/**
 * Feature Flag System for Safe Rollouts
 * Allows gradual feature deployment and instant rollback
 */

interface FeatureFlags {
  newTaskInterface: boolean;
  enhancedNotifications: boolean;
  advancedReporting: boolean;
  newPaymentFlow: boolean;
  betaFeatures: boolean;
}

interface User {
  id: string;
  email: string;
  organization_id: string;
  role: string;
  created_at: string;
}

class FeatureFlagManager {
  private flags: FeatureFlags;
  private rolloutPercentages: Record<string, number>;
  private betaUsers: Set<string>;
  private betaOrganizations: Set<string>;

  constructor() {
    // Default flag states (can be overridden by environment variables)
    this.flags = {
      newTaskInterface: process.env.VITE_FEATURE_NEW_TASK_INTERFACE === 'true',
      enhancedNotifications: process.env.VITE_FEATURE_ENHANCED_NOTIFICATIONS === 'true',
      advancedReporting: process.env.VITE_FEATURE_ADVANCED_REPORTING === 'true',
      newPaymentFlow: process.env.VITE_FEATURE_NEW_PAYMENT_FLOW === 'true',
      betaFeatures: process.env.VITE_FEATURE_BETA_FEATURES === 'true'
    };

    // Gradual rollout percentages (0-100)
    this.rolloutPercentages = {
      newTaskInterface: parseInt(process.env.VITE_ROLLOUT_NEW_TASK_INTERFACE || '0'),
      enhancedNotifications: parseInt(process.env.VITE_ROLLOUT_ENHANCED_NOTIFICATIONS || '0'),
      advancedReporting: parseInt(process.env.VITE_ROLLOUT_ADVANCED_REPORTING || '0'),
      newPaymentFlow: parseInt(process.env.VITE_ROLLOUT_NEW_PAYMENT_FLOW || '0')
    };

    // Beta users and organizations
    this.betaUsers = new Set(process.env.VITE_BETA_USERS?.split(',') || []);
    this.betaOrganizations = new Set(process.env.VITE_BETA_ORGANIZATIONS?.split(',') || []);
  }

  /**
   * Check if a feature is enabled for a specific user
   */
  isFeatureEnabled(featureName: keyof FeatureFlags, user: User): boolean {
    // Always enable for beta users/organizations
    if (this.isBetaUser(user)) {
      return true;
    }

    // Check global flag first
    if (!this.flags[featureName]) {
      return false;
    }

    // Check rollout percentage
    const rolloutPercentage = this.rolloutPercentages[featureName];
    if (rolloutPercentage === undefined) {
      return this.flags[featureName];
    }

    // Use user ID hash for consistent rollout
    const userHash = this.hashUserId(user.id);
    return userHash < rolloutPercentage;
  }

  /**
   * Check if user is in beta program
   */
  private isBetaUser(user: User): boolean {
    return this.betaUsers.has(user.id) || 
           this.betaUsers.has(user.email) ||
           this.betaOrganizations.has(user.organization_id);
  }

  /**
   * Generate consistent hash from user ID (0-100)
   */
  private hashUserId(userId: string): number {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash) % 100;
  }

  /**
   * Emergency feature disable (for instant rollback)
   */
  emergencyDisable(featureName: keyof FeatureFlags): void {
    this.flags[featureName] = false;
    this.rolloutPercentages[featureName] = 0;
    
    // In production, this would update a remote config
    console.warn(`🚨 Emergency disable: ${featureName}`);
  }

  /**
   * Get all feature states for debugging
   */
  getFeatureStates(user: User): Record<string, boolean> {
    const states: Record<string, boolean> = {};
    
    Object.keys(this.flags).forEach(feature => {
      states[feature] = this.isFeatureEnabled(feature as keyof FeatureFlags, user);
    });
    
    return states;
  }
}

// Singleton instance
export const featureFlags = new FeatureFlagManager();

// React hook for easy usage
import { useUser } from '@/hooks/useUser';

export function useFeatureFlag(featureName: keyof FeatureFlags): boolean {
  const { user } = useUser();
  
  if (!user) return false;
  
  return featureFlags.isFeatureEnabled(featureName, user);
}

// Usage examples:
/*
// In a component
const hasNewTaskInterface = useFeatureFlag('newTaskInterface');

// Conditional rendering
{hasNewTaskInterface ? <NewTaskForm /> : <OldTaskForm />}

// In utility functions
if (featureFlags.isFeatureEnabled('enhancedNotifications', user)) {
  sendEnhancedNotification();
} else {
  sendBasicNotification();
}

// Emergency rollback
featureFlags.emergencyDisable('newPaymentFlow');
*/
