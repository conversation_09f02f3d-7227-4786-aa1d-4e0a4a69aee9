/**
 * GetStream Debug Utilities
 *
 * This file contains utilities for debugging GetStream chat issues.
 */

import { StreamChat, Channel } from 'stream-chat';
import { supabase } from '@/integrations/supabase/client';
import {
  getStreamClient,
  connectUser,
  createOrUpdateTaskChannel,
  getTaskChannel,
  ensureTaskParticipantsInChannel,
  syncUserChannelsWithTasks,
  getAllUserTaskChannels
} from '@/integrations/getstream/client';

/**
 * Check if a user is a member of a channel
 * @param userId - The user ID to check
 * @param channelId - The channel ID to check
 * @returns A promise that resolves to a boolean indicating if the user is a member
 */
export const isUserChannelMember = async (userId: string, channelId: string): Promise<boolean> => {
  try {
    // Get the client
    const client = getStreamClient();

    // Get the channel
    const channel = client.channel('messaging', channelId);

    // Query the channel to get its state
    await channel.query();

    // Check if the user is a member
    const members = channel.state?.members || {};
    return !!members[userId];
  } catch (error) {
    console.error(`[isUserChannelMember] Error checking if user ${userId} is member of channel ${channelId}:`, error);
    return false;
  }
};

/**
 * Get all channels for a user
 * @param userId - The user ID
 * @returns A promise that resolves to an array of channels
 */
export const getUserChannels = async (userId: string): Promise<Channel[]> => {
  try {
    // Get the client
    const client = getStreamClient();

    // Query channels where the user is a member
    const filter = { type: 'messaging', members: { $in: [userId] } };
    const sort = { last_message_at: -1 };

    const channels = await client.queryChannels(filter, sort, {
      limit: 100, // Use a high limit to get all channels
      state: true,
      watch: true,
    });

    return channels;
  } catch (error) {
    console.error(`[getUserChannels] Error getting channels for user ${userId}:`, error);
    return [];
  }
};

/**
 * Get detailed information about a channel
 * @param channelId - The channel ID
 * @returns A promise that resolves to an object with channel details
 */
export const getChannelDetails = async (channelId: string): Promise<any> => {
  try {
    // Get the client
    const client = getStreamClient();

    // Get the channel
    const channel = client.channel('messaging', channelId);

    // Query the channel to get its state
    await channel.query();

    // Get channel details
    return {
      id: channel.id,
      type: channel.type,
      cid: channel.cid,
      data: channel.data,
      members: Object.keys(channel.state?.members || {}),
      memberDetails: channel.state?.members || {},
      messages: channel.state?.messages?.length || 0,
      created_at: channel.data?.created_at,
      updated_at: channel.data?.updated_at,
    };
  } catch (error) {
    console.error(`[getChannelDetails] Error getting details for channel ${channelId}:`, error);
    return null;
  }
};

/**
 * Check all tasks for a user and verify channel membership
 * @param userId - The user ID to check
 * @returns A promise that resolves to an object with the check results
 */
export const checkUserTaskChannels = async (userId: string): Promise<any> => {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[checkUserTaskChannels] Checking task channels for user ${userId}`);

      }
    // Get all tasks the user is involved with
    const { data: userTasks, error: tasksError } = await supabase
      .from('tasks')
      .select('id, title, user_id, assigned_to, status, visibility, getstream_channel_id, chat_migrated_to_stream')
      .or(`user_id.eq.${userId},assigned_to.eq.${userId}`);

    if (tasksError) {
      throw new Error(`Error fetching user tasks: ${tasksError.message}`);
    }

    if (process.env.NODE_ENV === 'development') {

      console.log(`[checkUserTaskChannels] Found ${userTasks?.length || 0} tasks for user`);


      }
    // Get all offers made by the user
    const { data: userOffers, error: offersError } = await supabase
      .from('offers')
      .select('task_id, status')
      .eq('user_id', userId);

    if (offersError) {
      throw new Error(`Error fetching user offers: ${offersError.message}`);
    }

    if (process.env.NODE_ENV === 'development') {

      console.log(`[checkUserTaskChannels] Found ${userOffers?.length || 0} offers made by user`);


      }
    // Get task IDs from offers
    const offerTaskIds = userOffers?.map(offer => offer.task_id) || [];

    // Get tasks for which the user has made offers
    const { data: offerTasks, error: offerTasksError } = await supabase
      .from('tasks')
      .select('id, title, user_id, assigned_to, status, visibility, getstream_channel_id, chat_migrated_to_stream')
      .in('id', offerTaskIds);

    if (offerTasksError) {
      throw new Error(`Error fetching offer tasks: ${offerTasksError.message}`);
    }

    if (process.env.NODE_ENV === 'development') {

      console.log(`[checkUserTaskChannels] Found ${offerTasks?.length || 0} tasks from user offers`);


      }
    // Combine tasks and remove duplicates
    const allTasks = [...(userTasks || [])];
    offerTasks?.forEach(offerTask => {
      if (!allTasks.some(task => task.id === offerTask.id)) {
        allTasks.push(offerTask);
      }
    });

    if (process.env.NODE_ENV === 'development') {

      console.log(`[checkUserTaskChannels] Total unique tasks: ${allTasks.length}`);


      }
    // Get all GetStream channels for the user
    await connectUser(userId, 'User', userId);
    const userChannels = await getUserChannels(userId);
    if (process.env.NODE_ENV === 'development') {
      console.log(`[checkUserTaskChannels] Found ${userChannels.length} GetStream channels for user`);

      }
    // Check each task
    const results: any = {
      user_id: userId,
      tasks_count: allTasks.length,
      channels_count: userChannels.length,
      tasks_with_channels: 0,
      tasks_without_channels: 0,
      channels_without_tasks: 0,
      task_details: []
    };

    // Track channel IDs that correspond to tasks
    const taskChannelIds = new Set<string>();

    // Check each task
    for (const task of allTasks) {
      const channelId = task.getstream_channel_id || `task-${task.id}`;
      const isChannelMember = await isUserChannelMember(userId, channelId);
      const channelExists = userChannels.some(channel => channel.id === channelId);

      if (channelExists) {
        taskChannelIds.add(channelId);
        results.tasks_with_channels++;
      } else {
        results.tasks_without_channels++;
      }

      results.task_details.push({
        task_id: task.id,
        title: task.title,
        status: task.status,
        visibility: task.visibility,
        user_is_creator: task.user_id === userId,
        user_is_assignee: task.assigned_to === userId,
        channel_id: channelId,
        channel_exists: channelExists,
        is_channel_member: isChannelMember,
        chat_migrated_to_stream: task.chat_migrated_to_stream
      });
    }

    // Check for channels that don't correspond to tasks
    for (const channel of userChannels) {
      if (channel.id.startsWith('task-') && !taskChannelIds.has(channel.id)) {
        results.channels_without_tasks++;
      }
    }

    if (process.env.NODE_ENV === 'development') {

      console.log('[checkUserTaskChannels] Check completed successfully');

      }
    return results;
  } catch (error) {
    console.error('[checkUserTaskChannels] Error checking user task channels:', error);
    throw error;
  }
};

/**
 * Simulate the task workflow and check channel membership at each stage
 * @param adminId - The admin user ID
 * @param teacherId - The teacher user ID
 * @param supplierId - The supplier user ID
 * @returns A promise that resolves to an object with the simulation results
 */
export const simulateTaskWorkflow = async (
  adminId: string,
  teacherId: string,
  supplierId: string
): Promise<any> => {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('[simulateTaskWorkflow] Starting task workflow simulation');
      }
    if (process.env.NODE_ENV === 'development') {
      console.log(`Admin: ${adminId}, Teacher: ${teacherId}, Supplier: ${supplierId}`);

      }
    const results: any = {
      taskId: '',
      channelId: '',
      stages: {}
    };

    // Step 1: Create a task as a teacher
    if (process.env.NODE_ENV === 'development') {
      console.log('[simulateTaskWorkflow] Step 1: Creating task as teacher');
      }
    const { data: taskData, error: taskError } = await supabase
      .from('tasks')
      .insert({
        title: 'Test Task for GetStream Simulation',
        description: 'This is a test task for simulating the GetStream workflow',
        user_id: teacherId,
        status: 'open',
        visibility: 'admin', // Start with admin visibility
        organization_id: null, // Set to null for testing
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (taskError || !taskData) {
      throw new Error(`Error creating task: ${taskError?.message || 'Unknown error'}`);
    }

    const taskId = taskData.id;
    results.taskId = taskId;
    if (process.env.NODE_ENV === 'development') {
      console.log(`[simulateTaskWorkflow] Task created with ID: ${taskId}`);

      }
    // Check channel creation and membership after task creation
    let channel = await getTaskChannel(taskId, false);
    results.stages.task_created = {
      channel_exists: !!channel,
      members: channel ? Object.keys(channel.state?.members || {}) : []
    };

    // Step 2: Admin reviews and makes task public
    if (process.env.NODE_ENV === 'development') {
      console.log('[simulateTaskWorkflow] Step 2: Admin makes task public');
      }
    const { error: updateError } = await supabase
      .from('tasks')
      .update({
        visibility: 'public',
        status: 'open',
        budget: 100,
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId);

    if (updateError) {
      throw new Error(`Error updating task: ${updateError.message}`);
    }

    // Create or update the channel as admin
    await connectUser(adminId, 'Admin User', adminId);
    channel = await createOrUpdateTaskChannel(taskId, 'Test Task for GetStream Simulation', [adminId, teacherId], true);
    results.channelId = channel.id;

    // Check channel membership after admin review
    results.stages.admin_review = {
      channel_exists: !!channel,
      members: Object.keys(channel.state?.members || {})
    };

    // Step 3: Supplier expresses interest
    if (process.env.NODE_ENV === 'development') {
      console.log('[simulateTaskWorkflow] Step 3: Supplier expresses interest');
      }
    const { error: interestError } = await supabase
      .from('tasks')
      .update({
        status: 'interest',
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId);

    if (interestError) {
      throw new Error(`Error updating task status to interest: ${interestError.message}`);
    }

    // Connect as supplier and add to channel
    await connectUser(supplierId, 'Supplier User', supplierId);
    channel = await createOrUpdateTaskChannel(taskId, 'Test Task for GetStream Simulation', [adminId, teacherId, supplierId], true);

    // Check channel membership after supplier interest
    results.stages.supplier_interest = {
      channel_exists: !!channel,
      members: Object.keys(channel.state?.members || {})
    };

    // Step 4: Supplier submits an offer
    if (process.env.NODE_ENV === 'development') {
      console.log('[simulateTaskWorkflow] Step 4: Supplier submits offer');
      }
    const { data: offerData, error: offerError } = await supabase
      .from('offers')
      .insert({
        task_id: taskId,
        user_id: supplierId,
        price: 100,
        description: 'This is a test offer',
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (offerError || !offerData) {
      throw new Error(`Error creating offer: ${offerError?.message || 'Unknown error'}`);
    }

    // Update task status to offer
    await supabase
      .from('tasks')
      .update({
        status: 'offer',
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId);

    // Check channel membership after offer submission
    channel = await getTaskChannel(taskId, false);
    results.stages.offer_submitted = {
      channel_exists: !!channel,
      members: channel ? Object.keys(channel.state?.members || {}) : []
    };

    // Step 5: Admin accepts the offer
    if (process.env.NODE_ENV === 'development') {
      console.log('[simulateTaskWorkflow] Step 5: Admin accepts offer');
      }
    await supabase
      .from('offers')
      .update({
        status: 'accepted',
        updated_at: new Date().toISOString()
      })
      .eq('id', offerData.id);

    await supabase
      .from('tasks')
      .update({
        status: 'assigned',
        assigned_to: supplierId,
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId);

    // Check channel membership after offer acceptance
    channel = await getTaskChannel(taskId, false);
    results.stages.offer_accepted = {
      channel_exists: !!channel,
      members: channel ? Object.keys(channel.state?.members || {}) : []
    };

    // Step 6: Supplier starts work
    if (process.env.NODE_ENV === 'development') {
      console.log('[simulateTaskWorkflow] Step 6: Supplier starts work');
      }
    await supabase
      .from('tasks')
      .update({
        status: 'in_progress',
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId);

    // Check channel membership after work starts
    channel = await getTaskChannel(taskId, false);
    results.stages.work_started = {
      channel_exists: !!channel,
      members: channel ? Object.keys(channel.state?.members || {}) : []
    };

    // Step 7: Supplier completes work
    if (process.env.NODE_ENV === 'development') {
      console.log('[simulateTaskWorkflow] Step 7: Supplier completes work');
      }
    await supabase
      .from('tasks')
      .update({
        status: 'completed',
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId);

    // Check channel membership after work completion
    channel = await getTaskChannel(taskId, false);
    results.stages.work_completed = {
      channel_exists: !!channel,
      members: channel ? Object.keys(channel.state?.members || {}) : []
    };

    // Step 8: Admin approves work
    if (process.env.NODE_ENV === 'development') {
      console.log('[simulateTaskWorkflow] Step 8: Admin approves work');
      }
    await supabase
      .from('tasks')
      .update({
        status: 'closed',
        updated_at: new Date().toISOString()
      })
      .eq('id', taskId);

    // Check channel membership after work approval
    channel = await getTaskChannel(taskId, false);
    results.stages.work_approved = {
      channel_exists: !!channel,
      members: channel ? Object.keys(channel.state?.members || {}) : []
    };

    // Final check: Can each user see the channel in their chat list?
    if (process.env.NODE_ENV === 'development') {
      console.log('[simulateTaskWorkflow] Final check: Checking chat list visibility');

      }
    // Connect as each user and check if they can see the channel
    await connectUser(adminId, 'Admin User', adminId);
    const adminChannels = await getUserChannels(adminId);
    const adminCanSeeChannel = adminChannels.some(c => c.id === channel?.id);

    await connectUser(teacherId, 'Teacher User', teacherId);
    const teacherChannels = await getUserChannels(teacherId);
    const teacherCanSeeChannel = teacherChannels.some(c => c.id === channel?.id);

    await connectUser(supplierId, 'Supplier User', supplierId);
    const supplierChannels = await getUserChannels(supplierId);
    const supplierCanSeeChannel = supplierChannels.some(c => c.id === channel?.id);

    results.final_visibility = {
      admin_can_see_channel: adminCanSeeChannel,
      teacher_can_see_channel: teacherCanSeeChannel,
      supplier_can_see_channel: supplierCanSeeChannel,
      admin_channels_count: adminChannels.length,
      teacher_channels_count: teacherChannels.length,
      supplier_channels_count: supplierChannels.length
    };

    if (process.env.NODE_ENV === 'development') {
    console.log('[simulateTaskWorkflow] Simulation completed successfully');
  }
    return results;
  } catch (error) {
    console.error('[simulateTaskWorkflow] Error in task workflow simulation:', error);
    throw error;
  }
};

export default {
  isUserChannelMember,
  getUserChannels,
  getChannelDetails,
  checkUserTaskChannels,
  simulateTaskWorkflow
};
