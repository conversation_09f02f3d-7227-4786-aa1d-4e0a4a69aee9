/**
 * Utility functions for location-based operations
 */

// Common UK locations with their coordinates
const UK_LOCATIONS: Record<string, { lat: number, lng: number }> = {
  'uk': { lat: 54.0000, lng: -2.5000 }, // Center of UK
  'united kingdom': { lat: 54.0000, lng: -2.5000 },
  'great britain': { lat: 54.0000, lng: -2.5000 },
  'london': { lat: 51.5074, lng: -0.1278 },
  'manchester': { lat: 53.4808, lng: -2.2426 },
  'birmingham': { lat: 52.4862, lng: -1.8904 },
  'liverpool': { lat: 53.4084, lng: -2.9916 },
  'leeds': { lat: 53.8008, lng: -1.5491 },
  'edinburgh': { lat: 55.9533, lng: -3.1883 },
  'glasgow': { lat: 55.8642, lng: -4.2518 },
  'cardiff': { lat: 51.4816, lng: -3.1791 },
  'belfast': { lat: 54.5973, lng: -5.9301 },
  'bristol': { lat: 51.4545, lng: -2.5879 },
  'farnborough': { lat: 51.2867, lng: -0.7569 }, // Updated coordinates
  'farnham': { lat: 51.2141, lng: -0.8014 }, // Updated coordinates
  'farnborough, uk': { lat: 51.2867, lng: -0.7569 }, // Alternative format
  'farnham, uk': { lat: 51.2141, lng: -0.8014 } // Alternative format
};

/**
 * Calculate the distance between two coordinates using the Haversine formula
 * @param lat1 Latitude of the first point
 * @param lon1 Longitude of the first point
 * @param lat2 Latitude of the second point
 * @param lon2 Longitude of the second point
 * @returns Distance in miles
 */
export function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Calculating distance between:
    Point 1: ${lat1}, ${lon1}
    Point 2: ${lat2}, ${lon2}`);
  }
  // Validate inputs
  if (typeof lat1 !== 'number' || typeof lon1 !== 'number' ||
      typeof lat2 !== 'number' || typeof lon2 !== 'number' ||
      isNaN(lat1) || isNaN(lon1) || isNaN(lat2) || isNaN(lon2)) {
    console.error('DEBUG: CRITICAL ERROR - Invalid coordinates for distance calculation');
    console.error('DEBUG: lat1:', lat1, 'lon1:', lon1, 'lat2:', lat2, 'lon2:', lon2);
    return 999999; // Return a very large distance to ensure it's outside any radius
  }

  // Convert latitude and longitude from degrees to radians
  const radLat1 = (Math.PI * lat1) / 180;
  const radLon1 = (Math.PI * lon1) / 180;
  const radLat2 = (Math.PI * lat2) / 180;
  const radLon2 = (Math.PI * lon2) / 180;

  // Haversine formula
  const dLat = radLat2 - radLat1;
  const dLon = radLon2 - radLon1;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(radLat1) * Math.cos(radLat2) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  // Earth's radius in miles
  const earthRadius = 3958.8;

  // Calculate the distance
  const distance = earthRadius * c;

  // Check for unreasonable distances (e.g., due to calculation errors)
  if (distance > 1000) {
    console.error('DEBUG: Calculated distance is unreasonably large:', distance);
    console.error('DEBUG: This may indicate a problem with the coordinates');
    return 999999; // Return a very large distance to ensure it's outside any radius
  }

  // Log the actual distance for debugging
  if (process.env.NODE_ENV === 'development') {

    console.log(`DEBUG: Actual calculated distance: ${distance.toFixed(2)} miles`);


    }
  return distance;
}

/**
 * Get coordinates from a location string using the Google Maps Geocoding API
 * @param location Location string or coordinates object to geocode
 * @returns Promise that resolves to coordinates {lat, lng} or null if geocoding fails
 */
// In-memory cache for coordinates to supplement sessionStorage
// This helps avoid parsing JSON repeatedly and provides faster access
const coordinatesCache = new Map<string, { lat: number, lng: number }>();

// Rate limiting variables
let lastApiCallTime = 0;
const API_CALL_DELAY = 100; // Minimum 100ms between API calls

// We'll move the clearLocationCaches function after all cache variables are declared

export async function getCoordinates(
  location: string | { lat: number, lng: number } | null
): Promise<{ lat: number, lng: number } | null> {
  if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: getCoordinates called with:', location);
  }
  // If location is null or undefined, return null
  if (!location) {
    console.error('DEBUG: CRITICAL ERROR - Location is null or undefined');
    return null;
  }

  // If location is already a coordinates object, return it directly
  if (typeof location !== 'string' && 'lat' in location && 'lng' in location) {
    if (process.env.NODE_ENV === 'development') {

      console.log('DEBUG: Location is already coordinates object:', JSON.stringify(location));


      }
    // Validate the coordinates
    if (typeof location.lat !== 'number' || typeof location.lng !== 'number' ||
        isNaN(location.lat) || isNaN(location.lng)) {
      console.error('DEBUG: CRITICAL ERROR - Invalid coordinates object:', location);
      return null;
    }

    return location;
  }

  // Now we know location is a string
  const locationStr = location as string;

  try {
    // Use browser's Geolocation API if available for "my location"
    if (navigator.geolocation && locationStr.toLowerCase() === 'my location') {
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Getting current location from browser');
  }
      return new Promise((resolve) => {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const coords = {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            };
            // Cache the result
            coordinatesCache.set('my location', coords);
            resolve(coords);
          },
          (error) => {
            console.error('Error getting current location:', error);
            // Return London as default
            resolve(UK_LOCATIONS['london']);
          },
          { timeout: 5000 }
        );
      });
    }

    // Check in-memory cache first (fastest)
    if (coordinatesCache.has(locationStr)) {
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Using in-memory cached coordinates for:', locationStr);
  }
      return coordinatesCache.get(locationStr)!;
    }

    // Check sessionStorage cache next
    const cacheKey = `geocode_${locationStr}`;
    const cachedResult = sessionStorage.getItem(cacheKey);
    if (cachedResult) {
      try {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Using sessionStorage cached coordinates for:', locationStr);
  }
        const coords = JSON.parse(cachedResult);
        // Store in memory cache too
        coordinatesCache.set(locationStr, coords);
        return coords;
      } catch (e) {
        console.warn('Failed to parse cached coordinates');
      }
    }

    // Check if this is a known UK location
    const locationLower = locationStr.toLowerCase().trim();

    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Checking for known UK location:', locationLower);
  }
    // For comma-separated locations (like "City, UK"), we want to prioritize the city part
    // but still use the full string for geocoding to get accurate results
    const parts = locationLower.split(',').map(part => part.trim());
    const isCommaFormat = parts.length > 1;

    // We'll skip the predefined list and go straight to Google Maps API
    // The predefined list is only used as a fallback if the API fails

    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Using Google Maps API for geocoding:', locationStr);
  }
    // Use Google Maps Geocoding API as the primary source of coordinates
    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

    if (!apiKey) {
      console.error('Google Maps API key not found in environment variables');
      throw new Error('Google Maps API key not configured');
    }

    // For UK locations, add 'UK' to the query if not already present and not comma-separated
    let queryLocation = locationStr;
    if (!isCommaFormat &&
        !queryLocation.toLowerCase().includes('uk') &&
        !queryLocation.toLowerCase().includes('united kingdom')) {
      queryLocation += ', UK';
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Geocoding query:', queryLocation);
  }
    // Implement rate limiting for API calls
    const now = Date.now();
    const timeElapsed = now - lastApiCallTime;
    if (timeElapsed < API_CALL_DELAY) {
      if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Rate limiting API call, waiting ${API_CALL_DELAY - timeElapsed}ms`);
  }
      await new Promise(resolve => setTimeout(resolve, API_CALL_DELAY - timeElapsed));
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Making Google Maps API call for:', queryLocation);
  }
    lastApiCallTime = Date.now();

    try {
      const geocodeUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(queryLocation)}&key=${apiKey}`;
      const response = await fetch(geocodeUrl);
      const data = await response.json();

      if (data.status === 'OK' && data.results && data.results.length > 0) {
        const { lat, lng } = data.results[0].geometry.location;
        const coords = { lat, lng };
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Successfully geocoded location:', queryLocation);
  }
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Coordinates:', JSON.stringify(coords));


          }
        // Cache the result in both caches
        sessionStorage.setItem(cacheKey, JSON.stringify(coords));
        coordinatesCache.set(locationStr, coords);
        return coords;
      } else {
        console.warn('DEBUG: Geocoding failed, status:', data.status);

        // If geocoding fails, try the predefined list as a fallback
        // This is only a fallback, not the primary source
        if (UK_LOCATIONS[locationLower]) {
          if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Using fallback from UK_LOCATIONS:', locationLower);
  }
          const coords = UK_LOCATIONS[locationLower];
          sessionStorage.setItem(cacheKey, JSON.stringify(coords));
          coordinatesCache.set(locationStr, coords);
          return coords;
        }

        // For comma-separated locations, try the first part
        if (isCommaFormat) {
          const firstPart = parts[0];
          if (UK_LOCATIONS[firstPart]) {
            if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Using fallback from UK_LOCATIONS for first part:', firstPart);
  }
            const coords = UK_LOCATIONS[firstPart];
            sessionStorage.setItem(cacheKey, JSON.stringify(coords));
            coordinatesCache.set(locationStr, coords);
            return coords;
          }
        }

        // If all else fails, return London as default
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: All geocoding attempts failed, using London as default');
  }
        sessionStorage.setItem(cacheKey, JSON.stringify(UK_LOCATIONS['london']));
        coordinatesCache.set(locationStr, UK_LOCATIONS['london']);
        return UK_LOCATIONS['london'];
      }
    } catch (error) {
      console.error('Error geocoding location:', error);
      // Return London as default
      return UK_LOCATIONS['london'];
    }
  } catch (error) {
    console.error('Error in getCoordinates:', error);
    return UK_LOCATIONS['london'];
  }
}

/**
 * Check if a location is within a specified radius of another location
 * @param locationA First location string or coordinates
 * @param locationB Second location string or coordinates
 * @param radius Radius in miles
 * @returns Promise that resolves to true if locationB is within radius of locationA, false otherwise
 */
// In-memory cache for distance calculations
const distanceCache = new Map<string, boolean>();

// Function to clear all location caches
export function clearLocationCaches() {
  if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Clearing all location caches');
  }
  // Clear in-memory caches
  coordinatesCache.clear();
  distanceCache.clear();

  // Clear all sessionStorage cache entries
  try {
    // First, clear specific cache entries
    Object.keys(sessionStorage).forEach(key => {
      if (key.startsWith('geocode_') || key.startsWith('distance_')) {
        sessionStorage.removeItem(key);
      }
    });

    // Then, for good measure, clear all sessionStorage
    // This ensures we don't miss any cached entries
    sessionStorage.clear();

    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: All caches cleared successfully');
  }
  } catch (error) {
    console.error('Error clearing caches:', error);
  }
}

// Clear caches on module load
clearLocationCaches();

export async function isLocationWithinRadius(
  locationA: string | { lat: number, lng: number } | null,
  locationB: string | { lat: number, lng: number } | null,
  radius: number
): Promise<boolean> {
  if (process.env.NODE_ENV === 'development') {

    console.log('DEBUG: isLocationWithinRadius called with:',
    'locationA:', typeof locationA === 'string' ? locationA : JSON.stringify(locationA),
    'locationB:', typeof locationB === 'string' ? locationB : JSON.stringify(locationB),
    'radius:', radius);


    }
  // If either location is empty or radius is very large, return true
  if (!locationA || !locationB) {
    console.error('DEBUG: CRITICAL ERROR - One or both locations are null');
    return true;
  }

  // For very large radii, we'll be more strict
  if (radius >= 100) {
    if (process.env.NODE_ENV === 'development') {

      console.log('DEBUG: Radius is very large (>=100), but we will still calculate distance');

      }
    // We'll continue with the calculation instead of returning true
  }

  try {
    // Create a consistent cache key
    const locationAStr = typeof locationA === 'string' ? locationA : `${locationA.lat},${locationA.lng}`;
    const locationBStr = typeof locationB === 'string' ? locationB : `${locationB.lat},${locationB.lng}`;
    const cacheKey = `distance_${locationAStr}_${locationBStr}_${radius}`;

    // TEMPORARILY DISABLE ALL CACHING FOR DISTANCE CALCULATIONS
    // This ensures we always get fresh results
    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Bypassing cache for all distance calculations to ensure accurate results');
  }
    // We'll still store the result in the cache for future use
    // but we won't use the cached result for now

    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Getting coordinates for both locations');
  }
    // Get coordinates for both locations
    const coordsA = await getCoordinates(locationA);
    const coordsB = await getCoordinates(locationB);

    // If either location couldn't be geocoded, return true
    if (!coordsA || !coordsB) {
      console.error('DEBUG: CRITICAL ERROR - Could not get coordinates for one or both locations');
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: coordsA:', coordsA);
  }
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: coordsB:', coordsB);
  }
      const result = true;
      sessionStorage.setItem(cacheKey, 'true');
      distanceCache.set(cacheKey, result);
      return result;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Successfully got coordinates for both locations:');
  }
    if (process.env.NODE_ENV === 'development') {

      console.log('DEBUG: coordsA:', JSON.stringify(coordsA));

      }
    if (process.env.NODE_ENV === 'development') {

      console.log('DEBUG: coordsB:', JSON.stringify(coordsB));


      }
    // Calculate the distance between the two locations
    const distance = calculateDistance(
      coordsA.lat,
      coordsA.lng,
      coordsB.lat,
      coordsB.lng
    );
    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Calculated distance:', distance, 'miles');
  }
    // VERY STRICT RADIUS CHECK - No buffer for larger radii
    let result;

    // Calculate buffer based on radius size - much stricter now
    let bufferPercentage, bufferDistance;

    if (radius > 40) {
      // For very large radii (> 40 miles), no buffer at all
      bufferPercentage = 0;
      bufferDistance = 0;
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Using extremely strict radius check (no buffer) for radius > 40 miles');

        }
    } else if (radius > 20) {
      // For medium-large radii (20-40 miles), use a minimal buffer
      bufferPercentage = 0.01; // 1% buffer
      bufferDistance = radius * bufferPercentage;
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Using very strict radius check (1% buffer) for radius 20-40 miles');

        }
    } else {
      // For smaller radii (< 20 miles), use a small buffer
      bufferPercentage = 0.05; // 5% buffer
      bufferDistance = radius * bufferPercentage;
      if (process.env.NODE_ENV === 'development') {

        console.log('DEBUG: Using standard radius check (5% buffer) for radius < 20 miles');

        }
    }

    if (distance <= radius) {
      // Clearly within radius
      if (process.env.NODE_ENV === 'development') {

        console.log(`DEBUG: Distance (${distance.toFixed(2)}) is within radius (${radius})`);

        }
      result = true;
    }
    else if (distance <= radius + bufferDistance) {
      // Within buffer zone - be lenient
      if (process.env.NODE_ENV === 'development') {

        console.log(`DEBUG: Distance (${distance.toFixed(2)}) is just outside radius (${radius}) but within buffer zone (${bufferDistance.toFixed(2)} miles)`);

        }
      result = true;
    }
    else {
      // Clearly outside radius
      if (process.env.NODE_ENV === 'development') {

        console.log(`DEBUG: Distance (${distance.toFixed(2)}) is outside radius (${radius}) and buffer zone (${bufferDistance.toFixed(2)} miles)`);

        }
      result = false;
    }

    if (process.env.NODE_ENV === 'development') {


      console.log('DEBUG: Is within radius:', result, `(${distance.toFixed(2)} <= ${radius} + ${bufferDistance.toFixed(2)})`);



      }
    // Cache the result in both caches
    sessionStorage.setItem(cacheKey, result.toString());
    distanceCache.set(cacheKey, result);

    return result;
  } catch (error) {
    console.error('Error checking location radius:', error);
    return true;
  }
}
