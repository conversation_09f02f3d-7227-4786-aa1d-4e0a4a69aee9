/**
 * Security utilities for input validation, sanitization, and protection
 */

import DOMPurify from 'isomorphic-dompurify';

// Email validation regex (RFC 5322 compliant)
const EMAIL_REGEX = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

// Phone number validation (international format)
const PHONE_REGEX = /^\+?[1-9]\d{1,14}$/;

// URL validation
const URL_REGEX = /^https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$/;

// Postcode validation (UK format)
const UK_POSTCODE_REGEX = /^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][ABD-HJLNP-UW-Z]{2}$/i;

/**
 * Input validation functions
 */
export const validate = {
  /**
   * Validate email address
   */
  email: (email: string): boolean => {
    if (!email || typeof email !== 'string') return false;
    return EMAIL_REGEX.test(email.trim());
  },

  /**
   * Validate phone number
   */
  phone: (phone: string): boolean => {
    if (!phone || typeof phone !== 'string') return false;
    return PHONE_REGEX.test(phone.replace(/\s/g, ''));
  },

  /**
   * Validate URL
   */
  url: (url: string): boolean => {
    if (!url || typeof url !== 'string') return false;
    return URL_REGEX.test(url.trim());
  },

  /**
   * Validate UK postcode
   */
  postcode: (postcode: string): boolean => {
    if (!postcode || typeof postcode !== 'string') return false;
    return UK_POSTCODE_REGEX.test(postcode.trim());
  },

  /**
   * Validate string length
   */
  length: (str: string, min: number, max: number): boolean => {
    if (!str || typeof str !== 'string') return false;
    const length = str.trim().length;
    return length >= min && length <= max;
  },

  /**
   * Validate required field
   */
  required: (value: any): boolean => {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string') return value.trim().length > 0;
    if (Array.isArray(value)) return value.length > 0;
    return true;
  },

  /**
   * Validate UUID format
   */
  uuid: (uuid: string): boolean => {
    if (!uuid || typeof uuid !== 'string') return false;
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  },

  /**
   * Validate numeric value
   */
  numeric: (value: any, min?: number, max?: number): boolean => {
    const num = Number(value);
    if (isNaN(num)) return false;
    if (min !== undefined && num < min) return false;
    if (max !== undefined && num > max) return false;
    return true;
  }
};

/**
 * Input sanitization functions
 */
export const sanitize = {
  /**
   * Sanitize HTML content
   */
  html: (html: string): string => {
    if (!html || typeof html !== 'string') return '';
    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li'],
      ALLOWED_ATTR: []
    });
  },

  /**
   * Sanitize plain text (remove HTML tags)
   */
  text: (text: string): string => {
    if (!text || typeof text !== 'string') return '';
    return DOMPurify.sanitize(text, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] });
  },

  /**
   * Sanitize email
   */
  email: (email: string): string => {
    if (!email || typeof email !== 'string') return '';
    return email.trim().toLowerCase();
  },

  /**
   * Sanitize phone number
   */
  phone: (phone: string): string => {
    if (!phone || typeof phone !== 'string') return '';
    return phone.replace(/[^\d+]/g, '');
  },

  /**
   * Sanitize filename
   */
  filename: (filename: string): string => {
    if (!filename || typeof filename !== 'string') return '';
    return filename.replace(/[^a-zA-Z0-9._-]/g, '').substring(0, 255);
  }
};

/**
 * Rate limiting utilities
 */
export const rateLimit = {
  /**
   * Simple in-memory rate limiter
   */
  attempts: new Map<string, { count: number; resetTime: number }>(),

  /**
   * Check if action is rate limited
   */
  isLimited: (key: string, maxAttempts: number, windowMs: number): boolean => {
    const now = Date.now();
    const record = rateLimit.attempts.get(key);

    if (!record || now > record.resetTime) {
      rateLimit.attempts.set(key, { count: 1, resetTime: now + windowMs });
      return false;
    }

    if (record.count >= maxAttempts) {
      return true;
    }

    record.count++;
    return false;
  },

  /**
   * Reset rate limit for a key
   */
  reset: (key: string): void => {
    rateLimit.attempts.delete(key);
  }
};

/**
 * Security headers for API responses
 */
export const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0'
};

/**
 * Validate form data with comprehensive checks
 */
export const validateFormData = (data: Record<string, any>, rules: Record<string, any>): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};

  for (const [field, rule] of Object.entries(rules)) {
    const value = data[field];

    if (rule.required && !validate.required(value)) {
      errors[field] = `${field} is required`;
      continue;
    }

    if (value && rule.type) {
      switch (rule.type) {
        case 'email':
          if (!validate.email(value)) errors[field] = 'Invalid email format';
          break;
        case 'phone':
          if (!validate.phone(value)) errors[field] = 'Invalid phone number';
          break;
        case 'url':
          if (!validate.url(value)) errors[field] = 'Invalid URL format';
          break;
        case 'uuid':
          if (!validate.uuid(value)) errors[field] = 'Invalid UUID format';
          break;
        case 'numeric':
          if (!validate.numeric(value, rule.min, rule.max)) {
            errors[field] = `Invalid numeric value${rule.min !== undefined ? ` (min: ${rule.min})` : ''}${rule.max !== undefined ? ` (max: ${rule.max})` : ''}`;
          }
          break;
      }
    }

    if (value && rule.length) {
      if (!validate.length(value, rule.length.min || 0, rule.length.max || Infinity)) {
        errors[field] = `${field} must be between ${rule.length.min || 0} and ${rule.length.max || 'unlimited'} characters`;
      }
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Generate secure random token
 */
export const generateSecureToken = (length: number = 32): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const randomArray = new Uint8Array(length);
  crypto.getRandomValues(randomArray);
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(randomArray[i] % chars.length);
  }
  
  return result;
};
