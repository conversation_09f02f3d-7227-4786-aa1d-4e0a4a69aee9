/**
 * Stream System Messages Utility
 *
 * This utility provides functions for sending system messages to GetStream channels
 * and integrates with the existing systemMessageService.
 */

import { Channel } from 'stream-chat';
import systemMessageService from '@/services/systemMessageService';
import { supabase } from '@/integrations/supabase/client';
import { getStreamApiUrl } from '@/utils/apiConfig';

/**
 * Send a system message to a GetStream channel
 * @param channel The GetStream channel to send the message to
 * @param text The text of the system message
 * @returns A promise that resolves to true if the message was sent successfully
 */
export const sendStreamSystemMessage = async (
  channel: Channel,
  text: string
): Promise<boolean> => {
  try {
    // Send a system message to the channel via the API route
    const response = await fetch(getStreamApiUrl('/system-message'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channelId: channel.id,
        text
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to send system message via server');
    }

    console.log('[streamSystemMessages] System message sent to GetStream via server:', text);
    return true;
  } catch (error) {
    console.error('[streamSystemMessages] Error sending system message to GetStream:', error);
    return false;
  }
};

/**
 * Create a status change message and send it to both Supabase and GetStream
 * @param taskId The ID of the task
 * @param status The new status of the task
 * @param channel The GetStream channel to send the message to
 * @param assignedTo Optional ID of the user the task is assigned to
 * @param isInternalTask Optional flag to indicate if this is an internal task
 * @returns A promise that resolves to true if the message was sent successfully
 */
export const createStatusChangeStreamMessage = async (
  taskId: string,
  status: string,
  channel: Channel,
  assignedTo?: string,
  isInternalTask?: boolean
): Promise<boolean> => {
  try {
    // First, generate the message text using the existing systemMessageService logic
    let message = '';

    // Use the same message generation logic as in systemMessageService
    switch (status) {
      case 'open':
        message = isInternalTask
          ? 'Internal task has been created and is awaiting assignment.'
          : 'Task created and is now open for offers.';
        break;
      case 'interest':
        message = 'A supplier has expressed interest in this task.';
        break;
      case 'questions':
        message = 'Discussion phase has started. Please discuss requirements before submitting formal offers.';
        break;
      case 'assigned':
        message = isInternalTask
          ? 'Task has been assigned to an internal staff member.'
          : 'Task has been assigned to a supplier.';
        break;
      case 'in_progress':
        message = isInternalTask
          ? 'Maintenance work has started on this task.'
          : 'Supplier has started work on this task.';
        break;
      case 'completed':
        message = isInternalTask
          ? 'Maintenance staff has marked this task as completed.'
          : 'Supplier has marked this task as completed.';
        break;
      case 'confirmed':
        message = 'Task has been confirmed as completed.';
        break;
      case 'payment_pending':
        message = 'Task is awaiting payment.';
        break;
      case 'paid':
        message = 'Payment has been processed for this task.';
        break;
      default:
        message = `Task status changed to: ${status}`;
    }

    // If we have assignedTo information, add it to the message
    if (assignedTo && (status === 'assigned' || status === 'in_progress')) {
      // Get the user's name
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('first_name, last_name, role')
        .eq('id', assignedTo)
        .single();

      if (!userError && userData) {
        // Use different wording based on user role
        const isMaintenanceStaff = userData.role === 'maintenance';

        const userName = userData.first_name && userData.last_name
          ? `${userData.first_name} ${userData.last_name}`
          : isMaintenanceStaff ? 'maintenance staff' : 'a supplier';

        message += ` The task has been assigned to ${userName}.`;
      }
    }

    // Send the message to GetStream
    const streamSuccess = await sendStreamSystemMessage(channel, message);

    // Also send the message to Supabase using the existing systemMessageService
    const supabaseSuccess = await systemMessageService.createStatusChangeMessage(
      taskId,
      status,
      assignedTo,
      undefined, // No thread ID needed
      isInternalTask
    );

    return streamSuccess && supabaseSuccess;
  } catch (error) {
    console.error('[streamSystemMessages] Error creating status change message:', error);
    return false;
  }
};

/**
 * Create a file upload message and send it to both Supabase and GetStream
 * @param taskId The ID of the task
 * @param userId The ID of the user who uploaded the file
 * @param fileType The type of file uploaded
 * @param channel The GetStream channel to send the message to
 * @returns A promise that resolves to true if the message was sent successfully
 */
export const createFileUploadStreamMessage = async (
  taskId: string,
  userId: string,
  fileType: string,
  channel: Channel
): Promise<boolean> => {
  try {
    // Get the user's name
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('first_name, last_name')
      .eq('id', userId)
      .single();

    let userName = 'A user';
    if (!userError && userData) {
      userName = userData.first_name && userData.last_name
        ? `${userData.first_name} ${userData.last_name}`
        : 'A user';
    }

    const message = `${userName} uploaded ${fileType === 'image' ? 'an image' : 'a file'}.`;

    // Send the message to GetStream
    const streamSuccess = await sendStreamSystemMessage(channel, message);

    // Also send the message to Supabase using the existing systemMessageService
    const supabaseSuccess = await systemMessageService.createFileUploadMessage(
      taskId,
      userId,
      fileType
    );

    return streamSuccess && supabaseSuccess;
  } catch (error) {
    console.error('[streamSystemMessages] Error creating file upload message:', error);
    return false;
  }
};

/**
 * Simplified function to create a file upload message for the PWA
 * @param channel The GetStream channel to send the message to
 * @param taskId The ID of the task
 * @param userId The ID of the user who uploaded the file
 * @param fileType The type of file uploaded
 * @returns A promise that resolves to true if the message was sent successfully
 */
export const createFileUploadMessage = async (
  channel: Channel,
  taskId: string,
  userId: string,
  fileType: string
): Promise<boolean> => {
  try {
    // Get the user's name
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('first_name, last_name')
      .eq('id', userId)
      .single();

    let userName = 'A user';
    if (!userError && userData) {
      userName = userData.first_name && userData.last_name
        ? `${userData.first_name} ${userData.last_name}`
        : 'A user';
    }

    const message = `${userName} uploaded ${fileType === 'image' ? 'an image' : 'a file'}.`;

    // Send the message to GetStream
    const streamSuccess = await sendStreamSystemMessage(channel, message);

    // Also send the message to Supabase using the existing systemMessageService
    const supabaseSuccess = await systemMessageService.createFileUploadMessage(
      taskId,
      userId,
      fileType
    );

    return streamSuccess && supabaseSuccess;
  } catch (error) {
    console.error('[streamSystemMessages] Error creating file upload message:', error);
    return false;
  }
};

export default {
  sendStreamSystemMessage,
  createStatusChangeStreamMessage,
  createFileUploadStreamMessage,
  createFileUploadMessage
};
