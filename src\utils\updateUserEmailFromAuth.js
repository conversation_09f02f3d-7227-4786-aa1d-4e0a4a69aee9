/**
 * Update a user's profile with their email from the Auth system
 * @param {Object} supabase - The Supabase client
 * @param {string} userId - The user ID
 * @returns {Promise<Object>} - The result of the update
 */
export async function updateUserEmailFromAuth(supabase, userId) {
  try {
    // Get the user from the Auth system
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(userId);
    
    if (authError) {
      console.error('Error fetching user from auth system:', authError);
      return { success: false, error: authError };
    }
    
    if (!authUser || !authUser.user) {
      console.error(`No user found in auth system with ID: ${userId}`);
      return { success: false, error: 'User not found in auth system' };
    }
    
    const authEmail = authUser.user.email;
    
    // Get the current profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('email')
      .eq('id', userId)
      .single();
    
    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return { success: false, error: profileError };
    }
    
    // Check if the email needs to be updated
    const profileEmail = Array.isArray(profile.email) ? profile.email[0] : profile.email;
    
    if (profileEmail === authEmail) {
      console.log(`Email for user ${userId} is already up to date.`);
      return { success: true, updated: false };
    }
    
    // Update the profile with the auth email
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        email: [authEmail],
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);
    
    if (updateError) {
      console.error('Error updating profile email:', updateError);
      return { success: false, error: updateError };
    }
    
    console.log(`Updated email for user ${userId} to ${authEmail}`);
    return { success: true, updated: true, email: authEmail };
  } catch (error) {
    console.error('Error in updateUserEmailFromAuth:', error);
    return { success: false, error };
  }
}

/**
 * Update all users' profiles with their emails from the Auth system
 * @param {Object} supabase - The Supabase client
 * @returns {Promise<Object>} - The result of the update
 */
export async function updateAllUsersEmailsFromAuth(supabase) {
  try {
    // Get all users with missing or empty emails
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id')
      .or('email.is.null,email.eq.{}');
    
    if (usersError) {
      console.error('Error fetching users:', usersError);
      return { success: false, error: usersError };
    }
    
    if (!users || users.length === 0) {
      console.log('No users with missing emails found.');
      return { success: true, updated: 0 };
    }
    
    console.log(`Found ${users.length} users with missing emails.`);
    
    // Update each user's email
    const results = [];
    
    for (const user of users) {
      const result = await updateUserEmailFromAuth(supabase, user.id);
      results.push({ userId: user.id, ...result });
    }
    
    const updatedCount = results.filter(r => r.updated).length;
    console.log(`Updated ${updatedCount} out of ${users.length} users.`);
    
    return { success: true, updated: updatedCount, results };
  } catch (error) {
    console.error('Error in updateAllUsersEmailsFromAuth:', error);
    return { success: false, error };
  }
}

export default {
  updateUserEmailFromAuth,
  updateAllUsersEmailsFromAuth,
};
