import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { corsHeaders } from '../_shared/cors.ts'

// Resend configuration for auth emails
const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY') || ''
const RESEND_FROM_EMAIL = Deno.env.get('RESEND_FROM_EMAIL') || '<EMAIL>'
const RESEND_FROM_NAME = Deno.env.get('RESEND_FROM_NAME') || 'Classtasker Support'

interface EmailRequest {
  to: string
  subject: string
  html: string
  text?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse the request body
    const { to, subject, html, text }: EmailRequest = await req.json()

    // Validate required fields
    if (!to || !subject || !html) {
      return new Response(
        JSON.stringify({
          error: 'Missing required fields: to, subject, html'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Validate email address
    if (!to.includes('@') || to.trim() === '') {
      return new Response(
        JSON.stringify({
          error: 'Invalid email address'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Check if Resend API key is set
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY environment variable is not set')
      return new Response(
        JSON.stringify({
          error: 'Email service not configured'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    console.log(`Sending auth email to ${to} with subject "${subject}" using Resend`)

    // Create plain text version if not provided
    const plainText = text || html.replace(/<[^>]*>/g, '')

    // Prepare the request to Resend API
    const emailData = {
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [to.trim()],
      subject: subject,
      html: html,
      text: plainText
    }

    console.log('Resend request details:', {
      apiKey: `${RESEND_API_KEY.substring(0, 8)}...`,
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: to.trim(),
      subject: subject
    })

    // Send the request to Resend API
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(emailData)
    })

    console.log(`Resend API response status: ${response.status}`)

    // Get the response text
    const responseText = await response.text()
    console.log(`Resend API response: ${responseText}`)

    // Check if the request was successful
    if (!response.ok) {
      throw new Error(`Resend API error: ${response.status} - ${responseText}`)
    }

    // Try to parse the response as JSON
    let result
    try {
      result = JSON.parse(responseText)
      console.log(`Auth email sent successfully to ${to} using Resend. Message ID: ${result.id}`)
    } catch (parseError) {
      console.log(`Auth email sent successfully to ${to} using Resend, but couldn't parse response: ${responseText}`)
      result = { success: true, response: responseText }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Email sent successfully',
        messageId: result.id || 'unknown'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error sending auth email:', error)
    return new Response(
      JSON.stringify({
        error: error.message || 'Failed to send email'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
