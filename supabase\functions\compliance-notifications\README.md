# Compliance Notifications

This Supabase Edge Function sends daily notifications to organization administrators about compliance tasks that need attention.

## Features

- Sends daily email summaries of compliance tasks to organization administrators
- Creates in-app notifications for each administrator
- Groups tasks by status (overdue, due today, upcoming)
- Provides a clean, responsive email template with task details
- Includes links to the Organization Dashboard for easy access

## How It Works

1. The Edge Function runs daily (typically at 7:00 AM)
2. It retrieves all organizations and their admin users
3. For each organization, it fetches outstanding compliance tasks
4. Tasks are grouped into three categories:
   - Overdue tasks (past due date)
   - Tasks due today
   - Upcoming tasks (due within the next 7 days)
5. For each admin user, it:
   - Creates an in-app notification in the existing notification system
   - Sends an email using the existing email service

## Deployment

### 1. Deploy the Edge Function

The simplest way to deploy is using the provided script:

```bash
# Navigate to the project root
cd /path/to/project

# Run the deployment script
node scripts/deploy-compliance-notifications.js
```

Alternatively, you can deploy manually:

```bash
# Deploy the Edge Function
npx supabase functions deploy compliance-notifications --project-ref qcnotlojmyvpqbbgoxbc

# Set environment variables
npx supabase secrets set APP_URL=https://classtasker.com --project-ref qcnotlojmyvpqbbgoxbc
```

### 2. Schedule the Function

#### Option 1: Using the Node.js Scheduler (Recommended)

This option uses the existing email configuration and is the simplest to set up:

1. Install dependencies:
   ```bash
   npm install node-fetch cron dotenv
   ```

2. Create a `.env` file with your Supabase credentials:
   ```
   VITE_SUPABASE_URL=https://qcnotlojmyvpqbbgoxbc.supabase.co
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

3. Run the scheduler:
   ```bash
   node scripts/schedule-compliance-notifications.js
   ```

4. To run it as a background service, use PM2:
   ```bash
   npm install -g pm2
   pm2 start scripts/schedule-compliance-notifications.js --name compliance-notifications
   pm2 save
   pm2 startup
   ```

#### Option 2: Using Supabase Cron Jobs (Pro Plan)

If you have the Supabase Pro plan, you can use the built-in scheduler:

1. Go to your Supabase dashboard
2. Navigate to Database > Functions > Hooks
3. Create a new cron job with the following settings:
   - Name: `daily-compliance-notifications`
   - Schedule: `0 7 * * *` (runs daily at 7:00 AM)
   - Function: `https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/compliance-notifications`
   - HTTP Method: `POST`
   - Headers: `Authorization: Bearer your-service-role-key`

#### Option 3: Using GitHub Actions

You can also use GitHub Actions to trigger the function daily:

1. Create a new file `.github/workflows/compliance-notifications.yml`:
   ```yaml
   name: Daily Compliance Notifications

   on:
     schedule:
       - cron: '0 7 * * *'  # Run daily at 7:00 AM UTC

   jobs:
     trigger-notifications:
       runs-on: ubuntu-latest
       steps:
         - name: Trigger Compliance Notifications
           run: |
             curl -X POST https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/compliance-notifications \
               -H "Authorization: Bearer ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" \
               -H "Content-Type: application/json"
   ```
2. Add your `SUPABASE_SERVICE_ROLE_KEY` to GitHub repository secrets

## Testing

You can manually trigger the function to test it:

```bash
# Using the provided script
node scripts/schedule-compliance-notifications.js --run-now

# Or using curl
curl -X POST https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/compliance-notifications \
  -H "Authorization: Bearer your-service-role-key" \
  -H "Content-Type: application/json"
```

## Integration with Existing Systems

This function integrates with:

1. **Email System**: Uses the existing `send-email` Edge Function with the standard SMTP configuration
2. **Notification System**: Creates notifications in the existing `notifications` table
3. **User Management**: Fetches admin users from the `profiles` table
4. **Compliance Tasks**: Retrieves tasks from the `compliance_tasks` table

No additional configuration is needed as it uses the same email setup that's already in place for the rest of the application.
