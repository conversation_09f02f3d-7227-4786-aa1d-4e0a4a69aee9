// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'
import { format, addDays, isAfter, isBefore } from 'https://esm.sh/date-fns'

const SUPABASE_URL = Deno.env.get('SUPABASE_URL') || ''
const SUPABASE_ANON_KEY = Deno.env.get('SUPABASE_ANON_KEY') || ''
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const APP_URL = Deno.env.get('APP_URL') || 'https://classtasker.com'

// Resend configuration
const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY') || ''
const RESEND_FROM_EMAIL = Deno.env.get('RESEND_FROM_EMAIL') || '<EMAIL>'
const RESEND_FROM_NAME = Deno.env.get('RESEND_FROM_NAME') || 'ClassTasker Compliance'

interface ComplianceTask {
  id: string
  title: string
  description: string
  cycle: 'daily' | 'weekly' | 'monthly' | 'annually'
  next_due_date: string
  organization_id: string
}

interface Organization {
  id: string
  name: string
}

interface AdminUser {
  id: string
  email: string
  organization_id: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the service role key
    const supabase = createClient(
      SUPABASE_URL,
      SUPABASE_SERVICE_ROLE_KEY
    )

    // Get all organizations
    const { data: organizations, error: orgError } = await supabase
      .from('organizations')
      .select('id, name')

    if (orgError) {
      throw orgError
    }

    // Track results for reporting
    const results = {
      totalOrganizations: organizations.length,
      processedOrganizations: 0,
      emailsSent: 0,
      notificationsCreated: 0,
      errors: [] as string[]
    }

    // Process each organization
    for (const organization of organizations) {
      try {
        // Get admin users for this organization
        const { data: adminUsers, error: adminError } = await supabase
          .from('profiles')
          .select('id, email')
          .eq('organization_id', organization.id)
          .eq('role', 'admin')

        if (adminError) {
          throw adminError
        }

        if (!adminUsers || adminUsers.length === 0) {
          console.log(`No admin users found for organization ${organization.id}`)
          continue
        }

        // Log the admin users for debugging
        console.log(`Found ${adminUsers.length} admin users for organization ${organization.id}:`, adminUsers)

        // Get outstanding compliance tasks for this organization
        const today = new Date()
        const { data: tasks, error: tasksError } = await supabase
          .from('compliance_tasks')
          .select('*')
          .eq('organization_id', organization.id)
          .is('is_completed', false)
          .order('next_due_date', { ascending: true })

        if (tasksError) {
          throw tasksError
        }

        // Group tasks by status
        const overdueTasks = tasks.filter(task => new Date(task.next_due_date) < today)
        const dueTodayTasks = tasks.filter(task => {
          const taskDate = new Date(task.next_due_date)
          return taskDate.getDate() === today.getDate() &&
                 taskDate.getMonth() === today.getMonth() &&
                 taskDate.getFullYear() === today.getFullYear()
        })
        const upcomingTasks = tasks.filter(task => {
          const taskDate = new Date(task.next_due_date)
          return taskDate > today &&
                 taskDate <= new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
        })

        // Skip if no tasks to report
        if (overdueTasks.length === 0 && dueTodayTasks.length === 0 && upcomingTasks.length === 0) {
          console.log(`No outstanding tasks for organization ${organization.id}`)
          continue
        }

        // Generate email content
        const emailContent = generateEmailContent(
          organization.name,
          overdueTasks,
          dueTodayTasks,
          upcomingTasks
        )

        // Send email and create notification for each admin
        for (const admin of adminUsers) {
          // Extract email from the admin user
          // The email could be stored as an array with curly braces like '{<EMAIL>}'
          let adminEmail = '';

          if (Array.isArray(admin.email) && admin.email.length > 0) {
            // If it's an array, use the first email
            adminEmail = admin.email[0];
          } else if (typeof admin.email === 'string') {
            // If it's a string, use it directly
            adminEmail = admin.email;
          } else if (admin.email && typeof admin.email === 'object') {
            // If it's an object (like PostgreSQL array representation), try to extract the first email
            const emailStr = JSON.stringify(admin.email);
            const match = emailStr.match(/"([^"]+@[^"]+)"/);
            if (match && match[1]) {
              adminEmail = match[1];
            }
          }

          console.log(`Extracted email for admin ${admin.id}: "${adminEmail}" (original: ${JSON.stringify(admin.email)})`);

          if (!adminEmail) {
            console.log(`Admin ${admin.id} has no valid email address`)
            continue
          }

          // Create a notification in the database
          const notificationMessage = `Daily compliance tasks summary: ${overdueTasks.length} overdue, ${dueTodayTasks.length} due today, ${upcomingTasks.length} upcoming`

          try {
            const { error: notificationError } = await supabase
              .from('notifications')
              .insert({
                user_id: admin.id,
                type: 'system',
                message: notificationMessage,
                related_type: 'compliance',
                related_id: organization.id,
                read: false,
                email_sent: false
              })

            if (notificationError) {
              console.error(`Error creating notification for admin ${admin.id}:`, notificationError)
              results.errors.push(`Failed to create notification for admin ${admin.id}: ${notificationError.message}`)
            } else {
              results.notificationsCreated++
            }
          } catch (notificationError) {
            console.error(`Error creating notification for admin ${admin.id}:`, notificationError)
            results.errors.push(`Failed to create notification for admin ${admin.id}: ${notificationError.message}`)
          }

          // Send email to the admin email address
          // Now that we have a verified domain, we can send emails to any recipient
          const emailSuccess = await sendEmail(
            adminEmail,
            `Daily Compliance Tasks Summary - ${format(today, 'dd MMM yyyy')}`,
            emailContent
          )

          if (emailSuccess) {
            results.emailsSent++
            console.log(`Successfully sent email to ${adminEmail}`)
          } else {
            console.error(`Failed to send email to ${adminEmail}`)
            results.errors.push(`Failed to send email to ${adminEmail}`)
          }
        }

        results.processedOrganizations++
      } catch (orgProcessError) {
        console.error(`Error processing organization ${organization.id}:`, orgProcessError)
        results.errors.push(`Failed to process organization ${organization.id}: ${orgProcessError.message}`)
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Compliance notifications processed',
        results
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

function generateEmailContent(
  organizationName: string,
  overdueTasks: ComplianceTask[],
  dueTodayTasks: ComplianceTask[],
  upcomingTasks: ComplianceTask[]
): string {
  const formatDate = (dateString: string) => format(new Date(dateString), 'dd MMM yyyy')

  let content = `
    <html>
    <head>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        h1 { color: #2563eb; margin-bottom: 20px; }
        h2 { color: #1e40af; margin-top: 30px; margin-bottom: 10px; }
        .task { margin-bottom: 15px; padding: 10px; border-radius: 5px; }
        .overdue { background-color: #fee2e2; border-left: 4px solid #ef4444; }
        .due-today { background-color: #e0f2fe; border-left: 4px solid #0ea5e9; }
        .upcoming { background-color: #f0fdf4; border-left: 4px solid #22c55e; }
        .task-title { font-weight: bold; margin-bottom: 5px; }
        .task-date { color: #666; font-size: 0.9em; }
        .task-cycle { display: inline-block; padding: 2px 8px; border-radius: 12px; font-size: 0.8em; margin-left: 5px; }
        .daily { background-color: #e0f2fe; color: #0369a1; }
        .weekly { background-color: #f0fdf4; color: #166534; }
        .monthly { background-color: #fef3c7; color: #92400e; }
        .annually { background-color: #fce7f3; color: #9d174d; }
        .footer { margin-top: 30px; font-size: 0.8em; color: #666; border-top: 1px solid #eee; padding-top: 15px; }
        .cta-button { display: inline-block; background-color: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 20px; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>Compliance Tasks Summary for ${organizationName}</h1>
        <p>Here's your daily summary of compliance tasks that need attention:</p>
  `

  // Add overdue tasks
  if (overdueTasks.length > 0) {
    content += `
        <h2>⚠️ Overdue Tasks (${overdueTasks.length})</h2>
    `

    overdueTasks.forEach(task => {
      content += `
        <div class="task overdue">
          <div class="task-title">${task.title} <span class="task-cycle ${task.cycle}">${task.cycle}</span></div>
          <div class="task-date">Due: ${formatDate(task.next_due_date)}</div>
          ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
        </div>
      `
    })
  }

  // Add due today tasks
  if (dueTodayTasks.length > 0) {
    content += `
        <h2>📅 Due Today (${dueTodayTasks.length})</h2>
    `

    dueTodayTasks.forEach(task => {
      content += `
        <div class="task due-today">
          <div class="task-title">${task.title} <span class="task-cycle ${task.cycle}">${task.cycle}</span></div>
          <div class="task-date">Due: ${formatDate(task.next_due_date)}</div>
          ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
        </div>
      `
    })
  }

  // Add upcoming tasks
  if (upcomingTasks.length > 0) {
    content += `
        <h2>🔜 Upcoming Tasks (${upcomingTasks.length})</h2>
    `

    upcomingTasks.forEach(task => {
      content += `
        <div class="task upcoming">
          <div class="task-title">${task.title} <span class="task-cycle ${task.cycle}">${task.cycle}</span></div>
          <div class="task-date">Due: ${formatDate(task.next_due_date)}</div>
          ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
        </div>
      `
    })
  }

  // Add CTA and footer
  content += `
        <a href="${APP_URL}/organization" class="cta-button">View All Tasks</a>

        <div class="footer">
          <p>This is an automated message from ClassTasker. Please do not reply to this email.</p>
          <p>To manage your notification settings, please visit your profile settings.</p>
        </div>
      </div>
    </body>
    </html>
  `

  return content
}

async function sendEmail(to: string, subject: string, htmlContent: string) {
  try {
    // Validate email address
    if (!to || !to.includes('@') || to.trim() === '') {
      console.error(`Invalid email address: "${to}"`);
      return false;
    }

    // Clean up the email address
    to = to.trim();

    // Check if Resend API key is set
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY environment variable is not set. Email sending will fail.');
      return false;
    }

    console.log(`Sending email to ${to} with subject "${subject}" using Resend`);

    // Create plain text version by stripping HTML tags
    const plainText = htmlContent.replace(/<[^>]*>/g, '');

    // For debugging, log the request details
    console.log('Resend request details:', {
      apiKey: `${RESEND_API_KEY.substring(0, 8)}...`,
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: to,
      subject: subject
    });

    // Prepare the request to Resend API
    const data = {
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [to],
      subject: subject,
      html: htmlContent,
      text: plainText
    };

    // Send the request to Resend API
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });

    // Log the response status
    console.log(`Resend API response status: ${response.status}`);

    // Get the response text
    const responseText = await response.text();
    console.log(`Resend API response: ${responseText}`);

    // Check if the request was successful
    if (!response.ok) {
      throw new Error(`Resend API error: ${response.status} - ${responseText}`);
    }

    // Try to parse the response as JSON
    let result;
    try {
      result = JSON.parse(responseText);
      console.log(`Email sent successfully to ${to} using Resend. Message ID: ${result.id}`);
    } catch (parseError) {
      console.log(`Email sent successfully to ${to} using Resend, but couldn't parse response: ${responseText}`);
    }

    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    // Don't throw the error, just log it and return false
    return false;
  }
}
