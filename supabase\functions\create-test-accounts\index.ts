
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log("Creating test accounts")
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const testAccounts = [
      {
        email: "<EMAIL>",
        password: "SchoolTest123!",
        options: {
          data: {
            name: "Test School Account",
            account_type: "school"
          }
        }
      },
      {
        email: "<EMAIL>",
        password: "SupplierTest123!",
        options: {
          data: {
            name: "Test Supplier Account", 
            account_type: "supplier"
          }
        }
      }
    ]

    const results = await Promise.all(
      testAccounts.map(async (account) => {
        console.log(`Processing account: ${account.email}`)
        
        // Check if user exists first
        const { data: existingUserData, error: listError } = await supabase.auth.admin.listUsers({
          filter: `email.eq.${account.email}`
        });
        
        if (listError) {
          console.error(`Error listing users for ${account.email}:`, listError)
          return { email: account.email, error: listError.message }
        }
        
        let userData;
        let error;
        
        if (existingUserData?.users && existingUserData.users.length > 0) {
          // User exists, ensure email is confirmed
          const userId = existingUserData.users[0].id;
          console.log(`User ${account.email} exists with ID: ${userId}, confirming email`)
          
          const { data, error: updateError } = await supabase.auth.admin.updateUserById(
            userId,
            { 
              email_confirm: true,
              password: account.password
            }
          );
          
          if (updateError) {
            console.error(`Error updating user ${account.email}:`, updateError)
          } else {
            console.log(`Successfully confirmed email for ${account.email}`)
          }
          
          userData = data;
          error = updateError;
        } else {
          // Create new user with email already confirmed
          console.log(`Creating new user for ${account.email} with confirmed email`)
          
          const { data, error: createError } = await supabase.auth.admin.createUser({
            email: account.email,
            password: account.password,
            email_confirm: true,
            user_metadata: {
              name: account.options.data.name,
              account_type: account.options.data.account_type
            }
          });
          
          if (createError) {
            console.error(`Error creating user ${account.email}:`, createError)
          } else {
            console.log(`Successfully created user ${account.email}`)
          }
          
          userData = data;
          error = createError;
        }

        return { 
          email: account.email, 
          data: userData,
          error: error ? error.message : null
        };
      })
    );

    console.log("Test accounts creation completed", results)

    return new Response(JSON.stringify(results), {
      headers: { 
        ...corsHeaders, 
        'Content-Type': 'application/json' 
      },
    });
  } catch (error) {
    console.error("Unexpected error in create-test-accounts function:", error)
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { 
        ...corsHeaders, 
        'Content-Type': 'application/json' 
      },
      status: 500,
    });
  }
});
