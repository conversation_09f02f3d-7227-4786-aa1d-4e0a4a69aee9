import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Resend configuration
const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY') || ''
const RESEND_FROM_EMAIL = '<EMAIL>'
const RESEND_FROM_NAME = 'Classtasker'

interface InvitationEmailRequest {
  to: string
  token: string
  organizationName: string
  role: string
  inviterName?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Log raw request details
    console.log('📧 Raw request details:', {
      method: req.method,
      url: req.url,
      headers: Object.fromEntries(req.headers.entries())
    })

    // Parse the request body
    const requestBody = await req.json()
    console.log('📧 Raw request body:', JSON.stringify(requestBody, null, 2))
    console.log('📧 Request body type:', typeof requestBody)
    console.log('📧 Request body keys:', Object.keys(requestBody || {}))

    console.log('📧 Invitation email request received:', {
      to: requestBody.to,
      organizationName: requestBody.organizationName,
      role: requestBody.role,
      inviterName: requestBody.inviterName,
      hasToken: !!requestBody.token
    })

    const { to, token, organizationName, role, inviterName }: InvitationEmailRequest = requestBody

    // Validate required fields
    if (!to || !token || !organizationName || !role) {
      console.error('❌ Missing required fields:', {
        to: !!to,
        token: !!token,
        organizationName: !!organizationName,
        role: !!role
      })
      return new Response(
        JSON.stringify({
          error: 'Missing required fields: to, token, organizationName, role'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Validate email address
    if (!to.includes('@') || to.trim() === '') {
      return new Response(
        JSON.stringify({
          error: 'Invalid email address'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Check if Resend API key is set
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY environment variable is not set')
      return new Response(
        JSON.stringify({
          error: 'Email service not configured'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    console.log(`Sending invitation email to ${to} for ${organizationName} (${role}) using Resend`)

    // Generate invitation URL - only include token, email will be looked up from database
    const invitationUrl = `https://classtasker.com/invitation/accept?token=${token}`

    // Generate the email content
    const { subject, emailContent } = generateInvitationEmailContent(to, token, organizationName, role, invitationUrl, inviterName)

    // Send the email using Resend API
    const emailSuccess = await sendEmail(to, subject, emailContent)

    if (emailSuccess) {
      console.log(`Successfully sent invitation email to ${to}`)
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Invitation email sent successfully'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    } else {
      throw new Error('Failed to send invitation email')
    }

  } catch (error) {
    console.error('Error sending invitation email:', error)
    return new Response(
      JSON.stringify({
        error: error.message || 'Failed to send invitation email'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

function generateInvitationEmailContent(email: string, token: string, organizationName: string, role: string, invitationUrl: string, inviterName?: string): { subject: string, emailContent: string } {
  const subject = `Invitation to join ${organizationName} on Classtasker`

  const emailContent = `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Invitation to Classtasker</title>
</head>
<body style="font-family: Arial, Helvetica, sans-serif; line-height: 1.6; color: #333333; margin: 0; padding: 0; background-color: #f5f5f5;">
  <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
    <tr>
      <td style="background-color: #4f46e5; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0;">
        <h1 style="margin: 0; font-size: 24px;">You've been invited to join ${organizationName}</h1>
      </td>
    </tr>
    <tr>
      <td style="padding: 20px; border: 1px solid #e0e0e0; border-top: none; border-radius: 0 0 5px 5px;">
        <p style="margin-top: 0;">Hello,</p>
        <p>You have been invited to join <strong>${organizationName}</strong> as a <strong>${role}</strong> on Classtasker${inviterName ? ` by ${inviterName}` : ''}.</p>
        <p>Classtasker is a platform that connects schools with maintenance workers and suppliers to efficiently manage tasks and services.</p>
        <p>To accept this invitation, please click the button below:</p>
        <table border="0" cellpadding="0" cellspacing="0" width="100%">
          <tr>
            <td align="center" style="padding: 20px 0;">
              <table border="0" cellpadding="0" cellspacing="0">
                <tr>
                  <td align="center" bgcolor="#4f46e5" style="border-radius: 5px;">
                    <a href="${invitationUrl}" target="_blank" style="display: inline-block; padding: 12px 24px; font-family: Arial, sans-serif; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 5px; font-weight: bold;">Accept Invitation</a>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
        <p>Or copy and paste this URL into your browser:</p>
        <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 3px; font-size: 14px;">${invitationUrl}</p>
        <p>This invitation will expire in 7 days.</p>
        <p style="margin-bottom: 0;">If you have any questions, please contact the organization administrator.</p>
      </td>
    </tr>
    <tr>
      <td style="padding: 20px; font-size: 12px; color: #666; text-align: center;">
        <p style="margin-top: 0;">This is an automated message from Classtasker. Please do not reply to this email.</p>
        <p style="margin-bottom: 0;">&copy; ${new Date().getFullYear()} Classtasker. All rights reserved.</p>
      </td>
    </tr>
  </table>
</body>
</html>`

  return { subject, emailContent }
}

async function sendEmail(to: string, subject: string, htmlContent: string): Promise<boolean> {
  try {
    // Validate email address
    if (!to || !to.includes('@') || to.trim() === '') {
      console.error(`Invalid email address: "${to}"`)
      return false
    }

    // Clean up the email address
    to = to.trim()

    // Check if Resend API key is set
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY environment variable is not set. Email sending will fail.')
      return false
    }

    console.log(`Sending email to ${to} with subject "${subject}" using Resend`)

    // Create plain text version by stripping HTML tags
    const plainText = htmlContent.replace(/<[^>]*>/g, '')

    // For debugging, log the request details (but mask the API key)
    console.log('Resend request details:', {
      apiKey: RESEND_API_KEY ? `${RESEND_API_KEY.substring(0, 8)}...` : 'NOT_SET',
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: to,
      subject: subject,
      bodyLength: htmlContent.length
    })

    // Prepare the request to Resend API
    const data = {
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [to],
      subject: subject,
      html: htmlContent,
      text: plainText
    }

    console.log('Sending request to Resend API...')

    // Send the request to Resend API
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })

    // Log the response status
    console.log(`Resend API response status: ${response.status}`)

    // Get the response text
    const responseText = await response.text()
    console.log(`Resend API response body: ${responseText}`)

    // Check if the request was successful
    if (!response.ok) {
      console.error(`Resend API error: ${response.status} - ${responseText}`)
      throw new Error(`Resend API error: ${response.status} - ${responseText}`)
    }

    // Try to parse the response as JSON
    let result
    try {
      result = JSON.parse(responseText)
      console.log(`✅ Email sent successfully to ${to} using Resend. Message ID: ${result.id}`)
      return true
    } catch (parseError) {
      console.error(`Failed to parse Resend response as JSON: ${parseError}`)
      console.log(`Raw response: ${responseText}`)
      // If we got a 200 but can't parse JSON, still consider it a success
      if (response.status === 200) {
        console.log(`✅ Email likely sent successfully to ${to}, but response format unexpected`)
        return true
      }
      return false
    }

  } catch (error) {
    console.error(`❌ Error sending email to ${to}:`, error)
    console.error('Error details:', {
      message: error.message,
      stack: error.stack
    })
    return false
  }
}
