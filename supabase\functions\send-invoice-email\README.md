# Send Invoice Email Edge Function

This Edge Function sends an invoice email using Stripe's API.

## Functionality

The function:
1. Receives a request with an invoice ID
2. Uses <PERSON>e's API to send the invoice email
3. Returns a success response with the sent invoice details

## API

### Request

```json
{
  "invoiceId": "in_1234567890"
}
```

### Response (Success)

```json
{
  "sent": true,
  "invoice": {
    "id": "in_1234567890",
    "number": "INV-001",
    "hosted_invoice_url": "https://invoice.stripe.com/i/...",
    "status": "open",
    // Other invoice properties...
  }
}
```

### Response (Error)

```json
{
  "error": "Failed to send invoice email",
  "details": "Error message from <PERSON><PERSON>"
}
```

## Deployment

To deploy this function:

```bash
# Set the Stripe secret key
supabase secrets set STRIPE_SECRET_KEY=sk_test_...

# Deploy the function
supabase functions deploy send-invoice-email
```

## Usage from Client

```typescript
// Get the user's session
const { data: { session } } = await supabase.auth.getSession();

// Call the Edge Function
const response = await fetch(
  `${import.meta.env.VITE_SUPABASE_FUNCTIONS_URL}/send-invoice-email`,
  {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    },
    body: JSON.stringify({ invoiceId: 'in_1234567890' }),
  }
);

const data = await response.json();
```
