import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@12.0.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Stripe - prefer live key if available
    const liveKey = Deno.env.get('STRIPE_LIVE_SECRET_KEY');
    const testKey = Deno.env.get('STRIPE_SECRET_KEY');
    const apiKey = liveKey || testKey || '';
    const isLiveMode = !!liveKey;

    console.log(`Using Stripe in ${isLiveMode ? 'LIVE' : 'TEST'} mode`);

    const stripe = new Stripe(apiKey, {
      apiVersion: '2023-10-16',
    })

    // Get the invoice ID from the request
    const { invoiceId } = await req.json()

    if (!invoiceId) {
      return new Response(
        JSON.stringify({ error: 'Invoice ID is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Sending email for invoice ${invoiceId}`)

    try {
      // First, retrieve the invoice to check its details
      console.log(`Retrieving invoice ${invoiceId} before sending...`)
      const invoice = await stripe.invoices.retrieve(invoiceId)

      console.log('Invoice details:', {
        id: invoice.id,
        customer_email: invoice.customer_email,
        customer_id: invoice.customer,
        status: invoice.status,
        collection_method: invoice.collection_method,
        number: invoice.number
      })

      // Send the invoice email
      console.log(`Sending invoice ${invoiceId} email...`)
      const sentInvoice = await stripe.invoices.sendInvoice(invoiceId)

      console.log('Invoice email sent successfully:', {
        id: sentInvoice.id,
        status: sentInvoice.status,
        hosted_invoice_url: sentInvoice.hosted_invoice_url
      })

      // Prepare response message based on mode
      const responseMessage = isLiveMode
        ? "Invoice email sent successfully. Since we're in LIVE mode, an actual email has been sent to the customer's email address."
        : "Invoice email sent successfully. Note: In test mode, Stripe doesn't actually send emails to real addresses. Check the Stripe Dashboard Events section to see the attempted email.";

      return new Response(
        JSON.stringify({
          sent: true,
          invoice: sentInvoice,
          message: responseMessage,
          mode: isLiveMode ? 'live' : 'test'
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } catch (stripeError) {
      console.error('Stripe error sending invoice email:', stripeError)

      return new Response(
        JSON.stringify({
          error: 'Stripe error sending invoice email',
          details: stripeError.message
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
  } catch (error) {
    console.error('Error sending invoice email:', error)

    return new Response(
      JSON.stringify({ error: 'Failed to send invoice email' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
