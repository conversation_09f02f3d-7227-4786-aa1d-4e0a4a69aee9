// Support Email Edge Function
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";
import { SmtpClient } from "https://deno.land/x/smtp@v0.7.0/mod.ts";

// Environment variables
const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY");
const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

// Interface for the support email payload
interface SupportEmailPayload {
  from: string;
  to: string;
  subject: string;
  name: string;
  email: string;
  organization?: string;
  organization_id?: string;
  support_type: string;
  message: string;
}

// Main handler function
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("Processing support email request");
    
    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

    // Get the request body
    const payload: SupportEmailPayload = await req.json();
    
    // Validate required fields
    if (!payload.name || !payload.email || !payload.support_type || !payload.message) {
      console.error("Missing required fields in payload");
      return new Response(
        JSON.stringify({ error: "Missing required fields" }),
        { 
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    // Create a client and connect to the SMTP server
    const client = new SmtpClient();

    try {
      console.log("Connecting to SMTP server...");
      await client.connectTLS({
        hostname: "smtp.resend.com",
        port: 465,
        username: "resend",
        password: RESEND_API_KEY || "",
      });
      console.log("Connected to SMTP server successfully");

      // Format the email content with HTML
      const htmlContent = `
        <h2>Support Request from ClassTasker</h2>
        <p><strong>Support Type:</strong> ${payload.support_type}</p>
        <p><strong>From:</strong> ${payload.name} (${payload.email})</p>
        ${payload.organization ? `<p><strong>Organization:</strong> ${payload.organization}</p>` : ""}
        ${payload.organization_id ? `<p><strong>Organization ID:</strong> ${payload.organization_id}</p>` : ""}
        <h3>Message:</h3>
        <p>${payload.message.replace(/\n/g, "<br>")}</p>
      `;

      console.log("Sending email...");
      // Send the email
      await client.send({
        from: payload.from,
        to: payload.to,
        subject: payload.subject,
        content: htmlContent,
        html: htmlContent,
      });
      console.log("Email sent successfully");

      // Log the support request in the database
      const { error: logError } = await supabaseAdmin
        .from("support_requests")
        .insert({
          name: payload.name,
          email: payload.email,
          organization: payload.organization,
          organization_id: payload.organization_id,
          support_type: payload.support_type,
          message: payload.message,
          status: "new"
        });

      if (logError) {
        console.error("Error logging support request:", logError);
      }

      await client.close();

      return new Response(
        JSON.stringify({ success: true }),
        { 
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Error sending email:", error);
      try {
        await client.close();
      } catch (closeError) {
        console.error("Error closing SMTP client:", closeError);
      }
      
      return new Response(
        JSON.stringify({ 
          error: "Failed to send email", 
          details: error.message || "Unknown error"
        }),
        { 
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }
  } catch (error) {
    console.error("Error processing request:", error);
    
    return new Response(
      JSON.stringify({ error: "Failed to send support email" }),
      { 
        status: 500,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      }
    );
  }
});
