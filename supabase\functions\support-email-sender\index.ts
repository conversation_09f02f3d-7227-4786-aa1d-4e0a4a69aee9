// Support Email Edge Function
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

// Define CORS headers directly
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type"
};

// Environment variables
const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY");
const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
const APP_URL = Deno.env.get("APP_URL") || "https://classtasker.com";

// Interface for the support email payload
interface SupportEmailPayload {
  from: string;
  to: string;
  subject: string;
  name: string;
  email: string;
  organization?: string;
  organization_id?: string;
  support_type: string;
  message: string;
  html_content?: string; // Optional HTML content to use directly
}

// Main handler function
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("Processing support email request");

    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

    // Get the request body
    const payload: SupportEmailPayload = await req.json();

    // Validate required fields
    if (!payload.name || !payload.email || !payload.support_type || !payload.message) {
      console.error("Missing required fields in payload");
      return new Response(
        JSON.stringify({ error: "Missing required fields" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    try {
      // Use provided HTML content or generate it
      const htmlContent = payload.html_content || generateEmailContent(payload);

      // Create plain text version by stripping HTML tags
      const plainText = htmlContent.replace(/<[^>]*>/g, '');

      console.log("Sending email via Resend API...");

      // For debugging, log the request details
      console.log('Resend request details:', {
        apiKey: `${RESEND_API_KEY?.substring(0, 8)}...`,
        from: payload.from,
        to: payload.to,
        subject: payload.subject,
        useCustomHtml: !!payload.html_content
      });

      // Prepare the request to Resend API
      const data = {
        from: payload.from,
        to: [payload.to],
        subject: payload.subject,
        html: htmlContent,
        text: plainText
      };

      // Send the request to Resend API
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${RESEND_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      // Log the response status
      console.log(`Resend API response status: ${response.status}`);

      // Get the response text
      const responseText = await response.text();
      console.log(`Resend API response: ${responseText}`);

      // Check if the request was successful
      if (!response.ok) {
        throw new Error(`Resend API error: ${response.status} - ${responseText}`);
      }

      // Try to parse the response as JSON
      let result;
      try {
        result = JSON.parse(responseText);
        console.log(`Email sent successfully to ${payload.to} using Resend. Message ID: ${result.id}`);
      } catch (parseError) {
        console.log(`Email sent successfully to ${payload.to} using Resend, but couldn't parse response: ${responseText}`);
      }

      // Log the support request in the database
      const { error: logError } = await supabaseAdmin
        .from("support_requests")
        .insert({
          name: payload.name,
          email: payload.email,
          organization: payload.organization,
          organization_id: payload.organization_id,
          support_type: payload.support_type,
          message: payload.message,
          status: "new"
        });

      if (logError) {
        console.error("Error logging support request:", logError);
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: "Support request submitted and email sent successfully",
          data: result
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    } catch (error) {
      console.error("Error sending email:", error);

      return new Response(
        JSON.stringify({
          error: "Failed to send email",
          details: error.message || "Unknown error"
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }
  } catch (error) {
    console.error("Error processing request:", error);

    return new Response(
      JSON.stringify({ error: "Failed to send support email" }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        }
      }
    );
  }
});

// Function to generate HTML email content
function generateEmailContent(payload: SupportEmailPayload): string {
  return `
    <html>
    <head>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        h1 { color: #2563eb; margin-bottom: 20px; }
        h2 { color: #1e40af; margin-top: 30px; margin-bottom: 10px; }
        .support-info { margin-bottom: 15px; padding: 10px; border-radius: 5px; background-color: #f0f9ff; border-left: 4px solid #0ea5e9; }
        .message-box { margin-top: 20px; padding: 15px; background-color: #f9fafb; border-radius: 5px; border: 1px solid #e5e7eb; }
        .footer { margin-top: 30px; font-size: 0.8em; color: #666; border-top: 1px solid #eee; padding-top: 15px; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>Support Request from ClassTasker</h1>

        <div class="support-info">
          <p><strong>Support Type:</strong> ${payload.support_type}</p>
          <p><strong>From:</strong> ${payload.name} (${payload.email})</p>
          ${payload.organization ? `<p><strong>Organization:</strong> ${payload.organization}</p>` : ""}
          ${payload.organization_id ? `<p><strong>Organization ID:</strong> ${payload.organization_id}</p>` : ""}
        </div>

        <h2>Message:</h2>
        <div class="message-box">
          <p>${payload.message.replace(/\n/g, "<br>")}</p>
        </div>

        <div class="footer">
          <p>This is an automated message from ClassTasker. Please do not reply to this email.</p>
          <p>To respond to this support request, please log in to the ClassTasker admin panel.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
