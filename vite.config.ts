import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import fs from "fs";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  // Define environment variables that should be exposed to the client
  // SECURITY: Only include public environment variables here
  define: {
    // Expose Supabase environment variables (anon key is safe for client-side)
    'import.meta.env.VITE_SUPABASE_URL': JSON.stringify(process.env.VITE_SUPABASE_URL),
    'import.meta.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(process.env.VITE_SUPABASE_ANON_KEY),
    'import.meta.env.VITE_SUPABASE_FUNCTIONS_URL': JSON.stringify(process.env.VITE_SUPABASE_FUNCTIONS_URL),
    // Expose client-safe API keys
    'import.meta.env.VITE_GETSTREAM_API_KEY': JSON.stringify(process.env.VITE_GETSTREAM_API_KEY),
    'import.meta.env.VITE_GOOGLE_MAPS_API_KEY': JSON.stringify(process.env.VITE_GOOGLE_MAPS_API_KEY),
    'import.meta.env.VITE_STRIPE_PUBLIC_KEY': JSON.stringify(process.env.VITE_STRIPE_PUBLIC_KEY),
  },
  server: {
    host: "::",
    port: 8082,
    strictPort: false,
    proxy: {
      '/api/admin-users': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      }
    }
  },
  plugins: [
    react(),
    {
      name: 'api-routes',
      configureServer(server) {
        server.middlewares.use(async (req, res, next) => {
          const url = req.url || '';

          // Check if the request is for an API route
          if (url.startsWith('/api/')) {
            console.log(`[API Route] Handling request for ${url}`);

            // Get the API route file path
            const routeName = url.replace('/api/', '').split('?')[0];
            const apiFilePath = path.resolve(__dirname, 'src/pages/api', `${routeName}.js`);
            const tsApiFilePath = path.resolve(__dirname, 'src/pages/api', `${routeName}.ts`);

            // Check if the API route file exists (first try .js, then .ts)
            let finalApiFilePath = apiFilePath;
            if (fs.existsSync(apiFilePath)) {
              console.log(`[API Route] Found API route file: ${apiFilePath}`);
            } else if (fs.existsSync(tsApiFilePath)) {
              console.log(`[API Route] Found TypeScript API route file: ${tsApiFilePath}`);
              console.log(`[API Route] Warning: TypeScript files cannot be imported directly. Using the JavaScript version instead.`);
              finalApiFilePath = apiFilePath; // Still use the .js path, but warn the user
            }

            if (fs.existsSync(finalApiFilePath)) {
              try {
                // Import the API route handler
                const apiModule = await import(finalApiFilePath);
                const handler = apiModule.default;

                // Parse the request body if it exists
                let body = null;
                if (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') {
                  const buffers = [];
                  for await (const chunk of req) {
                    buffers.push(chunk);
                  }
                  const data = Buffer.concat(buffers).toString();
                  if (data) {
                    try {
                      body = JSON.parse(data);
                    } catch (e) {
                      body = data;
                    }
                  }
                }

                // Create a simple request object
                const apiReq = {
                  method: req.method,
                  url,
                  headers: req.headers,
                  body,
                };

                // Create a simple response object
                const apiRes = {
                  statusCode: 200,
                  headers: {},

                  status(code) {
                    this.statusCode = code;
                    return this;
                  },

                  setHeader(name, value) {
                    this.headers[name] = value;
                    return this;
                  },

                  json(data) {
                    res.statusCode = this.statusCode;
                    for (const [name, value] of Object.entries(this.headers)) {
                      res.setHeader(name, value);
                    }
                    res.setHeader('Content-Type', 'application/json');
                    res.end(JSON.stringify(data));
                    return this;
                  },

                  end() {
                    res.statusCode = this.statusCode;
                    for (const [name, value] of Object.entries(this.headers)) {
                      res.setHeader(name, value);
                    }
                    res.end();
                    return this;
                  }
                };

                // Call the API route handler
                await handler(apiReq, apiRes);
              } catch (error) {
                console.error(`[API Route] Error handling API route:`, error);

                // Send an error response
                res.statusCode = 500;
                res.setHeader('Content-Type', 'application/json');
                res.end(JSON.stringify({
                  error: 'Internal Server Error',
                  message: error.message,
                  stack: error.stack
                }));
              }

              return;
            } else {
              console.error(`[API Route] API route file not found: ${finalApiFilePath}`);

              // Send a 404 response
              res.statusCode = 404;
              res.setHeader('Content-Type', 'application/json');
              res.end(JSON.stringify({
                error: 'Not Found',
                message: `API route not found: ${url}`
              }));

              return;
            }
          }

          next();
        });
      }
    }
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
